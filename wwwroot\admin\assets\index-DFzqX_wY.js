import{R as B,ai as Ji,r as u,ab as Jt,ac as Zt,F as Qe,E as Xe,a3 as Qr,$ as wa,g as Xr,d as A,aj as Tn,h as de,e as me,b as I,ak as Zi,v as ja,ad as oe,f as Ba,a as Z,u as ct,I as La,l as te,al as Qi,am as An,an as _a,ao as Xi,ap as Yi,aq as es,ar as ts,as as rs,at as ns,o as tt,s as Qt,au as as,av as zo,aw as xa,G as ut,z as ze,c as K,ax as os,W as is,_ as Q,M as Do,m as Yr,a2 as ss,ay as ls,az as Wo,H as Dr,C as pr,w as en,Z as cs,J as qo,n as Go,K as Uo,x as $a,aA as jn,aB as us,L as Ko,j as Wr,y as qe,aC as Xn,aD as ds,aE as fs,aF as ko,aG as vs,aH as cr,a0 as It,a1 as Ct,N as Ha,aa as gs,aI as Yn,aJ as ms,p as hs,q as ps,t as ys}from"./index-CgnIOky4.js";function Tt(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=[];return B.Children.forEach(e,function(a){a==null&&!t.keepEmpty||(Array.isArray(a)?r=r.concat(Tt(a)):Ji(a)&&a.props?r=r.concat(Tt(a.props.children,t)):r.push(a))}),r}var ea=u.createContext(null);function bs(e){var t=e.children,r=e.onBatchResize,a=u.useRef(0),n=u.useRef([]),o=u.useContext(ea),i=u.useCallback(function(s,l,c){a.current+=1;var d=a.current;n.current.push({size:s,element:l,data:c}),Promise.resolve().then(function(){d===a.current&&(r==null||r(n.current),n.current=[])}),o==null||o(s,l,c)},[r,o]);return u.createElement(ea.Provider,{value:i},t)}var Jo=function(){if(typeof Map<"u")return Map;function e(t,r){var a=-1;return t.some(function(n,o){return n[0]===r?(a=o,!0):!1}),a}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(r){var a=e(this.__entries__,r),n=this.__entries__[a];return n&&n[1]},t.prototype.set=function(r,a){var n=e(this.__entries__,r);~n?this.__entries__[n][1]=a:this.__entries__.push([r,a])},t.prototype.delete=function(r){var a=this.__entries__,n=e(a,r);~n&&a.splice(n,1)},t.prototype.has=function(r){return!!~e(this.__entries__,r)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(r,a){a===void 0&&(a=null);for(var n=0,o=this.__entries__;n<o.length;n++){var i=o[n];r.call(a,i[1],i[0])}},t}()}(),ta=typeof window<"u"&&typeof document<"u"&&window.document===document,qr=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),Cs=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(qr):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),Ss=2;function ws(e,t){var r=!1,a=!1,n=0;function o(){r&&(r=!1,e()),a&&s()}function i(){Cs(o)}function s(){var l=Date.now();if(r){if(l-n<Ss)return;a=!0}else r=!0,a=!1,setTimeout(i,t);n=l}return s}var xs=20,$s=["top","right","bottom","left","width","height","size","weight"],Es=typeof MutationObserver<"u",Fs=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=ws(this.refresh.bind(this),xs)}return e.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},e.prototype.removeObserver=function(t){var r=this.observers_,a=r.indexOf(t);~a&&r.splice(a,1),!r.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},e.prototype.updateObservers_=function(){var t=this.observers_.filter(function(r){return r.gatherActive(),r.hasActive()});return t.forEach(function(r){return r.broadcastActive()}),t.length>0},e.prototype.connect_=function(){!ta||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Es?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){!ta||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(t){var r=t.propertyName,a=r===void 0?"":r,n=$s.some(function(o){return!!~a.indexOf(o)});n&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),Zo=function(e,t){for(var r=0,a=Object.keys(t);r<a.length;r++){var n=a[r];Object.defineProperty(e,n,{value:t[n],enumerable:!1,writable:!1,configurable:!0})}return e},Kt=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||qr},Qo=tn(0,0,0,0);function Gr(e){return parseFloat(e)||0}function Va(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return t.reduce(function(a,n){var o=e["border-"+n+"-width"];return a+Gr(o)},0)}function Ps(e){for(var t=["top","right","bottom","left"],r={},a=0,n=t;a<n.length;a++){var o=n[a],i=e["padding-"+o];r[o]=Gr(i)}return r}function Rs(e){var t=e.getBBox();return tn(0,0,t.width,t.height)}function Os(e){var t=e.clientWidth,r=e.clientHeight;if(!t&&!r)return Qo;var a=Kt(e).getComputedStyle(e),n=Ps(a),o=n.left+n.right,i=n.top+n.bottom,s=Gr(a.width),l=Gr(a.height);if(a.boxSizing==="border-box"&&(Math.round(s+o)!==t&&(s-=Va(a,"left","right")+o),Math.round(l+i)!==r&&(l-=Va(a,"top","bottom")+i)),!Ns(e)){var c=Math.round(s+o)-t,d=Math.round(l+i)-r;Math.abs(c)!==1&&(s-=c),Math.abs(d)!==1&&(l-=d)}return tn(n.left,n.top,s,l)}var Is=function(){return typeof SVGGraphicsElement<"u"?function(e){return e instanceof Kt(e).SVGGraphicsElement}:function(e){return e instanceof Kt(e).SVGElement&&typeof e.getBBox=="function"}}();function Ns(e){return e===Kt(e).document.documentElement}function Ms(e){return ta?Is(e)?Rs(e):Os(e):Qo}function Ts(e){var t=e.x,r=e.y,a=e.width,n=e.height,o=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,i=Object.create(o.prototype);return Zo(i,{x:t,y:r,width:a,height:n,top:r,right:t+a,bottom:n+r,left:t}),i}function tn(e,t,r,a){return{x:e,y:t,width:r,height:a}}var As=function(){function e(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=tn(0,0,0,0),this.target=t}return e.prototype.isActive=function(){var t=Ms(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},e}(),js=function(){function e(t,r){var a=Ts(r);Zo(this,{target:t,contentRect:a})}return e}(),Bs=function(){function e(t,r,a){if(this.activeObservations_=[],this.observations_=new Jo,typeof t!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=r,this.callbackCtx_=a}return e.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof Kt(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var r=this.observations_;r.has(t)||(r.set(t,new As(t)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof Kt(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var r=this.observations_;r.has(t)&&(r.delete(t),r.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(r){r.isActive()&&t.activeObservations_.push(r)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,r=this.activeObservations_.map(function(a){return new js(a.target,a.broadcastRect())});this.callback_.call(t,r,t),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),Xo=typeof WeakMap<"u"?new WeakMap:new Jo,Yo=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var r=Fs.getInstance(),a=new Bs(t,r,this);Xo.set(this,a)}return e}();["observe","unobserve","disconnect"].forEach(function(e){Yo.prototype[e]=function(){var t;return(t=Xo.get(this))[e].apply(t,arguments)}});var Ls=function(){return typeof qr.ResizeObserver<"u"?qr.ResizeObserver:Yo}(),wt=new Map;function _s(e){e.forEach(function(t){var r,a=t.target;(r=wt.get(a))===null||r===void 0||r.forEach(function(n){return n(a)})})}var ei=new Ls(_s);function Hs(e,t){wt.has(e)||(wt.set(e,new Set),ei.observe(e)),wt.get(e).add(t)}function Vs(e,t){wt.has(e)&&(wt.get(e).delete(t),wt.get(e).size||(ei.unobserve(e),wt.delete(e)))}var zs=function(e){Jt(r,e);var t=Zt(r);function r(){return Qe(this,r),t.apply(this,arguments)}return Xe(r,[{key:"render",value:function(){return this.props.children}}]),r}(u.Component);function Ds(e,t){var r=e.children,a=e.disabled,n=u.useRef(null),o=u.useRef(null),i=u.useContext(ea),s=typeof r=="function",l=s?r(n):r,c=u.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),d=!s&&u.isValidElement(l)&&Qr(l),v=d?wa(l):null,g=Xr(v,n),y=function(){var C;return Tn(n.current)||(n.current&&de(n.current)==="object"?Tn((C=n.current)===null||C===void 0?void 0:C.nativeElement):null)||Tn(o.current)};u.useImperativeHandle(t,function(){return y()});var h=u.useRef(e);h.current=e;var m=u.useCallback(function(f){var C=h.current,p=C.onResize,S=C.data,R=f.getBoundingClientRect(),E=R.width,w=R.height,O=f.offsetWidth,F=f.offsetHeight,b=Math.floor(E),P=Math.floor(w);if(c.current.width!==b||c.current.height!==P||c.current.offsetWidth!==O||c.current.offsetHeight!==F){var M={width:b,height:P,offsetWidth:O,offsetHeight:F};c.current=M;var H=O===Math.round(E)?E:O,T=F===Math.round(w)?w:F,L=A(A({},M),{},{offsetWidth:H,offsetHeight:T});i==null||i(L,f,S),p&&Promise.resolve().then(function(){p(L,f)})}},[]);return u.useEffect(function(){var f=y();return f&&!a&&Hs(f,m),function(){return Vs(f,m)}},[n.current,a]),u.createElement(zs,{ref:o},d?u.cloneElement(l,{ref:g}):l)}var Ws=u.forwardRef(Ds),qs="rc-observer-key";function Gs(e,t){var r=e.children,a=typeof r=="function"?[r]:Tt(r);return a.map(function(n,o){var i=(n==null?void 0:n.key)||"".concat(qs,"-").concat(o);return u.createElement(Ws,me({},e,{key:i,ref:o===0?t:void 0}),n)})}var Ea=u.forwardRef(Gs);Ea.Collection=bs;var za=function(){function e(t,r){Qe(this,e),I(this,"name",void 0),I(this,"style",void 0),I(this,"_keyframe",!0),this.name=t,this.style=r}return Xe(e,[{key:"getName",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return r?"".concat(r,"-").concat(this.name):this.name}}]),e}();const Us=(e,t)=>{const r=u.useContext(Zi),a=u.useMemo(()=>{var o;const i=t||ja[e],s=(o=r==null?void 0:r[e])!==null&&o!==void 0?o:{};return Object.assign(Object.assign({},typeof i=="function"?i():i),s||{})},[e,t,r]),n=u.useMemo(()=>{const o=r==null?void 0:r.locale;return r!=null&&r.exist&&!o?ja.locale:o},[r]);return[a,n]};var ti=Xe(function e(){Qe(this,e)}),ri="CALC_UNIT",Ks=new RegExp(ri,"g");function Bn(e){return typeof e=="number"?"".concat(e).concat(ri):e}var ks=function(e){Jt(r,e);var t=Zt(r);function r(a,n){var o;Qe(this,r),o=t.call(this),I(oe(o),"result",""),I(oe(o),"unitlessCssVar",void 0),I(oe(o),"lowPriority",void 0);var i=de(a);return o.unitlessCssVar=n,a instanceof r?o.result="(".concat(a.result,")"):i==="number"?o.result=Bn(a):i==="string"&&(o.result=a),o}return Xe(r,[{key:"add",value:function(n){return n instanceof r?this.result="".concat(this.result," + ").concat(n.getResult()):(typeof n=="number"||typeof n=="string")&&(this.result="".concat(this.result," + ").concat(Bn(n))),this.lowPriority=!0,this}},{key:"sub",value:function(n){return n instanceof r?this.result="".concat(this.result," - ").concat(n.getResult()):(typeof n=="number"||typeof n=="string")&&(this.result="".concat(this.result," - ").concat(Bn(n))),this.lowPriority=!0,this}},{key:"mul",value:function(n){return this.lowPriority&&(this.result="(".concat(this.result,")")),n instanceof r?this.result="".concat(this.result," * ").concat(n.getResult(!0)):(typeof n=="number"||typeof n=="string")&&(this.result="".concat(this.result," * ").concat(n)),this.lowPriority=!1,this}},{key:"div",value:function(n){return this.lowPriority&&(this.result="(".concat(this.result,")")),n instanceof r?this.result="".concat(this.result," / ").concat(n.getResult(!0)):(typeof n=="number"||typeof n=="string")&&(this.result="".concat(this.result," / ").concat(n)),this.lowPriority=!1,this}},{key:"getResult",value:function(n){return this.lowPriority||n?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(n){var o=this,i=n||{},s=i.unit,l=!0;return typeof s=="boolean"?l=s:Array.from(this.unitlessCssVar).some(function(c){return o.result.includes(c)})&&(l=!1),this.result=this.result.replace(Ks,l?"px":""),typeof this.lowPriority<"u"?"calc(".concat(this.result,")"):this.result}}]),r}(ti),Js=function(e){Jt(r,e);var t=Zt(r);function r(a){var n;return Qe(this,r),n=t.call(this),I(oe(n),"result",0),a instanceof r?n.result=a.result:typeof a=="number"&&(n.result=a),n}return Xe(r,[{key:"add",value:function(n){return n instanceof r?this.result+=n.result:typeof n=="number"&&(this.result+=n),this}},{key:"sub",value:function(n){return n instanceof r?this.result-=n.result:typeof n=="number"&&(this.result-=n),this}},{key:"mul",value:function(n){return n instanceof r?this.result*=n.result:typeof n=="number"&&(this.result*=n),this}},{key:"div",value:function(n){return n instanceof r?this.result/=n.result:typeof n=="number"&&(this.result/=n),this}},{key:"equal",value:function(){return this.result}}]),r}(ti),Zs=function(t,r){var a=t==="css"?ks:Js;return function(n){return new a(n,r)}},Da=function(t,r){return"".concat([r,t.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))};function Ln(e){return e!==void 0}function Fa(e,t){var r=t||{},a=r.defaultValue,n=r.value,o=r.onChange,i=r.postState,s=Ba(function(){return Ln(n)?n:Ln(a)?typeof a=="function"?a():a:typeof e=="function"?e():e}),l=Z(s,2),c=l[0],d=l[1],v=n!==void 0?n:c,g=i?i(v):v,y=ct(o),h=Ba([v]),m=Z(h,2),f=m[0],C=m[1];La(function(){var S=f[0];c!==S&&y(c,S)},[f]),La(function(){Ln(n)||d(n)},[n]);var p=ct(function(S,R){d(S,R),C([v],R)});return[g,p]}function Wa(e,t,r,a){var n=A({},t[e]);if(a!=null&&a.deprecatedTokens){var o=a.deprecatedTokens;o.forEach(function(s){var l=Z(s,2),c=l[0],d=l[1];if(n!=null&&n[c]||n!=null&&n[d]){var v;(v=n[d])!==null&&v!==void 0||(n[d]=n==null?void 0:n[c])}})}var i=A(A({},r),n);return Object.keys(i).forEach(function(s){i[s]===t[s]&&delete i[s]}),i}var ni=typeof CSSINJS_STATISTIC<"u",ra=!0;function Ue(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!ni)return Object.assign.apply(Object,[{}].concat(t));ra=!1;var a={};return t.forEach(function(n){if(de(n)==="object"){var o=Object.keys(n);o.forEach(function(i){Object.defineProperty(a,i,{configurable:!0,enumerable:!0,get:function(){return n[i]}})})}}),ra=!0,a}var qa={};function Qs(){}var Xs=function(t){var r,a=t,n=Qs;return ni&&typeof Proxy<"u"&&(r=new Set,a=new Proxy(t,{get:function(i,s){if(ra){var l;(l=r)===null||l===void 0||l.add(s)}return i[s]}}),n=function(i,s){var l;qa[i]={global:Array.from(r),component:A(A({},(l=qa[i])===null||l===void 0?void 0:l.component),s)}}),{token:a,keys:r,flush:n}};function Ga(e,t,r){if(typeof r=="function"){var a;return r(Ue(t,(a=t[e])!==null&&a!==void 0?a:{}))}return r??{}}function Ys(e){return e==="js"?{max:Math.max,min:Math.min}:{max:function(){for(var r=arguments.length,a=new Array(r),n=0;n<r;n++)a[n]=arguments[n];return"max(".concat(a.map(function(o){return te(o)}).join(","),")")},min:function(){for(var r=arguments.length,a=new Array(r),n=0;n<r;n++)a[n]=arguments[n];return"min(".concat(a.map(function(o){return te(o)}).join(","),")")}}}var el=function(){return{}};function tl(e){var t=e.useCSP,r=t===void 0?el:t,a=e.useToken,n=e.usePrefix,o=e.getResetStyles,i=e.getCommonStyle,s=e.getCompUnitless;function l(g,y,h,m){var f=Array.isArray(g)?g[0]:g;function C(F){return"".concat(String(f)).concat(F.slice(0,1).toUpperCase()).concat(F.slice(1))}var p=(m==null?void 0:m.unitless)||{},S=typeof s=="function"?s(g):{},R=A(A({},S),{},I({},C("zIndexPopup"),!0));Object.keys(p).forEach(function(F){R[C(F)]=p[F]});var E=A(A({},m),{},{unitless:R,prefixToken:C}),w=d(g,y,h,E),O=c(f,h,E);return function(F){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:F,P=w(F,b),M=Z(P,2),H=M[1],T=O(b),L=Z(T,2),W=L[0],x=L[1];return[W,H,x]}}function c(g,y,h){var m=h.unitless,f=h.injectStyle,C=f===void 0?!0:f,p=h.prefixToken,S=h.ignore,R=function(O){var F=O.rootCls,b=O.cssVar,P=b===void 0?{}:b,M=a(),H=M.realToken;return Xi({path:[g],prefix:P.prefix,key:P.key,unitless:m,ignore:S,token:H,scope:F},function(){var T=Ga(g,H,y),L=Wa(g,H,T,{deprecatedTokens:h==null?void 0:h.deprecatedTokens});return Object.keys(T).forEach(function(W){L[p(W)]=L[W],delete L[W]}),L}),null},E=function(O){var F=a(),b=F.cssVar;return[function(P){return C&&b?B.createElement(B.Fragment,null,B.createElement(R,{rootCls:O,cssVar:b,component:g}),P):P},b==null?void 0:b.key]};return E}function d(g,y,h){var m=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},f=Array.isArray(g)?g:[g,g],C=Z(f,1),p=C[0],S=f.join("-"),R=e.layer||{name:"antd"};return function(E){var w=arguments.length>1&&arguments[1]!==void 0?arguments[1]:E,O=a(),F=O.theme,b=O.realToken,P=O.hashId,M=O.token,H=O.cssVar,T=n(),L=T.rootPrefixCls,W=T.iconPrefixCls,x=r(),$=H?"css":"js",N=Qi(function(){var j=new Set;return H&&Object.keys(m.unitless||{}).forEach(function(D){j.add(An(D,H.prefix)),j.add(An(D,Da(p,H.prefix)))}),Zs($,j)},[$,p,H==null?void 0:H.prefix]),V=Ys($),_=V.max,z=V.min,G={theme:F,token:M,hashId:P,nonce:function(){return x.nonce},clientOnly:m.clientOnly,layer:R,order:m.order||-999};typeof o=="function"&&_a(A(A({},G),{},{clientOnly:!1,path:["Shared",L]}),function(){return o(M,{prefix:{rootPrefixCls:L,iconPrefixCls:W},csp:x})});var U=_a(A(A({},G),{},{path:[S,E,W]}),function(){if(m.injectStyle===!1)return[];var j=Xs(M),D=j.token,k=j.flush,re=Ga(p,b,h),ne=".".concat(E),ee=Wa(p,b,re,{deprecatedTokens:m.deprecatedTokens});H&&re&&de(re)==="object"&&Object.keys(re).forEach(function(ge){re[ge]="var(".concat(An(ge,Da(p,H.prefix)),")")});var le=Ue(D,{componentCls:ne,prefixCls:E,iconCls:".".concat(W),antCls:".".concat(L),calc:N,max:_,min:z},H?re:ee),be=y(le,{hashId:P,prefixCls:E,rootPrefixCls:L,iconPrefixCls:W});k(p,ee);var se=typeof i=="function"?i(le,E,w,m.resetFont):null;return[m.resetStyle===!1?null:se,be]});return[U,P]}}function v(g,y,h){var m=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},f=d(g,y,h,A({resetStyle:!1,order:-998},m)),C=function(S){var R=S.prefixCls,E=S.rootCls,w=E===void 0?R:E;return f(R,w),null};return C}return{genStyleHooks:l,genSubStyleComponent:v,genComponentStyleHook:d}}const Pa=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"],{genStyleHooks:Xt,genComponentStyleHook:rl,genSubStyleComponent:nl}=tl({usePrefix:()=>{const{getPrefixCls:e,iconPrefixCls:t}=u.useContext(tt);return{rootPrefixCls:e(),iconPrefixCls:t}},useToken:()=>{const[e,t,r,a,n]=Qt();return{theme:e,realToken:t,hashId:r,token:a,cssVar:n}},useCSP:()=>{const{csp:e}=u.useContext(tt);return e??{}},getResetStyles:(e,t)=>{var r;const a=ts(e);return[a,{"&":a},rs((r=t==null?void 0:t.prefix.iconPrefixCls)!==null&&r!==void 0?r:ns)]},getCommonStyle:Yi,getCompUnitless:()=>es});var al={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"};function ai(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}function ol(e){return ai(e)instanceof ShadowRoot}function Ur(e){return ol(e)?ai(e):null}function il(e){return e.replace(/-(.)/g,function(t,r){return r.toUpperCase()})}function sl(e,t){ut(e,"[@ant-design/icons] ".concat(t))}function Ua(e){return de(e)==="object"&&typeof e.name=="string"&&typeof e.theme=="string"&&(de(e.icon)==="object"||typeof e.icon=="function")}function Ka(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(e).reduce(function(t,r){var a=e[r];switch(r){case"class":t.className=a,delete t.class;break;default:delete t[r],t[il(r)]=a}return t},{})}function na(e,t,r){return r?B.createElement(e.tag,A(A({key:t},Ka(e.attrs)),r),(e.children||[]).map(function(a,n){return na(a,"".concat(t,"-").concat(e.tag,"-").concat(n))})):B.createElement(e.tag,A({key:t},Ka(e.attrs)),(e.children||[]).map(function(a,n){return na(a,"".concat(t,"-").concat(e.tag,"-").concat(n))}))}function oi(e){return as(e)[0]}function ii(e){return e?Array.isArray(e)?e:[e]:[]}var ll=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,cl=function(t){var r=u.useContext(zo),a=r.csp,n=r.prefixCls,o=r.layer,i=ll;n&&(i=i.replace(/anticon/g,n)),o&&(i="@layer ".concat(o,` {
`).concat(i,`
}`)),u.useEffect(function(){var s=t.current,l=Ur(s);xa(i,"@ant-design-icons",{prepend:!o,csp:a,attachTo:l})},[])},ul=["icon","className","onClick","style","primaryColor","secondaryColor"],dr={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function dl(e){var t=e.primaryColor,r=e.secondaryColor;dr.primaryColor=t,dr.secondaryColor=r||oi(t),dr.calculated=!!r}function fl(){return A({},dr)}var Yt=function(t){var r=t.icon,a=t.className,n=t.onClick,o=t.style,i=t.primaryColor,s=t.secondaryColor,l=ze(t,ul),c=u.useRef(),d=dr;if(i&&(d={primaryColor:i,secondaryColor:s||oi(i)}),cl(c),sl(Ua(r),"icon should be icon definiton, but got ".concat(r)),!Ua(r))return null;var v=r;return v&&typeof v.icon=="function"&&(v=A(A({},v),{},{icon:v.icon(d.primaryColor,d.secondaryColor)})),na(v.icon,"svg-".concat(v.name),A(A({className:a,onClick:n,style:o,"data-icon":v.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},l),{},{ref:c}))};Yt.displayName="IconReact";Yt.getTwoToneColors=fl;Yt.setTwoToneColors=dl;function si(e){var t=ii(e),r=Z(t,2),a=r[0],n=r[1];return Yt.setTwoToneColors({primaryColor:a,secondaryColor:n})}function vl(){var e=Yt.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor}var gl=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];si(os.primary);var rt=u.forwardRef(function(e,t){var r=e.className,a=e.icon,n=e.spin,o=e.rotate,i=e.tabIndex,s=e.onClick,l=e.twoToneColor,c=ze(e,gl),d=u.useContext(zo),v=d.prefixCls,g=v===void 0?"anticon":v,y=d.rootClassName,h=K(y,g,I(I({},"".concat(g,"-").concat(a.name),!!a.name),"".concat(g,"-spin"),!!n||a.name==="loading"),r),m=i;m===void 0&&s&&(m=-1);var f=o?{msTransform:"rotate(".concat(o,"deg)"),transform:"rotate(".concat(o,"deg)")}:void 0,C=ii(l),p=Z(C,2),S=p[0],R=p[1];return u.createElement("span",me({role:"img","aria-label":a.name},c,{ref:t,tabIndex:m,onClick:s,className:h}),u.createElement(Yt,{icon:a,primaryColor:S,secondaryColor:R,style:f}))});rt.displayName="AntdIcon";rt.getTwoToneColor=vl;rt.setTwoToneColor=si;var ml=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:al}))},hl=u.forwardRef(ml),pl={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-circle",theme:"filled"},yl=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:pl}))},li=u.forwardRef(yl),bl={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"},Cl=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:bl}))},Sl=u.forwardRef(Cl),wl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"exclamation-circle",theme:"filled"},xl=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:wl}))},$l=u.forwardRef(xl),El={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"},Fl=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:El}))},Pl=u.forwardRef(Fl),Rl=`accept acceptCharset accessKey action allowFullScreen allowTransparency
    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge
    charSet checked classID className colSpan cols content contentEditable contextMenu
    controls coords crossOrigin data dateTime default defer dir disabled download draggable
    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder
    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity
    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media
    mediaGroup method min minLength multiple muted name noValidate nonce open
    optimum pattern placeholder poster preload radioGroup readOnly rel required
    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected
    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style
    summary tabIndex target title type useMap value width wmode wrap`,Ol=`onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown
    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick
    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown
    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel
    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough
    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata
    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError`,Il="".concat(Rl," ").concat(Ol).split(/[\s\n]+/),Nl="aria-",Ml="data-";function ka(e,t){return e.indexOf(t)===0}function ci(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r;t===!1?r={aria:!0,data:!0,attr:!0}:t===!0?r={aria:!0}:r=A({},t);var a={};return Object.keys(e).forEach(function(n){(r.aria&&(n==="role"||ka(n,Nl))||r.data&&ka(n,Ml)||r.attr&&Il.includes(n))&&(a[n]=e[n])}),a}function Tl(e){return e&&B.isValidElement(e)&&e.type===B.Fragment}const Al=(e,t,r)=>B.isValidElement(e)?B.cloneElement(e,typeof r=="function"?r(e.props||{}):r):t;function Kr(e,t){return Al(e,e,t)}const Ra=e=>{const[,,,,t]=Qt();return t?`${e}-css-var`:""};var aa={BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46,N:78,P:80,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,SEMICOLON:186,EQUALS:187,WIN_KEY:224},ui=u.forwardRef(function(e,t){var r=e.prefixCls,a=e.style,n=e.className,o=e.duration,i=o===void 0?4.5:o,s=e.showProgress,l=e.pauseOnHover,c=l===void 0?!0:l,d=e.eventKey,v=e.content,g=e.closable,y=e.closeIcon,h=y===void 0?"x":y,m=e.props,f=e.onClick,C=e.onNoticeClose,p=e.times,S=e.hovering,R=u.useState(!1),E=Z(R,2),w=E[0],O=E[1],F=u.useState(0),b=Z(F,2),P=b[0],M=b[1],H=u.useState(0),T=Z(H,2),L=T[0],W=T[1],x=S||w,$=i>0&&s,N=function(){C(d)},V=function(D){(D.key==="Enter"||D.code==="Enter"||D.keyCode===aa.ENTER)&&N()};u.useEffect(function(){if(!x&&i>0){var j=Date.now()-L,D=setTimeout(function(){N()},i*1e3-L);return function(){c&&clearTimeout(D),W(Date.now()-j)}}},[i,x,p]),u.useEffect(function(){if(!x&&$&&(c||L===0)){var j=performance.now(),D,k=function re(){cancelAnimationFrame(D),D=requestAnimationFrame(function(ne){var ee=ne+L-j,le=Math.min(ee/(i*1e3),1);M(le*100),le<1&&re()})};return k(),function(){c&&cancelAnimationFrame(D)}}},[i,L,x,$,p]);var _=u.useMemo(function(){return de(g)==="object"&&g!==null?g:g?{closeIcon:h}:{}},[g,h]),z=ci(_,!0),G=100-(!P||P<0?0:P>100?100:P),U="".concat(r,"-notice");return u.createElement("div",me({},m,{ref:t,className:K(U,n,I({},"".concat(U,"-closable"),g)),style:a,onMouseEnter:function(D){var k;O(!0),m==null||(k=m.onMouseEnter)===null||k===void 0||k.call(m,D)},onMouseLeave:function(D){var k;O(!1),m==null||(k=m.onMouseLeave)===null||k===void 0||k.call(m,D)},onClick:f}),u.createElement("div",{className:"".concat(U,"-content")},v),g&&u.createElement("a",me({tabIndex:0,className:"".concat(U,"-close"),onKeyDown:V,"aria-label":"Close"},z,{onClick:function(D){D.preventDefault(),D.stopPropagation(),N()}}),_.closeIcon),$&&u.createElement("progress",{className:"".concat(U,"-progress"),max:"100",value:G},G+"%"))}),di=B.createContext({}),jl=function(t){var r=t.children,a=t.classNames;return B.createElement(di.Provider,{value:{classNames:a}},r)},Ja=8,Za=3,Qa=16,Bl=function(t){var r={offset:Ja,threshold:Za,gap:Qa};if(t&&de(t)==="object"){var a,n,o;r.offset=(a=t.offset)!==null&&a!==void 0?a:Ja,r.threshold=(n=t.threshold)!==null&&n!==void 0?n:Za,r.gap=(o=t.gap)!==null&&o!==void 0?o:Qa}return[!!t,r]},Ll=["className","style","classNames","styles"],_l=function(t){var r=t.configList,a=t.placement,n=t.prefixCls,o=t.className,i=t.style,s=t.motion,l=t.onAllNoticeRemoved,c=t.onNoticeClose,d=t.stack,v=u.useContext(di),g=v.classNames,y=u.useRef({}),h=u.useState(null),m=Z(h,2),f=m[0],C=m[1],p=u.useState([]),S=Z(p,2),R=S[0],E=S[1],w=r.map(function(x){return{config:x,key:String(x.key)}}),O=Bl(d),F=Z(O,2),b=F[0],P=F[1],M=P.offset,H=P.threshold,T=P.gap,L=b&&(R.length>0||w.length<=H),W=typeof s=="function"?s(a):s;return u.useEffect(function(){b&&R.length>1&&E(function(x){return x.filter(function($){return w.some(function(N){var V=N.key;return $===V})})})},[R,w,b]),u.useEffect(function(){var x;if(b&&y.current[(x=w[w.length-1])===null||x===void 0?void 0:x.key]){var $;C(y.current[($=w[w.length-1])===null||$===void 0?void 0:$.key])}},[w,b]),B.createElement(is,me({key:a,className:K(n,"".concat(n,"-").concat(a),g==null?void 0:g.list,o,I(I({},"".concat(n,"-stack"),!!b),"".concat(n,"-stack-expanded"),L)),style:i,keys:w,motionAppear:!0},W,{onAllRemoved:function(){l(a)}}),function(x,$){var N=x.config,V=x.className,_=x.style,z=x.index,G=N,U=G.key,j=G.times,D=String(U),k=N,re=k.className,ne=k.style,ee=k.classNames,le=k.styles,be=ze(k,Ll),se=w.findIndex(function(Y){return Y.key===D}),ge={};if(b){var fe=w.length-1-(se>-1?se:z-1),ce=a==="top"||a==="bottom"?"-50%":"0";if(fe>0){var Ce,Fe,ue;ge.height=L?(Ce=y.current[D])===null||Ce===void 0?void 0:Ce.offsetHeight:f==null?void 0:f.offsetHeight;for(var we=0,Ae=0;Ae<fe;Ae++){var xe;we+=((xe=y.current[w[w.length-1-Ae].key])===null||xe===void 0?void 0:xe.offsetHeight)+T}var nt=(L?we:fe*M)*(a.startsWith("top")?1:-1),J=!L&&f!==null&&f!==void 0&&f.offsetWidth&&(Fe=y.current[D])!==null&&Fe!==void 0&&Fe.offsetWidth?((f==null?void 0:f.offsetWidth)-M*2*(fe<3?fe:3))/((ue=y.current[D])===null||ue===void 0?void 0:ue.offsetWidth):1;ge.transform="translate3d(".concat(ce,", ").concat(nt,"px, 0) scaleX(").concat(J,")")}else ge.transform="translate3d(".concat(ce,", 0, 0)")}return B.createElement("div",{ref:$,className:K("".concat(n,"-notice-wrapper"),V,ee==null?void 0:ee.wrapper),style:A(A(A({},_),ge),le==null?void 0:le.wrapper),onMouseEnter:function(){return E(function(ae){return ae.includes(D)?ae:[].concat(Q(ae),[D])})},onMouseLeave:function(){return E(function(ae){return ae.filter(function(pe){return pe!==D})})}},B.createElement(ui,me({},be,{ref:function(ae){se>-1?y.current[D]=ae:delete y.current[D]},prefixCls:n,classNames:ee,styles:le,className:K(re,g==null?void 0:g.notice),style:ne,times:j,key:U,eventKey:U,onNoticeClose:c,hovering:b&&R.length>0})))})},Hl=u.forwardRef(function(e,t){var r=e.prefixCls,a=r===void 0?"rc-notification":r,n=e.container,o=e.motion,i=e.maxCount,s=e.className,l=e.style,c=e.onAllRemoved,d=e.stack,v=e.renderNotifications,g=u.useState([]),y=Z(g,2),h=y[0],m=y[1],f=function(b){var P,M=h.find(function(H){return H.key===b});M==null||(P=M.onClose)===null||P===void 0||P.call(M),m(function(H){return H.filter(function(T){return T.key!==b})})};u.useImperativeHandle(t,function(){return{open:function(b){m(function(P){var M=Q(P),H=M.findIndex(function(W){return W.key===b.key}),T=A({},b);if(H>=0){var L;T.times=(((L=P[H])===null||L===void 0?void 0:L.times)||0)+1,M[H]=T}else T.times=0,M.push(T);return i>0&&M.length>i&&(M=M.slice(-i)),M})},close:function(b){f(b)},destroy:function(){m([])}}});var C=u.useState({}),p=Z(C,2),S=p[0],R=p[1];u.useEffect(function(){var F={};h.forEach(function(b){var P=b.placement,M=P===void 0?"topRight":P;M&&(F[M]=F[M]||[],F[M].push(b))}),Object.keys(S).forEach(function(b){F[b]=F[b]||[]}),R(F)},[h]);var E=function(b){R(function(P){var M=A({},P),H=M[b]||[];return H.length||delete M[b],M})},w=u.useRef(!1);if(u.useEffect(function(){Object.keys(S).length>0?w.current=!0:w.current&&(c==null||c(),w.current=!1)},[S]),!n)return null;var O=Object.keys(S);return Do.createPortal(u.createElement(u.Fragment,null,O.map(function(F){var b=S[F],P=u.createElement(_l,{key:F,configList:b,placement:F,prefixCls:a,className:s==null?void 0:s(F),style:l==null?void 0:l(F),motion:o,onNoticeClose:f,onAllNoticeRemoved:E,stack:d});return v?v(P,{prefixCls:a,key:F}):P})),n)}),Vl=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],zl=function(){return document.body},Xa=0;function Dl(){for(var e={},t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return r.forEach(function(n){n&&Object.keys(n).forEach(function(o){var i=n[o];i!==void 0&&(e[o]=i)})}),e}function Wl(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.getContainer,r=t===void 0?zl:t,a=e.motion,n=e.prefixCls,o=e.maxCount,i=e.className,s=e.style,l=e.onAllRemoved,c=e.stack,d=e.renderNotifications,v=ze(e,Vl),g=u.useState(),y=Z(g,2),h=y[0],m=y[1],f=u.useRef(),C=u.createElement(Hl,{container:h,ref:f,prefixCls:n,motion:a,maxCount:o,className:i,style:s,onAllRemoved:l,stack:c,renderNotifications:d}),p=u.useState([]),S=Z(p,2),R=S[0],E=S[1],w=u.useMemo(function(){return{open:function(F){var b=Dl(v,F);(b.key===null||b.key===void 0)&&(b.key="rc-notification-".concat(Xa),Xa+=1),E(function(P){return[].concat(Q(P),[{type:"open",config:b}])})},close:function(F){E(function(b){return[].concat(Q(b),[{type:"close",key:F}])})},destroy:function(){E(function(F){return[].concat(Q(F),[{type:"destroy"}])})}}},[]);return u.useEffect(function(){m(r())}),u.useEffect(function(){if(f.current&&R.length){R.forEach(function(b){switch(b.type){case"open":f.current.open(b.config);break;case"close":f.current.close(b.key);break;case"destroy":f.current.destroy();break}});var O,F;E(function(b){return(O!==b||!F)&&(O=b,F=b.filter(function(P){return!R.includes(P)})),F})}},[R]),[w,C]}var ql={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"}}]},name:"loading",theme:"outlined"},Gl=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:ql}))},fi=u.forwardRef(Gl);const Ul=B.createContext(void 0),St=100,Kl=10,kl=St*Kl,vi={Modal:St,Drawer:St,Popover:St,Popconfirm:St,Tooltip:St,Tour:St,FloatButton:St},Jl={SelectLike:50,Dropdown:50,DatePicker:50,Menu:50,ImagePreview:1};function Zl(e){return e in vi}const tv=(e,t)=>{const[,r]=Qt(),a=B.useContext(Ul),n=Zl(e);let o;if(t!==void 0)o=[t,t];else{let i=a??0;n?i+=(a?0:r.zIndexPopupBase)+vi[e]:i+=Jl[e],o=[a===void 0?t:i,i]}return o},Ql=e=>{const{componentCls:t,iconCls:r,boxShadow:a,colorText:n,colorSuccess:o,colorError:i,colorWarning:s,colorInfo:l,fontSizeLG:c,motionEaseInOutCirc:d,motionDurationSlow:v,marginXS:g,paddingXS:y,borderRadiusLG:h,zIndexPopup:m,contentPadding:f,contentBg:C}=e,p=`${t}-notice`,S=new za("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:y,transform:"translateY(0)",opacity:1}}),R=new za("MessageMoveOut",{"0%":{maxHeight:e.height,padding:y,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),E={padding:y,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${r}`]:{marginInlineEnd:g,fontSize:c},[`${p}-content`]:{display:"inline-block",padding:f,background:C,borderRadius:h,boxShadow:a,pointerEvents:"all"},[`${t}-success > ${r}`]:{color:o},[`${t}-error > ${r}`]:{color:i},[`${t}-warning > ${r}`]:{color:s},[`${t}-info > ${r},
      ${t}-loading > ${r}`]:{color:l}};return[{[t]:Object.assign(Object.assign({},Yr(e)),{color:n,position:"fixed",top:g,width:"100%",pointerEvents:"none",zIndex:m,[`${t}-move-up`]:{animationFillMode:"forwards"},[`
        ${t}-move-up-appear,
        ${t}-move-up-enter
      `]:{animationName:S,animationDuration:v,animationPlayState:"paused",animationTimingFunction:d},[`
        ${t}-move-up-appear${t}-move-up-appear-active,
        ${t}-move-up-enter${t}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:R,animationDuration:v,animationPlayState:"paused",animationTimingFunction:d},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${p}-wrapper`]:Object.assign({},E)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},E),{padding:0,textAlign:"start"})}]},Xl=e=>({zIndexPopup:e.zIndexPopupBase+kl+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`}),gi=Xt("Message",e=>{const t=Ue(e,{height:150});return[Ql(t)]},Xl);var Yl=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};const ec={info:u.createElement(Pl,null),success:u.createElement(hl,null),error:u.createElement(li,null),warning:u.createElement($l,null),loading:u.createElement(fi,null)},mi=e=>{let{prefixCls:t,type:r,icon:a,children:n}=e;return u.createElement("div",{className:K(`${t}-custom-content`,`${t}-${r}`)},a||ec[r],u.createElement("span",null,n))},tc=e=>{const{prefixCls:t,className:r,type:a,icon:n,content:o}=e,i=Yl(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:s}=u.useContext(tt),l=t||s("message"),c=Ra(l),[d,v,g]=gi(l,c);return d(u.createElement(ui,Object.assign({},i,{prefixCls:l,className:K(r,v,`${l}-notice-pure-panel`,g,c),eventKey:"pure",duration:null,content:u.createElement(mi,{prefixCls:l,type:a,icon:n},o)})))};function rc(e,t){return{motionName:t??`${e}-move-up`}}function Oa(e){let t;const r=new Promise(n=>{t=e(()=>{n(!0)})}),a=()=>{t==null||t()};return a.then=(n,o)=>r.then(n,o),a.promise=r,a}var nc=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};const ac=8,oc=3,ic=e=>{let{children:t,prefixCls:r}=e;const a=Ra(r),[n,o,i]=gi(r,a);return n(u.createElement(jl,{classNames:{list:K(o,i,a)}},t))},sc=(e,t)=>{let{prefixCls:r,key:a}=t;return u.createElement(ic,{prefixCls:r,key:a},e)},lc=u.forwardRef((e,t)=>{const{top:r,prefixCls:a,getContainer:n,maxCount:o,duration:i=oc,rtl:s,transitionName:l,onAllRemoved:c}=e,{getPrefixCls:d,getPopupContainer:v,message:g,direction:y}=u.useContext(tt),h=a||d("message"),m=()=>({left:"50%",transform:"translateX(-50%)",top:r??ac}),f=()=>K({[`${h}-rtl`]:s??y==="rtl"}),C=()=>rc(h,l),p=u.createElement("span",{className:`${h}-close-x`},u.createElement(Sl,{className:`${h}-close-icon`})),[S,R]=Wl({prefixCls:h,style:m,className:f,motion:C,closable:!1,closeIcon:p,duration:i,getContainer:()=>(n==null?void 0:n())||(v==null?void 0:v())||document.body,maxCount:o,onAllRemoved:c,renderNotifications:sc});return u.useImperativeHandle(t,()=>Object.assign(Object.assign({},S),{prefixCls:h,message:g})),R});let Ya=0;function hi(e){const t=u.useRef(null);return ss(),[u.useMemo(()=>{const a=l=>{var c;(c=t.current)===null||c===void 0||c.close(l)},n=l=>{if(!t.current){const w=()=>{};return w.then=()=>{},w}const{open:c,prefixCls:d,message:v}=t.current,g=`${d}-notice`,{content:y,icon:h,type:m,key:f,className:C,style:p,onClose:S}=l,R=nc(l,["content","icon","type","key","className","style","onClose"]);let E=f;return E==null&&(Ya+=1,E=`antd-message-${Ya}`),Oa(w=>(c(Object.assign(Object.assign({},R),{key:E,content:u.createElement(mi,{prefixCls:d,type:m,icon:h},y),placement:"top",className:K(m&&`${g}-${m}`,C,v==null?void 0:v.className),style:Object.assign(Object.assign({},v==null?void 0:v.style),p),onClose:()=>{S==null||S(),w()}})),()=>{a(E)}))},i={open:n,destroy:l=>{var c;l!==void 0?a(l):(c=t.current)===null||c===void 0||c.destroy()}};return["info","success","warning","error","loading"].forEach(l=>{const c=(d,v,g)=>{let y;d&&typeof d=="object"&&"content"in d?y=d:y={content:d};let h,m;typeof v=="function"?m=v:(h=v,m=g);const f=Object.assign(Object.assign({onClose:m,duration:h},y),{type:l});return n(f)};i[l]=c}),i},[]),u.createElement(lc,Object.assign({key:"message-holder"},e,{ref:t}))]}function cc(e){return hi(e)}function _e(){_e=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(x,$,N){x[$]=N.value},o=typeof Symbol=="function"?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(x,$,N){return Object.defineProperty(x,$,{value:N,enumerable:!0,configurable:!0,writable:!0}),x[$]}try{c({},"")}catch{c=function(N,V,_){return N[V]=_}}function d(x,$,N,V){var _=$&&$.prototype instanceof C?$:C,z=Object.create(_.prototype),G=new L(V||[]);return n(z,"_invoke",{value:P(x,N,G)}),z}function v(x,$,N){try{return{type:"normal",arg:x.call($,N)}}catch(V){return{type:"throw",arg:V}}}t.wrap=d;var g="suspendedStart",y="suspendedYield",h="executing",m="completed",f={};function C(){}function p(){}function S(){}var R={};c(R,i,function(){return this});var E=Object.getPrototypeOf,w=E&&E(E(W([])));w&&w!==r&&a.call(w,i)&&(R=w);var O=S.prototype=C.prototype=Object.create(R);function F(x){["next","throw","return"].forEach(function($){c(x,$,function(N){return this._invoke($,N)})})}function b(x,$){function N(_,z,G,U){var j=v(x[_],x,z);if(j.type!=="throw"){var D=j.arg,k=D.value;return k&&de(k)=="object"&&a.call(k,"__await")?$.resolve(k.__await).then(function(re){N("next",re,G,U)},function(re){N("throw",re,G,U)}):$.resolve(k).then(function(re){D.value=re,G(D)},function(re){return N("throw",re,G,U)})}U(j.arg)}var V;n(this,"_invoke",{value:function(z,G){function U(){return new $(function(j,D){N(z,G,j,D)})}return V=V?V.then(U,U):U()}})}function P(x,$,N){var V=g;return function(_,z){if(V===h)throw Error("Generator is already running");if(V===m){if(_==="throw")throw z;return{value:e,done:!0}}for(N.method=_,N.arg=z;;){var G=N.delegate;if(G){var U=M(G,N);if(U){if(U===f)continue;return U}}if(N.method==="next")N.sent=N._sent=N.arg;else if(N.method==="throw"){if(V===g)throw V=m,N.arg;N.dispatchException(N.arg)}else N.method==="return"&&N.abrupt("return",N.arg);V=h;var j=v(x,$,N);if(j.type==="normal"){if(V=N.done?m:y,j.arg===f)continue;return{value:j.arg,done:N.done}}j.type==="throw"&&(V=m,N.method="throw",N.arg=j.arg)}}}function M(x,$){var N=$.method,V=x.iterator[N];if(V===e)return $.delegate=null,N==="throw"&&x.iterator.return&&($.method="return",$.arg=e,M(x,$),$.method==="throw")||N!=="return"&&($.method="throw",$.arg=new TypeError("The iterator does not provide a '"+N+"' method")),f;var _=v(V,x.iterator,$.arg);if(_.type==="throw")return $.method="throw",$.arg=_.arg,$.delegate=null,f;var z=_.arg;return z?z.done?($[x.resultName]=z.value,$.next=x.nextLoc,$.method!=="return"&&($.method="next",$.arg=e),$.delegate=null,f):z:($.method="throw",$.arg=new TypeError("iterator result is not an object"),$.delegate=null,f)}function H(x){var $={tryLoc:x[0]};1 in x&&($.catchLoc=x[1]),2 in x&&($.finallyLoc=x[2],$.afterLoc=x[3]),this.tryEntries.push($)}function T(x){var $=x.completion||{};$.type="normal",delete $.arg,x.completion=$}function L(x){this.tryEntries=[{tryLoc:"root"}],x.forEach(H,this),this.reset(!0)}function W(x){if(x||x===""){var $=x[i];if($)return $.call(x);if(typeof x.next=="function")return x;if(!isNaN(x.length)){var N=-1,V=function _(){for(;++N<x.length;)if(a.call(x,N))return _.value=x[N],_.done=!1,_;return _.value=e,_.done=!0,_};return V.next=V}}throw new TypeError(de(x)+" is not iterable")}return p.prototype=S,n(O,"constructor",{value:S,configurable:!0}),n(S,"constructor",{value:p,configurable:!0}),p.displayName=c(S,l,"GeneratorFunction"),t.isGeneratorFunction=function(x){var $=typeof x=="function"&&x.constructor;return!!$&&($===p||($.displayName||$.name)==="GeneratorFunction")},t.mark=function(x){return Object.setPrototypeOf?Object.setPrototypeOf(x,S):(x.__proto__=S,c(x,l,"GeneratorFunction")),x.prototype=Object.create(O),x},t.awrap=function(x){return{__await:x}},F(b.prototype),c(b.prototype,s,function(){return this}),t.AsyncIterator=b,t.async=function(x,$,N,V,_){_===void 0&&(_=Promise);var z=new b(d(x,$,N,V),_);return t.isGeneratorFunction($)?z:z.next().then(function(G){return G.done?G.value:z.next()})},F(O),c(O,l,"Generator"),c(O,i,function(){return this}),c(O,"toString",function(){return"[object Generator]"}),t.keys=function(x){var $=Object(x),N=[];for(var V in $)N.push(V);return N.reverse(),function _(){for(;N.length;){var z=N.pop();if(z in $)return _.value=z,_.done=!1,_}return _.done=!0,_}},t.values=W,L.prototype={constructor:L,reset:function($){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!$)for(var N in this)N.charAt(0)==="t"&&a.call(this,N)&&!isNaN(+N.slice(1))&&(this[N]=e)},stop:function(){this.done=!0;var $=this.tryEntries[0].completion;if($.type==="throw")throw $.arg;return this.rval},dispatchException:function($){if(this.done)throw $;var N=this;function V(D,k){return G.type="throw",G.arg=$,N.next=D,k&&(N.method="next",N.arg=e),!!k}for(var _=this.tryEntries.length-1;_>=0;--_){var z=this.tryEntries[_],G=z.completion;if(z.tryLoc==="root")return V("end");if(z.tryLoc<=this.prev){var U=a.call(z,"catchLoc"),j=a.call(z,"finallyLoc");if(U&&j){if(this.prev<z.catchLoc)return V(z.catchLoc,!0);if(this.prev<z.finallyLoc)return V(z.finallyLoc)}else if(U){if(this.prev<z.catchLoc)return V(z.catchLoc,!0)}else{if(!j)throw Error("try statement without catch or finally");if(this.prev<z.finallyLoc)return V(z.finallyLoc)}}}},abrupt:function($,N){for(var V=this.tryEntries.length-1;V>=0;--V){var _=this.tryEntries[V];if(_.tryLoc<=this.prev&&a.call(_,"finallyLoc")&&this.prev<_.finallyLoc){var z=_;break}}z&&($==="break"||$==="continue")&&z.tryLoc<=N&&N<=z.finallyLoc&&(z=null);var G=z?z.completion:{};return G.type=$,G.arg=N,z?(this.method="next",this.next=z.finallyLoc,f):this.complete(G)},complete:function($,N){if($.type==="throw")throw $.arg;return $.type==="break"||$.type==="continue"?this.next=$.arg:$.type==="return"?(this.rval=this.arg=$.arg,this.method="return",this.next="end"):$.type==="normal"&&N&&(this.next=N),f},finish:function($){for(var N=this.tryEntries.length-1;N>=0;--N){var V=this.tryEntries[N];if(V.finallyLoc===$)return this.complete(V.completion,V.afterLoc),T(V),f}},catch:function($){for(var N=this.tryEntries.length-1;N>=0;--N){var V=this.tryEntries[N];if(V.tryLoc===$){var _=V.completion;if(_.type==="throw"){var z=_.arg;T(V)}return z}}throw Error("illegal catch attempt")},delegateYield:function($,N,V){return this.delegate={iterator:W($),resultName:N,nextLoc:V},this.method==="next"&&(this.arg=e),f}},t}function eo(e,t,r,a,n,o,i){try{var s=e[o](i),l=s.value}catch(c){return void r(c)}s.done?t(l):Promise.resolve(l).then(a,n)}function jt(e){return function(){var t=this,r=arguments;return new Promise(function(a,n){var o=e.apply(t,r);function i(l){eo(o,a,n,i,s,"next",l)}function s(l){eo(o,a,n,i,s,"throw",l)}i(void 0)})}}var yr=A({},ls),uc=yr.version,_n=yr.render,dc=yr.unmountComponentAtNode,rn;try{var fc=Number((uc||"").split(".")[0]);fc>=18&&(rn=yr.createRoot)}catch{}function to(e){var t=yr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;t&&de(t)==="object"&&(t.usingClientEntryPoint=e)}var kr="__rc_react_root__";function vc(e,t){to(!0);var r=t[kr]||rn(t);to(!1),r.render(e),t[kr]=r}function gc(e,t){_n==null||_n(e,t)}function mc(e,t){if(rn){vc(e,t);return}gc(e,t)}function hc(e){return oa.apply(this,arguments)}function oa(){return oa=jt(_e().mark(function e(t){return _e().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",Promise.resolve().then(function(){var n;(n=t[kr])===null||n===void 0||n.unmount(),delete t[kr]}));case 1:case"end":return a.stop()}},e)})),oa.apply(this,arguments)}function pc(e){dc(e)}function yc(e){return ia.apply(this,arguments)}function ia(){return ia=jt(_e().mark(function e(t){return _e().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(rn===void 0){a.next=2;break}return a.abrupt("return",hc(t));case 2:pc(t);case 3:case"end":return a.stop()}},e)})),ia.apply(this,arguments)}const bc=(e,t)=>(mc(e,t),()=>yc(t));let Cc=bc;function pi(){return Cc}const Hn=()=>({height:0,opacity:0}),ro=e=>{const{scrollHeight:t}=e;return{height:t,opacity:1}},Sc=e=>({height:e?e.offsetHeight:0}),Vn=(e,t)=>(t==null?void 0:t.deadline)===!0||t.propertyName==="height",wc=function(){return{motionName:`${arguments.length>0&&arguments[0]!==void 0?arguments[0]:Wo}-motion-collapse`,onAppearStart:Hn,onEnterStart:Hn,onAppearActive:ro,onEnterActive:ro,onLeaveStart:Sc,onLeaveActive:Hn,onAppearEnd:Vn,onEnterEnd:Vn,onLeaveEnd:Vn,motionDeadline:500}},rv=(e,t,r)=>r!==void 0?r:`${e}-${t}`;function vr(e,t){var r=Object.assign({},e);return Array.isArray(t)&&t.forEach(function(a){delete r[a]}),r}const yi=function(e){if(!e)return!1;if(e instanceof Element){if(e.offsetParent)return!0;if(e.getBBox){var t=e.getBBox(),r=t.width,a=t.height;if(r||a)return!0}if(e.getBoundingClientRect){var n=e.getBoundingClientRect(),o=n.width,i=n.height;if(o||i)return!0}}return!1},xc=e=>{const{componentCls:t,colorPrimary:r}=e;return{[t]:{position:"absolute",background:"transparent",pointerEvents:"none",boxSizing:"border-box",color:`var(--wave-color, ${r})`,boxShadow:"0 0 0 0 currentcolor",opacity:.2,"&.wave-motion-appear":{transition:[`box-shadow 0.4s ${e.motionEaseOutCirc}`,`opacity 2s ${e.motionEaseOutCirc}`].join(","),"&-active":{boxShadow:"0 0 0 6px currentcolor",opacity:0},"&.wave-quick":{transition:[`box-shadow ${e.motionDurationSlow} ${e.motionEaseInOut}`,`opacity ${e.motionDurationSlow} ${e.motionEaseInOut}`].join(",")}}}}},$c=rl("Wave",e=>[xc(e)]),bi=`${Wo}-wave-target`;function zn(e){return e&&e!=="#fff"&&e!=="#ffffff"&&e!=="rgb(255, 255, 255)"&&e!=="rgba(255, 255, 255, 1)"&&!/rgba\((?:\d*, ){3}0\)/.test(e)&&e!=="transparent"}function Ec(e){const{borderTopColor:t,borderColor:r,backgroundColor:a}=getComputedStyle(e);return zn(t)?t:zn(r)?r:zn(a)?a:null}function Dn(e){return Number.isNaN(e)?0:e}const Fc=e=>{const{className:t,target:r,component:a,registerUnmount:n}=e,o=u.useRef(null),i=u.useRef(null);u.useEffect(()=>{i.current=n()},[]);const[s,l]=u.useState(null),[c,d]=u.useState([]),[v,g]=u.useState(0),[y,h]=u.useState(0),[m,f]=u.useState(0),[C,p]=u.useState(0),[S,R]=u.useState(!1),E={left:v,top:y,width:m,height:C,borderRadius:c.map(F=>`${F}px`).join(" ")};s&&(E["--wave-color"]=s);function w(){const F=getComputedStyle(r);l(Ec(r));const b=F.position==="static",{borderLeftWidth:P,borderTopWidth:M}=F;g(b?r.offsetLeft:Dn(-parseFloat(P))),h(b?r.offsetTop:Dn(-parseFloat(M))),f(r.offsetWidth),p(r.offsetHeight);const{borderTopLeftRadius:H,borderTopRightRadius:T,borderBottomLeftRadius:L,borderBottomRightRadius:W}=F;d([H,T,W,L].map(x=>Dn(parseFloat(x))))}if(u.useEffect(()=>{if(r){const F=Dr(()=>{w(),R(!0)});let b;return typeof ResizeObserver<"u"&&(b=new ResizeObserver(w),b.observe(r)),()=>{Dr.cancel(F),b==null||b.disconnect()}}},[]),!S)return null;const O=(a==="Checkbox"||a==="Radio")&&(r==null?void 0:r.classList.contains(bi));return u.createElement(pr,{visible:!0,motionAppear:!0,motionName:"wave-motion",motionDeadline:5e3,onAppearEnd:(F,b)=>{var P,M;if(b.deadline||b.propertyName==="opacity"){const H=(P=o.current)===null||P===void 0?void 0:P.parentElement;(M=i.current)===null||M===void 0||M.call(i).then(()=>{H==null||H.remove()})}return!1}},(F,b)=>{let{className:P}=F;return u.createElement("div",{ref:en(o,b),className:K(t,P,{"wave-quick":O}),style:E})})},Pc=(e,t)=>{var r;const{component:a}=t;if(a==="Checkbox"&&!(!((r=e.querySelector("input"))===null||r===void 0)&&r.checked))return;const n=document.createElement("div");n.style.position="absolute",n.style.left="0px",n.style.top="0px",e==null||e.insertBefore(n,e==null?void 0:e.firstChild);const o=pi();let i=null;function s(){return i}i=o(u.createElement(Fc,Object.assign({},t,{target:e,registerUnmount:s})),n)},Rc=(e,t,r)=>{const{wave:a}=u.useContext(tt),[,n,o]=Qt(),i=ct(c=>{const d=e.current;if(a!=null&&a.disabled||!d)return;const v=d.querySelector(`.${bi}`)||d,{showEffect:g}=a||{};(g||Pc)(v,{className:t,token:n,component:r,event:c,hashId:o})}),s=u.useRef(null);return c=>{Dr.cancel(s.current),s.current=Dr(()=>{i(c)})}},Oc=e=>{const{children:t,disabled:r,component:a}=e,{getPrefixCls:n}=u.useContext(tt),o=u.useRef(null),i=n("wave"),[,s]=$c(i),l=Rc(o,K(i,s),a);if(B.useEffect(()=>{const d=o.current;if(!d||d.nodeType!==1||r)return;const v=g=>{!yi(g.target)||!d.getAttribute||d.getAttribute("disabled")||d.disabled||d.className.includes("disabled")||d.className.includes("-leave")||l(g)};return d.addEventListener("click",v,!0),()=>{d.removeEventListener("click",v,!0)}},[r]),!B.isValidElement(t))return t??null;const c=Qr(t)?en(wa(t),o):o;return Kr(t,{ref:c})},nn=e=>{const t=B.useContext(cs);return B.useMemo(()=>e?typeof e=="string"?e??t:typeof e=="function"?e(t):t:t,[e,t])},Ic=e=>{const{componentCls:t}=e;return{[t]:{"&-block":{display:"flex",width:"100%"},"&-vertical":{flexDirection:"column"}}}},Nc=e=>{const{componentCls:t,antCls:r}=e;return{[t]:{display:"inline-flex","&-rtl":{direction:"rtl"},"&-vertical":{flexDirection:"column"},"&-align":{flexDirection:"column","&-center":{alignItems:"center"},"&-start":{alignItems:"flex-start"},"&-end":{alignItems:"flex-end"},"&-baseline":{alignItems:"baseline"}},[`${t}-item:empty`]:{display:"none"},[`${t}-item > ${r}-badge-not-a-wrapper:only-child`]:{display:"block"}}}},Mc=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-row-small":{rowGap:e.spaceGapSmallSize},"&-gap-row-middle":{rowGap:e.spaceGapMiddleSize},"&-gap-row-large":{rowGap:e.spaceGapLargeSize},"&-gap-col-small":{columnGap:e.spaceGapSmallSize},"&-gap-col-middle":{columnGap:e.spaceGapMiddleSize},"&-gap-col-large":{columnGap:e.spaceGapLargeSize}}}},Tc=Xt("Space",e=>{const t=Ue(e,{spaceGapSmallSize:e.paddingXS,spaceGapMiddleSize:e.padding,spaceGapLargeSize:e.paddingLG});return[Nc(t),Mc(t),Ic(t)]},()=>({}),{resetStyle:!1});var Ci=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};const an=u.createContext(null),Si=(e,t)=>{const r=u.useContext(an),a=u.useMemo(()=>{if(!r)return"";const{compactDirection:n,isFirstItem:o,isLastItem:i}=r,s=n==="vertical"?"-vertical-":"-";return K(`${e}-compact${s}item`,{[`${e}-compact${s}first-item`]:o,[`${e}-compact${s}last-item`]:i,[`${e}-compact${s}item-rtl`]:t==="rtl"})},[e,t,r]);return{compactSize:r==null?void 0:r.compactSize,compactDirection:r==null?void 0:r.compactDirection,compactItemClassnames:a}},Ac=e=>{const{children:t}=e;return u.createElement(an.Provider,{value:null},t)},jc=e=>{const{children:t}=e,r=Ci(e,["children"]);return u.createElement(an.Provider,{value:u.useMemo(()=>r,[r])},t)},nv=e=>{const{getPrefixCls:t,direction:r}=u.useContext(tt),{size:a,direction:n,block:o,prefixCls:i,className:s,rootClassName:l,children:c}=e,d=Ci(e,["size","direction","block","prefixCls","className","rootClassName","children"]),v=nn(S=>a??S),g=t("space-compact",i),[y,h]=Tc(g),m=K(g,h,{[`${g}-rtl`]:r==="rtl",[`${g}-block`]:o,[`${g}-vertical`]:n==="vertical"},s,l),f=u.useContext(an),C=Tt(c),p=u.useMemo(()=>C.map((S,R)=>{const E=(S==null?void 0:S.key)||`${g}-item-${R}`;return u.createElement(jc,{key:E,compactSize:v,compactDirection:n,isFirstItem:R===0&&(!f||(f==null?void 0:f.isFirstItem)),isLastItem:R===C.length-1&&(!f||(f==null?void 0:f.isLastItem))},S)}),[a,C,f]);return C.length===0?null:y(u.createElement("div",Object.assign({className:m},d),p))};var Bc=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};const wi=u.createContext(void 0),Lc=e=>{const{getPrefixCls:t,direction:r}=u.useContext(tt),{prefixCls:a,size:n,className:o}=e,i=Bc(e,["prefixCls","size","className"]),s=t("btn-group",a),[,,l]=Qt(),c=u.useMemo(()=>{switch(n){case"large":return"lg";case"small":return"sm";default:return""}},[n]),d=K(s,{[`${s}-${c}`]:c,[`${s}-rtl`]:r==="rtl"},o,l);return u.createElement(wi.Provider,{value:n},u.createElement("div",Object.assign({},i,{className:d})))},no=/^[\u4E00-\u9FA5]{2}$/,sa=no.test.bind(no);function av(e){return e==="danger"?{danger:!0}:{type:e}}function ao(e){return typeof e=="string"}function Wn(e){return e==="text"||e==="link"}function _c(e,t){if(e==null)return;const r=t?" ":"";return typeof e!="string"&&typeof e!="number"&&ao(e.type)&&sa(e.props.children)?Kr(e,{children:e.props.children.split("").join(r)}):ao(e)?sa(e)?B.createElement("span",null,e.split("").join(r)):B.createElement("span",null,e):Tl(e)?B.createElement("span",null,e):e}function Hc(e,t){let r=!1;const a=[];return B.Children.forEach(e,n=>{const o=typeof n,i=o==="string"||o==="number";if(r&&i){const s=a.length-1,l=a[s];a[s]=`${l}${n}`}else a.push(n);r=i}),B.Children.map(a,n=>_c(n,t))}["default","primary","danger"].concat(Q(Pa));const la=u.forwardRef((e,t)=>{const{className:r,style:a,children:n,prefixCls:o}=e,i=K(`${o}-icon`,r);return B.createElement("span",{ref:t,className:i,style:a},n)}),oo=u.forwardRef((e,t)=>{const{prefixCls:r,className:a,style:n,iconClassName:o}=e,i=K(`${r}-loading-icon`,a);return B.createElement(la,{prefixCls:r,className:i,style:n,ref:t},B.createElement(fi,{className:o}))}),qn=()=>({width:0,opacity:0,transform:"scale(0)"}),Gn=e=>({width:e.scrollWidth,opacity:1,transform:"scale(1)"}),Vc=e=>{const{prefixCls:t,loading:r,existIcon:a,className:n,style:o,mount:i}=e,s=!!r;return a?B.createElement(oo,{prefixCls:t,className:n,style:o}):B.createElement(pr,{visible:s,motionName:`${t}-loading-icon-motion`,motionAppear:!i,motionEnter:!i,motionLeave:!i,removeOnLeave:!0,onAppearStart:qn,onAppearActive:Gn,onEnterStart:qn,onEnterActive:Gn,onLeaveStart:Gn,onLeaveActive:qn},(l,c)=>{let{className:d,style:v}=l;const g=Object.assign(Object.assign({},o),v);return B.createElement(oo,{prefixCls:t,className:K(n,d),style:g,ref:c})})},io=(e,t)=>({[`> span, > ${e}`]:{"&:not(:last-child)":{[`&, & > ${e}`]:{"&:not(:disabled)":{borderInlineEndColor:t}}},"&:not(:first-child)":{[`&, & > ${e}`]:{"&:not(:disabled)":{borderInlineStartColor:t}}}}}),zc=e=>{const{componentCls:t,fontSize:r,lineWidth:a,groupBorderColor:n,colorErrorHover:o}=e;return{[`${t}-group`]:[{position:"relative",display:"inline-flex",[`> span, > ${t}`]:{"&:not(:last-child)":{[`&, & > ${t}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},"&:not(:first-child)":{marginInlineStart:e.calc(a).mul(-1).equal(),[`&, & > ${t}`]:{borderStartStartRadius:0,borderEndStartRadius:0}}},[t]:{position:"relative",zIndex:1,"&:hover, &:focus, &:active":{zIndex:2},"&[disabled]":{zIndex:0}},[`${t}-icon-only`]:{fontSize:r}},io(`${t}-primary`,n),io(`${t}-danger`,o)]}};var Dc=["b"],Wc=["v"],Un=function(t){return Math.round(Number(t||0))},qc=function(t){if(t instanceof qo)return t;if(t&&de(t)==="object"&&"h"in t&&"b"in t){var r=t,a=r.b,n=ze(r,Dc);return A(A({},n),{},{v:a})}return typeof t=="string"&&/hsb/.test(t)?t.replace(/hsb/,"hsv"):t},At=function(e){Jt(r,e);var t=Zt(r);function r(a){return Qe(this,r),t.call(this,qc(a))}return Xe(r,[{key:"toHsbString",value:function(){var n=this.toHsb(),o=Un(n.s*100),i=Un(n.b*100),s=Un(n.h),l=n.a,c="hsb(".concat(s,", ").concat(o,"%, ").concat(i,"%)"),d="hsba(".concat(s,", ").concat(o,"%, ").concat(i,"%, ").concat(l.toFixed(l===0?0:2),")");return l===1?c:d}},{key:"toHsb",value:function(){var n=this.toHsv(),o=n.v,i=ze(n,Wc);return A(A({},i),{},{b:o,a:this.a})}}]),r}(qo),ov="rc-color-picker",zr=function(t){return t instanceof At?t:new At(t)},iv=zr("#1677ff"),sv=function(t){var r=t.offset,a=t.targetRef,n=t.containerRef,o=t.color,i=t.type,s=n.current.getBoundingClientRect(),l=s.width,c=s.height,d=a.current.getBoundingClientRect(),v=d.width,g=d.height,y=v/2,h=g/2,m=(r.x+y)/l,f=1-(r.y+h)/c,C=o.toHsb(),p=m,S=(r.x+y)/l*360;if(i)switch(i){case"hue":return zr(A(A({},C),{},{h:S<=0?0:S}));case"alpha":return zr(A(A({},C),{},{a:p<=0?0:p}))}return zr({h:C.h,s:m<=0?0:m,b:f>=1?1:f,a:C.a})},lv=function(t,r){var a=t.toHsb();switch(r){case"hue":return{x:a.h/360*100,y:50};case"alpha":return{x:t.a*100,y:50};default:return{x:a.s*100,y:(1-a.b)*100}}},Gc=function(t){var r=t.color,a=t.prefixCls,n=t.className,o=t.style,i=t.onClick,s="".concat(a,"-color-block");return B.createElement("div",{className:K(s,n),style:o,onClick:i},B.createElement("div",{className:"".concat(s,"-inner"),style:{background:r}}))};const Uc=(e,t)=>(e==null?void 0:e.replace(/[^\w/]/g,"").slice(0,t?8:6))||"",Kc=(e,t)=>e?Uc(e,t):"";let ca=function(){function e(t){Qe(this,e);var r;if(this.cleared=!1,t instanceof e){this.metaColor=t.metaColor.clone(),this.colors=(r=t.colors)===null||r===void 0?void 0:r.map(n=>({color:new e(n.color),percent:n.percent})),this.cleared=t.cleared;return}const a=Array.isArray(t);a&&t.length?(this.colors=t.map(n=>{let{color:o,percent:i}=n;return{color:new e(o),percent:i}}),this.metaColor=new At(this.colors[0].color.metaColor)):this.metaColor=new At(a?"":t),(!t||a&&!this.colors)&&(this.metaColor=this.metaColor.setA(0),this.cleared=!0)}return Xe(e,[{key:"toHsb",value:function(){return this.metaColor.toHsb()}},{key:"toHsbString",value:function(){return this.metaColor.toHsbString()}},{key:"toHex",value:function(){return Kc(this.toHexString(),this.metaColor.a<1)}},{key:"toHexString",value:function(){return this.metaColor.toHexString()}},{key:"toRgb",value:function(){return this.metaColor.toRgb()}},{key:"toRgbString",value:function(){return this.metaColor.toRgbString()}},{key:"isGradient",value:function(){return!!this.colors&&!this.cleared}},{key:"getColors",value:function(){return this.colors||[{color:this,percent:0}]}},{key:"toCssString",value:function(){const{colors:r}=this;return r?`linear-gradient(90deg, ${r.map(n=>`${n.color.toRgbString()} ${n.percent}%`).join(", ")})`:this.metaColor.toRgbString()}},{key:"equals",value:function(r){return!r||this.isGradient()!==r.isGradient()?!1:this.isGradient()?this.colors.length===r.colors.length&&this.colors.every((a,n)=>{const o=r.colors[n];return a.percent===o.percent&&a.color.equals(o.color)}):this.toHexString()===r.toHexString()}}])}();var kc={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},Jc=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:kc}))},Zc=u.forwardRef(Jc),xi=B.forwardRef(function(e,t){var r=e.prefixCls,a=e.forceRender,n=e.className,o=e.style,i=e.children,s=e.isActive,l=e.role,c=e.classNames,d=e.styles,v=B.useState(s||a),g=Z(v,2),y=g[0],h=g[1];return B.useEffect(function(){(a||s)&&h(!0)},[a,s]),y?B.createElement("div",{ref:t,className:K("".concat(r,"-content"),I(I({},"".concat(r,"-content-active"),s),"".concat(r,"-content-inactive"),!s),n),style:o,role:l},B.createElement("div",{className:K("".concat(r,"-content-box"),c==null?void 0:c.body),style:d==null?void 0:d.body},i)):null});xi.displayName="PanelContent";var Qc=["showArrow","headerClass","isActive","onItemClick","forceRender","className","classNames","styles","prefixCls","collapsible","accordion","panelKey","extra","header","expandIcon","openMotion","destroyInactivePanel","children"],$i=B.forwardRef(function(e,t){var r=e.showArrow,a=r===void 0?!0:r,n=e.headerClass,o=e.isActive,i=e.onItemClick,s=e.forceRender,l=e.className,c=e.classNames,d=c===void 0?{}:c,v=e.styles,g=v===void 0?{}:v,y=e.prefixCls,h=e.collapsible,m=e.accordion,f=e.panelKey,C=e.extra,p=e.header,S=e.expandIcon,R=e.openMotion,E=e.destroyInactivePanel,w=e.children,O=ze(e,Qc),F=h==="disabled",b=C!=null&&typeof C!="boolean",P=I(I(I({onClick:function(){i==null||i(f)},onKeyDown:function($){($.key==="Enter"||$.keyCode===aa.ENTER||$.which===aa.ENTER)&&(i==null||i(f))},role:m?"tab":"button"},"aria-expanded",o),"aria-disabled",F),"tabIndex",F?-1:0),M=typeof S=="function"?S(e):B.createElement("i",{className:"arrow"}),H=M&&B.createElement("div",me({className:"".concat(y,"-expand-icon")},["header","icon"].includes(h)?P:{}),M),T=K("".concat(y,"-item"),I(I({},"".concat(y,"-item-active"),o),"".concat(y,"-item-disabled"),F),l),L=K(n,"".concat(y,"-header"),I({},"".concat(y,"-collapsible-").concat(h),!!h),d.header),W=A({className:L,style:g.header},["header","icon"].includes(h)?{}:P);return B.createElement("div",me({},O,{ref:t,className:T}),B.createElement("div",W,a&&H,B.createElement("span",me({className:"".concat(y,"-header-text")},h==="header"?P:{}),p),b&&B.createElement("div",{className:"".concat(y,"-extra")},C)),B.createElement(pr,me({visible:o,leavedClassName:"".concat(y,"-content-hidden")},R,{forceRender:s,removeOnLeave:E}),function(x,$){var N=x.className,V=x.style;return B.createElement(xi,{ref:$,prefixCls:y,className:N,classNames:d,style:V,styles:g,isActive:o,forceRender:s,role:m?"tabpanel":void 0},w)}))}),Xc=["children","label","key","collapsible","onItemClick","destroyInactivePanel"],Yc=function(t,r){var a=r.prefixCls,n=r.accordion,o=r.collapsible,i=r.destroyInactivePanel,s=r.onItemClick,l=r.activeKey,c=r.openMotion,d=r.expandIcon;return t.map(function(v,g){var y=v.children,h=v.label,m=v.key,f=v.collapsible,C=v.onItemClick,p=v.destroyInactivePanel,S=ze(v,Xc),R=String(m??g),E=f??o,w=p??i,O=function(P){E!=="disabled"&&(s(P),C==null||C(P))},F=!1;return n?F=l[0]===R:F=l.indexOf(R)>-1,B.createElement($i,me({},S,{prefixCls:a,key:R,panelKey:R,isActive:F,accordion:n,openMotion:c,expandIcon:d,header:h,collapsible:E,onItemClick:O,destroyInactivePanel:w}),y)})},eu=function(t,r,a){if(!t)return null;var n=a.prefixCls,o=a.accordion,i=a.collapsible,s=a.destroyInactivePanel,l=a.onItemClick,c=a.activeKey,d=a.openMotion,v=a.expandIcon,g=t.key||String(r),y=t.props,h=y.header,m=y.headerClass,f=y.destroyInactivePanel,C=y.collapsible,p=y.onItemClick,S=!1;o?S=c[0]===g:S=c.indexOf(g)>-1;var R=C??i,E=function(F){R!=="disabled"&&(l(F),p==null||p(F))},w={key:g,panelKey:g,header:h,headerClass:m,isActive:S,prefixCls:n,destroyInactivePanel:f??s,openMotion:d,accordion:o,children:t.props.children,onItemClick:E,expandIcon:v,collapsible:R};return typeof t.type=="string"?t:(Object.keys(w).forEach(function(O){typeof w[O]>"u"&&delete w[O]}),B.cloneElement(t,w))};function tu(e,t,r){return Array.isArray(e)?Yc(e,r):Tt(t).map(function(a,n){return eu(a,n,r)})}function ru(e){var t=e;if(!Array.isArray(t)){var r=de(t);t=r==="number"||r==="string"?[t]:[]}return t.map(function(a){return String(a)})}var nu=B.forwardRef(function(e,t){var r=e.prefixCls,a=r===void 0?"rc-collapse":r,n=e.destroyInactivePanel,o=n===void 0?!1:n,i=e.style,s=e.accordion,l=e.className,c=e.children,d=e.collapsible,v=e.openMotion,g=e.expandIcon,y=e.activeKey,h=e.defaultActiveKey,m=e.onChange,f=e.items,C=K(a,l),p=Fa([],{value:y,onChange:function(b){return m==null?void 0:m(b)},defaultValue:h,postState:ru}),S=Z(p,2),R=S[0],E=S[1],w=function(b){return E(function(){if(s)return R[0]===b?[]:[b];var P=R.indexOf(b),M=P>-1;return M?R.filter(function(H){return H!==b}):[].concat(Q(R),[b])})};ut(!c,"[rc-collapse] `children` will be removed in next major version. Please use `items` instead.");var O=tu(f,c,{prefixCls:a,accordion:s,openMotion:v,expandIcon:g,collapsible:d,destroyInactivePanel:o,onItemClick:w,activeKey:R});return B.createElement("div",me({ref:t,className:C,style:i,role:s?"tablist":void 0},ci(e,{aria:!0,data:!0})),O)});const Ia=Object.assign(nu,{Panel:$i});Ia.Panel;const au=u.forwardRef((e,t)=>{const{getPrefixCls:r}=u.useContext(tt),{prefixCls:a,className:n,showArrow:o=!0}=e,i=r("collapse",a),s=K({[`${i}-no-arrow`]:!o},n);return u.createElement(Ia.Panel,Object.assign({ref:t},e,{prefixCls:i,className:s}))}),ou=e=>({[e.componentCls]:{[`${e.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}},[`${e.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}}}),iu=e=>({animationDuration:e,animationFillMode:"both"}),su=e=>({animationDuration:e,animationFillMode:"both"}),cv=function(e,t,r,a){const o=(arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1)?"&":"";return{[`
      ${o}${e}-enter,
      ${o}${e}-appear
    `]:Object.assign(Object.assign({},iu(a)),{animationPlayState:"paused"}),[`${o}${e}-leave`]:Object.assign(Object.assign({},su(a)),{animationPlayState:"paused"}),[`
      ${o}${e}-enter${e}-enter-active,
      ${o}${e}-appear${e}-appear-active
    `]:{animationName:t,animationPlayState:"running"},[`${o}${e}-leave${e}-leave-active`]:{animationName:r,animationPlayState:"running",pointerEvents:"none"}}},lu=e=>{const{componentCls:t,contentBg:r,padding:a,headerBg:n,headerPadding:o,collapseHeaderPaddingSM:i,collapseHeaderPaddingLG:s,collapsePanelBorderRadius:l,lineWidth:c,lineType:d,colorBorder:v,colorText:g,colorTextHeading:y,colorTextDisabled:h,fontSizeLG:m,lineHeight:f,lineHeightLG:C,marginSM:p,paddingSM:S,paddingLG:R,paddingXS:E,motionDurationSlow:w,fontSizeIcon:O,contentPadding:F,fontHeight:b,fontHeightLG:P}=e,M=`${te(c)} ${d} ${v}`;return{[t]:Object.assign(Object.assign({},Yr(e)),{backgroundColor:n,border:M,borderRadius:l,"&-rtl":{direction:"rtl"},[`& > ${t}-item`]:{borderBottom:M,"&:first-child":{[`
            &,
            & > ${t}-header`]:{borderRadius:`${te(l)} ${te(l)} 0 0`}},"&:last-child":{[`
            &,
            & > ${t}-header`]:{borderRadius:`0 0 ${te(l)} ${te(l)}`}},[`> ${t}-header`]:Object.assign(Object.assign({position:"relative",display:"flex",flexWrap:"nowrap",alignItems:"flex-start",padding:o,color:y,lineHeight:f,cursor:"pointer",transition:`all ${w}, visibility 0s`},Go(e)),{[`> ${t}-header-text`]:{flex:"auto"},[`${t}-expand-icon`]:{height:b,display:"flex",alignItems:"center",paddingInlineEnd:p},[`${t}-arrow`]:Object.assign(Object.assign({},Uo()),{fontSize:O,transition:`transform ${w}`,svg:{transition:`transform ${w}`}}),[`${t}-header-text`]:{marginInlineEnd:"auto"}}),[`${t}-collapsible-header`]:{cursor:"default",[`${t}-header-text`]:{flex:"none",cursor:"pointer"}},[`${t}-collapsible-icon`]:{cursor:"unset",[`${t}-expand-icon`]:{cursor:"pointer"}}},[`${t}-content`]:{color:g,backgroundColor:r,borderTop:M,[`& > ${t}-content-box`]:{padding:F},"&-hidden":{display:"none"}},"&-small":{[`> ${t}-item`]:{[`> ${t}-header`]:{padding:i,paddingInlineStart:E,[`> ${t}-expand-icon`]:{marginInlineStart:e.calc(S).sub(E).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:S}}},"&-large":{[`> ${t}-item`]:{fontSize:m,lineHeight:C,[`> ${t}-header`]:{padding:s,paddingInlineStart:a,[`> ${t}-expand-icon`]:{height:P,marginInlineStart:e.calc(R).sub(a).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:R}}},[`${t}-item:last-child`]:{borderBottom:0,[`> ${t}-content`]:{borderRadius:`0 0 ${te(l)} ${te(l)}`}},[`& ${t}-item-disabled > ${t}-header`]:{"\n          &,\n          & > .arrow\n        ":{color:h,cursor:"not-allowed"}},[`&${t}-icon-position-end`]:{[`& > ${t}-item`]:{[`> ${t}-header`]:{[`${t}-expand-icon`]:{order:1,paddingInlineEnd:0,paddingInlineStart:p}}}}})}},cu=e=>{const{componentCls:t}=e,r=`> ${t}-item > ${t}-header ${t}-arrow`;return{[`${t}-rtl`]:{[r]:{transform:"rotate(180deg)"}}}},uu=e=>{const{componentCls:t,headerBg:r,paddingXXS:a,colorBorder:n}=e;return{[`${t}-borderless`]:{backgroundColor:r,border:0,[`> ${t}-item`]:{borderBottom:`1px solid ${n}`},[`
        > ${t}-item:last-child,
        > ${t}-item:last-child ${t}-header
      `]:{borderRadius:0},[`> ${t}-item:last-child`]:{borderBottom:0},[`> ${t}-item > ${t}-content`]:{backgroundColor:"transparent",borderTop:0},[`> ${t}-item > ${t}-content > ${t}-content-box`]:{paddingTop:a}}}},du=e=>{const{componentCls:t,paddingSM:r}=e;return{[`${t}-ghost`]:{backgroundColor:"transparent",border:0,[`> ${t}-item`]:{borderBottom:0,[`> ${t}-content`]:{backgroundColor:"transparent",border:0,[`> ${t}-content-box`]:{paddingBlock:r}}}}}},fu=e=>({headerPadding:`${e.paddingSM}px ${e.padding}px`,headerBg:e.colorFillAlter,contentPadding:`${e.padding}px 16px`,contentBg:e.colorBgContainer}),vu=Xt("Collapse",e=>{const t=Ue(e,{collapseHeaderPaddingSM:`${te(e.paddingXS)} ${te(e.paddingSM)}`,collapseHeaderPaddingLG:`${te(e.padding)} ${te(e.paddingLG)}`,collapsePanelBorderRadius:e.borderRadiusLG});return[lu(t),uu(t),du(t),cu(t),ou(t)]},fu),gu=u.forwardRef((e,t)=>{const{getPrefixCls:r,direction:a,expandIcon:n,className:o,style:i}=$a("collapse"),{prefixCls:s,className:l,rootClassName:c,style:d,bordered:v=!0,ghost:g,size:y,expandIconPosition:h="start",children:m,expandIcon:f}=e,C=nn(T=>{var L;return(L=y??T)!==null&&L!==void 0?L:"middle"}),p=r("collapse",s),S=r(),[R,E,w]=vu(p),O=u.useMemo(()=>h==="left"?"start":h==="right"?"end":h,[h]),F=f??n,b=u.useCallback(function(){let T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const L=typeof F=="function"?F(T):u.createElement(Zc,{rotate:T.isActive?a==="rtl"?-90:90:void 0,"aria-label":T.isActive?"expanded":"collapsed"});return Kr(L,()=>{var W;return{className:K((W=L==null?void 0:L.props)===null||W===void 0?void 0:W.className,`${p}-arrow`)}})},[F,p]),P=K(`${p}-icon-position-${O}`,{[`${p}-borderless`]:!v,[`${p}-rtl`]:a==="rtl",[`${p}-ghost`]:!!g,[`${p}-${C}`]:C!=="middle"},o,l,c,E,w),M=Object.assign(Object.assign({},wc(S)),{motionAppear:!1,leavedClassName:`${p}-content-hidden`}),H=u.useMemo(()=>m?Tt(m).map((T,L)=>{var W,x;const $=T.props;if($!=null&&$.disabled){const N=(W=T.key)!==null&&W!==void 0?W:String(L),V=Object.assign(Object.assign({},vr(T.props,["disabled"])),{key:N,collapsible:(x=$.collapsible)!==null&&x!==void 0?x:"disabled"});return Kr(T,V)}return T}):null,[m]);return R(u.createElement(Ia,Object.assign({ref:t,openMotion:M},vr(e,["rootClassName"]),{expandIcon:b,prefixCls:p,className:P,style:Object.assign(Object.assign({},i),d)}),H))}),mu=Object.assign(gu,{Panel:au}),Jr=e=>e instanceof ca?e:new ca(e),hu=e=>Math.round(Number(e||0)),uv=e=>hu(e.toHsb().a*100),dv=(e,t)=>{const r=e.toRgb();if(!r.r&&!r.g&&!r.b){const a=e.toHsb();return a.a=1,Jr(a)}return r.a=1,Jr(r)},fv=(e,t)=>{const r=[{percent:0,color:e[0].color}].concat(Q(e),[{percent:100,color:e[e.length-1].color}]);for(let a=0;a<r.length-1;a+=1){const n=r[a].percent,o=r[a+1].percent,i=r[a].color,s=r[a+1].color;if(n<=t&&t<=o){const l=o-n;if(l===0)return i;const c=(t-n)/l*100,d=new At(i),v=new At(s);return d.mix(v,c).toRgbString()}}return""},Kn=e=>e.map(t=>(t.colors=t.colors.map(Jr),t)),Ei=(e,t)=>{const{r,g:a,b:n,a:o}=e.toRgb(),i=new At(e.toRgbString()).onBackground(t).toHsv();return o<=.5?i.v>.5:r*.299+a*.587+n*.114>192},so=(e,t)=>{var r;return`panel-${(r=e.key)!==null&&r!==void 0?r:t}`},vv=e=>{let{prefixCls:t,presets:r,value:a,onChange:n}=e;const[o]=Us("ColorPicker"),[,i]=Qt(),[s]=Fa(Kn(r),{value:Kn(r),postState:Kn}),l=`${t}-presets`,c=u.useMemo(()=>s.reduce((g,y,h)=>{const{defaultOpen:m=!0}=y;return m&&g.push(so(y,h)),g},[]),[s]),d=g=>{n==null||n(g)},v=s.map((g,y)=>{var h;return{key:so(g,y),label:B.createElement("div",{className:`${l}-label`},g==null?void 0:g.label),children:B.createElement("div",{className:`${l}-items`},Array.isArray(g==null?void 0:g.colors)&&((h=g.colors)===null||h===void 0?void 0:h.length)>0?g.colors.map((m,f)=>B.createElement(Gc,{key:`preset-${f}-${m.toHexString()}`,color:Jr(m).toRgbString(),prefixCls:t,className:K(`${l}-color`,{[`${l}-color-checked`]:m.toHexString()===(a==null?void 0:a.toHexString()),[`${l}-color-bright`]:Ei(m,i.colorBgElevated)}),onClick:()=>d(m)})):B.createElement("span",{className:`${l}-empty`},o.presetEmpty))}});return B.createElement("div",{className:l},B.createElement(mu,{defaultActiveKey:c,ghost:!0,items:v}))},Fi=e=>{const{paddingInline:t,onlyIconSize:r}=e;return Ue(e,{buttonPaddingHorizontal:t,buttonPaddingVertical:0,buttonIconOnlyFontSize:r})},Pi=e=>{var t,r,a,n,o,i;const s=(t=e.contentFontSize)!==null&&t!==void 0?t:e.fontSize,l=(r=e.contentFontSizeSM)!==null&&r!==void 0?r:e.fontSize,c=(a=e.contentFontSizeLG)!==null&&a!==void 0?a:e.fontSizeLG,d=(n=e.contentLineHeight)!==null&&n!==void 0?n:jn(s),v=(o=e.contentLineHeightSM)!==null&&o!==void 0?o:jn(l),g=(i=e.contentLineHeightLG)!==null&&i!==void 0?i:jn(c),y=Ei(new ca(e.colorBgSolid),"#fff")?"#000":"#fff",h=Pa.reduce((m,f)=>Object.assign(Object.assign({},m),{[`${f}ShadowColor`]:`0 ${te(e.controlOutlineWidth)} 0 ${us(e[`${f}1`],e.colorBgContainer)}`}),{});return Object.assign(Object.assign({},h),{fontWeight:400,defaultShadow:`0 ${e.controlOutlineWidth}px 0 ${e.controlTmpOutline}`,primaryShadow:`0 ${e.controlOutlineWidth}px 0 ${e.controlOutline}`,dangerShadow:`0 ${e.controlOutlineWidth}px 0 ${e.colorErrorOutline}`,primaryColor:e.colorTextLightSolid,dangerColor:e.colorTextLightSolid,borderColorDisabled:e.colorBorder,defaultGhostColor:e.colorBgContainer,ghostBg:"transparent",defaultGhostBorderColor:e.colorBgContainer,paddingInline:e.paddingContentHorizontal-e.lineWidth,paddingInlineLG:e.paddingContentHorizontal-e.lineWidth,paddingInlineSM:8-e.lineWidth,onlyIconSize:"inherit",onlyIconSizeSM:"inherit",onlyIconSizeLG:"inherit",groupBorderColor:e.colorPrimaryHover,linkHoverBg:"transparent",textTextColor:e.colorText,textTextHoverColor:e.colorText,textTextActiveColor:e.colorText,textHoverBg:e.colorFillTertiary,defaultColor:e.colorText,defaultBg:e.colorBgContainer,defaultBorderColor:e.colorBorder,defaultBorderColorDisabled:e.colorBorder,defaultHoverBg:e.colorBgContainer,defaultHoverColor:e.colorPrimaryHover,defaultHoverBorderColor:e.colorPrimaryHover,defaultActiveBg:e.colorBgContainer,defaultActiveColor:e.colorPrimaryActive,defaultActiveBorderColor:e.colorPrimaryActive,solidTextColor:y,contentFontSize:s,contentFontSizeSM:l,contentFontSizeLG:c,contentLineHeight:d,contentLineHeightSM:v,contentLineHeightLG:g,paddingBlock:Math.max((e.controlHeight-s*d)/2-e.lineWidth,0),paddingBlockSM:Math.max((e.controlHeightSM-l*v)/2-e.lineWidth,0),paddingBlockLG:Math.max((e.controlHeightLG-c*g)/2-e.lineWidth,0)})},pu=e=>{const{componentCls:t,iconCls:r,fontWeight:a,opacityLoading:n,motionDurationSlow:o,motionEaseInOut:i,marginXS:s,calc:l}=e;return{[t]:{outline:"none",position:"relative",display:"inline-flex",gap:e.marginXS,alignItems:"center",justifyContent:"center",fontWeight:a,whiteSpace:"nowrap",textAlign:"center",backgroundImage:"none",background:"transparent",border:`${te(e.lineWidth)} ${e.lineType} transparent`,cursor:"pointer",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,userSelect:"none",touchAction:"manipulation",color:e.colorText,"&:disabled > *":{pointerEvents:"none"},[`${t}-icon > svg`]:Uo(),"> a":{color:"currentColor"},"&:not(:disabled)":Go(e),[`&${t}-two-chinese-chars::first-letter`]:{letterSpacing:"0.34em"},[`&${t}-two-chinese-chars > *:not(${r})`]:{marginInlineEnd:"-0.34em",letterSpacing:"0.34em"},[`&${t}-icon-only`]:{paddingInline:0,[`&${t}-compact-item`]:{flex:"none"},[`&${t}-round`]:{width:"auto"}},[`&${t}-loading`]:{opacity:n,cursor:"default"},[`${t}-loading-icon`]:{transition:["width","opacity","margin"].map(c=>`${c} ${o} ${i}`).join(",")},[`&:not(${t}-icon-end)`]:{[`${t}-loading-icon-motion`]:{"&-appear-start, &-enter-start":{marginInlineEnd:l(s).mul(-1).equal()},"&-appear-active, &-enter-active":{marginInlineEnd:0},"&-leave-start":{marginInlineEnd:0},"&-leave-active":{marginInlineEnd:l(s).mul(-1).equal()}}},"&-icon-end":{flexDirection:"row-reverse",[`${t}-loading-icon-motion`]:{"&-appear-start, &-enter-start":{marginInlineStart:l(s).mul(-1).equal()},"&-appear-active, &-enter-active":{marginInlineStart:0},"&-leave-start":{marginInlineStart:0},"&-leave-active":{marginInlineStart:l(s).mul(-1).equal()}}}}}},Ri=(e,t,r)=>({[`&:not(:disabled):not(${e}-disabled)`]:{"&:hover":t,"&:active":r}}),yu=e=>({minWidth:e.controlHeight,paddingInlineStart:0,paddingInlineEnd:0,borderRadius:"50%"}),bu=e=>({borderRadius:e.controlHeight,paddingInlineStart:e.calc(e.controlHeight).div(2).equal(),paddingInlineEnd:e.calc(e.controlHeight).div(2).equal()}),Cu=e=>({cursor:"not-allowed",borderColor:e.borderColorDisabled,color:e.colorTextDisabled,background:e.colorBgContainerDisabled,boxShadow:"none"}),on=(e,t,r,a,n,o,i,s)=>({[`&${e}-background-ghost`]:Object.assign(Object.assign({color:r||void 0,background:t,borderColor:a||void 0,boxShadow:"none"},Ri(e,Object.assign({background:t},i),Object.assign({background:t},s))),{"&:disabled":{cursor:"not-allowed",color:n||void 0,borderColor:o||void 0}})}),Su=e=>({[`&:disabled, &${e.componentCls}-disabled`]:Object.assign({},Cu(e))}),wu=e=>({[`&:disabled, &${e.componentCls}-disabled`]:{cursor:"not-allowed",color:e.colorTextDisabled}}),sn=(e,t,r,a)=>{const o=a&&["link","text"].includes(a)?wu:Su;return Object.assign(Object.assign({},o(e)),Ri(e.componentCls,t,r))},ln=(e,t,r,a,n)=>({[`&${e.componentCls}-variant-solid`]:Object.assign({color:t,background:r},sn(e,a,n))}),cn=(e,t,r,a,n)=>({[`&${e.componentCls}-variant-outlined, &${e.componentCls}-variant-dashed`]:Object.assign({borderColor:t,background:r},sn(e,a,n))}),un=e=>({[`&${e.componentCls}-variant-dashed`]:{borderStyle:"dashed"}}),dn=(e,t,r,a)=>({[`&${e.componentCls}-variant-filled`]:Object.assign({boxShadow:"none",background:t},sn(e,r,a))}),dt=(e,t,r,a,n)=>({[`&${e.componentCls}-variant-${r}`]:Object.assign({color:t,boxShadow:"none"},sn(e,a,n,r))}),xu=e=>{const{componentCls:t}=e;return Pa.reduce((r,a)=>{const n=e[`${a}6`],o=e[`${a}1`],i=e[`${a}5`],s=e[`${a}2`],l=e[`${a}3`],c=e[`${a}7`];return Object.assign(Object.assign({},r),{[`&${t}-color-${a}`]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:n,boxShadow:e[`${a}ShadowColor`]},ln(e,e.colorTextLightSolid,n,{background:i},{background:c})),cn(e,n,e.colorBgContainer,{color:i,borderColor:i,background:e.colorBgContainer},{color:c,borderColor:c,background:e.colorBgContainer})),un(e)),dn(e,o,{background:s},{background:l})),dt(e,n,"link",{color:i},{color:c})),dt(e,n,"text",{color:i,background:o},{color:c,background:l}))})},{})},$u=e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.defaultColor,boxShadow:e.defaultShadow},ln(e,e.solidTextColor,e.colorBgSolid,{color:e.solidTextColor,background:e.colorBgSolidHover},{color:e.solidTextColor,background:e.colorBgSolidActive})),un(e)),dn(e,e.colorFillTertiary,{background:e.colorFillSecondary},{background:e.colorFill})),on(e.componentCls,e.ghostBg,e.defaultGhostColor,e.defaultGhostBorderColor,e.colorTextDisabled,e.colorBorder)),dt(e,e.textTextColor,"link",{color:e.colorLinkHover,background:e.linkHoverBg},{color:e.colorLinkActive})),Eu=e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorPrimary,boxShadow:e.primaryShadow},cn(e,e.colorPrimary,e.colorBgContainer,{color:e.colorPrimaryTextHover,borderColor:e.colorPrimaryHover,background:e.colorBgContainer},{color:e.colorPrimaryTextActive,borderColor:e.colorPrimaryActive,background:e.colorBgContainer})),un(e)),dn(e,e.colorPrimaryBg,{background:e.colorPrimaryBgHover},{background:e.colorPrimaryBorder})),dt(e,e.colorPrimaryText,"text",{color:e.colorPrimaryTextHover,background:e.colorPrimaryBg},{color:e.colorPrimaryTextActive,background:e.colorPrimaryBorder})),dt(e,e.colorPrimaryText,"link",{color:e.colorPrimaryTextHover,background:e.linkHoverBg},{color:e.colorPrimaryTextActive})),on(e.componentCls,e.ghostBg,e.colorPrimary,e.colorPrimary,e.colorTextDisabled,e.colorBorder,{color:e.colorPrimaryHover,borderColor:e.colorPrimaryHover},{color:e.colorPrimaryActive,borderColor:e.colorPrimaryActive})),Fu=e=>Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorError,boxShadow:e.dangerShadow},ln(e,e.dangerColor,e.colorError,{background:e.colorErrorHover},{background:e.colorErrorActive})),cn(e,e.colorError,e.colorBgContainer,{color:e.colorErrorHover,borderColor:e.colorErrorBorderHover},{color:e.colorErrorActive,borderColor:e.colorErrorActive})),un(e)),dn(e,e.colorErrorBg,{background:e.colorErrorBgFilledHover},{background:e.colorErrorBgActive})),dt(e,e.colorError,"text",{color:e.colorErrorHover,background:e.colorErrorBg},{color:e.colorErrorHover,background:e.colorErrorBgActive})),dt(e,e.colorError,"link",{color:e.colorErrorHover},{color:e.colorErrorActive})),on(e.componentCls,e.ghostBg,e.colorError,e.colorError,e.colorTextDisabled,e.colorBorder,{color:e.colorErrorHover,borderColor:e.colorErrorHover},{color:e.colorErrorActive,borderColor:e.colorErrorActive})),Pu=e=>Object.assign(Object.assign({},dt(e,e.colorLink,"link",{color:e.colorLinkHover},{color:e.colorLinkActive})),on(e.componentCls,e.ghostBg,e.colorInfo,e.colorInfo,e.colorTextDisabled,e.colorBorder,{color:e.colorInfoHover,borderColor:e.colorInfoHover},{color:e.colorInfoActive,borderColor:e.colorInfoActive})),Ru=e=>{const{componentCls:t}=e;return Object.assign({[`${t}-color-default`]:$u(e),[`${t}-color-primary`]:Eu(e),[`${t}-color-dangerous`]:Fu(e),[`${t}-color-link`]:Pu(e)},xu(e))},Ou=e=>Object.assign(Object.assign(Object.assign(Object.assign({},cn(e,e.defaultBorderColor,e.defaultBg,{color:e.defaultHoverColor,borderColor:e.defaultHoverBorderColor,background:e.defaultHoverBg},{color:e.defaultActiveColor,borderColor:e.defaultActiveBorderColor,background:e.defaultActiveBg})),dt(e,e.textTextColor,"text",{color:e.textTextHoverColor,background:e.textHoverBg},{color:e.textTextActiveColor,background:e.colorBgTextActive})),ln(e,e.primaryColor,e.colorPrimary,{background:e.colorPrimaryHover,color:e.primaryColor},{background:e.colorPrimaryActive,color:e.primaryColor})),dt(e,e.colorLink,"link",{color:e.colorLinkHover,background:e.linkHoverBg},{color:e.colorLinkActive})),Na=function(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const{componentCls:r,controlHeight:a,fontSize:n,borderRadius:o,buttonPaddingHorizontal:i,iconCls:s,buttonPaddingVertical:l,buttonIconOnlyFontSize:c}=e;return[{[t]:{fontSize:n,height:a,padding:`${te(l)} ${te(i)}`,borderRadius:o,[`&${r}-icon-only`]:{width:a,[s]:{fontSize:c}}}},{[`${r}${r}-circle${t}`]:yu(e)},{[`${r}${r}-round${t}`]:bu(e)}]},Iu=e=>{const t=Ue(e,{fontSize:e.contentFontSize});return Na(t,e.componentCls)},Nu=e=>{const t=Ue(e,{controlHeight:e.controlHeightSM,fontSize:e.contentFontSizeSM,padding:e.paddingXS,buttonPaddingHorizontal:e.paddingInlineSM,buttonPaddingVertical:0,borderRadius:e.borderRadiusSM,buttonIconOnlyFontSize:e.onlyIconSizeSM});return Na(t,`${e.componentCls}-sm`)},Mu=e=>{const t=Ue(e,{controlHeight:e.controlHeightLG,fontSize:e.contentFontSizeLG,buttonPaddingHorizontal:e.paddingInlineLG,buttonPaddingVertical:0,borderRadius:e.borderRadiusLG,buttonIconOnlyFontSize:e.onlyIconSizeLG});return Na(t,`${e.componentCls}-lg`)},Tu=e=>{const{componentCls:t}=e;return{[t]:{[`&${t}-block`]:{width:"100%"}}}},Au=Xt("Button",e=>{const t=Fi(e);return[pu(t),Iu(t),Nu(t),Mu(t),Tu(t),Ru(t),Ou(t),zc(t)]},Pi,{unitless:{fontWeight:!0,contentLineHeight:!0,contentLineHeightSM:!0,contentLineHeightLG:!0}});function ju(e,t,r){const{focusElCls:a,focus:n,borderElCls:o}=r,i=o?"> *":"",s=["hover",n?"focus":null,"active"].filter(Boolean).map(l=>`&:${l} ${i}`).join(",");return{[`&-item:not(${t}-last-item)`]:{marginInlineEnd:e.calc(e.lineWidth).mul(-1).equal()},"&-item":Object.assign(Object.assign({[s]:{zIndex:2}},a?{[`&${a}`]:{zIndex:2}}:{}),{[`&[disabled] ${i}`]:{zIndex:0}})}}function Bu(e,t,r){const{borderElCls:a}=r,n=a?`> ${a}`:"";return{[`&-item:not(${t}-first-item):not(${t}-last-item) ${n}`]:{borderRadius:0},[`&-item:not(${t}-last-item)${t}-first-item`]:{[`& ${n}, &${e}-sm ${n}, &${e}-lg ${n}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&-item:not(${t}-first-item)${t}-last-item`]:{[`& ${n}, &${e}-sm ${n}, &${e}-lg ${n}`]:{borderStartStartRadius:0,borderEndStartRadius:0}}}}function Oi(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{focus:!0};const{componentCls:r}=e,a=`${r}-compact`;return{[a]:Object.assign(Object.assign({},ju(e,a,t)),Bu(r,a,t))}}function Lu(e,t){return{[`&-item:not(${t}-last-item)`]:{marginBottom:e.calc(e.lineWidth).mul(-1).equal()},"&-item":{"&:hover,&:focus,&:active":{zIndex:2},"&[disabled]":{zIndex:0}}}}function _u(e,t){return{[`&-item:not(${t}-first-item):not(${t}-last-item)`]:{borderRadius:0},[`&-item${t}-first-item:not(${t}-last-item)`]:{[`&, &${e}-sm, &${e}-lg`]:{borderEndEndRadius:0,borderEndStartRadius:0}},[`&-item${t}-last-item:not(${t}-first-item)`]:{[`&, &${e}-sm, &${e}-lg`]:{borderStartStartRadius:0,borderStartEndRadius:0}}}}function Hu(e){const t=`${e.componentCls}-compact-vertical`;return{[t]:Object.assign(Object.assign({},Lu(e,t)),_u(e.componentCls,t))}}const Vu=e=>{const{componentCls:t,colorPrimaryHover:r,lineWidth:a,calc:n}=e,o=n(a).mul(-1).equal(),i=s=>{const l=`${t}-compact${s?"-vertical":""}-item${t}-primary:not([disabled])`;return{[`${l} + ${l}::before`]:{position:"absolute",top:s?o:0,insetInlineStart:s?0:o,backgroundColor:r,content:'""',width:s?"100%":a,height:s?a:"100%"}}};return Object.assign(Object.assign({},i()),i(!0))},zu=nl(["Button","compact"],e=>{const t=Fi(e);return[Oi(t),Hu(t),Vu(t)]},Pi);var Du=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};function Wu(e){if(typeof e=="object"&&e){let t=e==null?void 0:e.delay;return t=!Number.isNaN(t)&&typeof t=="number"?t:0,{loading:t<=0,delay:t}}return{loading:!!e,delay:0}}const qu={default:["default","outlined"],primary:["primary","solid"],dashed:["default","dashed"],link:["link","link"],text:["default","text"]},Gu=B.forwardRef((e,t)=>{var r,a;const{loading:n=!1,prefixCls:o,color:i,variant:s,type:l,danger:c=!1,shape:d="default",size:v,styles:g,disabled:y,className:h,rootClassName:m,children:f,icon:C,iconPosition:p="start",ghost:S=!1,block:R=!1,htmlType:E="button",classNames:w,style:O={},autoInsertSpace:F,autoFocus:b}=e,P=Du(e,["loading","prefixCls","color","variant","type","danger","shape","size","styles","disabled","className","rootClassName","children","icon","iconPosition","ghost","block","htmlType","classNames","style","autoInsertSpace","autoFocus"]),M=l||"default",[H,T]=u.useMemo(()=>{if(i&&s)return[i,s];const Re=qu[M]||[];return c?["danger",Re[1]]:Re},[l,i,s,c]),W=H==="danger"?"dangerous":H,{getPrefixCls:x,direction:$,autoInsertSpace:N,className:V,style:_,classNames:z,styles:G}=$a("button"),U=(r=F??N)!==null&&r!==void 0?r:!0,j=x("btn",o),[D,k,re]=Au(j),ne=u.useContext(Ko),ee=y??ne,le=u.useContext(wi),be=u.useMemo(()=>Wu(n),[n]),[se,ge]=u.useState(be.loading),[fe,ce]=u.useState(!1),Ce=u.useRef(null),Fe=Xr(t,Ce),ue=u.Children.count(f)===1&&!C&&!Wn(T),we=u.useRef(!0);B.useEffect(()=>(we.current=!1,()=>{we.current=!0}),[]),u.useEffect(()=>{let Re=null;be.delay>0?Re=setTimeout(()=>{Re=null,ge(!0)},be.delay):ge(be.loading);function Me(){Re&&(clearTimeout(Re),Re=null)}return Me},[be]),u.useEffect(()=>{if(!Ce.current||!U)return;const Re=Ce.current.textContent||"";ue&&sa(Re)?fe||ce(!0):fe&&ce(!1)}),u.useEffect(()=>{b&&Ce.current&&Ce.current.focus()},[]);const Ae=B.useCallback(Re=>{var Me;if(se||ee){Re.preventDefault();return}(Me=e.onClick)===null||Me===void 0||Me.call(e,("href"in e,Re))},[e.onClick,se,ee]),{compactSize:xe,compactItemClassnames:nt}=Si(j,$),J={large:"lg",small:"sm",middle:void 0},Y=nn(Re=>{var Me,Be;return(Be=(Me=v??xe)!==null&&Me!==void 0?Me:le)!==null&&Be!==void 0?Be:Re}),ae=Y&&(a=J[Y])!==null&&a!==void 0?a:"",pe=se?"loading":C,Oe=vr(P,["navigate"]),Pe=K(j,k,re,{[`${j}-${d}`]:d!=="default"&&d,[`${j}-${M}`]:M,[`${j}-dangerous`]:c,[`${j}-color-${W}`]:W,[`${j}-variant-${T}`]:T,[`${j}-${ae}`]:ae,[`${j}-icon-only`]:!f&&f!==0&&!!pe,[`${j}-background-ghost`]:S&&!Wn(T),[`${j}-loading`]:se,[`${j}-two-chinese-chars`]:fe&&U&&!se,[`${j}-block`]:R,[`${j}-rtl`]:$==="rtl",[`${j}-icon-end`]:p==="end"},nt,h,m,V),at=Object.assign(Object.assign({},_),O),Bt=K(w==null?void 0:w.icon,z.icon),xt=Object.assign(Object.assign({},(g==null?void 0:g.icon)||{}),G.icon||{}),He=C&&!se?B.createElement(la,{prefixCls:j,className:Bt,style:xt},C):n&&typeof n=="object"&&n.icon?B.createElement(la,{prefixCls:j,className:Bt,style:xt},n.icon):B.createElement(Vc,{existIcon:!!C,prefixCls:j,loading:se,mount:we.current}),Ve=f||f===0?Hc(f,ue&&U):null;if(Oe.href!==void 0)return D(B.createElement("a",Object.assign({},Oe,{className:K(Pe,{[`${j}-disabled`]:ee}),href:ee?void 0:Oe.href,style:at,onClick:Ae,ref:Fe,tabIndex:ee?-1:0}),He,Ve));let je=B.createElement("button",Object.assign({},P,{type:E,className:Pe,style:at,onClick:Ae,disabled:ee,ref:Fe}),He,Ve,nt&&B.createElement(zu,{prefixCls:j}));return Wn(T)||(je=B.createElement(Oc,{component:"Button",disabled:se},je)),D(je)}),Ii=Gu;Ii.Group=Lc;Ii.__ANT_BUTTON=!0;var Ni=u.createContext(null),lo=[];function Uu(e,t){var r=u.useState(function(){if(!Wr())return null;var h=document.createElement("div");return h}),a=Z(r,1),n=a[0],o=u.useRef(!1),i=u.useContext(Ni),s=u.useState(lo),l=Z(s,2),c=l[0],d=l[1],v=i||(o.current?void 0:function(h){d(function(m){var f=[h].concat(Q(m));return f})});function g(){n.parentElement||document.body.appendChild(n),o.current=!0}function y(){var h;(h=n.parentElement)===null||h===void 0||h.removeChild(n),o.current=!1}return qe(function(){return e?i?i(g):g():y(),y},[e]),qe(function(){c.length&&(c.forEach(function(h){return h()}),d(lo))},[c]),[n,v]}var kn;function Mi(e){var t="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),r=document.createElement("div");r.id=t;var a=r.style;a.position="absolute",a.left="0",a.top="0",a.width="100px",a.height="100px",a.overflow="scroll";var n,o;if(e){var i=getComputedStyle(e);a.scrollbarColor=i.scrollbarColor,a.scrollbarWidth=i.scrollbarWidth;var s=getComputedStyle(e,"::-webkit-scrollbar"),l=parseInt(s.width,10),c=parseInt(s.height,10);try{var d=l?"width: ".concat(s.width,";"):"",v=c?"height: ".concat(s.height,";"):"";xa(`
#`.concat(t,`::-webkit-scrollbar {
`).concat(d,`
`).concat(v,`
}`),t)}catch(h){console.error(h),n=l,o=c}}document.body.appendChild(r);var g=e&&n&&!isNaN(n)?n:r.offsetWidth-r.clientWidth,y=e&&o&&!isNaN(o)?o:r.offsetHeight-r.clientHeight;return document.body.removeChild(r),Xn(t),{width:g,height:y}}function gv(e){return typeof document>"u"?0:(kn===void 0&&(kn=Mi()),kn.width)}function Ku(e){return typeof document>"u"||!e||!(e instanceof Element)?{width:0,height:0}:Mi(e)}function ku(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var Ju="rc-util-locker-".concat(Date.now()),co=0;function Zu(e){var t=!!e,r=u.useState(function(){return co+=1,"".concat(Ju,"_").concat(co)}),a=Z(r,1),n=a[0];qe(function(){if(t){var o=Ku(document.body).width,i=ku();xa(`
html body {
  overflow-y: hidden;
  `.concat(i?"width: calc(100% - ".concat(o,"px);"):"",`
}`),n)}else Xn(n);return function(){Xn(n)}},[t,n])}var Qu=!1;function Xu(e){return Qu}var uo=function(t){return t===!1?!1:!Wr()||!t?null:typeof t=="string"?document.querySelector(t):typeof t=="function"?t():t},Ti=u.forwardRef(function(e,t){var r=e.open,a=e.autoLock,n=e.getContainer;e.debug;var o=e.autoDestroy,i=o===void 0?!0:o,s=e.children,l=u.useState(r),c=Z(l,2),d=c[0],v=c[1],g=d||r;u.useEffect(function(){(i||r)&&v(r)},[r,i]);var y=u.useState(function(){return uo(n)}),h=Z(y,2),m=h[0],f=h[1];u.useEffect(function(){var M=uo(n);f(M??null)});var C=Uu(g&&!m),p=Z(C,2),S=p[0],R=p[1],E=m??S;Zu(a&&r&&Wr()&&(E===S||E===document.body));var w=null;if(s&&Qr(s)&&t){var O=s;w=O.ref}var F=Xr(w,t);if(!g||!Wr()||m===void 0)return null;var b=E===!1||Xu(),P=s;return t&&(P=u.cloneElement(s,{ref:F})),u.createElement(Ni.Provider,{value:R},b?P:Do.createPortal(P,E))});function Yu(){var e=A({},ds);return e.useId}var fo=0,vo=Yu();const ed=vo?function(t){var r=vo();return t||r}:function(t){var r=u.useState("ssr-id"),a=Z(r,2),n=a[0],o=a[1];return u.useEffect(function(){var i=fo;fo+=1,o("rc_unique_".concat(i))},[]),t||n};var Nt="RC_FORM_INTERNAL_HOOKS",ve=function(){ut(!1,"Can not find FormContext. Please make sure you wrap Field under Form.")},kt=u.createContext({getFieldValue:ve,getFieldsValue:ve,getFieldError:ve,getFieldWarning:ve,getFieldsError:ve,isFieldsTouched:ve,isFieldTouched:ve,isFieldValidating:ve,isFieldsValidating:ve,resetFields:ve,setFields:ve,setFieldValue:ve,setFieldsValue:ve,validateFields:ve,submit:ve,getInternalHooks:function(){return ve(),{dispatch:ve,initEntityValue:ve,registerField:ve,useSubscribe:ve,setInitialValues:ve,destroyForm:ve,setCallbacks:ve,registerWatch:ve,getFields:ve,setValidateMessages:ve,setPreserve:ve,getInitialValue:ve}}}),Zr=u.createContext(null);function ua(e){return e==null?[]:Array.isArray(e)?e:[e]}function td(e){return e&&!!e._init}function da(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var t=JSON.parse(JSON.stringify(this));return t.clone=this.clone,t}}}var fa=da();function rd(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}function nd(e,t,r){if(fs())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,t);var n=new(e.bind.apply(e,a));return r&&ko(n,r.prototype),n}function va(e){var t=typeof Map=="function"?new Map:void 0;return va=function(a){if(a===null||!rd(a))return a;if(typeof a!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(a))return t.get(a);t.set(a,n)}function n(){return nd(a,arguments,vs(this).constructor)}return n.prototype=Object.create(a.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),ko(n,a)},va(e)}var ad=/%[sdj%]/g,od=function(){};function ga(e){if(!e||!e.length)return null;var t={};return e.forEach(function(r){var a=r.field;t[a]=t[a]||[],t[a].push(r)}),t}function Ge(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];var n=0,o=r.length;if(typeof e=="function")return e.apply(null,r);if(typeof e=="string"){var i=e.replace(ad,function(s){if(s==="%%")return"%";if(n>=o)return s;switch(s){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch{return"[Circular]"}break;default:return s}});return i}return e}function id(e){return e==="string"||e==="url"||e==="hex"||e==="email"||e==="date"||e==="pattern"}function Ne(e,t){return!!(e==null||t==="array"&&Array.isArray(e)&&!e.length||id(t)&&typeof e=="string"&&!e)}function sd(e,t,r){var a=[],n=0,o=e.length;function i(s){a.push.apply(a,Q(s||[])),n++,n===o&&r(a)}e.forEach(function(s){t(s,i)})}function go(e,t,r){var a=0,n=e.length;function o(i){if(i&&i.length){r(i);return}var s=a;a=a+1,s<n?t(e[s],o):r([])}o([])}function ld(e){var t=[];return Object.keys(e).forEach(function(r){t.push.apply(t,Q(e[r]||[]))}),t}var mo=function(e){Jt(r,e);var t=Zt(r);function r(a,n){var o;return Qe(this,r),o=t.call(this,"Async Validation Error"),I(oe(o),"errors",void 0),I(oe(o),"fields",void 0),o.errors=a,o.fields=n,o}return Xe(r)}(va(Error));function cd(e,t,r,a,n){if(t.first){var o=new Promise(function(g,y){var h=function(C){return a(C),C.length?y(new mo(C,ga(C))):g(n)},m=ld(e);go(m,r,h)});return o.catch(function(g){return g}),o}var i=t.firstFields===!0?Object.keys(e):t.firstFields||[],s=Object.keys(e),l=s.length,c=0,d=[],v=new Promise(function(g,y){var h=function(f){if(d.push.apply(d,f),c++,c===l)return a(d),d.length?y(new mo(d,ga(d))):g(n)};s.length||(a(d),g(n)),s.forEach(function(m){var f=e[m];i.indexOf(m)!==-1?go(f,r,h):sd(f,r,h)})});return v.catch(function(g){return g}),v}function ud(e){return!!(e&&e.message!==void 0)}function dd(e,t){for(var r=e,a=0;a<t.length;a++){if(r==null)return r;r=r[t[a]]}return r}function ho(e,t){return function(r){var a;return e.fullFields?a=dd(t,e.fullFields):a=t[r.field||e.fullField],ud(r)?(r.field=r.field||e.fullField,r.fieldValue=a,r):{message:typeof r=="function"?r():r,fieldValue:a,field:r.field||e.fullField}}}function po(e,t){if(t){for(var r in t)if(t.hasOwnProperty(r)){var a=t[r];de(a)==="object"&&de(e[r])==="object"?e[r]=A(A({},e[r]),a):e[r]=a}}return e}var Wt="enum",fd=function(t,r,a,n,o){t[Wt]=Array.isArray(t[Wt])?t[Wt]:[],t[Wt].indexOf(r)===-1&&n.push(Ge(o.messages[Wt],t.fullField,t[Wt].join(", ")))},vd=function(t,r,a,n,o){if(t.pattern){if(t.pattern instanceof RegExp)t.pattern.lastIndex=0,t.pattern.test(r)||n.push(Ge(o.messages.pattern.mismatch,t.fullField,r,t.pattern));else if(typeof t.pattern=="string"){var i=new RegExp(t.pattern);i.test(r)||n.push(Ge(o.messages.pattern.mismatch,t.fullField,r,t.pattern))}}},gd=function(t,r,a,n,o){var i=typeof t.len=="number",s=typeof t.min=="number",l=typeof t.max=="number",c=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,d=r,v=null,g=typeof r=="number",y=typeof r=="string",h=Array.isArray(r);if(g?v="number":y?v="string":h&&(v="array"),!v)return!1;h&&(d=r.length),y&&(d=r.replace(c,"_").length),i?d!==t.len&&n.push(Ge(o.messages[v].len,t.fullField,t.len)):s&&!l&&d<t.min?n.push(Ge(o.messages[v].min,t.fullField,t.min)):l&&!s&&d>t.max?n.push(Ge(o.messages[v].max,t.fullField,t.max)):s&&l&&(d<t.min||d>t.max)&&n.push(Ge(o.messages[v].range,t.fullField,t.min,t.max))},Ai=function(t,r,a,n,o,i){t.required&&(!a.hasOwnProperty(t.field)||Ne(r,i||t.type))&&n.push(Ge(o.messages.required,t.fullField))},Vr;const md=function(){if(Vr)return Vr;var e="[a-fA-F\\d:]",t=function(w){return w&&w.includeBoundaries?"(?:(?<=\\s|^)(?=".concat(e,")|(?<=").concat(e,")(?=\\s|$))"):""},r="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",a="[a-fA-F\\d]{1,4}",n=["(?:".concat(a,":){7}(?:").concat(a,"|:)"),"(?:".concat(a,":){6}(?:").concat(r,"|:").concat(a,"|:)"),"(?:".concat(a,":){5}(?::").concat(r,"|(?::").concat(a,"){1,2}|:)"),"(?:".concat(a,":){4}(?:(?::").concat(a,"){0,1}:").concat(r,"|(?::").concat(a,"){1,3}|:)"),"(?:".concat(a,":){3}(?:(?::").concat(a,"){0,2}:").concat(r,"|(?::").concat(a,"){1,4}|:)"),"(?:".concat(a,":){2}(?:(?::").concat(a,"){0,3}:").concat(r,"|(?::").concat(a,"){1,5}|:)"),"(?:".concat(a,":){1}(?:(?::").concat(a,"){0,4}:").concat(r,"|(?::").concat(a,"){1,6}|:)"),"(?::(?:(?::".concat(a,"){0,5}:").concat(r,"|(?::").concat(a,"){1,7}|:))")],o="(?:%[0-9a-zA-Z]{1,})?",i="(?:".concat(n.join("|"),")").concat(o),s=new RegExp("(?:^".concat(r,"$)|(?:^").concat(i,"$)")),l=new RegExp("^".concat(r,"$")),c=new RegExp("^".concat(i,"$")),d=function(w){return w&&w.exact?s:new RegExp("(?:".concat(t(w)).concat(r).concat(t(w),")|(?:").concat(t(w)).concat(i).concat(t(w),")"),"g")};d.v4=function(E){return E&&E.exact?l:new RegExp("".concat(t(E)).concat(r).concat(t(E)),"g")},d.v6=function(E){return E&&E.exact?c:new RegExp("".concat(t(E)).concat(i).concat(t(E)),"g")};var v="(?:(?:[a-z]+:)?//)",g="(?:\\S+(?::\\S*)?@)?",y=d.v4().source,h=d.v6().source,m="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",f="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",C="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",p="(?::\\d{2,5})?",S='(?:[/?#][^\\s"]*)?',R="(?:".concat(v,"|www\\.)").concat(g,"(?:localhost|").concat(y,"|").concat(h,"|").concat(m).concat(f).concat(C,")").concat(p).concat(S);return Vr=new RegExp("(?:^".concat(R,"$)"),"i"),Vr};var yo={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},ur={integer:function(t){return ur.number(t)&&parseInt(t,10)===t},float:function(t){return ur.number(t)&&!ur.integer(t)},array:function(t){return Array.isArray(t)},regexp:function(t){if(t instanceof RegExp)return!0;try{return!!new RegExp(t)}catch{return!1}},date:function(t){return typeof t.getTime=="function"&&typeof t.getMonth=="function"&&typeof t.getYear=="function"&&!isNaN(t.getTime())},number:function(t){return isNaN(t)?!1:typeof t=="number"},object:function(t){return de(t)==="object"&&!ur.array(t)},method:function(t){return typeof t=="function"},email:function(t){return typeof t=="string"&&t.length<=320&&!!t.match(yo.email)},url:function(t){return typeof t=="string"&&t.length<=2048&&!!t.match(md())},hex:function(t){return typeof t=="string"&&!!t.match(yo.hex)}},hd=function(t,r,a,n,o){if(t.required&&r===void 0){Ai(t,r,a,n,o);return}var i=["integer","float","array","regexp","object","method","email","number","date","url","hex"],s=t.type;i.indexOf(s)>-1?ur[s](r)||n.push(Ge(o.messages.types[s],t.fullField,t.type)):s&&de(r)!==t.type&&n.push(Ge(o.messages.types[s],t.fullField,t.type))},pd=function(t,r,a,n,o){(/^\s+$/.test(r)||r==="")&&n.push(Ge(o.messages.whitespace,t.fullField))};const ie={required:Ai,whitespace:pd,type:hd,range:gd,enum:fd,pattern:vd};var yd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o)}a(i)},bd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(r==null&&!t.required)return a();ie.required(t,r,n,i,o,"array"),r!=null&&(ie.type(t,r,n,i,o),ie.range(t,r,n,i,o))}a(i)},Cd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),r!==void 0&&ie.type(t,r,n,i,o)}a(i)},Sd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r,"date")&&!t.required)return a();if(ie.required(t,r,n,i,o),!Ne(r,"date")){var l;r instanceof Date?l=r:l=new Date(r),ie.type(t,l,n,i,o),l&&ie.range(t,l.getTime(),n,i,o)}}a(i)},wd="enum",xd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),r!==void 0&&ie[wd](t,r,n,i,o)}a(i)},$d=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),r!==void 0&&(ie.type(t,r,n,i,o),ie.range(t,r,n,i,o))}a(i)},Ed=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),r!==void 0&&(ie.type(t,r,n,i,o),ie.range(t,r,n,i,o))}a(i)},Fd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),r!==void 0&&ie.type(t,r,n,i,o)}a(i)},Pd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(r===""&&(r=void 0),Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),r!==void 0&&(ie.type(t,r,n,i,o),ie.range(t,r,n,i,o))}a(i)},Rd=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),r!==void 0&&ie.type(t,r,n,i,o)}a(i)},Od=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r,"string")&&!t.required)return a();ie.required(t,r,n,i,o),Ne(r,"string")||ie.pattern(t,r,n,i,o)}a(i)},Id=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r)&&!t.required)return a();ie.required(t,r,n,i,o),Ne(r)||ie.type(t,r,n,i,o)}a(i)},Nd=function(t,r,a,n,o){var i=[],s=Array.isArray(r)?"array":de(r);ie.required(t,r,n,i,o,s),a(i)},Md=function(t,r,a,n,o){var i=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(Ne(r,"string")&&!t.required)return a();ie.required(t,r,n,i,o,"string"),Ne(r,"string")||(ie.type(t,r,n,i,o),ie.range(t,r,n,i,o),ie.pattern(t,r,n,i,o),t.whitespace===!0&&ie.whitespace(t,r,n,i,o))}a(i)},Jn=function(t,r,a,n,o){var i=t.type,s=[],l=t.required||!t.required&&n.hasOwnProperty(t.field);if(l){if(Ne(r,i)&&!t.required)return a();ie.required(t,r,n,s,o,i),Ne(r,i)||ie.type(t,r,n,s,o)}a(s)};const fr={string:Md,method:Fd,number:Pd,boolean:Cd,regexp:Id,integer:Ed,float:$d,array:bd,object:Rd,enum:xd,pattern:Od,date:Sd,url:Jn,hex:Jn,email:Jn,required:Nd,any:yd};var br=function(){function e(t){Qe(this,e),I(this,"rules",null),I(this,"_messages",fa),this.define(t)}return Xe(e,[{key:"define",value:function(r){var a=this;if(!r)throw new Error("Cannot configure a schema with no rules");if(de(r)!=="object"||Array.isArray(r))throw new Error("Rules must be an object");this.rules={},Object.keys(r).forEach(function(n){var o=r[n];a.rules[n]=Array.isArray(o)?o:[o]})}},{key:"messages",value:function(r){return r&&(this._messages=po(da(),r)),this._messages}},{key:"validate",value:function(r){var a=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){},i=r,s=n,l=o;if(typeof s=="function"&&(l=s,s={}),!this.rules||Object.keys(this.rules).length===0)return l&&l(null,i),Promise.resolve(i);function c(h){var m=[],f={};function C(S){if(Array.isArray(S)){var R;m=(R=m).concat.apply(R,Q(S))}else m.push(S)}for(var p=0;p<h.length;p++)C(h[p]);m.length?(f=ga(m),l(m,f)):l(null,i)}if(s.messages){var d=this.messages();d===fa&&(d=da()),po(d,s.messages),s.messages=d}else s.messages=this.messages();var v={},g=s.keys||Object.keys(this.rules);g.forEach(function(h){var m=a.rules[h],f=i[h];m.forEach(function(C){var p=C;typeof p.transform=="function"&&(i===r&&(i=A({},i)),f=i[h]=p.transform(f),f!=null&&(p.type=p.type||(Array.isArray(f)?"array":de(f)))),typeof p=="function"?p={validator:p}:p=A({},p),p.validator=a.getValidationMethod(p),p.validator&&(p.field=h,p.fullField=p.fullField||h,p.type=a.getType(p),v[h]=v[h]||[],v[h].push({rule:p,value:f,source:i,field:h}))})});var y={};return cd(v,s,function(h,m){var f=h.rule,C=(f.type==="object"||f.type==="array")&&(de(f.fields)==="object"||de(f.defaultField)==="object");C=C&&(f.required||!f.required&&h.value),f.field=h.field;function p(O,F){return A(A({},F),{},{fullField:"".concat(f.fullField,".").concat(O),fullFields:f.fullFields?[].concat(Q(f.fullFields),[O]):[O]})}function S(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],F=Array.isArray(O)?O:[O];!s.suppressWarning&&F.length&&e.warning("async-validator:",F),F.length&&f.message!==void 0&&(F=[].concat(f.message));var b=F.map(ho(f,i));if(s.first&&b.length)return y[f.field]=1,m(b);if(!C)m(b);else{if(f.required&&!h.value)return f.message!==void 0?b=[].concat(f.message).map(ho(f,i)):s.error&&(b=[s.error(f,Ge(s.messages.required,f.field))]),m(b);var P={};f.defaultField&&Object.keys(h.value).map(function(T){P[T]=f.defaultField}),P=A(A({},P),h.rule.fields);var M={};Object.keys(P).forEach(function(T){var L=P[T],W=Array.isArray(L)?L:[L];M[T]=W.map(p.bind(null,T))});var H=new e(M);H.messages(s.messages),h.rule.options&&(h.rule.options.messages=s.messages,h.rule.options.error=s.error),H.validate(h.value,h.rule.options||s,function(T){var L=[];b&&b.length&&L.push.apply(L,Q(b)),T&&T.length&&L.push.apply(L,Q(T)),m(L.length?L:null)})}}var R;if(f.asyncValidator)R=f.asyncValidator(f,h.value,S,h.source,s);else if(f.validator){try{R=f.validator(f,h.value,S,h.source,s)}catch(O){var E,w;(E=(w=console).error)===null||E===void 0||E.call(w,O),s.suppressValidatorError||setTimeout(function(){throw O},0),S(O.message)}R===!0?S():R===!1?S(typeof f.message=="function"?f.message(f.fullField||f.field):f.message||"".concat(f.fullField||f.field," fails")):R instanceof Array?S(R):R instanceof Error&&S(R.message)}R&&R.then&&R.then(function(){return S()},function(O){return S(O)})},function(h){c(h)},i)}},{key:"getType",value:function(r){if(r.type===void 0&&r.pattern instanceof RegExp&&(r.type="pattern"),typeof r.validator!="function"&&r.type&&!fr.hasOwnProperty(r.type))throw new Error(Ge("Unknown rule type %s",r.type));return r.type||"string"}},{key:"getValidationMethod",value:function(r){if(typeof r.validator=="function")return r.validator;var a=Object.keys(r),n=a.indexOf("message");return n!==-1&&a.splice(n,1),a.length===1&&a[0]==="required"?fr.required:fr[this.getType(r)]||void 0}}]),e}();I(br,"register",function(t,r){if(typeof r!="function")throw new Error("Cannot register a validator by type, validator is not a function");fr[t]=r});I(br,"warning",od);I(br,"messages",fa);I(br,"validators",fr);var We="'${name}' is not a valid ${type}",ji={default:"Validation error on field '${name}'",required:"'${name}' is required",enum:"'${name}' must be one of [${enum}]",whitespace:"'${name}' cannot be empty",date:{format:"'${name}' is invalid for format date",parse:"'${name}' could not be parsed as date",invalid:"'${name}' is invalid date"},types:{string:We,method:We,array:We,object:We,number:We,date:We,boolean:We,integer:We,float:We,regexp:We,email:We,url:We,hex:We},string:{len:"'${name}' must be exactly ${len} characters",min:"'${name}' must be at least ${min} characters",max:"'${name}' cannot be longer than ${max} characters",range:"'${name}' must be between ${min} and ${max} characters"},number:{len:"'${name}' must equal ${len}",min:"'${name}' cannot be less than ${min}",max:"'${name}' cannot be greater than ${max}",range:"'${name}' must be between ${min} and ${max}"},array:{len:"'${name}' must be exactly ${len} in length",min:"'${name}' cannot be less than ${min} in length",max:"'${name}' cannot be greater than ${max} in length",range:"'${name}' must be between ${min} and ${max} in length"},pattern:{mismatch:"'${name}' does not match pattern ${pattern}"}},bo=br;function Td(e,t){return e.replace(/\\?\$\{\w+\}/g,function(r){if(r.startsWith("\\"))return r.slice(1);var a=r.slice(2,-1);return t[a]})}var Co="CODE_LOGIC_ERROR";function ma(e,t,r,a,n){return ha.apply(this,arguments)}function ha(){return ha=jt(_e().mark(function e(t,r,a,n,o){var i,s,l,c,d,v,g,y,h;return _e().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return i=A({},a),delete i.ruleIndex,bo.warning=function(){},i.validator&&(s=i.validator,i.validator=function(){try{return s.apply(void 0,arguments)}catch(C){return console.error(C),Promise.reject(Co)}}),l=null,i&&i.type==="array"&&i.defaultField&&(l=i.defaultField,delete i.defaultField),c=new bo(I({},t,[i])),d=cr(ji,n.validateMessages),c.messages(d),v=[],f.prev=10,f.next=13,Promise.resolve(c.validate(I({},t,r),A({},n)));case 13:f.next=18;break;case 15:f.prev=15,f.t0=f.catch(10),f.t0.errors&&(v=f.t0.errors.map(function(C,p){var S=C.message,R=S===Co?d.default:S;return u.isValidElement(R)?u.cloneElement(R,{key:"error_".concat(p)}):R}));case 18:if(!(!v.length&&l)){f.next=23;break}return f.next=21,Promise.all(r.map(function(C,p){return ma("".concat(t,".").concat(p),C,l,n,o)}));case 21:return g=f.sent,f.abrupt("return",g.reduce(function(C,p){return[].concat(Q(C),Q(p))},[]));case 23:return y=A(A({},a),{},{name:t,enum:(a.enum||[]).join(", ")},o),h=v.map(function(C){return typeof C=="string"?Td(C,y):C}),f.abrupt("return",h);case 26:case"end":return f.stop()}},e,null,[[10,15]])})),ha.apply(this,arguments)}function Ad(e,t,r,a,n,o){var i=e.join("."),s=r.map(function(d,v){var g=d.validator,y=A(A({},d),{},{ruleIndex:v});return g&&(y.validator=function(h,m,f){var C=!1,p=function(){for(var E=arguments.length,w=new Array(E),O=0;O<E;O++)w[O]=arguments[O];Promise.resolve().then(function(){ut(!C,"Your validator function has already return a promise. `callback` will be ignored."),C||f.apply(void 0,w)})},S=g(h,m,p);C=S&&typeof S.then=="function"&&typeof S.catch=="function",ut(C,"`callback` is deprecated. Please return a promise instead."),C&&S.then(function(){f()}).catch(function(R){f(R||" ")})}),y}).sort(function(d,v){var g=d.warningOnly,y=d.ruleIndex,h=v.warningOnly,m=v.ruleIndex;return!!g==!!h?y-m:g?1:-1}),l;if(n===!0)l=new Promise(function(){var d=jt(_e().mark(function v(g,y){var h,m,f;return _e().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:h=0;case 1:if(!(h<s.length)){p.next=12;break}return m=s[h],p.next=5,ma(i,t,m,a,o);case 5:if(f=p.sent,!f.length){p.next=9;break}return y([{errors:f,rule:m}]),p.abrupt("return");case 9:h+=1,p.next=1;break;case 12:g([]);case 13:case"end":return p.stop()}},v)}));return function(v,g){return d.apply(this,arguments)}}());else{var c=s.map(function(d){return ma(i,t,d,a,o).then(function(v){return{errors:v,rule:d}})});l=(n?Bd(c):jd(c)).then(function(d){return Promise.reject(d)})}return l.catch(function(d){return d}),l}function jd(e){return pa.apply(this,arguments)}function pa(){return pa=jt(_e().mark(function e(t){return _e().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",Promise.all(t).then(function(n){var o,i=(o=[]).concat.apply(o,Q(n));return i}));case 1:case"end":return a.stop()}},e)})),pa.apply(this,arguments)}function Bd(e){return ya.apply(this,arguments)}function ya(){return ya=jt(_e().mark(function e(t){var r;return _e().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return r=0,n.abrupt("return",new Promise(function(o){t.forEach(function(i){i.then(function(s){s.errors.length&&o([s]),r+=1,r===t.length&&o([])})})}));case 2:case"end":return n.stop()}},e)})),ya.apply(this,arguments)}function Ee(e){return ua(e)}function So(e,t){var r={};return t.forEach(function(a){var n=It(e,a);r=Ct(r,a,n)}),r}function Ut(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return e&&e.some(function(a){return Bi(t,a,r)})}function Bi(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return!e||!t||!r&&e.length!==t.length?!1:t.every(function(a,n){return e[n]===a})}function Ld(e,t){if(e===t)return!0;if(!e&&t||e&&!t||!e||!t||de(e)!=="object"||de(t)!=="object")return!1;var r=Object.keys(e),a=Object.keys(t),n=new Set([].concat(r,a));return Q(n).every(function(o){var i=e[o],s=t[o];return typeof i=="function"&&typeof s=="function"?!0:i===s})}function _d(e){var t=arguments.length<=1?void 0:arguments[1];return t&&t.target&&de(t.target)==="object"&&e in t.target?t.target[e]:t}function wo(e,t,r){var a=e.length;if(t<0||t>=a||r<0||r>=a)return e;var n=e[t],o=t-r;return o>0?[].concat(Q(e.slice(0,r)),[n],Q(e.slice(r,t)),Q(e.slice(t+1,a))):o<0?[].concat(Q(e.slice(0,t)),Q(e.slice(t+1,r+1)),[n],Q(e.slice(r+1,a))):e}var Hd=["name"],Je=[];function Zn(e,t,r,a,n,o){return typeof e=="function"?e(t,r,"source"in o?{source:o.source}:{}):a!==n}var Ma=function(e){Jt(r,e);var t=Zt(r);function r(a){var n;if(Qe(this,r),n=t.call(this,a),I(oe(n),"state",{resetCount:0}),I(oe(n),"cancelRegisterFunc",null),I(oe(n),"mounted",!1),I(oe(n),"touched",!1),I(oe(n),"dirty",!1),I(oe(n),"validatePromise",void 0),I(oe(n),"prevValidating",void 0),I(oe(n),"errors",Je),I(oe(n),"warnings",Je),I(oe(n),"cancelRegister",function(){var l=n.props,c=l.preserve,d=l.isListField,v=l.name;n.cancelRegisterFunc&&n.cancelRegisterFunc(d,c,Ee(v)),n.cancelRegisterFunc=null}),I(oe(n),"getNamePath",function(){var l=n.props,c=l.name,d=l.fieldContext,v=d.prefixName,g=v===void 0?[]:v;return c!==void 0?[].concat(Q(g),Q(c)):[]}),I(oe(n),"getRules",function(){var l=n.props,c=l.rules,d=c===void 0?[]:c,v=l.fieldContext;return d.map(function(g){return typeof g=="function"?g(v):g})}),I(oe(n),"refresh",function(){n.mounted&&n.setState(function(l){var c=l.resetCount;return{resetCount:c+1}})}),I(oe(n),"metaCache",null),I(oe(n),"triggerMetaEvent",function(l){var c=n.props.onMetaChange;if(c){var d=A(A({},n.getMeta()),{},{destroy:l});Ha(n.metaCache,d)||c(d),n.metaCache=d}else n.metaCache=null}),I(oe(n),"onStoreChange",function(l,c,d){var v=n.props,g=v.shouldUpdate,y=v.dependencies,h=y===void 0?[]:y,m=v.onReset,f=d.store,C=n.getNamePath(),p=n.getValue(l),S=n.getValue(f),R=c&&Ut(c,C);switch(d.type==="valueUpdate"&&d.source==="external"&&!Ha(p,S)&&(n.touched=!0,n.dirty=!0,n.validatePromise=null,n.errors=Je,n.warnings=Je,n.triggerMetaEvent()),d.type){case"reset":if(!c||R){n.touched=!1,n.dirty=!1,n.validatePromise=void 0,n.errors=Je,n.warnings=Je,n.triggerMetaEvent(),m==null||m(),n.refresh();return}break;case"remove":{if(g&&Zn(g,l,f,p,S,d)){n.reRender();return}break}case"setField":{var E=d.data;if(R){"touched"in E&&(n.touched=E.touched),"validating"in E&&!("originRCField"in E)&&(n.validatePromise=E.validating?Promise.resolve([]):null),"errors"in E&&(n.errors=E.errors||Je),"warnings"in E&&(n.warnings=E.warnings||Je),n.dirty=!0,n.triggerMetaEvent(),n.reRender();return}else if("value"in E&&Ut(c,C,!0)){n.reRender();return}if(g&&!C.length&&Zn(g,l,f,p,S,d)){n.reRender();return}break}case"dependenciesUpdate":{var w=h.map(Ee);if(w.some(function(O){return Ut(d.relatedFields,O)})){n.reRender();return}break}default:if(R||(!h.length||C.length||g)&&Zn(g,l,f,p,S,d)){n.reRender();return}break}g===!0&&n.reRender()}),I(oe(n),"validateRules",function(l){var c=n.getNamePath(),d=n.getValue(),v=l||{},g=v.triggerName,y=v.validateOnly,h=y===void 0?!1:y,m=Promise.resolve().then(jt(_e().mark(function f(){var C,p,S,R,E,w,O;return _e().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:if(n.mounted){b.next=2;break}return b.abrupt("return",[]);case 2:if(C=n.props,p=C.validateFirst,S=p===void 0?!1:p,R=C.messageVariables,E=C.validateDebounce,w=n.getRules(),g&&(w=w.filter(function(P){return P}).filter(function(P){var M=P.validateTrigger;if(!M)return!0;var H=ua(M);return H.includes(g)})),!(E&&g)){b.next=10;break}return b.next=8,new Promise(function(P){setTimeout(P,E)});case 8:if(n.validatePromise===m){b.next=10;break}return b.abrupt("return",[]);case 10:return O=Ad(c,d,w,l,S,R),O.catch(function(P){return P}).then(function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Je;if(n.validatePromise===m){var M;n.validatePromise=null;var H=[],T=[];(M=P.forEach)===null||M===void 0||M.call(P,function(L){var W=L.rule.warningOnly,x=L.errors,$=x===void 0?Je:x;W?T.push.apply(T,Q($)):H.push.apply(H,Q($))}),n.errors=H,n.warnings=T,n.triggerMetaEvent(),n.reRender()}}),b.abrupt("return",O);case 13:case"end":return b.stop()}},f)})));return h||(n.validatePromise=m,n.dirty=!0,n.errors=Je,n.warnings=Je,n.triggerMetaEvent(),n.reRender()),m}),I(oe(n),"isFieldValidating",function(){return!!n.validatePromise}),I(oe(n),"isFieldTouched",function(){return n.touched}),I(oe(n),"isFieldDirty",function(){if(n.dirty||n.props.initialValue!==void 0)return!0;var l=n.props.fieldContext,c=l.getInternalHooks(Nt),d=c.getInitialValue;return d(n.getNamePath())!==void 0}),I(oe(n),"getErrors",function(){return n.errors}),I(oe(n),"getWarnings",function(){return n.warnings}),I(oe(n),"isListField",function(){return n.props.isListField}),I(oe(n),"isList",function(){return n.props.isList}),I(oe(n),"isPreserve",function(){return n.props.preserve}),I(oe(n),"getMeta",function(){n.prevValidating=n.isFieldValidating();var l={touched:n.isFieldTouched(),validating:n.prevValidating,errors:n.errors,warnings:n.warnings,name:n.getNamePath(),validated:n.validatePromise===null};return l}),I(oe(n),"getOnlyChild",function(l){if(typeof l=="function"){var c=n.getMeta();return A(A({},n.getOnlyChild(l(n.getControlled(),c,n.props.fieldContext))),{},{isFunction:!0})}var d=Tt(l);return d.length!==1||!u.isValidElement(d[0])?{child:d,isFunction:!1}:{child:d[0],isFunction:!1}}),I(oe(n),"getValue",function(l){var c=n.props.fieldContext.getFieldsValue,d=n.getNamePath();return It(l||c(!0),d)}),I(oe(n),"getControlled",function(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},c=n.props,d=c.name,v=c.trigger,g=c.validateTrigger,y=c.getValueFromEvent,h=c.normalize,m=c.valuePropName,f=c.getValueProps,C=c.fieldContext,p=g!==void 0?g:C.validateTrigger,S=n.getNamePath(),R=C.getInternalHooks,E=C.getFieldsValue,w=R(Nt),O=w.dispatch,F=n.getValue(),b=f||function(L){return I({},m,L)},P=l[v],M=d!==void 0?b(F):{},H=A(A({},l),M);H[v]=function(){n.touched=!0,n.dirty=!0,n.triggerMetaEvent();for(var L,W=arguments.length,x=new Array(W),$=0;$<W;$++)x[$]=arguments[$];y?L=y.apply(void 0,x):L=_d.apply(void 0,[m].concat(x)),h&&(L=h(L,F,E(!0))),L!==F&&O({type:"updateValue",namePath:S,value:L}),P&&P.apply(void 0,x)};var T=ua(p||[]);return T.forEach(function(L){var W=H[L];H[L]=function(){W&&W.apply(void 0,arguments);var x=n.props.rules;x&&x.length&&O({type:"validateField",namePath:S,triggerName:L})}}),H}),a.fieldContext){var o=a.fieldContext.getInternalHooks,i=o(Nt),s=i.initEntityValue;s(oe(n))}return n}return Xe(r,[{key:"componentDidMount",value:function(){var n=this.props,o=n.shouldUpdate,i=n.fieldContext;if(this.mounted=!0,i){var s=i.getInternalHooks,l=s(Nt),c=l.registerField;this.cancelRegisterFunc=c(this)}o===!0&&this.reRender()}},{key:"componentWillUnmount",value:function(){this.cancelRegister(),this.triggerMetaEvent(!0),this.mounted=!1}},{key:"reRender",value:function(){this.mounted&&this.forceUpdate()}},{key:"render",value:function(){var n=this.state.resetCount,o=this.props.children,i=this.getOnlyChild(o),s=i.child,l=i.isFunction,c;return l?c=s:u.isValidElement(s)?c=u.cloneElement(s,this.getControlled(s.props)):(ut(!s,"`children` of Field is not validate ReactElement."),c=s),u.createElement(u.Fragment,{key:n},c)}}]),r}(u.Component);I(Ma,"contextType",kt);I(Ma,"defaultProps",{trigger:"onChange",valuePropName:"value"});function Li(e){var t,r=e.name,a=ze(e,Hd),n=u.useContext(kt),o=u.useContext(Zr),i=r!==void 0?Ee(r):void 0,s=(t=a.isListField)!==null&&t!==void 0?t:!!o,l="keep";return s||(l="_".concat((i||[]).join("_"))),u.createElement(Ma,me({key:l,name:i,isListField:s},a,{fieldContext:n}))}function Vd(e){var t=e.name,r=e.initialValue,a=e.children,n=e.rules,o=e.validateTrigger,i=e.isListField,s=u.useContext(kt),l=u.useContext(Zr),c=u.useRef({keys:[],id:0}),d=c.current,v=u.useMemo(function(){var m=Ee(s.prefixName)||[];return[].concat(Q(m),Q(Ee(t)))},[s.prefixName,t]),g=u.useMemo(function(){return A(A({},s),{},{prefixName:v})},[s,v]),y=u.useMemo(function(){return{getKey:function(f){var C=v.length,p=f[C];return[d.keys[p],f.slice(C+1)]}}},[v]);if(typeof a!="function")return ut(!1,"Form.List only accepts function as children."),null;var h=function(f,C,p){var S=p.source;return S==="internal"?!1:f!==C};return u.createElement(Zr.Provider,{value:y},u.createElement(kt.Provider,{value:g},u.createElement(Li,{name:[],shouldUpdate:h,rules:n,validateTrigger:o,initialValue:r,isList:!0,isListField:i??!!l},function(m,f){var C=m.value,p=C===void 0?[]:C,S=m.onChange,R=s.getFieldValue,E=function(){var b=R(v||[]);return b||[]},w={add:function(b,P){var M=E();P>=0&&P<=M.length?(d.keys=[].concat(Q(d.keys.slice(0,P)),[d.id],Q(d.keys.slice(P))),S([].concat(Q(M.slice(0,P)),[b],Q(M.slice(P))))):(d.keys=[].concat(Q(d.keys),[d.id]),S([].concat(Q(M),[b]))),d.id+=1},remove:function(b){var P=E(),M=new Set(Array.isArray(b)?b:[b]);M.size<=0||(d.keys=d.keys.filter(function(H,T){return!M.has(T)}),S(P.filter(function(H,T){return!M.has(T)})))},move:function(b,P){if(b!==P){var M=E();b<0||b>=M.length||P<0||P>=M.length||(d.keys=wo(d.keys,b,P),S(wo(M,b,P)))}}},O=p||[];return Array.isArray(O)||(O=[]),a(O.map(function(F,b){var P=d.keys[b];return P===void 0&&(d.keys[b]=d.id,P=d.keys[b],d.id+=1),{name:b,key:P,isListField:!0}}),w,f)})))}function zd(e){var t=!1,r=e.length,a=[];return e.length?new Promise(function(n,o){e.forEach(function(i,s){i.catch(function(l){return t=!0,l}).then(function(l){r-=1,a[s]=l,!(r>0)&&(t&&o(a),n(a))})})}):Promise.resolve([])}var _i="__@field_split__";function Qn(e){return e.map(function(t){return"".concat(de(t),":").concat(t)}).join(_i)}var qt=function(){function e(){Qe(this,e),I(this,"kvs",new Map)}return Xe(e,[{key:"set",value:function(r,a){this.kvs.set(Qn(r),a)}},{key:"get",value:function(r){return this.kvs.get(Qn(r))}},{key:"update",value:function(r,a){var n=this.get(r),o=a(n);o?this.set(r,o):this.delete(r)}},{key:"delete",value:function(r){this.kvs.delete(Qn(r))}},{key:"map",value:function(r){return Q(this.kvs.entries()).map(function(a){var n=Z(a,2),o=n[0],i=n[1],s=o.split(_i);return r({key:s.map(function(l){var c=l.match(/^([^:]*):(.*)$/),d=Z(c,3),v=d[1],g=d[2];return v==="number"?Number(g):g}),value:i})})}},{key:"toJSON",value:function(){var r={};return this.map(function(a){var n=a.key,o=a.value;return r[n.join(".")]=o,null}),r}}]),e}(),Dd=["name"],Wd=Xe(function e(t){var r=this;Qe(this,e),I(this,"formHooked",!1),I(this,"forceRootUpdate",void 0),I(this,"subscribable",!0),I(this,"store",{}),I(this,"fieldEntities",[]),I(this,"initialValues",{}),I(this,"callbacks",{}),I(this,"validateMessages",null),I(this,"preserve",null),I(this,"lastValidatePromise",null),I(this,"getForm",function(){return{getFieldValue:r.getFieldValue,getFieldsValue:r.getFieldsValue,getFieldError:r.getFieldError,getFieldWarning:r.getFieldWarning,getFieldsError:r.getFieldsError,isFieldsTouched:r.isFieldsTouched,isFieldTouched:r.isFieldTouched,isFieldValidating:r.isFieldValidating,isFieldsValidating:r.isFieldsValidating,resetFields:r.resetFields,setFields:r.setFields,setFieldValue:r.setFieldValue,setFieldsValue:r.setFieldsValue,validateFields:r.validateFields,submit:r.submit,_init:!0,getInternalHooks:r.getInternalHooks}}),I(this,"getInternalHooks",function(a){return a===Nt?(r.formHooked=!0,{dispatch:r.dispatch,initEntityValue:r.initEntityValue,registerField:r.registerField,useSubscribe:r.useSubscribe,setInitialValues:r.setInitialValues,destroyForm:r.destroyForm,setCallbacks:r.setCallbacks,setValidateMessages:r.setValidateMessages,getFields:r.getFields,setPreserve:r.setPreserve,getInitialValue:r.getInitialValue,registerWatch:r.registerWatch}):(ut(!1,"`getInternalHooks` is internal usage. Should not call directly."),null)}),I(this,"useSubscribe",function(a){r.subscribable=a}),I(this,"prevWithoutPreserves",null),I(this,"setInitialValues",function(a,n){if(r.initialValues=a||{},n){var o,i=cr(a,r.store);(o=r.prevWithoutPreserves)===null||o===void 0||o.map(function(s){var l=s.key;i=Ct(i,l,It(a,l))}),r.prevWithoutPreserves=null,r.updateStore(i)}}),I(this,"destroyForm",function(a){if(a)r.updateStore({});else{var n=new qt;r.getFieldEntities(!0).forEach(function(o){r.isMergedPreserve(o.isPreserve())||n.set(o.getNamePath(),!0)}),r.prevWithoutPreserves=n}}),I(this,"getInitialValue",function(a){var n=It(r.initialValues,a);return a.length?cr(n):n}),I(this,"setCallbacks",function(a){r.callbacks=a}),I(this,"setValidateMessages",function(a){r.validateMessages=a}),I(this,"setPreserve",function(a){r.preserve=a}),I(this,"watchList",[]),I(this,"registerWatch",function(a){return r.watchList.push(a),function(){r.watchList=r.watchList.filter(function(n){return n!==a})}}),I(this,"notifyWatch",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];if(r.watchList.length){var n=r.getFieldsValue(),o=r.getFieldsValue(!0);r.watchList.forEach(function(i){i(n,o,a)})}}),I(this,"timeoutId",null),I(this,"warningUnhooked",function(){}),I(this,"updateStore",function(a){r.store=a}),I(this,"getFieldEntities",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return a?r.fieldEntities.filter(function(n){return n.getNamePath().length}):r.fieldEntities}),I(this,"getFieldsMap",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,n=new qt;return r.getFieldEntities(a).forEach(function(o){var i=o.getNamePath();n.set(i,o)}),n}),I(this,"getFieldEntitiesForNamePathList",function(a){if(!a)return r.getFieldEntities(!0);var n=r.getFieldsMap(!0);return a.map(function(o){var i=Ee(o);return n.get(i)||{INVALIDATE_NAME_PATH:Ee(o)}})}),I(this,"getFieldsValue",function(a,n){r.warningUnhooked();var o,i,s;if(a===!0||Array.isArray(a)?(o=a,i=n):a&&de(a)==="object"&&(s=a.strict,i=a.filter),o===!0&&!i)return r.store;var l=r.getFieldEntitiesForNamePathList(Array.isArray(o)?o:null),c=[];return l.forEach(function(d){var v,g,y="INVALIDATE_NAME_PATH"in d?d.INVALIDATE_NAME_PATH:d.getNamePath();if(s){var h,m;if((h=(m=d).isList)!==null&&h!==void 0&&h.call(m))return}else if(!o&&(v=(g=d).isListField)!==null&&v!==void 0&&v.call(g))return;if(!i)c.push(y);else{var f="getMeta"in d?d.getMeta():null;i(f)&&c.push(y)}}),So(r.store,c.map(Ee))}),I(this,"getFieldValue",function(a){r.warningUnhooked();var n=Ee(a);return It(r.store,n)}),I(this,"getFieldsError",function(a){r.warningUnhooked();var n=r.getFieldEntitiesForNamePathList(a);return n.map(function(o,i){return o&&!("INVALIDATE_NAME_PATH"in o)?{name:o.getNamePath(),errors:o.getErrors(),warnings:o.getWarnings()}:{name:Ee(a[i]),errors:[],warnings:[]}})}),I(this,"getFieldError",function(a){r.warningUnhooked();var n=Ee(a),o=r.getFieldsError([n])[0];return o.errors}),I(this,"getFieldWarning",function(a){r.warningUnhooked();var n=Ee(a),o=r.getFieldsError([n])[0];return o.warnings}),I(this,"isFieldsTouched",function(){r.warningUnhooked();for(var a=arguments.length,n=new Array(a),o=0;o<a;o++)n[o]=arguments[o];var i=n[0],s=n[1],l,c=!1;n.length===0?l=null:n.length===1?Array.isArray(i)?(l=i.map(Ee),c=!1):(l=null,c=i):(l=i.map(Ee),c=s);var d=r.getFieldEntities(!0),v=function(f){return f.isFieldTouched()};if(!l)return c?d.every(function(m){return v(m)||m.isList()}):d.some(v);var g=new qt;l.forEach(function(m){g.set(m,[])}),d.forEach(function(m){var f=m.getNamePath();l.forEach(function(C){C.every(function(p,S){return f[S]===p})&&g.update(C,function(p){return[].concat(Q(p),[m])})})});var y=function(f){return f.some(v)},h=g.map(function(m){var f=m.value;return f});return c?h.every(y):h.some(y)}),I(this,"isFieldTouched",function(a){return r.warningUnhooked(),r.isFieldsTouched([a])}),I(this,"isFieldsValidating",function(a){r.warningUnhooked();var n=r.getFieldEntities();if(!a)return n.some(function(i){return i.isFieldValidating()});var o=a.map(Ee);return n.some(function(i){var s=i.getNamePath();return Ut(o,s)&&i.isFieldValidating()})}),I(this,"isFieldValidating",function(a){return r.warningUnhooked(),r.isFieldsValidating([a])}),I(this,"resetWithFieldInitialValue",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=new qt,o=r.getFieldEntities(!0);o.forEach(function(l){var c=l.props.initialValue,d=l.getNamePath();if(c!==void 0){var v=n.get(d)||new Set;v.add({entity:l,value:c}),n.set(d,v)}});var i=function(c){c.forEach(function(d){var v=d.props.initialValue;if(v!==void 0){var g=d.getNamePath(),y=r.getInitialValue(g);if(y!==void 0)ut(!1,"Form already set 'initialValues' with path '".concat(g.join("."),"'. Field can not overwrite it."));else{var h=n.get(g);if(h&&h.size>1)ut(!1,"Multiple Field with path '".concat(g.join("."),"' set 'initialValue'. Can not decide which one to pick."));else if(h){var m=r.getFieldValue(g),f=d.isListField();!f&&(!a.skipExist||m===void 0)&&r.updateStore(Ct(r.store,g,Q(h)[0].value))}}}})},s;a.entities?s=a.entities:a.namePathList?(s=[],a.namePathList.forEach(function(l){var c=n.get(l);if(c){var d;(d=s).push.apply(d,Q(Q(c).map(function(v){return v.entity})))}})):s=o,i(s)}),I(this,"resetFields",function(a){r.warningUnhooked();var n=r.store;if(!a){r.updateStore(cr(r.initialValues)),r.resetWithFieldInitialValue(),r.notifyObservers(n,null,{type:"reset"}),r.notifyWatch();return}var o=a.map(Ee);o.forEach(function(i){var s=r.getInitialValue(i);r.updateStore(Ct(r.store,i,s))}),r.resetWithFieldInitialValue({namePathList:o}),r.notifyObservers(n,o,{type:"reset"}),r.notifyWatch(o)}),I(this,"setFields",function(a){r.warningUnhooked();var n=r.store,o=[];a.forEach(function(i){var s=i.name,l=ze(i,Dd),c=Ee(s);o.push(c),"value"in l&&r.updateStore(Ct(r.store,c,l.value)),r.notifyObservers(n,[c],{type:"setField",data:i})}),r.notifyWatch(o)}),I(this,"getFields",function(){var a=r.getFieldEntities(!0),n=a.map(function(o){var i=o.getNamePath(),s=o.getMeta(),l=A(A({},s),{},{name:i,value:r.getFieldValue(i)});return Object.defineProperty(l,"originRCField",{value:!0}),l});return n}),I(this,"initEntityValue",function(a){var n=a.props.initialValue;if(n!==void 0){var o=a.getNamePath(),i=It(r.store,o);i===void 0&&r.updateStore(Ct(r.store,o,n))}}),I(this,"isMergedPreserve",function(a){var n=a!==void 0?a:r.preserve;return n??!0}),I(this,"registerField",function(a){r.fieldEntities.push(a);var n=a.getNamePath();if(r.notifyWatch([n]),a.props.initialValue!==void 0){var o=r.store;r.resetWithFieldInitialValue({entities:[a],skipExist:!0}),r.notifyObservers(o,[a.getNamePath()],{type:"valueUpdate",source:"internal"})}return function(i,s){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];if(r.fieldEntities=r.fieldEntities.filter(function(v){return v!==a}),!r.isMergedPreserve(s)&&(!i||l.length>1)){var c=i?void 0:r.getInitialValue(n);if(n.length&&r.getFieldValue(n)!==c&&r.fieldEntities.every(function(v){return!Bi(v.getNamePath(),n)})){var d=r.store;r.updateStore(Ct(d,n,c,!0)),r.notifyObservers(d,[n],{type:"remove"}),r.triggerDependenciesUpdate(d,n)}}r.notifyWatch([n])}}),I(this,"dispatch",function(a){switch(a.type){case"updateValue":{var n=a.namePath,o=a.value;r.updateValue(n,o);break}case"validateField":{var i=a.namePath,s=a.triggerName;r.validateFields([i],{triggerName:s});break}}}),I(this,"notifyObservers",function(a,n,o){if(r.subscribable){var i=A(A({},o),{},{store:r.getFieldsValue(!0)});r.getFieldEntities().forEach(function(s){var l=s.onStoreChange;l(a,n,i)})}else r.forceRootUpdate()}),I(this,"triggerDependenciesUpdate",function(a,n){var o=r.getDependencyChildrenFields(n);return o.length&&r.validateFields(o),r.notifyObservers(a,o,{type:"dependenciesUpdate",relatedFields:[n].concat(Q(o))}),o}),I(this,"updateValue",function(a,n){var o=Ee(a),i=r.store;r.updateStore(Ct(r.store,o,n)),r.notifyObservers(i,[o],{type:"valueUpdate",source:"internal"}),r.notifyWatch([o]);var s=r.triggerDependenciesUpdate(i,o),l=r.callbacks.onValuesChange;if(l){var c=So(r.store,[o]);l(c,r.getFieldsValue())}r.triggerOnFieldsChange([o].concat(Q(s)))}),I(this,"setFieldsValue",function(a){r.warningUnhooked();var n=r.store;if(a){var o=cr(r.store,a);r.updateStore(o)}r.notifyObservers(n,null,{type:"valueUpdate",source:"external"}),r.notifyWatch()}),I(this,"setFieldValue",function(a,n){r.setFields([{name:a,value:n,errors:[],warnings:[]}])}),I(this,"getDependencyChildrenFields",function(a){var n=new Set,o=[],i=new qt;r.getFieldEntities().forEach(function(l){var c=l.props.dependencies;(c||[]).forEach(function(d){var v=Ee(d);i.update(v,function(){var g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:new Set;return g.add(l),g})})});var s=function l(c){var d=i.get(c)||new Set;d.forEach(function(v){if(!n.has(v)){n.add(v);var g=v.getNamePath();v.isFieldDirty()&&g.length&&(o.push(g),l(g))}})};return s(a),o}),I(this,"triggerOnFieldsChange",function(a,n){var o=r.callbacks.onFieldsChange;if(o){var i=r.getFields();if(n){var s=new qt;n.forEach(function(c){var d=c.name,v=c.errors;s.set(d,v)}),i.forEach(function(c){c.errors=s.get(c.name)||c.errors})}var l=i.filter(function(c){var d=c.name;return Ut(a,d)});l.length&&o(l,i)}}),I(this,"validateFields",function(a,n){r.warningUnhooked();var o,i;Array.isArray(a)||typeof a=="string"||typeof n=="string"?(o=a,i=n):i=a;var s=!!o,l=s?o.map(Ee):[],c=[],d=String(Date.now()),v=new Set,g=i||{},y=g.recursive,h=g.dirty;r.getFieldEntities(!0).forEach(function(p){if(s||l.push(p.getNamePath()),!(!p.props.rules||!p.props.rules.length)&&!(h&&!p.isFieldDirty())){var S=p.getNamePath();if(v.add(S.join(d)),!s||Ut(l,S,y)){var R=p.validateRules(A({validateMessages:A(A({},ji),r.validateMessages)},i));c.push(R.then(function(){return{name:S,errors:[],warnings:[]}}).catch(function(E){var w,O=[],F=[];return(w=E.forEach)===null||w===void 0||w.call(E,function(b){var P=b.rule.warningOnly,M=b.errors;P?F.push.apply(F,Q(M)):O.push.apply(O,Q(M))}),O.length?Promise.reject({name:S,errors:O,warnings:F}):{name:S,errors:O,warnings:F}}))}}});var m=zd(c);r.lastValidatePromise=m,m.catch(function(p){return p}).then(function(p){var S=p.map(function(R){var E=R.name;return E});r.notifyObservers(r.store,S,{type:"validateFinish"}),r.triggerOnFieldsChange(S,p)});var f=m.then(function(){return r.lastValidatePromise===m?Promise.resolve(r.getFieldsValue(l)):Promise.reject([])}).catch(function(p){var S=p.filter(function(R){return R&&R.errors.length});return Promise.reject({values:r.getFieldsValue(l),errorFields:S,outOfDate:r.lastValidatePromise!==m})});f.catch(function(p){return p});var C=l.filter(function(p){return v.has(p.join(d))});return r.triggerOnFieldsChange(C),f}),I(this,"submit",function(){r.warningUnhooked(),r.validateFields().then(function(a){var n=r.callbacks.onFinish;if(n)try{n(a)}catch(o){console.error(o)}}).catch(function(a){var n=r.callbacks.onFinishFailed;n&&n(a)})}),this.forceRootUpdate=t});function Hi(e){var t=u.useRef(),r=u.useState({}),a=Z(r,2),n=a[1];if(!t.current)if(e)t.current=e;else{var o=function(){n({})},i=new Wd(o);t.current=i.getForm()}return[t.current]}var ba=u.createContext({triggerFormChange:function(){},triggerFormFinish:function(){},registerForm:function(){},unregisterForm:function(){}}),Vi=function(t){var r=t.validateMessages,a=t.onFormChange,n=t.onFormFinish,o=t.children,i=u.useContext(ba),s=u.useRef({});return u.createElement(ba.Provider,{value:A(A({},i),{},{validateMessages:A(A({},i.validateMessages),r),triggerFormChange:function(c,d){a&&a(c,{changedFields:d,forms:s.current}),i.triggerFormChange(c,d)},triggerFormFinish:function(c,d){n&&n(c,{values:d,forms:s.current}),i.triggerFormFinish(c,d)},registerForm:function(c,d){c&&(s.current=A(A({},s.current),{},I({},c,d))),i.registerForm(c,d)},unregisterForm:function(c){var d=A({},s.current);delete d[c],s.current=d,i.unregisterForm(c)}})},o)},qd=["name","initialValues","fields","form","preserve","children","component","validateMessages","validateTrigger","onValuesChange","onFieldsChange","onFinish","onFinishFailed","clearOnDestroy"],Gd=function(t,r){var a=t.name,n=t.initialValues,o=t.fields,i=t.form,s=t.preserve,l=t.children,c=t.component,d=c===void 0?"form":c,v=t.validateMessages,g=t.validateTrigger,y=g===void 0?"onChange":g,h=t.onValuesChange,m=t.onFieldsChange,f=t.onFinish,C=t.onFinishFailed,p=t.clearOnDestroy,S=ze(t,qd),R=u.useRef(null),E=u.useContext(ba),w=Hi(i),O=Z(w,1),F=O[0],b=F.getInternalHooks(Nt),P=b.useSubscribe,M=b.setInitialValues,H=b.setCallbacks,T=b.setValidateMessages,L=b.setPreserve,W=b.destroyForm;u.useImperativeHandle(r,function(){return A(A({},F),{},{nativeElement:R.current})}),u.useEffect(function(){return E.registerForm(a,F),function(){E.unregisterForm(a)}},[E,F,a]),T(A(A({},E.validateMessages),v)),H({onValuesChange:h,onFieldsChange:function(j){if(E.triggerFormChange(a,j),m){for(var D=arguments.length,k=new Array(D>1?D-1:0),re=1;re<D;re++)k[re-1]=arguments[re];m.apply(void 0,[j].concat(k))}},onFinish:function(j){E.triggerFormFinish(a,j),f&&f(j)},onFinishFailed:C}),L(s);var x=u.useRef(null);M(n,!x.current),x.current||(x.current=!0),u.useEffect(function(){return function(){return W(p)}},[]);var $,N=typeof l=="function";if(N){var V=F.getFieldsValue(!0);$=l(V,F)}else $=l;P(!N);var _=u.useRef();u.useEffect(function(){Ld(_.current||[],o||[])||F.setFields(o||[]),_.current=o},[o,F]);var z=u.useMemo(function(){return A(A({},F),{},{validateTrigger:y})},[F,y]),G=u.createElement(Zr.Provider,{value:null},u.createElement(kt.Provider,{value:z},$));return d===!1?G:u.createElement(d,me({},S,{ref:R,onSubmit:function(j){j.preventDefault(),j.stopPropagation(),F.submit()},onReset:function(j){var D;j.preventDefault(),F.resetFields(),(D=S.onReset)===null||D===void 0||D.call(S,j)}}),G)};function xo(e){try{return JSON.stringify(e)}catch{return Math.random()}}function Ud(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=t[0],n=t[1],o=n===void 0?{}:n,i=td(o)?{form:o}:o,s=i.form,l=u.useState(),c=Z(l,2),d=c[0],v=c[1],g=u.useMemo(function(){return xo(d)},[d]),y=u.useRef(g);y.current=g;var h=u.useContext(kt),m=s||h,f=m&&m._init,C=Ee(a),p=u.useRef(C);return p.current=C,u.useEffect(function(){if(f){var S=m.getFieldsValue,R=m.getInternalHooks,E=R(Nt),w=E.registerWatch,O=function(M,H){var T=i.preserve?H:M;return typeof a=="function"?a(T):It(T,p.current)},F=w(function(P,M){var H=O(P,M),T=xo(H);y.current!==T&&(y.current=T,v(H))}),b=O(S(),S(!0));return d!==b&&v(b),F}},[f]),d}var Kd=u.forwardRef(Gd),Cr=Kd;Cr.FormProvider=Vi;Cr.Field=Li;Cr.List=Vd;Cr.useForm=Hi;Cr.useWatch=Ud;const mv=u.createContext({labelAlign:"right",vertical:!1,itemRef:()=>{}}),hv=u.createContext(null),pv=e=>{const t=vr(e,["prefixCls"]);return u.createElement(Vi,Object.assign({},t))},yv=u.createContext({prefixCls:""}),Ca=u.createContext({}),kd=e=>{let{children:t,status:r,override:a}=e;const n=u.useContext(Ca),o=u.useMemo(()=>{const i=Object.assign({},n);return a&&delete i.isFormItemInput,r&&(delete i.status,delete i.hasFeedback,delete i.feedbackIcon),i},[r,a,n]);return u.createElement(Ca.Provider,{value:o},t)},Jd=u.createContext(void 0),$o=e=>{const{space:t,form:r,children:a}=e;if(a==null)return null;let n=a;return r&&(n=B.createElement(kd,{override:!0,status:!0},n)),t&&(n=B.createElement(Ac,null,n)),n},Zd=B.createContext({}),Qd=function(){if(typeof navigator>"u"||typeof window>"u")return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(e==null?void 0:e.substr(0,4))};function Xd(e){var t=e.prefixCls,r=e.align,a=e.arrow,n=e.arrowPos,o=a||{},i=o.className,s=o.content,l=n.x,c=l===void 0?0:l,d=n.y,v=d===void 0?0:d,g=u.useRef();if(!r||!r.points)return null;var y={position:"absolute"};if(r.autoArrow!==!1){var h=r.points[0],m=r.points[1],f=h[0],C=h[1],p=m[0],S=m[1];f===p||!["t","b"].includes(f)?y.top=v:f==="t"?y.top=0:y.bottom=0,C===S||!["l","r"].includes(C)?y.left=c:C==="l"?y.left=0:y.right=0}return u.createElement("div",{ref:g,className:K("".concat(t,"-arrow"),i),style:y},s)}function Yd(e){var t=e.prefixCls,r=e.open,a=e.zIndex,n=e.mask,o=e.motion;return n?u.createElement(pr,me({},o,{motionAppear:!0,visible:r,removeOnLeave:!0}),function(i){var s=i.className;return u.createElement("div",{style:{zIndex:a},className:K("".concat(t,"-mask"),s)})}):null}var ef=u.memo(function(e){var t=e.children;return t},function(e,t){return t.cache}),tf=u.forwardRef(function(e,t){var r=e.popup,a=e.className,n=e.prefixCls,o=e.style,i=e.target,s=e.onVisibleChanged,l=e.open,c=e.keepDom,d=e.fresh,v=e.onClick,g=e.mask,y=e.arrow,h=e.arrowPos,m=e.align,f=e.motion,C=e.maskMotion,p=e.forceRender,S=e.getPopupContainer,R=e.autoDestroy,E=e.portal,w=e.zIndex,O=e.onMouseEnter,F=e.onMouseLeave,b=e.onPointerEnter,P=e.onPointerDownCapture,M=e.ready,H=e.offsetX,T=e.offsetY,L=e.offsetR,W=e.offsetB,x=e.onAlign,$=e.onPrepare,N=e.stretch,V=e.targetWidth,_=e.targetHeight,z=typeof r=="function"?r():r,G=l||c,U=(S==null?void 0:S.length)>0,j=u.useState(!S||!U),D=Z(j,2),k=D[0],re=D[1];if(qe(function(){!k&&U&&i&&re(!0)},[k,U,i]),!k)return null;var ne="auto",ee={left:"-1000vw",top:"-1000vh",right:ne,bottom:ne};if(M||!l){var le,be=m.points,se=m.dynamicInset||((le=m._experimental)===null||le===void 0?void 0:le.dynamicInset),ge=se&&be[0][1]==="r",fe=se&&be[0][0]==="b";ge?(ee.right=L,ee.left=ne):(ee.left=H,ee.right=ne),fe?(ee.bottom=W,ee.top=ne):(ee.top=T,ee.bottom=ne)}var ce={};return N&&(N.includes("height")&&_?ce.height=_:N.includes("minHeight")&&_&&(ce.minHeight=_),N.includes("width")&&V?ce.width=V:N.includes("minWidth")&&V&&(ce.minWidth=V)),l||(ce.pointerEvents="none"),u.createElement(E,{open:p||G,getContainer:S&&function(){return S(i)},autoDestroy:R},u.createElement(Yd,{prefixCls:n,open:l,zIndex:w,mask:g,motion:C}),u.createElement(Ea,{onResize:x,disabled:!l},function(Ce){return u.createElement(pr,me({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:p,leavedClassName:"".concat(n,"-hidden")},f,{onAppearPrepare:$,onEnterPrepare:$,visible:l,onVisibleChanged:function(ue){var we;f==null||(we=f.onVisibleChanged)===null||we===void 0||we.call(f,ue),s(ue)}}),function(Fe,ue){var we=Fe.className,Ae=Fe.style,xe=K(n,we,a);return u.createElement("div",{ref:en(Ce,t,ue),className:xe,style:A(A(A(A({"--arrow-x":"".concat(h.x||0,"px"),"--arrow-y":"".concat(h.y||0,"px")},ee),ce),Ae),{},{boxSizing:"border-box",zIndex:w},o),onMouseEnter:O,onMouseLeave:F,onPointerEnter:b,onClick:v,onPointerDownCapture:P},y&&u.createElement(Xd,{prefixCls:n,arrow:y,arrowPos:h,align:m}),u.createElement(ef,{cache:!l&&!d},z))})}))}),rf=u.forwardRef(function(e,t){var r=e.children,a=e.getTriggerDOMNode,n=Qr(r),o=u.useCallback(function(s){gs(t,a?a(s):s)},[a]),i=Xr(o,wa(r));return n?u.cloneElement(r,{ref:i}):r}),Eo=u.createContext(null);function Fo(e){return e?Array.isArray(e)?e:[e]:[]}function nf(e,t,r,a){return u.useMemo(function(){var n=Fo(r??t),o=Fo(a??t),i=new Set(n),s=new Set(o);return e&&(i.has("hover")&&(i.delete("hover"),i.add("click")),s.has("hover")&&(s.delete("hover"),s.add("click"))),[i,s]},[e,t,r,a])}function af(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return r?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function of(e,t,r,a){for(var n=r.points,o=Object.keys(e),i=0;i<o.length;i+=1){var s,l=o[i];if(af((s=e[l])===null||s===void 0?void 0:s.points,n,a))return"".concat(t,"-placement-").concat(l)}return""}function Po(e,t,r,a){return t||(r?{motionName:"".concat(e,"-").concat(r)}:a?{motionName:a}:null)}function Sr(e){return e.ownerDocument.defaultView}function Sa(e){for(var t=[],r=e==null?void 0:e.parentElement,a=["hidden","scroll","clip","auto"];r;){var n=Sr(r).getComputedStyle(r),o=n.overflowX,i=n.overflowY,s=n.overflow;[o,i,s].some(function(l){return a.includes(l)})&&t.push(r),r=r.parentElement}return t}function gr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;return Number.isNaN(e)?t:e}function lr(e){return gr(parseFloat(e),0)}function Ro(e,t){var r=A({},e);return(t||[]).forEach(function(a){if(!(a instanceof HTMLBodyElement||a instanceof HTMLHtmlElement)){var n=Sr(a).getComputedStyle(a),o=n.overflow,i=n.overflowClipMargin,s=n.borderTopWidth,l=n.borderBottomWidth,c=n.borderLeftWidth,d=n.borderRightWidth,v=a.getBoundingClientRect(),g=a.offsetHeight,y=a.clientHeight,h=a.offsetWidth,m=a.clientWidth,f=lr(s),C=lr(l),p=lr(c),S=lr(d),R=gr(Math.round(v.width/h*1e3)/1e3),E=gr(Math.round(v.height/g*1e3)/1e3),w=(h-m-p-S)*R,O=(g-y-f-C)*E,F=f*E,b=C*E,P=p*R,M=S*R,H=0,T=0;if(o==="clip"){var L=lr(i);H=L*R,T=L*E}var W=v.x+P-H,x=v.y+F-T,$=W+v.width+2*H-P-M-w,N=x+v.height+2*T-F-b-O;r.left=Math.max(r.left,W),r.top=Math.max(r.top,x),r.right=Math.min(r.right,$),r.bottom=Math.min(r.bottom,N)}}),r}function Oo(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r="".concat(t),a=r.match(/^(.*)\%$/);return a?e*(parseFloat(a[1])/100):parseFloat(r)}function Io(e,t){var r=t||[],a=Z(r,2),n=a[0],o=a[1];return[Oo(e.width,n),Oo(e.height,o)]}function No(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return[e[0],e[1]]}function Gt(e,t){var r=t[0],a=t[1],n,o;return r==="t"?o=e.y:r==="b"?o=e.y+e.height:o=e.y+e.height/2,a==="l"?n=e.x:a==="r"?n=e.x+e.width:n=e.x+e.width/2,{x:n,y:o}}function bt(e,t){var r={t:"b",b:"t",l:"r",r:"l"};return e.map(function(a,n){return n===t?r[a]||"c":a}).join("")}function sf(e,t,r,a,n,o,i){var s=u.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:n[a]||{}}),l=Z(s,2),c=l[0],d=l[1],v=u.useRef(0),g=u.useMemo(function(){return t?Sa(t):[]},[t]),y=u.useRef({}),h=function(){y.current={}};e||h();var m=ct(function(){if(t&&r&&e){let ke=function(Dt,pt){var yt=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Y,sr=j.x+Dt,Lr=j.y+pt,On=sr+ge,In=Lr+se,Nn=Math.max(sr,yt.left),q=Math.max(Lr,yt.top),X=Math.min(On,yt.right),Se=Math.min(In,yt.bottom);return Math.max(0,(X-Nn)*(Se-q))},Br=function(){Et=j.y+ye,Ft=Et+se,ft=j.x+he,_t=ft+ge};var p,S,R,E,w=t,O=w.ownerDocument,F=Sr(w),b=F.getComputedStyle(w),P=b.width,M=b.height,H=b.position,T=w.style.left,L=w.style.top,W=w.style.right,x=w.style.bottom,$=w.style.overflow,N=A(A({},n[a]),o),V=O.createElement("div");(p=w.parentElement)===null||p===void 0||p.appendChild(V),V.style.left="".concat(w.offsetLeft,"px"),V.style.top="".concat(w.offsetTop,"px"),V.style.position=H,V.style.height="".concat(w.offsetHeight,"px"),V.style.width="".concat(w.offsetWidth,"px"),w.style.left="0",w.style.top="0",w.style.right="auto",w.style.bottom="auto",w.style.overflow="hidden";var _;if(Array.isArray(r))_={x:r[0],y:r[1],width:0,height:0};else{var z,G,U=r.getBoundingClientRect();U.x=(z=U.x)!==null&&z!==void 0?z:U.left,U.y=(G=U.y)!==null&&G!==void 0?G:U.top,_={x:U.x,y:U.y,width:U.width,height:U.height}}var j=w.getBoundingClientRect();j.x=(S=j.x)!==null&&S!==void 0?S:j.left,j.y=(R=j.y)!==null&&R!==void 0?R:j.top;var D=O.documentElement,k=D.clientWidth,re=D.clientHeight,ne=D.scrollWidth,ee=D.scrollHeight,le=D.scrollTop,be=D.scrollLeft,se=j.height,ge=j.width,fe=_.height,ce=_.width,Ce={left:0,top:0,right:k,bottom:re},Fe={left:-be,top:-le,right:ne-be,bottom:ee-le},ue=N.htmlRegion,we="visible",Ae="visibleFirst";ue!=="scroll"&&ue!==Ae&&(ue=we);var xe=ue===Ae,nt=Ro(Fe,g),J=Ro(Ce,g),Y=ue===we?J:nt,ae=xe?J:Y;w.style.left="auto",w.style.top="auto",w.style.right="0",w.style.bottom="0";var pe=w.getBoundingClientRect();w.style.left=T,w.style.top=L,w.style.right=W,w.style.bottom=x,w.style.overflow=$,(E=w.parentElement)===null||E===void 0||E.removeChild(V);var Oe=gr(Math.round(ge/parseFloat(P)*1e3)/1e3),Pe=gr(Math.round(se/parseFloat(M)*1e3)/1e3);if(Oe===0||Pe===0||Yn(r)&&!yi(r))return;var at=N.offset,Bt=N.targetOffset,xt=Io(j,at),He=Z(xt,2),Ve=He[0],je=He[1],Re=Io(_,Bt),Me=Z(Re,2),Be=Me[0],gt=Me[1];_.x-=Be,_.y-=gt;var vn=N.points||[],wr=Z(vn,2),gn=wr[0],mn=wr[1],ot=No(mn),De=No(gn),er=Gt(_,ot),Le=Gt(j,De),mt=A({},N),he=er.x-Le.x+Ve,ye=er.y-Le.y+je,Ye=ke(he,ye),$t=ke(he,ye,J),tr=Gt(_,["t","l"]),Ke=Gt(j,["t","l"]),xr=Gt(_,["b","r"]),rr=Gt(j,["b","r"]),ht=N.overflow||{},$r=ht.adjustX,hn=ht.adjustY,nr=ht.shiftX,Lt=ht.shiftY,ar=function(pt){return typeof pt=="boolean"?pt:pt>=0},Et,Ft,ft,_t;Br();var Pt=ar(hn),Er=De[0]===ot[0];if(Pt&&De[0]==="t"&&(Ft>ae.bottom||y.current.bt)){var Te=ye;Er?Te-=se-fe:Te=tr.y-rr.y-je;var Fr=ke(he,Te),pn=ke(he,Te,J);Fr>Ye||Fr===Ye&&(!xe||pn>=$t)?(y.current.bt=!0,ye=Te,je=-je,mt.points=[bt(De,0),bt(ot,0)]):y.current.bt=!1}if(Pt&&De[0]==="b"&&(Et<ae.top||y.current.tb)){var Rt=ye;Er?Rt+=se-fe:Rt=xr.y-Ke.y-je;var Pr=ke(he,Rt),yn=ke(he,Rt,J);Pr>Ye||Pr===Ye&&(!xe||yn>=$t)?(y.current.tb=!0,ye=Rt,je=-je,mt.points=[bt(De,0),bt(ot,0)]):y.current.tb=!1}var Rr=ar($r),Or=De[1]===ot[1];if(Rr&&De[1]==="l"&&(_t>ae.right||y.current.rl)){var Ot=he;Or?Ot-=ge-ce:Ot=tr.x-rr.x-Ve;var Ir=ke(Ot,ye),Ht=ke(Ot,ye,J);Ir>Ye||Ir===Ye&&(!xe||Ht>=$t)?(y.current.rl=!0,he=Ot,Ve=-Ve,mt.points=[bt(De,1),bt(ot,1)]):y.current.rl=!1}if(Rr&&De[1]==="r"&&(ft<ae.left||y.current.lr)){var vt=he;Or?vt+=ge-ce:vt=xr.x-Ke.x-Ve;var Nr=ke(vt,ye),Mr=ke(vt,ye,J);Nr>Ye||Nr===Ye&&(!xe||Mr>=$t)?(y.current.lr=!0,he=vt,Ve=-Ve,mt.points=[bt(De,1),bt(ot,1)]):y.current.lr=!1}Br();var it=nr===!0?0:nr;typeof it=="number"&&(ft<J.left&&(he-=ft-J.left-Ve,_.x+ce<J.left+it&&(he+=_.x-J.left+ce-it)),_t>J.right&&(he-=_t-J.right-Ve,_.x>J.right-it&&(he+=_.x-J.right+it)));var et=Lt===!0?0:Lt;typeof et=="number"&&(Et<J.top&&(ye-=Et-J.top-je,_.y+fe<J.top+et&&(ye+=_.y-J.top+fe-et)),Ft>J.bottom&&(ye-=Ft-J.bottom-je,_.y>J.bottom-et&&(ye+=_.y-J.bottom+et)));var Vt=j.x+he,zt=Vt+ge,st=j.y+ye,bn=st+se,Tr=_.x,Cn=Tr+ce,or=_.y,Sn=or+fe,wn=Math.max(Vt,Tr),xn=Math.min(zt,Cn),Ar=(wn+xn)/2,$n=Ar-Vt,En=Math.max(st,or),jr=Math.min(bn,Sn),Fn=(En+jr)/2,Pn=Fn-st;i==null||i(t,mt);var ir=pe.right-j.x-(he+j.width),lt=pe.bottom-j.y-(ye+j.height);Oe===1&&(he=Math.round(he),ir=Math.round(ir)),Pe===1&&(ye=Math.round(ye),lt=Math.round(lt));var Rn={ready:!0,offsetX:he/Oe,offsetY:ye/Pe,offsetR:ir/Oe,offsetB:lt/Pe,arrowX:$n/Oe,arrowY:Pn/Pe,scaleX:Oe,scaleY:Pe,align:mt};d(Rn)}}),f=function(){v.current+=1;var S=v.current;Promise.resolve().then(function(){v.current===S&&m()})},C=function(){d(function(S){return A(A({},S),{},{ready:!1})})};return qe(C,[a]),qe(function(){e||C()},[e]),[c.ready,c.offsetX,c.offsetY,c.offsetR,c.offsetB,c.arrowX,c.arrowY,c.scaleX,c.scaleY,c.align,f]}function lf(e,t,r,a,n){qe(function(){if(e&&t&&r){let v=function(){a(),n()};var o=t,i=r,s=Sa(o),l=Sa(i),c=Sr(i),d=new Set([c].concat(Q(s),Q(l)));return d.forEach(function(g){g.addEventListener("scroll",v,{passive:!0})}),c.addEventListener("resize",v,{passive:!0}),a(),function(){d.forEach(function(g){g.removeEventListener("scroll",v),c.removeEventListener("resize",v)})}}},[e,t,r])}function cf(e,t,r,a,n,o,i,s){var l=u.useRef(e);l.current=e;var c=u.useRef(!1);u.useEffect(function(){if(t&&a&&(!n||o)){var v=function(){c.current=!1},g=function(f){var C;l.current&&!i(((C=f.composedPath)===null||C===void 0||(C=C.call(f))===null||C===void 0?void 0:C[0])||f.target)&&!c.current&&s(!1)},y=Sr(a);y.addEventListener("pointerdown",v,!0),y.addEventListener("mousedown",g,!0),y.addEventListener("contextmenu",g,!0);var h=Ur(r);return h&&(h.addEventListener("mousedown",g,!0),h.addEventListener("contextmenu",g,!0)),function(){y.removeEventListener("pointerdown",v,!0),y.removeEventListener("mousedown",g,!0),y.removeEventListener("contextmenu",g,!0),h&&(h.removeEventListener("mousedown",g,!0),h.removeEventListener("contextmenu",g,!0))}}},[t,r,a,n,o]);function d(){c.current=!0}return d}var uf=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];function df(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ti,t=u.forwardRef(function(r,a){var n=r.prefixCls,o=n===void 0?"rc-trigger-popup":n,i=r.children,s=r.action,l=s===void 0?"hover":s,c=r.showAction,d=r.hideAction,v=r.popupVisible,g=r.defaultPopupVisible,y=r.onPopupVisibleChange,h=r.afterPopupVisibleChange,m=r.mouseEnterDelay,f=r.mouseLeaveDelay,C=f===void 0?.1:f,p=r.focusDelay,S=r.blurDelay,R=r.mask,E=r.maskClosable,w=E===void 0?!0:E,O=r.getPopupContainer,F=r.forceRender,b=r.autoDestroy,P=r.destroyPopupOnHide,M=r.popup,H=r.popupClassName,T=r.popupStyle,L=r.popupPlacement,W=r.builtinPlacements,x=W===void 0?{}:W,$=r.popupAlign,N=r.zIndex,V=r.stretch,_=r.getPopupClassNameFromAlign,z=r.fresh,G=r.alignPoint,U=r.onPopupClick,j=r.onPopupAlign,D=r.arrow,k=r.popupMotion,re=r.maskMotion,ne=r.popupTransitionName,ee=r.popupAnimation,le=r.maskTransitionName,be=r.maskAnimation,se=r.className,ge=r.getTriggerDOMNode,fe=ze(r,uf),ce=b||P||!1,Ce=u.useState(!1),Fe=Z(Ce,2),ue=Fe[0],we=Fe[1];qe(function(){we(Qd())},[]);var Ae=u.useRef({}),xe=u.useContext(Eo),nt=u.useMemo(function(){return{registerSubPopup:function(X,Se){Ae.current[X]=Se,xe==null||xe.registerSubPopup(X,Se)}}},[xe]),J=ed(),Y=u.useState(null),ae=Z(Y,2),pe=ae[0],Oe=ae[1],Pe=u.useRef(null),at=ct(function(q){Pe.current=q,Yn(q)&&pe!==q&&Oe(q),xe==null||xe.registerSubPopup(J,q)}),Bt=u.useState(null),xt=Z(Bt,2),He=xt[0],Ve=xt[1],je=u.useRef(null),Re=ct(function(q){Yn(q)&&He!==q&&(Ve(q),je.current=q)}),Me=u.Children.only(i),Be=(Me==null?void 0:Me.props)||{},gt={},vn=ct(function(q){var X,Se,Ie=He;return(Ie==null?void 0:Ie.contains(q))||((X=Ur(Ie))===null||X===void 0?void 0:X.host)===q||q===Ie||(pe==null?void 0:pe.contains(q))||((Se=Ur(pe))===null||Se===void 0?void 0:Se.host)===q||q===pe||Object.values(Ae.current).some(function($e){return($e==null?void 0:$e.contains(q))||q===$e})}),wr=Po(o,k,ee,ne),gn=Po(o,re,be,le),mn=u.useState(g||!1),ot=Z(mn,2),De=ot[0],er=ot[1],Le=v??De,mt=ct(function(q){v===void 0&&er(q)});qe(function(){er(v||!1)},[v]);var he=u.useRef(Le);he.current=Le;var ye=u.useRef([]);ye.current=[];var Ye=ct(function(q){var X;mt(q),((X=ye.current[ye.current.length-1])!==null&&X!==void 0?X:Le)!==q&&(ye.current.push(q),y==null||y(q))}),$t=u.useRef(),tr=function(){clearTimeout($t.current)},Ke=function(X){var Se=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;tr(),Se===0?Ye(X):$t.current=setTimeout(function(){Ye(X)},Se*1e3)};u.useEffect(function(){return tr},[]);var xr=u.useState(!1),rr=Z(xr,2),ht=rr[0],$r=rr[1];qe(function(q){(!q||Le)&&$r(!0)},[Le]);var hn=u.useState(null),nr=Z(hn,2),Lt=nr[0],ar=nr[1],Et=u.useState(null),Ft=Z(Et,2),ft=Ft[0],_t=Ft[1],Pt=function(X){_t([X.clientX,X.clientY])},Er=sf(Le,pe,G&&ft!==null?ft:He,L,x,$,j),Te=Z(Er,11),Fr=Te[0],pn=Te[1],Rt=Te[2],Pr=Te[3],yn=Te[4],Rr=Te[5],Or=Te[6],Ot=Te[7],Ir=Te[8],Ht=Te[9],vt=Te[10],Nr=nf(ue,l,c,d),Mr=Z(Nr,2),it=Mr[0],et=Mr[1],Vt=it.has("click"),zt=et.has("click")||et.has("contextMenu"),st=ct(function(){ht||vt()}),bn=function(){he.current&&G&&zt&&Ke(!1)};lf(Le,He,pe,st,bn),qe(function(){st()},[ft,L]),qe(function(){Le&&!(x!=null&&x[L])&&st()},[JSON.stringify($)]);var Tr=u.useMemo(function(){var q=of(x,o,Ht,G);return K(q,_==null?void 0:_(Ht))},[Ht,_,x,o,G]);u.useImperativeHandle(a,function(){return{nativeElement:je.current,popupElement:Pe.current,forceAlign:st}});var Cn=u.useState(0),or=Z(Cn,2),Sn=or[0],wn=or[1],xn=u.useState(0),Ar=Z(xn,2),$n=Ar[0],En=Ar[1],jr=function(){if(V&&He){var X=He.getBoundingClientRect();wn(X.width),En(X.height)}},Fn=function(){jr(),st()},Pn=function(X){$r(!1),vt(),h==null||h(X)},ir=function(){return new Promise(function(X){jr(),ar(function(){return X})})};qe(function(){Lt&&(vt(),Lt(),ar(null))},[Lt]);function lt(q,X,Se,Ie){gt[q]=function($e){var _r;Ie==null||Ie($e),Ke(X,Se);for(var Mn=arguments.length,Aa=new Array(Mn>1?Mn-1:0),Hr=1;Hr<Mn;Hr++)Aa[Hr-1]=arguments[Hr];(_r=Be[q])===null||_r===void 0||_r.call.apply(_r,[Be,$e].concat(Aa))}}(Vt||zt)&&(gt.onClick=function(q){var X;he.current&&zt?Ke(!1):!he.current&&Vt&&(Pt(q),Ke(!0));for(var Se=arguments.length,Ie=new Array(Se>1?Se-1:0),$e=1;$e<Se;$e++)Ie[$e-1]=arguments[$e];(X=Be.onClick)===null||X===void 0||X.call.apply(X,[Be,q].concat(Ie))});var Rn=cf(Le,zt,He,pe,R,w,vn,Ke),ke=it.has("hover"),Br=et.has("hover"),Dt,pt;ke&&(lt("onMouseEnter",!0,m,function(q){Pt(q)}),lt("onPointerEnter",!0,m,function(q){Pt(q)}),Dt=function(X){(Le||ht)&&pe!==null&&pe!==void 0&&pe.contains(X.target)&&Ke(!0,m)},G&&(gt.onMouseMove=function(q){var X;(X=Be.onMouseMove)===null||X===void 0||X.call(Be,q)})),Br&&(lt("onMouseLeave",!1,C),lt("onPointerLeave",!1,C),pt=function(){Ke(!1,C)}),it.has("focus")&&lt("onFocus",!0,p),et.has("focus")&&lt("onBlur",!1,S),it.has("contextMenu")&&(gt.onContextMenu=function(q){var X;he.current&&et.has("contextMenu")?Ke(!1):(Pt(q),Ke(!0)),q.preventDefault();for(var Se=arguments.length,Ie=new Array(Se>1?Se-1:0),$e=1;$e<Se;$e++)Ie[$e-1]=arguments[$e];(X=Be.onContextMenu)===null||X===void 0||X.call.apply(X,[Be,q].concat(Ie))}),se&&(gt.className=K(Be.className,se));var yt=A(A({},Be),gt),sr={},Lr=["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"];Lr.forEach(function(q){fe[q]&&(sr[q]=function(){for(var X,Se=arguments.length,Ie=new Array(Se),$e=0;$e<Se;$e++)Ie[$e]=arguments[$e];(X=yt[q])===null||X===void 0||X.call.apply(X,[yt].concat(Ie)),fe[q].apply(fe,Ie)})});var On=u.cloneElement(Me,A(A({},yt),sr)),In={x:Rr,y:Or},Nn=D?A({},D!==!0?D:{}):null;return u.createElement(u.Fragment,null,u.createElement(Ea,{disabled:!Le,ref:Re,onResize:Fn},u.createElement(rf,{getTriggerDOMNode:ge},On)),u.createElement(Eo.Provider,{value:nt},u.createElement(tf,{portal:e,ref:at,prefixCls:o,popup:M,className:K(H,Tr),style:T,target:He,onMouseEnter:Dt,onMouseLeave:pt,onPointerEnter:Dt,zIndex:N,open:Le,keepDom:ht,fresh:z,onClick:U,onPointerDownCapture:Rn,mask:R,motion:wr,maskMotion:gn,onVisibleChanged:Pn,onPrepare:ir,forceRender:F,autoDestroy:ce,getPopupContainer:O,align:Ht,arrow:Nn,arrowPos:In,ready:Fr,offsetX:pn,offsetY:Rt,offsetR:Pr,offsetB:yn,onAlign:st,stretch:V,targetWidth:Sn/Ot,targetHeight:$n/Ir})))});return t}const bv=df(Ti);function Mo(e,t,r){return K({[`${e}-status-success`]:t==="success",[`${e}-status-warning`]:t==="warning",[`${e}-status-error`]:t==="error",[`${e}-status-validating`]:t==="validating",[`${e}-has-feedback`]:r})}const ff=(e,t)=>t||e,vf=function(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;var a,n;const{variant:o,[e]:i}=u.useContext(tt),s=u.useContext(Jd),l=i==null?void 0:i.variant;let c;typeof t<"u"?c=t:r===!1?c="borderless":c=(n=(a=s??l)!==null&&a!==void 0?a:o)!==null&&n!==void 0?n:"outlined";const d=ms.includes(c);return[c,d]};var gf={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"},mf=function(t,r){return u.createElement(rt,me({},t,{ref:r,icon:gf}))},Cv=u.forwardRef(mf);function zi(e){return Ue(e,{inputAffixPadding:e.paddingXXS})}const Di=e=>{const{controlHeight:t,fontSize:r,lineHeight:a,lineWidth:n,controlHeightSM:o,controlHeightLG:i,fontSizeLG:s,lineHeightLG:l,paddingSM:c,controlPaddingHorizontalSM:d,controlPaddingHorizontal:v,colorFillAlter:g,colorPrimaryHover:y,colorPrimary:h,controlOutlineWidth:m,controlOutline:f,colorErrorOutline:C,colorWarningOutline:p,colorBgContainer:S,inputFontSize:R,inputFontSizeLG:E,inputFontSizeSM:w}=e,O=R||r,F=w||O,b=E||s,P=Math.round((t-O*a)/2*10)/10-n,M=Math.round((o-F*a)/2*10)/10-n,H=Math.ceil((i-b*l)/2*10)/10-n;return{paddingBlock:Math.max(P,0),paddingBlockSM:Math.max(M,0),paddingBlockLG:Math.max(H,0),paddingInline:c-n,paddingInlineSM:d-n,paddingInlineLG:v-n,addonBg:g,activeBorderColor:h,hoverBorderColor:y,activeShadow:`0 0 0 ${m}px ${f}`,errorActiveShadow:`0 0 0 ${m}px ${C}`,warningActiveShadow:`0 0 0 ${m}px ${p}`,hoverBg:S,activeBg:S,inputFontSize:O,inputFontSizeLG:b,inputFontSizeSM:F}},hf=e=>({borderColor:e.hoverBorderColor,backgroundColor:e.hoverBg}),Ta=e=>({color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,boxShadow:"none",cursor:"not-allowed",opacity:1,"input[disabled], textarea[disabled]":{cursor:"not-allowed"},"&:hover:not([disabled])":Object.assign({},hf(Ue(e,{hoverBorderColor:e.colorBorder,hoverBg:e.colorBgContainerDisabled})))}),Wi=(e,t)=>({background:e.colorBgContainer,borderWidth:e.lineWidth,borderStyle:e.lineType,borderColor:t.borderColor,"&:hover":{borderColor:t.hoverBorderColor,backgroundColor:e.hoverBg},"&:focus, &:focus-within":{borderColor:t.activeBorderColor,boxShadow:t.activeShadow,outline:0,backgroundColor:e.activeBg}}),To=(e,t)=>({[`&${e.componentCls}-status-${t.status}:not(${e.componentCls}-disabled)`]:Object.assign(Object.assign({},Wi(e,t)),{[`${e.componentCls}-prefix, ${e.componentCls}-suffix`]:{color:t.affixColor}}),[`&${e.componentCls}-status-${t.status}${e.componentCls}-disabled`]:{borderColor:t.borderColor}}),pf=(e,t)=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Wi(e,{borderColor:e.colorBorder,hoverBorderColor:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeShadow:e.activeShadow})),{[`&${e.componentCls}-disabled, &[disabled]`]:Object.assign({},Ta(e))}),To(e,{status:"error",borderColor:e.colorError,hoverBorderColor:e.colorErrorBorderHover,activeBorderColor:e.colorError,activeShadow:e.errorActiveShadow,affixColor:e.colorError})),To(e,{status:"warning",borderColor:e.colorWarning,hoverBorderColor:e.colorWarningBorderHover,activeBorderColor:e.colorWarning,activeShadow:e.warningActiveShadow,affixColor:e.colorWarning})),t)}),Ao=(e,t)=>({[`&${e.componentCls}-group-wrapper-status-${t.status}`]:{[`${e.componentCls}-group-addon`]:{borderColor:t.addonBorderColor,color:t.addonColor}}}),yf=e=>({"&-outlined":Object.assign(Object.assign(Object.assign({[`${e.componentCls}-group`]:{"&-addon":{background:e.addonBg,border:`${te(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},"&-addon:first-child":{borderInlineEnd:0},"&-addon:last-child":{borderInlineStart:0}}},Ao(e,{status:"error",addonBorderColor:e.colorError,addonColor:e.colorErrorText})),Ao(e,{status:"warning",addonBorderColor:e.colorWarning,addonColor:e.colorWarningText})),{[`&${e.componentCls}-group-wrapper-disabled`]:{[`${e.componentCls}-group-addon`]:Object.assign({},Ta(e))}})}),bf=(e,t)=>{const{componentCls:r}=e;return{"&-borderless":Object.assign({background:"transparent",border:"none","&:focus, &:focus-within":{outline:"none"},[`&${r}-disabled, &[disabled]`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${r}-status-error`]:{"&, & input, & textarea":{color:e.colorError}},[`&${r}-status-warning`]:{"&, & input, & textarea":{color:e.colorWarning}}},t)}},qi=(e,t)=>({background:t.bg,borderWidth:e.lineWidth,borderStyle:e.lineType,borderColor:"transparent","input&, & input, textarea&, & textarea":{color:t==null?void 0:t.inputColor},"&:hover":{background:t.hoverBg},"&:focus, &:focus-within":{outline:0,borderColor:t.activeBorderColor,backgroundColor:e.activeBg}}),jo=(e,t)=>({[`&${e.componentCls}-status-${t.status}:not(${e.componentCls}-disabled)`]:Object.assign(Object.assign({},qi(e,t)),{[`${e.componentCls}-prefix, ${e.componentCls}-suffix`]:{color:t.affixColor}})}),Cf=(e,t)=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},qi(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor})),{[`&${e.componentCls}-disabled, &[disabled]`]:Object.assign({},Ta(e))}),jo(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,inputColor:e.colorErrorText,affixColor:e.colorError})),jo(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,inputColor:e.colorWarningText,affixColor:e.colorWarning})),t)}),Bo=(e,t)=>({[`&${e.componentCls}-group-wrapper-status-${t.status}`]:{[`${e.componentCls}-group-addon`]:{background:t.addonBg,color:t.addonColor}}}),Sf=e=>({"&-filled":Object.assign(Object.assign(Object.assign({[`${e.componentCls}-group`]:{"&-addon":{background:e.colorFillTertiary},[`${e.componentCls}-filled:not(:focus):not(:focus-within)`]:{"&:not(:first-child)":{borderInlineStart:`${te(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},"&:not(:last-child)":{borderInlineEnd:`${te(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}}}},Bo(e,{status:"error",addonBg:e.colorErrorBg,addonColor:e.colorErrorText})),Bo(e,{status:"warning",addonBg:e.colorWarningBg,addonColor:e.colorWarningText})),{[`&${e.componentCls}-group-wrapper-disabled`]:{[`${e.componentCls}-group`]:{"&-addon":{background:e.colorFillTertiary,color:e.colorTextDisabled},"&-addon:first-child":{borderInlineStart:`${te(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderTop:`${te(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderBottom:`${te(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},"&-addon:last-child":{borderInlineEnd:`${te(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderTop:`${te(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderBottom:`${te(e.lineWidth)} ${e.lineType} ${e.colorBorder}`}}}})}),Gi=(e,t)=>({background:e.colorBgContainer,borderWidth:`${te(e.lineWidth)} 0`,borderStyle:`${e.lineType} none`,borderColor:`transparent transparent ${t.borderColor} transparent`,borderRadius:0,"&:hover":{borderColor:`transparent transparent ${t.borderColor} transparent`,backgroundColor:e.hoverBg},"&:focus, &:focus-within":{borderColor:`transparent transparent ${t.borderColor} transparent`,outline:0,backgroundColor:e.activeBg}}),Lo=(e,t)=>({[`&${e.componentCls}-status-${t.status}:not(${e.componentCls}-disabled)`]:Object.assign(Object.assign({},Gi(e,t)),{[`${e.componentCls}-prefix, ${e.componentCls}-suffix`]:{color:t.affixColor}}),[`&${e.componentCls}-status-${t.status}${e.componentCls}-disabled`]:{borderColor:`transparent transparent ${t.borderColor} transparent`}}),wf=(e,t)=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Gi(e,{borderColor:e.colorBorder,hoverBorderColor:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeShadow:e.activeShadow})),{[`&${e.componentCls}-disabled, &[disabled]`]:{color:e.colorTextDisabled,boxShadow:"none",cursor:"not-allowed","&:hover":{borderColor:`transparent transparent ${e.colorBorder} transparent`}},"input[disabled], textarea[disabled]":{cursor:"not-allowed"}}),Lo(e,{status:"error",borderColor:e.colorError,hoverBorderColor:e.colorErrorBorderHover,activeBorderColor:e.colorError,activeShadow:e.errorActiveShadow,affixColor:e.colorError})),Lo(e,{status:"warning",borderColor:e.colorWarning,hoverBorderColor:e.colorWarningBorderHover,activeBorderColor:e.colorWarning,activeShadow:e.warningActiveShadow,affixColor:e.colorWarning})),t)}),xf=e=>({"&::-moz-placeholder":{opacity:1},"&::placeholder":{color:e,userSelect:"none"},"&:placeholder-shown":{textOverflow:"ellipsis"}}),Ui=e=>{const{paddingBlockLG:t,lineHeightLG:r,borderRadiusLG:a,paddingInlineLG:n}=e;return{padding:`${te(t)} ${te(n)}`,fontSize:e.inputFontSizeLG,lineHeight:r,borderRadius:a}},Ki=e=>({padding:`${te(e.paddingBlockSM)} ${te(e.paddingInlineSM)}`,fontSize:e.inputFontSizeSM,borderRadius:e.borderRadiusSM}),ki=e=>Object.assign(Object.assign({position:"relative",display:"inline-block",width:"100%",minWidth:0,padding:`${te(e.paddingBlock)} ${te(e.paddingInline)}`,color:e.colorText,fontSize:e.inputFontSize,lineHeight:e.lineHeight,borderRadius:e.borderRadius,transition:`all ${e.motionDurationMid}`},xf(e.colorTextPlaceholder)),{"textarea&":{maxWidth:"100%",height:"auto",minHeight:e.controlHeight,lineHeight:e.lineHeight,verticalAlign:"bottom",transition:`all ${e.motionDurationSlow}, height 0s`,resize:"vertical"},"&-lg":Object.assign({},Ui(e)),"&-sm":Object.assign({},Ki(e)),"&-rtl, &-textarea-rtl":{direction:"rtl"}}),$f=e=>{const{componentCls:t,antCls:r}=e;return{position:"relative",display:"table",width:"100%",borderCollapse:"separate",borderSpacing:0,"&[class*='col-']":{paddingInlineEnd:e.paddingXS,"&:last-child":{paddingInlineEnd:0}},[`&-lg ${t}, &-lg > ${t}-group-addon`]:Object.assign({},Ui(e)),[`&-sm ${t}, &-sm > ${t}-group-addon`]:Object.assign({},Ki(e)),[`&-lg ${r}-select-single ${r}-select-selector`]:{height:e.controlHeightLG},[`&-sm ${r}-select-single ${r}-select-selector`]:{height:e.controlHeightSM},[`> ${t}`]:{display:"table-cell","&:not(:first-child):not(:last-child)":{borderRadius:0}},[`${t}-group`]:{"&-addon, &-wrap":{display:"table-cell",width:1,whiteSpace:"nowrap",verticalAlign:"middle","&:not(:first-child):not(:last-child)":{borderRadius:0}},"&-wrap > *":{display:"block !important"},"&-addon":{position:"relative",padding:`0 ${te(e.paddingInline)}`,color:e.colorText,fontWeight:"normal",fontSize:e.inputFontSize,textAlign:"center",borderRadius:e.borderRadius,transition:`all ${e.motionDurationSlow}`,lineHeight:1,[`${r}-select`]:{margin:`${te(e.calc(e.paddingBlock).add(1).mul(-1).equal())} ${te(e.calc(e.paddingInline).mul(-1).equal())}`,[`&${r}-select-single:not(${r}-select-customize-input):not(${r}-pagination-size-changer)`]:{[`${r}-select-selector`]:{backgroundColor:"inherit",border:`${te(e.lineWidth)} ${e.lineType} transparent`,boxShadow:"none"}}},[`${r}-cascader-picker`]:{margin:`-9px ${te(e.calc(e.paddingInline).mul(-1).equal())}`,backgroundColor:"transparent",[`${r}-cascader-input`]:{textAlign:"start",border:0,boxShadow:"none"}}}},[t]:{width:"100%",marginBottom:0,textAlign:"inherit","&:focus":{zIndex:1,borderInlineEndWidth:1},"&:hover":{zIndex:1,borderInlineEndWidth:1,[`${t}-search-with-button &`]:{zIndex:0}}},[`> ${t}:first-child, ${t}-group-addon:first-child`]:{borderStartEndRadius:0,borderEndEndRadius:0,[`${r}-select ${r}-select-selector`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`> ${t}-affix-wrapper`]:{[`&:not(:first-child) ${t}`]:{borderStartStartRadius:0,borderEndStartRadius:0},[`&:not(:last-child) ${t}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`> ${t}:last-child, ${t}-group-addon:last-child`]:{borderStartStartRadius:0,borderEndStartRadius:0,[`${r}-select ${r}-select-selector`]:{borderStartStartRadius:0,borderEndStartRadius:0}},[`${t}-affix-wrapper`]:{"&:not(:last-child)":{borderStartEndRadius:0,borderEndEndRadius:0,[`${t}-search &`]:{borderStartStartRadius:e.borderRadius,borderEndStartRadius:e.borderRadius}},[`&:not(:first-child), ${t}-search &:not(:first-child)`]:{borderStartStartRadius:0,borderEndStartRadius:0}},[`&${t}-group-compact`]:Object.assign(Object.assign({display:"block"},hs()),{[`${t}-group-addon, ${t}-group-wrap, > ${t}`]:{"&:not(:first-child):not(:last-child)":{borderInlineEndWidth:e.lineWidth,"&:hover, &:focus":{zIndex:1}}},"& > *":{display:"inline-flex",float:"none",verticalAlign:"top",borderRadius:0},[`
        & > ${t}-affix-wrapper,
        & > ${t}-number-affix-wrapper,
        & > ${r}-picker-range
      `]:{display:"inline-flex"},"& > *:not(:last-child)":{marginInlineEnd:e.calc(e.lineWidth).mul(-1).equal(),borderInlineEndWidth:e.lineWidth},[t]:{float:"none"},[`& > ${r}-select > ${r}-select-selector,
      & > ${r}-select-auto-complete ${t},
      & > ${r}-cascader-picker ${t},
      & > ${t}-group-wrapper ${t}`]:{borderInlineEndWidth:e.lineWidth,borderRadius:0,"&:hover, &:focus":{zIndex:1}},[`& > ${r}-select-focused`]:{zIndex:1},[`& > ${r}-select > ${r}-select-arrow`]:{zIndex:1},[`& > *:first-child,
      & > ${r}-select:first-child > ${r}-select-selector,
      & > ${r}-select-auto-complete:first-child ${t},
      & > ${r}-cascader-picker:first-child ${t}`]:{borderStartStartRadius:e.borderRadius,borderEndStartRadius:e.borderRadius},[`& > *:last-child,
      & > ${r}-select:last-child > ${r}-select-selector,
      & > ${r}-cascader-picker:last-child ${t},
      & > ${r}-cascader-picker-focused:last-child ${t}`]:{borderInlineEndWidth:e.lineWidth,borderStartEndRadius:e.borderRadius,borderEndEndRadius:e.borderRadius},[`& > ${r}-select-auto-complete ${t}`]:{verticalAlign:"top"},[`${t}-group-wrapper + ${t}-group-wrapper`]:{marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),[`${t}-affix-wrapper`]:{borderRadius:0}},[`${t}-group-wrapper:not(:last-child)`]:{[`&${t}-search > ${t}-group`]:{[`& > ${t}-group-addon > ${t}-search-button`]:{borderRadius:0},[`& > ${t}`]:{borderStartStartRadius:e.borderRadius,borderStartEndRadius:0,borderEndEndRadius:0,borderEndStartRadius:e.borderRadius}}}})}},Ef=e=>{const{componentCls:t,controlHeightSM:r,lineWidth:a,calc:n}=e,i=n(r).sub(n(a).mul(2)).sub(16).div(2).equal();return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Yr(e)),ki(e)),pf(e)),Cf(e)),bf(e)),wf(e)),{'&[type="color"]':{height:e.controlHeight,[`&${t}-lg`]:{height:e.controlHeightLG},[`&${t}-sm`]:{height:r,paddingTop:i,paddingBottom:i}},'&[type="search"]::-webkit-search-cancel-button, &[type="search"]::-webkit-search-decoration':{"-webkit-appearance":"none"}})}},Ff=e=>{const{componentCls:t}=e;return{[`${t}-clear-icon`]:{margin:0,padding:0,lineHeight:0,color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,verticalAlign:-1,cursor:"pointer",transition:`color ${e.motionDurationSlow}`,border:"none",outline:"none",backgroundColor:"transparent","&:hover":{color:e.colorTextTertiary},"&:active":{color:e.colorText},"&-hidden":{visibility:"hidden"},"&-has-suffix":{margin:`0 ${te(e.inputAffixPadding)}`}}}},Pf=e=>{const{componentCls:t,inputAffixPadding:r,colorTextDescription:a,motionDurationSlow:n,colorIcon:o,colorIconHover:i,iconCls:s}=e,l=`${t}-affix-wrapper`,c=`${t}-affix-wrapper-disabled`;return{[l]:Object.assign(Object.assign(Object.assign(Object.assign({},ki(e)),{display:"inline-flex",[`&:not(${t}-disabled):hover`]:{zIndex:1,[`${t}-search-with-button &`]:{zIndex:0}},"&-focused, &:focus":{zIndex:1},[`> input${t}`]:{padding:0},[`> input${t}, > textarea${t}`]:{fontSize:"inherit",border:"none",borderRadius:0,outline:"none",background:"transparent",color:"inherit","&::-ms-reveal":{display:"none"},"&:focus":{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[t]:{"&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center","> *:not(:last-child)":{marginInlineEnd:e.paddingXS}},"&-show-count-suffix":{color:a},"&-show-count-has-suffix":{marginInlineEnd:e.paddingXXS},"&-prefix":{marginInlineEnd:r},"&-suffix":{marginInlineStart:r}}}),Ff(e)),{[`${s}${t}-password-icon`]:{color:o,cursor:"pointer",transition:`all ${n}`,"&:hover":{color:i}}}),[`${t}-underlined`]:{borderRadius:0},[c]:{[`${s}${t}-password-icon`]:{color:o,cursor:"not-allowed","&:hover":{color:o}}}}},Rf=e=>{const{componentCls:t,borderRadiusLG:r,borderRadiusSM:a}=e;return{[`${t}-group`]:Object.assign(Object.assign(Object.assign({},Yr(e)),$f(e)),{"&-rtl":{direction:"rtl"},"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",width:"100%",textAlign:"start",verticalAlign:"top","&-rtl":{direction:"rtl"},"&-lg":{[`${t}-group-addon`]:{borderRadius:r,fontSize:e.inputFontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:a}}},yf(e)),Sf(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}-affix-wrapper`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-item`]:{[`${t}-affix-wrapper`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})})}},Of=e=>{const{componentCls:t,antCls:r}=e,a=`${t}-search`;return{[a]:{[t]:{"&:hover, &:focus":{[`+ ${t}-group-addon ${a}-button:not(${r}-btn-primary)`]:{borderInlineStartColor:e.colorPrimaryHover}}},[`${t}-affix-wrapper`]:{height:e.controlHeight,borderRadius:0},[`${t}-lg`]:{lineHeight:e.calc(e.lineHeightLG).sub(2e-4).equal()},[`> ${t}-group`]:{[`> ${t}-group-addon:last-child`]:{insetInlineStart:-1,padding:0,border:0,[`${a}-button`]:{marginInlineEnd:-1,borderStartStartRadius:0,borderEndStartRadius:0,boxShadow:"none"},[`${a}-button:not(${r}-btn-primary)`]:{color:e.colorTextDescription,"&:hover":{color:e.colorPrimaryHover},"&:active":{color:e.colorPrimaryActive},[`&${r}-btn-loading::before`]:{insetInlineStart:0,insetInlineEnd:0,insetBlockStart:0,insetBlockEnd:0}}}},[`${a}-button`]:{height:e.controlHeight,"&:hover, &:focus":{zIndex:1}},"&-large":{[`${t}-affix-wrapper, ${a}-button`]:{height:e.controlHeightLG}},"&-small":{[`${t}-affix-wrapper, ${a}-button`]:{height:e.controlHeightSM}},"&-rtl":{direction:"rtl"},[`&${t}-compact-item`]:{[`&:not(${t}-compact-last-item)`]:{[`${t}-group-addon`]:{[`${t}-search-button`]:{marginInlineEnd:e.calc(e.lineWidth).mul(-1).equal(),borderRadius:0}}},[`&:not(${t}-compact-first-item)`]:{[`${t},${t}-affix-wrapper`]:{borderRadius:0}},[`> ${t}-group-addon ${t}-search-button,
        > ${t},
        ${t}-affix-wrapper`]:{"&:hover, &:focus, &:active":{zIndex:2}},[`> ${t}-affix-wrapper-focused`]:{zIndex:2}}}}},If=e=>{const{componentCls:t}=e;return{[`${t}-out-of-range`]:{[`&, & input, & textarea, ${t}-show-count-suffix, ${t}-data-count`]:{color:e.colorError}}}},Nf=Xt(["Input","Shared"],e=>{const t=Ue(e,zi(e));return[Ef(t),Pf(t)]},Di,{resetFont:!1}),Mf=Xt(["Input","Component"],e=>{const t=Ue(e,zi(e));return[Rf(t),Of(t),If(t),Oi(t)]},Di,{resetFont:!1});function Tf(e){return!!(e.addonBefore||e.addonAfter)}function Af(e){return!!(e.prefix||e.suffix||e.allowClear)}function _o(e,t,r){var a=t.cloneNode(!0),n=Object.create(e,{target:{value:a},currentTarget:{value:a}});return a.value=r,typeof t.selectionStart=="number"&&typeof t.selectionEnd=="number"&&(a.selectionStart=t.selectionStart,a.selectionEnd=t.selectionEnd),a.setSelectionRange=function(){t.setSelectionRange.apply(t,arguments)},n}function Ho(e,t,r,a){if(r){var n=t;if(t.type==="click"){n=_o(t,e,""),r(n);return}if(e.type!=="file"&&a!==void 0){n=_o(t,e,a),r(n);return}r(n)}}function jf(e,t){if(e){e.focus(t);var r=t||{},a=r.cursor;if(a){var n=e.value.length;switch(a){case"start":e.setSelectionRange(0,0);break;case"end":e.setSelectionRange(n,n);break;default:e.setSelectionRange(0,n)}}}}var Bf=B.forwardRef(function(e,t){var r,a,n,o=e.inputElement,i=e.children,s=e.prefixCls,l=e.prefix,c=e.suffix,d=e.addonBefore,v=e.addonAfter,g=e.className,y=e.style,h=e.disabled,m=e.readOnly,f=e.focused,C=e.triggerFocus,p=e.allowClear,S=e.value,R=e.handleReset,E=e.hidden,w=e.classes,O=e.classNames,F=e.dataAttrs,b=e.styles,P=e.components,M=e.onClear,H=i??o,T=(P==null?void 0:P.affixWrapper)||"span",L=(P==null?void 0:P.groupWrapper)||"span",W=(P==null?void 0:P.wrapper)||"span",x=(P==null?void 0:P.groupAddon)||"span",$=u.useRef(null),N=function(ce){var Ce;(Ce=$.current)!==null&&Ce!==void 0&&Ce.contains(ce.target)&&(C==null||C())},V=Af(e),_=u.cloneElement(H,{value:S,className:K((r=H.props)===null||r===void 0?void 0:r.className,!V&&(O==null?void 0:O.variant))||null}),z=u.useRef(null);if(B.useImperativeHandle(t,function(){return{nativeElement:z.current||$.current}}),V){var G=null;if(p){var U=!h&&!m&&S,j="".concat(s,"-clear-icon"),D=de(p)==="object"&&p!==null&&p!==void 0&&p.clearIcon?p.clearIcon:"✖";G=B.createElement("button",{type:"button",tabIndex:-1,onClick:function(ce){R==null||R(ce),M==null||M()},onMouseDown:function(ce){return ce.preventDefault()},className:K(j,I(I({},"".concat(j,"-hidden"),!U),"".concat(j,"-has-suffix"),!!c))},D)}var k="".concat(s,"-affix-wrapper"),re=K(k,I(I(I(I(I({},"".concat(s,"-disabled"),h),"".concat(k,"-disabled"),h),"".concat(k,"-focused"),f),"".concat(k,"-readonly"),m),"".concat(k,"-input-with-clear-btn"),c&&p&&S),w==null?void 0:w.affixWrapper,O==null?void 0:O.affixWrapper,O==null?void 0:O.variant),ne=(c||p)&&B.createElement("span",{className:K("".concat(s,"-suffix"),O==null?void 0:O.suffix),style:b==null?void 0:b.suffix},G,c);_=B.createElement(T,me({className:re,style:b==null?void 0:b.affixWrapper,onClick:N},F==null?void 0:F.affixWrapper,{ref:$}),l&&B.createElement("span",{className:K("".concat(s,"-prefix"),O==null?void 0:O.prefix),style:b==null?void 0:b.prefix},l),_,ne)}if(Tf(e)){var ee="".concat(s,"-group"),le="".concat(ee,"-addon"),be="".concat(ee,"-wrapper"),se=K("".concat(s,"-wrapper"),ee,w==null?void 0:w.wrapper,O==null?void 0:O.wrapper),ge=K(be,I({},"".concat(be,"-disabled"),h),w==null?void 0:w.group,O==null?void 0:O.groupWrapper);_=B.createElement(L,{className:ge,ref:z},B.createElement(W,{className:se},d&&B.createElement(x,{className:le},d),_,v&&B.createElement(x,{className:le},v)))}return B.cloneElement(_,{className:K((a=_.props)===null||a===void 0?void 0:a.className,g)||null,style:A(A({},(n=_.props)===null||n===void 0?void 0:n.style),y),hidden:E})}),Lf=["show"];function _f(e,t){return u.useMemo(function(){var r={};t&&(r.show=de(t)==="object"&&t.formatter?t.formatter:!!t),r=A(A({},r),e);var a=r,n=a.show,o=ze(a,Lf);return A(A({},o),{},{show:!!n,showFormatter:typeof n=="function"?n:void 0,strategy:o.strategy||function(i){return i.length}})},[e,t])}var Hf=["autoComplete","onChange","onFocus","onBlur","onPressEnter","onKeyDown","onKeyUp","prefixCls","disabled","htmlSize","className","maxLength","suffix","showCount","count","type","classes","classNames","styles","onCompositionStart","onCompositionEnd"],Vf=u.forwardRef(function(e,t){var r=e.autoComplete,a=e.onChange,n=e.onFocus,o=e.onBlur,i=e.onPressEnter,s=e.onKeyDown,l=e.onKeyUp,c=e.prefixCls,d=c===void 0?"rc-input":c,v=e.disabled,g=e.htmlSize,y=e.className,h=e.maxLength,m=e.suffix,f=e.showCount,C=e.count,p=e.type,S=p===void 0?"text":p,R=e.classes,E=e.classNames,w=e.styles,O=e.onCompositionStart,F=e.onCompositionEnd,b=ze(e,Hf),P=u.useState(!1),M=Z(P,2),H=M[0],T=M[1],L=u.useRef(!1),W=u.useRef(!1),x=u.useRef(null),$=u.useRef(null),N=function(Y){x.current&&jf(x.current,Y)},V=Fa(e.defaultValue,{value:e.value}),_=Z(V,2),z=_[0],G=_[1],U=z==null?"":String(z),j=u.useState(null),D=Z(j,2),k=D[0],re=D[1],ne=_f(C,f),ee=ne.max||h,le=ne.strategy(U),be=!!ee&&le>ee;u.useImperativeHandle(t,function(){var J;return{focus:N,blur:function(){var ae;(ae=x.current)===null||ae===void 0||ae.blur()},setSelectionRange:function(ae,pe,Oe){var Pe;(Pe=x.current)===null||Pe===void 0||Pe.setSelectionRange(ae,pe,Oe)},select:function(){var ae;(ae=x.current)===null||ae===void 0||ae.select()},input:x.current,nativeElement:((J=$.current)===null||J===void 0?void 0:J.nativeElement)||x.current}}),u.useEffect(function(){W.current&&(W.current=!1),T(function(J){return J&&v?!1:J})},[v]);var se=function(Y,ae,pe){var Oe=ae;if(!L.current&&ne.exceedFormatter&&ne.max&&ne.strategy(ae)>ne.max){if(Oe=ne.exceedFormatter(ae,{max:ne.max}),ae!==Oe){var Pe,at;re([((Pe=x.current)===null||Pe===void 0?void 0:Pe.selectionStart)||0,((at=x.current)===null||at===void 0?void 0:at.selectionEnd)||0])}}else if(pe.source==="compositionEnd")return;G(Oe),x.current&&Ho(x.current,Y,a,Oe)};u.useEffect(function(){if(k){var J;(J=x.current)===null||J===void 0||J.setSelectionRange.apply(J,Q(k))}},[k]);var ge=function(Y){se(Y,Y.target.value,{source:"change"})},fe=function(Y){L.current=!1,se(Y,Y.currentTarget.value,{source:"compositionEnd"}),F==null||F(Y)},ce=function(Y){i&&Y.key==="Enter"&&!W.current&&(W.current=!0,i(Y)),s==null||s(Y)},Ce=function(Y){Y.key==="Enter"&&(W.current=!1),l==null||l(Y)},Fe=function(Y){T(!0),n==null||n(Y)},ue=function(Y){W.current&&(W.current=!1),T(!1),o==null||o(Y)},we=function(Y){G(""),N(),x.current&&Ho(x.current,Y,a)},Ae=be&&"".concat(d,"-out-of-range"),xe=function(){var Y=vr(e,["prefixCls","onPressEnter","addonBefore","addonAfter","prefix","suffix","allowClear","defaultValue","showCount","count","classes","htmlSize","styles","classNames","onClear"]);return B.createElement("input",me({autoComplete:r},Y,{onChange:ge,onFocus:Fe,onBlur:ue,onKeyDown:ce,onKeyUp:Ce,className:K(d,I({},"".concat(d,"-disabled"),v),E==null?void 0:E.input),style:w==null?void 0:w.input,ref:x,size:g,type:S,onCompositionStart:function(pe){L.current=!0,O==null||O(pe)},onCompositionEnd:fe}))},nt=function(){var Y=Number(ee)>0;if(m||ne.show){var ae=ne.showFormatter?ne.showFormatter({value:U,count:le,maxLength:ee}):"".concat(le).concat(Y?" / ".concat(ee):"");return B.createElement(B.Fragment,null,ne.show&&B.createElement("span",{className:K("".concat(d,"-show-count-suffix"),I({},"".concat(d,"-show-count-has-suffix"),!!m),E==null?void 0:E.count),style:A({},w==null?void 0:w.count)},ae),m)}return null};return B.createElement(Bf,me({},b,{prefixCls:d,className:K(y,Ae),handleReset:we,value:U,focused:H,triggerFocus:N,suffix:nt(),disabled:v,classes:R,classNames:E,styles:w}),xe())});const zf=e=>{let t;return typeof e=="object"&&(e!=null&&e.clearIcon)?t=e:e&&(t={clearIcon:B.createElement(li,null)}),t};function Df(e,t){const r=u.useRef([]),a=()=>{r.current.push(setTimeout(()=>{var n,o,i,s;!((n=e.current)===null||n===void 0)&&n.input&&((o=e.current)===null||o===void 0?void 0:o.input.getAttribute("type"))==="password"&&(!((i=e.current)===null||i===void 0)&&i.input.hasAttribute("value"))&&((s=e.current)===null||s===void 0||s.input.removeAttribute("value"))}))};return u.useEffect(()=>(t&&a(),()=>r.current.forEach(n=>{n&&clearTimeout(n)})),[]),a}function Wf(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}var qf=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};const Sv=u.forwardRef((e,t)=>{const{prefixCls:r,bordered:a=!0,status:n,size:o,disabled:i,onBlur:s,onFocus:l,suffix:c,allowClear:d,addonAfter:v,addonBefore:g,className:y,style:h,styles:m,rootClassName:f,onChange:C,classNames:p,variant:S}=e,R=qf(e,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]),{getPrefixCls:E,direction:w,allowClear:O,autoComplete:F,className:b,style:P,classNames:M,styles:H}=$a("input"),T=E("input",r),L=u.useRef(null),W=Ra(T),[x,$,N]=Nf(T,f),[V]=Mf(T,W),{compactSize:_,compactItemClassnames:z}=Si(T,w),G=nn(ue=>{var we;return(we=o??_)!==null&&we!==void 0?we:ue}),U=B.useContext(Ko),j=i??U,{status:D,hasFeedback:k,feedbackIcon:re}=u.useContext(Ca),ne=ff(D,n),ee=Wf(e)||!!k;u.useRef(ee);const le=Df(L,!0),be=ue=>{le(),s==null||s(ue)},se=ue=>{le(),l==null||l(ue)},ge=ue=>{le(),C==null||C(ue)},fe=(k||c)&&B.createElement(B.Fragment,null,c,k&&re),ce=zf(d??O),[Ce,Fe]=vf("input",S,a);return x(V(B.createElement(Vf,Object.assign({ref:en(t,L),prefixCls:T,autoComplete:F},R,{disabled:j,onBlur:be,onFocus:se,style:Object.assign(Object.assign({},P),h),styles:Object.assign(Object.assign({},H),m),suffix:fe,allowClear:ce,className:K(y,f,N,W,z,b),onChange:ge,addonBefore:g&&B.createElement($o,{form:!0,space:!0},g),addonAfter:v&&B.createElement($o,{form:!0,space:!0},v),classNames:Object.assign(Object.assign(Object.assign({},p),M),{input:K({[`${T}-sm`]:G==="small",[`${T}-lg`]:G==="large",[`${T}-rtl`]:w==="rtl"},p==null?void 0:p.input,M.input,$),variant:K({[`${T}-${Ce}`]:Fe},Mo(T,ne)),affixWrapper:K({[`${T}-affix-wrapper-sm`]:G==="small",[`${T}-affix-wrapper-lg`]:G==="large",[`${T}-affix-wrapper-rtl`]:w==="rtl"},$),wrapper:K({[`${T}-group-rtl`]:w==="rtl"},$),groupWrapper:K({[`${T}-group-wrapper-sm`]:G==="small",[`${T}-group-wrapper-lg`]:G==="large",[`${T}-group-wrapper-rtl`]:w==="rtl",[`${T}-group-wrapper-${Ce}`]:Fe},Mo(`${T}-group-wrapper`,ne,k),$)})}))))});let Ze=null,Mt=e=>e(),mr=[],hr={};function Vo(){const{getContainer:e,duration:t,rtl:r,maxCount:a,top:n}=hr,o=(e==null?void 0:e())||document.body;return{getContainer:()=>o,duration:t,rtl:r,maxCount:a,top:n}}const Gf=B.forwardRef((e,t)=>{const{messageConfig:r,sync:a}=e,{getPrefixCls:n}=u.useContext(tt),o=hr.prefixCls||n("message"),i=u.useContext(Zd),[s,l]=hi(Object.assign(Object.assign(Object.assign({},r),{prefixCls:o}),i.message));return B.useImperativeHandle(t,()=>{const c=Object.assign({},s);return Object.keys(c).forEach(d=>{c[d]=function(){return a(),s[d].apply(s,arguments)}}),{instance:c,sync:a}}),l}),Uf=B.forwardRef((e,t)=>{const[r,a]=B.useState(Vo),n=()=>{a(Vo)};B.useEffect(n,[]);const o=ys(),i=o.getRootPrefixCls(),s=o.getIconPrefixCls(),l=o.getTheme(),c=B.createElement(Gf,{ref:t,sync:n,messageConfig:r});return B.createElement(ps,{prefixCls:i,iconPrefixCls:s,theme:l},o.holderRender?o.holderRender(c):c)});function fn(){if(!Ze){const e=document.createDocumentFragment(),t={fragment:e};Ze=t,Mt(()=>{pi()(B.createElement(Uf,{ref:a=>{const{instance:n,sync:o}=a||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=o,fn())})}}),e)});return}Ze.instance&&(mr.forEach(e=>{const{type:t,skipped:r}=e;if(!r)switch(t){case"open":{Mt(()=>{const a=Ze.instance.open(Object.assign(Object.assign({},hr),e.config));a==null||a.then(e.resolve),e.setCloseFn(a)});break}case"destroy":Mt(()=>{Ze==null||Ze.instance.destroy(e.key)});break;default:Mt(()=>{var a;const n=(a=Ze.instance)[t].apply(a,Q(e.args));n==null||n.then(e.resolve),e.setCloseFn(n)})}}),mr=[])}function Kf(e){hr=Object.assign(Object.assign({},hr),e),Mt(()=>{var t;(t=Ze==null?void 0:Ze.sync)===null||t===void 0||t.call(Ze)})}function kf(e){const t=Oa(r=>{let a;const n={type:"open",config:e,resolve:r,setCloseFn:o=>{a=o}};return mr.push(n),()=>{a?Mt(()=>{a()}):n.skipped=!0}});return fn(),t}function Jf(e,t){const r=Oa(a=>{let n;const o={type:e,args:t,resolve:a,setCloseFn:i=>{n=i}};return mr.push(o),()=>{n?Mt(()=>{n()}):o.skipped=!0}});return fn(),r}const Zf=e=>{mr.push({type:"destroy",key:e}),fn()},Qf=["success","info","warning","error","loading"],Xf={open:kf,destroy:Zf,config:Kf,useMessage:cc,_InternalPanelDoNotUseOrYouWillBeFired:tc},Yf=Xf;Qf.forEach(e=>{Yf[e]=function(){for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return Jf(e,r)}});export{yf as $,pi as A,Ii as B,At as C,nn as D,Ea as E,Pa as F,Kr as G,Tt as H,vr as I,Jr as J,za as K,rt as L,Qd as M,Bf as N,jf as O,Ti as P,Di as Q,Sl as R,zi as S,Oi as T,ki as U,pf as V,Cf as W,wf as X,bf as Y,xf as Z,$f as _,lv as a,Sf as a0,Si as a1,Ca as a2,vf as a3,Mo as a4,ff as a5,uv as a6,Uc as a7,Sv as a8,hu as a9,Zc as aA,gv as aB,Ku as aC,Cv as aD,bi as aE,Tc as aF,nv as aG,Tl as aH,Mf as aI,Df as aJ,_f as aK,Ho as aL,Nf as aM,zf as aN,fv as aa,ca as ab,dv as ac,vv as ad,Yf as ae,ou as af,yv as ag,wc as ah,Hi as ai,Jd as aj,pv as ak,mv as al,Cr as am,fi as an,yi as ao,hv as ap,kt as aq,Zr as ar,Li as as,Vd as at,Ud as au,bv as av,Oc as aw,Wi as ax,Ta as ay,Ki as az,ov as b,sv as c,Gc as d,iv as e,av as f,zr as g,ed as h,cv as i,aa as j,Us as k,Xt as l,Ue as m,Ra as n,tv as o,ci as p,$o as q,rv as r,nl as s,kl as t,Fa as u,$l as v,li as w,hl as x,Pl as y,Ul as z};
