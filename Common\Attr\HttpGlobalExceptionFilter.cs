﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Xml.XPath;
using zhiying_online.Common;

namespace zhiying_online.Common.Attr
{
    /// <summary>
    /// 全局监听异常
    /// </summary>
    public class HttpGlobalExceptionFilter : ExceptionFilterAttribute
    {
        public override void OnException(ExceptionContext context)
        {
            try
            {
                var action = "/" + context.HttpContext.Request.RouteValues["controller"] + "/" + context.HttpContext.Request.RouteValues["action"];
                if (!context.ExceptionHandled)
                {
                    //获取请求的ip
                    string clientIP = Tools.GetUserIp();
                    if (clientIP == "::1")
                    {
                        return;
                    }

                    //请求类型
                    var method = context.HttpContext.Request.Method;

                    //form错误参数
                    Dictionary<string, string> formdata = new Dictionary<string, string> { };
                    try
                    {
                        if (method.ToUpper() != "GET" && context.HttpContext.Request.HasFormContentType == true)
                        {
                            foreach (var item in context.HttpContext.Request.Form)
                            {
                                if (!formdata.ContainsKey(item.Key))
                                {
                                    formdata.Add(item.Key, item.Value);
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        return;
                    }

                    //querydata参数
                    Dictionary<string, string> querydata = new Dictionary<string, string> { };
                    try
                    {
                        foreach (var item in context.HttpContext.Request.Query)
                        {
                            if (!querydata.ContainsKey(item.Key))
                            {
                                querydata.Add(item.Key, item.Value);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        return;
                    }

                    // 开启接收流
                    var contextdata = "";
                    try
                    {
                        long contentLen = context.HttpContext.Request.ContentLength == null ? 0 : context.HttpContext.Request.ContentLength.Value;
                        if (contentLen > 0)
                        {
                            var syncIOFeature = context.HttpContext.Features.Get<IHttpBodyControlFeature>();
                            if (syncIOFeature != null)
                            {
                                syncIOFeature.AllowSynchronousIO = true;
                            }
                            context.HttpContext.Request.EnableBuffering();
                            contextdata = new StreamReader(context.HttpContext.Request.Body).ReadToEnd();
                        }
                    }
                    catch (Exception e)
                    {
                        Logger.debug("获取contextdata错误");
                    }


                    //错误代码行号
                    StackTrace st = new StackTrace(context.Exception, true);
                    int lineNumber = st.GetFrame(0).GetFileLineNumber();

                    //获取请求进来的控制器与方法
                    var controad = context.ActionDescriptor as ControllerActionDescriptor;

                    //获取接口中文注释名称
                    var actionname = DocumentationExtensions.GetSummary(controad.MethodInfo);

                    //服务名称
                    var servername = controad.MethodInfo.Module.Assembly.GetName().Name;

                    context.ExceptionHandled = true;

                    //记录数据库内
                    var il = new
                    {
                        clientip = clientIP,
                        errmsg = JsonConvert.SerializeObject(context.Exception),
                        method = method,
                        parms = JsonConvert.SerializeObject(new { formdata = formdata, querydata, contextdata }),
                        serverip = context.HttpContext.Connection.LocalIpAddress.MapToIPv4().ToString() + ":" + context.HttpContext.Connection.LocalPort,
                        servername = servername,
                        url = action,
                        actionname = actionname
                    };

                    //记录日志
                    Logger.debug("接口错误，错误信息：" + JsonConvert.SerializeObject(il));

                    //接口错误   
                    ErrHandle(context, new { code = 1, msg = "请求失败接口异常", result = context.Exception.Message + ",行号：" + lineNumber });
                    return;
                }
            }
            catch (Exception e)
            {
                Logger.debug("接口异常监听错误：" + Logger.GetExceptionStr(e));
                ErrHandle(context, new { code = 1, msg = "请求失败接口异常" });
                return;
            }
        }

        private void ErrHandle(ExceptionContext context, object eo)
        {
            var Result = new ContentResult { Content = Newtonsoft.Json.JsonConvert.SerializeObject(eo), ContentType = "application/json" };
            context.Result = Result;
        }

    }
}
