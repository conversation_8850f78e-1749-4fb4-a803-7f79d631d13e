﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Common.BLL;
using zhiying_online.Common.Jwt;

namespace zhiying_online.Controllers
{
    /// <summary>
    /// 系统配置
    /// </summary>
    [Route("[controller]/[action]")]
    public class SystemController : Controller
    {
        /// <summary>
        /// 获取系统配置
        /// </summary>
        /// <param name="type">配置类型自定义</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":"结果result"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetSystemConfig(string type)
        {
            return Json(SystemHandle.GetSystemConfig(type));
        }

        /// <summary>
        /// 保存系统配置
        /// </summary>
        /// <param name="type">配置类型自定义，相同类型只可一次</param>
        /// <param name="result">配置规则</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtAttribute]
        public IActionResult SaveSystemConfig(string type, string result)
        {
            return Json(SystemHandle.SaveSystemConfig(type, result));
        }
    }
}
