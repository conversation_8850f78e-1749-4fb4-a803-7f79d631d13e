import{c as h,r,l as G,o as f,x as W}from"./index-C0F_BgRl.js";import{l as D,m as X,K,I as M}from"./index-Cv9X8VLK.js";const O=e=>{const{prefixCls:t,className:s,style:n,size:a,shape:i}=e,c=h({[`${t}-lg`]:a==="large",[`${t}-sm`]:a==="small"}),l=h({[`${t}-circle`]:i==="circle",[`${t}-square`]:i==="square",[`${t}-round`]:i==="round"}),o=r.useMemo(()=>typeof a=="number"?{width:a,height:a,lineHeight:`${a}px`}:{},[a]);return r.createElement("span",{className:h(t,c,l,s),style:Object.assign(Object.assign({},o),n)})},_=new K("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),w=e=>({height:e,lineHeight:G(e)}),b=e=>Object.assign({width:e},w(e)),J=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:_,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),q=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},w(e)),Q=e=>{const{skeletonAvatarCls:t,gradientFromColor:s,controlHeight:n,controlHeightLG:a,controlHeightSM:i}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:s},b(n)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:Object.assign({},b(a)),[`${t}${t}-sm`]:Object.assign({},b(i))}},U=e=>{const{controlHeight:t,borderRadiusSM:s,skeletonInputCls:n,controlHeightLG:a,controlHeightSM:i,gradientFromColor:c,calc:l}=e;return{[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:c,borderRadius:s},q(t,l)),[`${n}-lg`]:Object.assign({},q(a,l)),[`${n}-sm`]:Object.assign({},q(i,l))}},F=e=>Object.assign({width:e},w(e)),Y=e=>{const{skeletonImageCls:t,imageSizeBase:s,gradientFromColor:n,borderRadiusSM:a,calc:i}=e;return{[t]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:n,borderRadius:a},F(i(s).mul(2).equal())),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:Object.assign(Object.assign({},F(s)),{maxWidth:i(s).mul(4).equal(),maxHeight:i(s).mul(4).equal()}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},B=(e,t,s)=>{const{skeletonButtonCls:n}=e;return{[`${s}${n}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${s}${n}-round`]:{borderRadius:t}}},z=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},w(e)),Z=e=>{const{borderRadiusSM:t,skeletonButtonCls:s,controlHeight:n,controlHeightLG:a,controlHeightSM:i,gradientFromColor:c,calc:l}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[s]:Object.assign({display:"inline-block",verticalAlign:"top",background:c,borderRadius:t,width:l(n).mul(2).equal(),minWidth:l(n).mul(2).equal()},z(n,l))},B(e,n,s)),{[`${s}-lg`]:Object.assign({},z(a,l))}),B(e,a,`${s}-lg`)),{[`${s}-sm`]:Object.assign({},z(i,l))}),B(e,i,`${s}-sm`))},ee=e=>{const{componentCls:t,skeletonAvatarCls:s,skeletonTitleCls:n,skeletonParagraphCls:a,skeletonButtonCls:i,skeletonInputCls:c,skeletonImageCls:l,controlHeight:o,controlHeightLG:d,controlHeightSM:m,gradientFromColor:g,padding:p,marginSM:C,borderRadius:N,titleHeight:E,blockRadius:u,paragraphLiHeight:y,controlHeightXS:P,paragraphMarginTop:I}=e;return{[t]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:p,verticalAlign:"top",[s]:Object.assign({display:"inline-block",verticalAlign:"top",background:g},b(o)),[`${s}-circle`]:{borderRadius:"50%"},[`${s}-lg`]:Object.assign({},b(d)),[`${s}-sm`]:Object.assign({},b(m))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[n]:{width:"100%",height:E,background:g,borderRadius:u,[`+ ${a}`]:{marginBlockStart:m}},[a]:{padding:0,"> li":{width:"100%",height:y,listStyle:"none",background:g,borderRadius:u,"+ li":{marginBlockStart:P}}},[`${a}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${n}, ${a} > li`]:{borderRadius:N}}},[`${t}-with-avatar ${t}-content`]:{[n]:{marginBlockStart:C,[`+ ${a}`]:{marginBlockStart:I}}},[`${t}${t}-element`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},Z(e)),Q(e)),U(e)),Y(e)),[`${t}${t}-block`]:{width:"100%",[i]:{width:"100%"},[c]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${n},
        ${a} > li,
        ${s},
        ${i},
        ${c},
        ${l}
      `]:Object.assign({},J(e))}}},te=e=>{const{colorFillContent:t,colorFill:s}=e,n=t,a=s;return{color:n,colorGradientEnd:a,gradientFromColor:n,gradientToColor:a,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}},$=D("Skeleton",e=>{const{componentCls:t,calc:s}=e,n=X(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:s(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.gradientFromColor} 25%, ${e.gradientToColor} 37%, ${e.gradientFromColor} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[ee(n)]},te,{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),se=e=>{const{prefixCls:t,className:s,rootClassName:n,active:a,shape:i="circle",size:c="default"}=e,{getPrefixCls:l}=r.useContext(f),o=l("skeleton",t),[d,m,g]=$(o),p=M(e,["prefixCls","className"]),C=h(o,`${o}-element`,{[`${o}-active`]:a},s,n,m,g);return d(r.createElement("div",{className:C},r.createElement(O,Object.assign({prefixCls:`${o}-avatar`,shape:i,size:c},p))))},ne=e=>{const{prefixCls:t,className:s,rootClassName:n,active:a,block:i=!1,size:c="default"}=e,{getPrefixCls:l}=r.useContext(f),o=l("skeleton",t),[d,m,g]=$(o),p=M(e,["prefixCls"]),C=h(o,`${o}-element`,{[`${o}-active`]:a,[`${o}-block`]:i},s,n,m,g);return d(r.createElement("div",{className:C},r.createElement(O,Object.assign({prefixCls:`${o}-button`,size:c},p))))},ae="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",le=e=>{const{prefixCls:t,className:s,rootClassName:n,style:a,active:i}=e,{getPrefixCls:c}=r.useContext(f),l=c("skeleton",t),[o,d,m]=$(l),g=h(l,`${l}-element`,{[`${l}-active`]:i},s,n,d,m);return o(r.createElement("div",{className:g},r.createElement("div",{className:h(`${l}-image`,s),style:a},r.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:`${l}-image-svg`},r.createElement("title",null,"Image placeholder"),r.createElement("path",{d:ae,className:`${l}-image-path`})))))},oe=e=>{const{prefixCls:t,className:s,rootClassName:n,active:a,block:i,size:c="default"}=e,{getPrefixCls:l}=r.useContext(f),o=l("skeleton",t),[d,m,g]=$(o),p=M(e,["prefixCls"]),C=h(o,`${o}-element`,{[`${o}-active`]:a,[`${o}-block`]:i},s,n,m,g);return d(r.createElement("div",{className:C},r.createElement(O,Object.assign({prefixCls:`${o}-input`,size:c},p))))},ie=e=>{const{prefixCls:t,className:s,rootClassName:n,style:a,active:i,children:c}=e,{getPrefixCls:l}=r.useContext(f),o=l("skeleton",t),[d,m,g]=$(o),p=h(o,`${o}-element`,{[`${o}-active`]:i},m,s,n,g);return d(r.createElement("div",{className:p},r.createElement("div",{className:h(`${o}-image`,s),style:a},c)))},re=(e,t)=>{const{width:s,rows:n=2}=t;if(Array.isArray(s))return s[e];if(n-1===e)return s},ce=e=>{const{prefixCls:t,className:s,style:n,rows:a=0}=e,i=Array.from({length:a}).map((c,l)=>r.createElement("li",{key:l,style:{width:re(l,e)}}));return r.createElement("ul",{className:h(t,s),style:n},i)},ge=e=>{let{prefixCls:t,className:s,width:n,style:a}=e;return r.createElement("h3",{className:h(t,s),style:Object.assign({width:n},a)})};function R(e){return e&&typeof e=="object"?e:{}}function de(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function me(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function ue(e,t){const s={};return(!e||!t)&&(s.width="61%"),!e&&t?s.rows=3:s.rows=2,s}const k=e=>{const{prefixCls:t,loading:s,className:n,rootClassName:a,style:i,children:c,avatar:l=!1,title:o=!0,paragraph:d=!0,active:m,round:g}=e,{getPrefixCls:p,direction:C,className:N,style:E}=W("skeleton"),u=p("skeleton",t),[y,P,I]=$(u);if(s||!("loading"in e)){const S=!!l,v=!!o,x=!!d;let A;if(S){const j=Object.assign(Object.assign({prefixCls:`${u}-avatar`},de(v,x)),R(l));A=r.createElement("div",{className:`${u}-header`},r.createElement(O,Object.assign({},j)))}let L;if(v||x){let j;if(v){const H=Object.assign(Object.assign({prefixCls:`${u}-title`},me(S,x)),R(o));j=r.createElement(ge,Object.assign({},H))}let T;if(x){const H=Object.assign(Object.assign({prefixCls:`${u}-paragraph`},ue(S,v)),R(d));T=r.createElement(ce,Object.assign({},H))}L=r.createElement("div",{className:`${u}-content`},j,T)}const V=h(u,{[`${u}-with-avatar`]:S,[`${u}-active`]:m,[`${u}-rtl`]:C==="rtl",[`${u}-round`]:g},N,n,a,P,I);return y(r.createElement("div",{className:V,style:Object.assign(Object.assign({},E),i)},A,L))}return c??null};k.Button=ne;k.Avatar=se;k.Input=oe;k.Image=le;k.Node=ie;export{k as S};
