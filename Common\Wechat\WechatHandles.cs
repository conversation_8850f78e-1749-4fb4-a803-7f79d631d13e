﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zhiying_online.Common.wechat
{
    public class WechatHandles
    {
        /// <summary>
        /// 统一返回码
        /// </summary>
        public class WxJsonResult
        {
            public int errcode { get; set; }
            public string errmsg { get; set; }
        }

        /// <summary>
        /// 获取accesstoken
        /// </summary>
        public class OAuthAccessTokenResult : WxJsonResult
        {
            /// <summary>
            /// 接口调用凭证
            /// </summary>
            public string access_token { get; set; }
            /// <summary>
            /// access_token接口调用凭证超时时间，单位（秒）
            /// </summary>
            public int expires_in { get; set; }
            /// <summary>
            /// 用户刷新access_token
            /// </summary>
            public string refresh_token { get; set; }
            /// <summary>
            /// 授权用户唯一标识
            /// </summary>
            public string openid { get; set; }
            /// <summary>
            /// 用户授权的作用域，使用逗号（,）分隔
            /// </summary>
            public string scope { get; set; }
            /// <summary>
            /// 授权方令牌
            /// </summary>
            public string authorizer_access_token { get; set; }
            /// <summary>
            /// 刷新令牌
            /// </summary>
            public string authorizer_refresh_token { get; set; }
        }

        /// <summary>
        /// 微信用户信息
        /// </summary>
        public class WxUserInfo : WxJsonResult
        {
            public int? subscribe { get; set; }
            public string openid { get; set; }
            public string nickname { get; set; }
            public int? sex { get; set; }
            public string language { get; set; }
            public string city { get; set; }
            public string province { get; set; }
            public string country { get; set; }
            public string headimgurl { get; set; }
            public long? subscribe_time { get; set; }
            public string unionid { get; set; }
            public string remark { get; set; }
            public int groupid { get; set; }
            public int[] tagid_list { get; set; }
            public string subscribe_scene { get; set; }
            public string qr_scene { get; set; }
            public string qr_scene_str { get; set; }
            public int gender { get; set; }
            public string avatarUrl { get; set; }
            public string phoneNumber { get; set; }
            public string purePhoneNumber { get; set; }
            public string countryCode { get; set; }
        }

        /// <summary>
        /// 获取accesstoken
        /// </summary>
        /// <returns></returns>
        public static string Get_Access_Token()
        {
            var Configuration = Tools.GetConfiguration();
            var accesstokne = CacheManager.Default.Get<string>("accesstoken");
            if (accesstokne == null)
            {
                var ct = JsonConvert.DeserializeObject<OAuthAccessTokenResult>(Tools.HtmlGet("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + Configuration["Wechat:AppId"] + "&secret=" + Configuration["Wechat:Secret"] + ""));
                if (ct.errcode == 0)
                {
                    accesstokne = ct.access_token;
                    CacheManager.Default.Set_AbsoluteExpire<string>("accesstoken", accesstokne, TimeSpan.FromSeconds(ct.expires_in - 200));
                }
                else
                {
                    Logger.debug("AccesstokenBySecret：" + Configuration["Wechat:AppId"] + "_" + Configuration["Wechat:Secret"] + "：" + JsonConvert.SerializeObject(ct));
                }
            }
            return accesstokne;
        }

        /// <summary>
        /// 获取授权的地址
        /// </summary>
        /// <param name="redirect_uri"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        public static string GetOauthUrl(string redirect_uri, string state)
        {
            var Configuration = Tools.GetConfiguration();
            return $"https://open.weixin.qq.com/connect/oauth2/authorize?appid={Configuration["Wechat: AppId"]}&redirect_uri={System.Web.HttpUtility.UrlEncode(redirect_uri)}&response_type=code&scope=snsapi_base&state={state}#wechat_redirect";
        }


        /// <summary>
        /// code获取用户
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static OAuthAccessTokenResult GetOpenid(string code)
        {
            var Configuration = Tools.GetConfiguration();
            return JsonConvert.DeserializeObject<OAuthAccessTokenResult>(Tools.HtmlPost(string.Format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + Configuration["Wechat:AppId"] + "&secret=" + Configuration["Wechat:Secret"] + "&code=" + code + "&grant_type=authorization_code"), "", Encoding.UTF8));
        }

        /// <summary>
        /// 获取用户新信息
        /// </summary>
        /// <param name="openid"></param>
        /// <returns></returns>
        public static WxUserInfo GetWxUserInfo(string openid)
        {
            //获取accesstoken
            var accesstoken = Get_Access_Token();

            //获取用户信息
            return JsonConvert.DeserializeObject<WxUserInfo>(Tools.HtmlPost(string.Format("https://api.weixin.qq.com/cgi-bin/user/info?access_token={0}&openid={1}&lang={2}",
                    accesstoken, openid, "zh_CN"), "", Encoding.UTF8));
        }
    }
}
