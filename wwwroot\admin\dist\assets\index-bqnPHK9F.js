import{r as i,q as er,o as nt,c as le,R as yt,h as wt,a as de,af as tr,d as ce,w as lo,b as fe,e as Ae,z as It,G as nr,ag as or,_ as Ue,g as so,y as Pt,H as it,E as rr,F as ir,u as Gt,M as zn,a9 as ar,j as lr,s as kt,J as Dt,x as hn,m as vt,B as qt,K as bn,l as L,L as co,ah as sr,$ as cr,A as Sn,p as Tn,n as Nn,a2 as ur}from"./index-C0F_BgRl.js";import{K as pt,i as dr,u as zt,j as re,p as _t,av as mr,M as fr,E as uo,I as xt,H as Cn,k as $n,l as Et,m as at,T as gr,L as Jt,w as vr,R as pr,an as hr,aD as br,a1 as mo,a3 as Sr,n as At,a2 as fo,D as go,a4 as Cr,o as yn,r as $r,a5 as yr,aA as dn,G as en,q as wr,af as Ir,ah as xr,z as Er,aw as Or,aE as Rr,h as Br,aF as Mr,aG as Pr,B as Hn}from"./index-Cv9X8VLK.js";import{F as Dr,i as Tt,a as vo,s as po,c as ho,b as bo,D as zr,M as Tr,u as Nr,S as Hr,E as Lr,R as So,d as jr,e as _r}from"./EllipsisOutlined-DUbZADlg.js";import{T as Ar,i as Co,g as Wr,b as Vr,a as Fr,e as Xr,f as Kr,C as Gr}from"./useBubbleLock-ftt3IjSJ.js";const qr=new pt("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Ur=new pt("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),Yr=new pt("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Qr=new pt("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Zr=new pt("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),kr=new pt("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Jr=new pt("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),ei=new pt("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}}),ti={"move-up":{inKeyframes:Jr,outKeyframes:ei},"move-down":{inKeyframes:qr,outKeyframes:Ur},"move-left":{inKeyframes:Yr,outKeyframes:Qr},"move-right":{inKeyframes:Zr,outKeyframes:kr}},Ut=(e,t)=>{const{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:r,outKeyframes:l}=ti[t];return[dr(o,r,l,e.motionDurationMid),{[`
        ${o}-enter,
        ${o}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]};function ni(e){return t=>i.createElement(er,{theme:{token:{motion:!1,zIndexPopupBase:0}}},i.createElement(e,Object.assign({},t)))}const $o=(e,t,n,o,r)=>ni(a=>{const{prefixCls:s,style:c}=a,m=i.useRef(null),[d,f]=i.useState(0),[p,u]=i.useState(0),[v,g]=zt(!1,{value:a.open}),{getPrefixCls:h}=i.useContext(nt),C=h(o||"select",s);i.useEffect(()=>{if(g(!0),typeof ResizeObserver<"u"){const S=new ResizeObserver(w=>{const I=w[0].target;f(I.offsetHeight+8),u(I.offsetWidth)}),x=setInterval(()=>{var w;const I=r?`.${r(C)}`:`.${C}-dropdown`,R=(w=m.current)===null||w===void 0?void 0:w.querySelector(I);R&&(clearInterval(x),S.observe(R))},10);return()=>{clearInterval(x),S.disconnect()}}},[]);let b=Object.assign(Object.assign({},a),{style:Object.assign(Object.assign({},c),{margin:0}),open:v,visible:v,getPopupContainer:()=>m.current});n&&(b=n(b)),t&&Object.assign(b,{[t]:{overflow:{adjustX:!1,adjustY:!1}}});const $={paddingBottom:d,position:"relative",minWidth:p};return i.createElement("div",{ref:m,style:$},i.createElement(e,Object.assign({},b)))});var tn=function(t){var n=t.className,o=t.customizeIcon,r=t.customizeIconProps,l=t.children,a=t.onMouseDown,s=t.onClick,c=typeof o=="function"?o(r):o;return i.createElement("span",{className:n,onMouseDown:function(d){d.preventDefault(),a==null||a(d)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:s,"aria-hidden":!0},c!==void 0?c:i.createElement("span",{className:le(n.split(/\s+/).map(function(m){return"".concat(m,"-icon")}))},l))},oi=function(t,n,o,r,l){var a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,s=arguments.length>6?arguments[6]:void 0,c=arguments.length>7?arguments[7]:void 0,m=yt.useMemo(function(){if(wt(r)==="object")return r.clearIcon;if(l)return l},[r,l]),d=yt.useMemo(function(){return!!(!a&&r&&(o.length||s)&&!(c==="combobox"&&s===""))},[r,a,o.length,s,c]);return{allowClear:d,clearIcon:yt.createElement(tn,{className:"".concat(t,"-clear"),onMouseDown:n,customizeIcon:m},"×")}},yo=i.createContext(null);function ri(){return i.useContext(yo)}function ii(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10,t=i.useState(!1),n=de(t,2),o=n[0],r=n[1],l=i.useRef(null),a=function(){window.clearTimeout(l.current)};i.useEffect(function(){return a},[]);var s=function(m,d){a(),l.current=window.setTimeout(function(){r(m),d&&d()},e)};return[o,s,a]}function wo(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,t=i.useRef(null),n=i.useRef(null);i.useEffect(function(){return function(){window.clearTimeout(n.current)}},[]);function o(r){(r||t.current===null)&&(t.current=r),window.clearTimeout(n.current),n.current=window.setTimeout(function(){t.current=null},e)}return[function(){return t.current},o]}function ai(e,t,n,o){var r=i.useRef(null);r.current={open:t,triggerOpen:n,customizedTrigger:o},i.useEffect(function(){function l(a){var s;if(!((s=r.current)!==null&&s!==void 0&&s.customizedTrigger)){var c=a.target;c.shadowRoot&&a.composed&&(c=a.composedPath()[0]||c),r.current.open&&e().filter(function(m){return m}).every(function(m){return!m.contains(c)&&m!==c})&&r.current.triggerOpen(!1)}}return window.addEventListener("mousedown",l),function(){return window.removeEventListener("mousedown",l)}},[])}function li(e){return e&&![re.ESC,re.SHIFT,re.BACKSPACE,re.TAB,re.WIN_KEY,re.ALT,re.META,re.WIN_KEY_RIGHT,re.CTRL,re.SEMICOLON,re.EQUALS,re.CAPS_LOCK,re.CONTEXT_MENU,re.F1,re.F2,re.F3,re.F4,re.F5,re.F6,re.F7,re.F8,re.F9,re.F10,re.F11,re.F12].includes(e)}var si=function(t,n){var o,r=t.prefixCls,l=t.id,a=t.inputElement,s=t.disabled,c=t.tabIndex,m=t.autoFocus,d=t.autoComplete,f=t.editable,p=t.activeDescendantId,u=t.value,v=t.maxLength,g=t.onKeyDown,h=t.onMouseDown,C=t.onChange,b=t.onPaste,$=t.onCompositionStart,S=t.onCompositionEnd,x=t.onBlur,w=t.open,I=t.attrs,R=a||i.createElement("input",null),y=R,P=y.ref,E=y.props,N=E.onKeyDown,z=E.onChange,T=E.onMouseDown,D=E.onCompositionStart,V=E.onCompositionEnd,q=E.onBlur,_=E.style;return tr(!("maxLength"in R.props)),R=i.cloneElement(R,ce(ce(ce({type:"search"},E),{},{id:l,ref:lo(n,P),disabled:s,tabIndex:c,autoComplete:d||"off",autoFocus:m,className:le("".concat(r,"-selection-search-input"),(o=R)===null||o===void 0||(o=o.props)===null||o===void 0?void 0:o.className),role:"combobox","aria-expanded":w||!1,"aria-haspopup":"listbox","aria-owns":"".concat(l,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(l,"_list"),"aria-activedescendant":w?p:void 0},I),{},{value:f?u:"",maxLength:v,readOnly:!f,unselectable:f?null:"on",style:ce(ce({},_),{},{opacity:f?null:0}),onKeyDown:function(O){g(O),N&&N(O)},onMouseDown:function(O){h(O),T&&T(O)},onChange:function(O){C(O),z&&z(O)},onCompositionStart:function(O){$(O),D&&D(O)},onCompositionEnd:function(O){S(O),V&&V(O)},onPaste:b,onBlur:function(O){x(O),q&&q(O)}})),R},Io=i.forwardRef(si);function xo(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}var ci=typeof window<"u"&&window.document&&window.document.documentElement,ui=ci;function di(e){return e!=null}function mi(e){return!e&&e!==0}function Ln(e){return["string","number"].includes(wt(e))}function Eo(e){var t=void 0;return e&&(Ln(e.title)?t=e.title.toString():Ln(e.label)&&(t=e.label.toString())),t}function fi(e,t){ui?i.useLayoutEffect(e,t):i.useEffect(e,t)}function gi(e){var t;return(t=e.key)!==null&&t!==void 0?t:e.value}var jn=function(t){t.preventDefault(),t.stopPropagation()},vi=function(t){var n=t.id,o=t.prefixCls,r=t.values,l=t.open,a=t.searchValue,s=t.autoClearSearchValue,c=t.inputRef,m=t.placeholder,d=t.disabled,f=t.mode,p=t.showSearch,u=t.autoFocus,v=t.autoComplete,g=t.activeDescendantId,h=t.tabIndex,C=t.removeIcon,b=t.maxTagCount,$=t.maxTagTextLength,S=t.maxTagPlaceholder,x=S===void 0?function(oe){return"+ ".concat(oe.length," ...")}:S,w=t.tagRender,I=t.onToggleOpen,R=t.onRemove,y=t.onInputChange,P=t.onInputPaste,E=t.onInputKeyDown,N=t.onInputMouseDown,z=t.onInputCompositionStart,T=t.onInputCompositionEnd,D=t.onInputBlur,V=i.useRef(null),q=i.useState(0),_=de(q,2),G=_[0],O=_[1],H=i.useState(!1),j=de(H,2),Z=j[0],K=j[1],te="".concat(o,"-selection"),ge=l||f==="multiple"&&s===!1||f==="tags"?a:"",ue=f==="tags"||f==="multiple"&&s===!1||p&&(l||Z);fi(function(){O(V.current.scrollWidth)},[ge]);var ne=function(W,ee,ve,Se,me){return i.createElement("span",{title:Eo(W),className:le("".concat(te,"-item"),fe({},"".concat(te,"-item-disabled"),ve))},i.createElement("span",{className:"".concat(te,"-item-content")},ee),Se&&i.createElement(tn,{className:"".concat(te,"-item-remove"),onMouseDown:jn,onClick:me,customizeIcon:C},"×"))},k=function(W,ee,ve,Se,me,we){var He=function(De){jn(De),I(!l)};return i.createElement("span",{onMouseDown:He},w({label:ee,value:W,disabled:ve,closable:Se,onClose:me,isMaxTag:!!we}))},se=function(W){var ee=W.disabled,ve=W.label,Se=W.value,me=!d&&!ee,we=ve;if(typeof $=="number"&&(typeof ve=="string"||typeof ve=="number")){var He=String(we);He.length>$&&(we="".concat(He.slice(0,$),"..."))}var Ee=function(be){be&&be.stopPropagation(),R(W)};return typeof w=="function"?k(Se,we,ee,me,Ee):ne(W,we,ee,me,Ee)},A=function(W){if(!r.length)return null;var ee=typeof x=="function"?x(W):x;return typeof w=="function"?k(void 0,ee,!1,!1,void 0,!0):ne({title:ee},ee,!1)},B=i.createElement("div",{className:"".concat(te,"-search"),style:{width:G},onFocus:function(){K(!0)},onBlur:function(){K(!1)}},i.createElement(Io,{ref:c,open:l,prefixCls:o,id:n,inputElement:null,disabled:d,autoFocus:u,autoComplete:v,editable:ue,activeDescendantId:g,value:ge,onKeyDown:E,onMouseDown:N,onChange:y,onPaste:P,onCompositionStart:z,onCompositionEnd:T,onBlur:D,tabIndex:h,attrs:_t(t,!0)}),i.createElement("span",{ref:V,className:"".concat(te,"-search-mirror"),"aria-hidden":!0},ge," ")),M=i.createElement(Dr,{prefixCls:"".concat(te,"-overflow"),data:r,renderItem:se,renderRest:A,suffix:B,itemKey:gi,maxCount:b});return i.createElement("span",{className:"".concat(te,"-wrap")},M,!r.length&&!ge&&i.createElement("span",{className:"".concat(te,"-placeholder")},m))},pi=function(t){var n=t.inputElement,o=t.prefixCls,r=t.id,l=t.inputRef,a=t.disabled,s=t.autoFocus,c=t.autoComplete,m=t.activeDescendantId,d=t.mode,f=t.open,p=t.values,u=t.placeholder,v=t.tabIndex,g=t.showSearch,h=t.searchValue,C=t.activeValue,b=t.maxLength,$=t.onInputKeyDown,S=t.onInputMouseDown,x=t.onInputChange,w=t.onInputPaste,I=t.onInputCompositionStart,R=t.onInputCompositionEnd,y=t.onInputBlur,P=t.title,E=i.useState(!1),N=de(E,2),z=N[0],T=N[1],D=d==="combobox",V=D||g,q=p[0],_=h||"";D&&C&&!z&&(_=C),i.useEffect(function(){D&&T(!1)},[D,C]);var G=d!=="combobox"&&!f&&!g?!1:!!_,O=P===void 0?Eo(q):P,H=i.useMemo(function(){return q?null:i.createElement("span",{className:"".concat(o,"-selection-placeholder"),style:G?{visibility:"hidden"}:void 0},u)},[q,G,u,o]);return i.createElement("span",{className:"".concat(o,"-selection-wrap")},i.createElement("span",{className:"".concat(o,"-selection-search")},i.createElement(Io,{ref:l,prefixCls:o,id:r,open:f,inputElement:n,disabled:a,autoFocus:s,autoComplete:c,editable:V,activeDescendantId:m,value:_,onKeyDown:$,onMouseDown:S,onChange:function(Z){T(!0),x(Z)},onPaste:w,onCompositionStart:I,onCompositionEnd:R,onBlur:y,tabIndex:v,attrs:_t(t,!0),maxLength:D?b:void 0})),!D&&q?i.createElement("span",{className:"".concat(o,"-selection-item"),title:O,style:G?{visibility:"hidden"}:void 0},q.label):null,H)},hi=function(t,n){var o=i.useRef(null),r=i.useRef(!1),l=t.prefixCls,a=t.open,s=t.mode,c=t.showSearch,m=t.tokenWithEnter,d=t.disabled,f=t.prefix,p=t.autoClearSearchValue,u=t.onSearch,v=t.onSearchSubmit,g=t.onToggleOpen,h=t.onInputKeyDown,C=t.onInputBlur,b=t.domRef;i.useImperativeHandle(n,function(){return{focus:function(O){o.current.focus(O)},blur:function(){o.current.blur()}}});var $=wo(0),S=de($,2),x=S[0],w=S[1],I=function(O){var H=O.which,j=o.current instanceof HTMLTextAreaElement;!j&&a&&(H===re.UP||H===re.DOWN)&&O.preventDefault(),h&&h(O),H===re.ENTER&&s==="tags"&&!r.current&&!a&&(v==null||v(O.target.value)),!(j&&!a&&~[re.UP,re.DOWN,re.LEFT,re.RIGHT].indexOf(H))&&li(H)&&g(!0)},R=function(){w(!0)},y=i.useRef(null),P=function(O){u(O,!0,r.current)!==!1&&g(!0)},E=function(){r.current=!0},N=function(O){r.current=!1,s!=="combobox"&&P(O.target.value)},z=function(O){var H=O.target.value;if(m&&y.current&&/[\r\n]/.test(y.current)){var j=y.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");H=H.replace(j,y.current)}y.current=null,P(H)},T=function(O){var H=O.clipboardData,j=H==null?void 0:H.getData("text");y.current=j||""},D=function(O){var H=O.target;if(H!==o.current){var j=document.body.style.msTouchAction!==void 0;j?setTimeout(function(){o.current.focus()}):o.current.focus()}},V=function(O){var H=x();O.target!==o.current&&!H&&!(s==="combobox"&&d)&&O.preventDefault(),(s!=="combobox"&&(!c||!H)||!a)&&(a&&p!==!1&&u("",!0,!1),g())},q={inputRef:o,onInputKeyDown:I,onInputMouseDown:R,onInputChange:z,onInputPaste:T,onInputCompositionStart:E,onInputCompositionEnd:N,onInputBlur:C},_=s==="multiple"||s==="tags"?i.createElement(vi,Ae({},t,q)):i.createElement(pi,Ae({},t,q));return i.createElement("div",{ref:b,className:"".concat(l,"-selector"),onClick:D,onMouseDown:V},f&&i.createElement("div",{className:"".concat(l,"-prefix")},f),_)},bi=i.forwardRef(hi),Si=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],Ci=function(t){var n=t===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"}}},$i=function(t,n){var o=t.prefixCls;t.disabled;var r=t.visible,l=t.children,a=t.popupElement,s=t.animation,c=t.transitionName,m=t.dropdownStyle,d=t.dropdownClassName,f=t.direction,p=f===void 0?"ltr":f,u=t.placement,v=t.builtinPlacements,g=t.dropdownMatchSelectWidth,h=t.dropdownRender,C=t.dropdownAlign,b=t.getPopupContainer,$=t.empty,S=t.getTriggerDOMNode,x=t.onPopupVisibleChange,w=t.onPopupMouseEnter,I=It(t,Si),R="".concat(o,"-dropdown"),y=a;h&&(y=h(a));var P=i.useMemo(function(){return v||Ci(g)},[v,g]),E=s?"".concat(R,"-").concat(s):c,N=typeof g=="number",z=i.useMemo(function(){return N?null:g===!1?"minWidth":"width"},[g,N]),T=m;N&&(T=ce(ce({},T),{},{width:g}));var D=i.useRef(null);return i.useImperativeHandle(n,function(){return{getPopupElement:function(){var q;return(q=D.current)===null||q===void 0?void 0:q.popupElement}}}),i.createElement(mr,Ae({},I,{showAction:x?["click"]:[],hideAction:x?["click"]:[],popupPlacement:u||(p==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:P,prefixCls:R,popupTransitionName:E,popup:i.createElement("div",{onMouseEnter:w},y),ref:D,stretch:z,popupAlign:C,popupVisible:r,getPopupContainer:b,popupClassName:le(d,fe({},"".concat(R,"-empty"),$)),popupStyle:T,getTriggerDOMNode:S,onPopupVisibleChange:x}),l)},yi=i.forwardRef($i);function _n(e,t){var n=e.key,o;return"value"in e&&(o=e.value),n??(o!==void 0?o:"rc-index-key-".concat(t))}function mn(e){return typeof e<"u"&&!Number.isNaN(e)}function Oo(e,t){var n=e||{},o=n.label,r=n.value,l=n.options,a=n.groupLabel,s=o||(t?"children":"label");return{label:s,value:r||"value",options:l||"options",groupLabel:a||s}}function wi(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,r=[],l=Oo(n,!1),a=l.label,s=l.value,c=l.options,m=l.groupLabel;function d(f,p){Array.isArray(f)&&f.forEach(function(u){if(p||!(c in u)){var v=u[s];r.push({key:_n(u,r.length),groupOption:p,data:u,label:u[a],value:v})}else{var g=u[m];g===void 0&&o&&(g=u.label),r.push({key:_n(u,r.length),group:!0,data:u,label:g}),d(u[c],!0)}})}return d(e,!1),r}function fn(e){var t=ce({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return nr(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var Ii=function(t,n,o){if(!n||!n.length)return null;var r=!1,l=function s(c,m){var d=or(m),f=d[0],p=d.slice(1);if(!f)return[c];var u=c.split(f);return r=r||u.length>1,u.reduce(function(v,g){return[].concat(Ue(v),Ue(s(g,p)))},[]).filter(Boolean)},a=l(t,n);return r?typeof o<"u"?a.slice(0,o):a:null},wn=i.createContext(null);function xi(e){var t=e.visible,n=e.values;if(!t)return null;var o=50;return i.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,o).map(function(r){var l=r.label,a=r.value;return["number","string"].includes(wt(l))?l:a}).join(", ")),n.length>o?", ...":null)}var Ei=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],Oi=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],gn=function(t){return t==="tags"||t==="multiple"},Ri=i.forwardRef(function(e,t){var n,o=e.id,r=e.prefixCls,l=e.className,a=e.showSearch,s=e.tagRender,c=e.direction,m=e.omitDomProps,d=e.displayValues,f=e.onDisplayValuesChange,p=e.emptyOptions,u=e.notFoundContent,v=u===void 0?"Not Found":u,g=e.onClear,h=e.mode,C=e.disabled,b=e.loading,$=e.getInputElement,S=e.getRawInputElement,x=e.open,w=e.defaultOpen,I=e.onDropdownVisibleChange,R=e.activeValue,y=e.onActiveValueChange,P=e.activeDescendantId,E=e.searchValue,N=e.autoClearSearchValue,z=e.onSearch,T=e.onSearchSplit,D=e.tokenSeparators,V=e.allowClear,q=e.prefix,_=e.suffixIcon,G=e.clearIcon,O=e.OptionList,H=e.animation,j=e.transitionName,Z=e.dropdownStyle,K=e.dropdownClassName,te=e.dropdownMatchSelectWidth,ge=e.dropdownRender,ue=e.dropdownAlign,ne=e.placement,k=e.builtinPlacements,se=e.getPopupContainer,A=e.showAction,B=A===void 0?[]:A,M=e.onFocus,oe=e.onBlur,W=e.onKeyUp,ee=e.onKeyDown,ve=e.onMouseDown,Se=It(e,Ei),me=gn(h),we=(a!==void 0?a:me)||h==="combobox",He=ce({},Se);Oi.forEach(function(pe){delete He[pe]}),m==null||m.forEach(function(pe){delete He[pe]});var Ee=i.useState(!1),De=de(Ee,2),be=De[0],Ke=De[1];i.useEffect(function(){Ke(fr())},[]);var Ge=i.useRef(null),ye=i.useRef(null),Re=i.useRef(null),Ie=i.useRef(null),Oe=i.useRef(null),ze=i.useRef(!1),ke=ii(),We=de(ke,3),je=We[0],xe=We[1],ht=We[2];i.useImperativeHandle(t,function(){var pe,ie;return{focus:(pe=Ie.current)===null||pe===void 0?void 0:pe.focus,blur:(ie=Ie.current)===null||ie===void 0?void 0:ie.blur,scrollTo:function(Xe){var Ne;return(Ne=Oe.current)===null||Ne===void 0?void 0:Ne.scrollTo(Xe)},nativeElement:Ge.current||ye.current}});var _e=i.useMemo(function(){var pe;if(h!=="combobox")return E;var ie=(pe=d[0])===null||pe===void 0?void 0:pe.value;return typeof ie=="string"||typeof ie=="number"?String(ie):""},[E,h,d]),qe=h==="combobox"&&typeof $=="function"&&$()||null,Me=typeof S=="function"&&S(),Nt=so(ye,Me==null||(n=Me.props)===null||n===void 0?void 0:n.ref),Ot=i.useState(!1),ut=de(Ot,2),bt=ut[0],Rt=ut[1];Pt(function(){Rt(!0)},[]);var lt=zt(!1,{defaultValue:w,value:x}),Ye=de(lt,2),dt=Ye[0],mt=Ye[1],Be=bt?dt:!1,ft=!v&&p;(C||ft&&Be&&h==="combobox")&&(Be=!1);var ot=ft?!1:Be,X=i.useCallback(function(pe){var ie=pe!==void 0?pe:!Be;C||(mt(ie),Be!==ie&&(I==null||I(ie)))},[C,Be,mt,I]),J=i.useMemo(function(){return(D||[]).some(function(pe){return[`
`,`\r
`].includes(pe)})},[D]),Y=i.useContext(wn)||{},Q=Y.maxCount,he=Y.rawValues,$e=function(ie,Fe,Xe){if(!(me&&mn(Q)&&(he==null?void 0:he.size)>=Q)){var Ne=!0,Le=ie;y==null||y(null);var ct=Ii(ie,D,mn(Q)?Q-he.size:void 0),rt=Xe?null:ct;return h!=="combobox"&&rt&&(Le="",T==null||T(rt),X(!1),Ne=!1),z&&_e!==Le&&z(Le,{source:Fe?"typing":"effect"}),Ne}},Qe=function(ie){!ie||!ie.trim()||z(ie,{source:"submit"})};i.useEffect(function(){!Be&&!me&&h!=="combobox"&&$e("",!1,!1)},[Be]),i.useEffect(function(){dt&&C&&mt(!1),C&&!ze.current&&xe(!1)},[C]);var Ve=wo(),Je=de(Ve,2),Pe=Je[0],et=Je[1],gt=i.useRef(!1),Ht=function(ie){var Fe=Pe(),Xe=ie.key,Ne=Xe==="Enter";if(Ne&&(h!=="combobox"&&ie.preventDefault(),Be||X(!0)),et(!!_e),Xe==="Backspace"&&!Fe&&me&&!_e&&d.length){for(var Le=Ue(d),ct=null,rt=Le.length-1;rt>=0;rt-=1){var St=Le[rt];if(!St.disabled){Le.splice(rt,1),ct=St;break}}ct&&f(Le,{type:"remove",values:[ct]})}for(var Mt=arguments.length,Ct=new Array(Mt>1?Mt-1:0),Xt=1;Xt<Mt;Xt++)Ct[Xt-1]=arguments[Xt];if(Be&&(!Ne||!gt.current)){var Kt;Ne&&(gt.current=!0),(Kt=Oe.current)===null||Kt===void 0||Kt.onKeyDown.apply(Kt,[ie].concat(Ct))}ee==null||ee.apply(void 0,[ie].concat(Ct))},Ft=function(ie){for(var Fe=arguments.length,Xe=new Array(Fe>1?Fe-1:0),Ne=1;Ne<Fe;Ne++)Xe[Ne-1]=arguments[Ne];if(Be){var Le;(Le=Oe.current)===null||Le===void 0||Le.onKeyUp.apply(Le,[ie].concat(Xe))}ie.key==="Enter"&&(gt.current=!1),W==null||W.apply(void 0,[ie].concat(Xe))},tt=function(ie){var Fe=d.filter(function(Xe){return Xe!==ie});f(Fe,{type:"remove",values:[ie]})},Ze=function(){gt.current=!1},U=i.useRef(!1),F=function(){xe(!0),C||(M&&!U.current&&M.apply(void 0,arguments),B.includes("focus")&&X(!0)),U.current=!0},ae=function(){ze.current=!0,xe(!1,function(){U.current=!1,ze.current=!1,X(!1)}),!C&&(_e&&(h==="tags"?z(_e,{source:"submit"}):h==="multiple"&&z("",{source:"blur"})),oe&&oe.apply(void 0,arguments))},Ce=[];i.useEffect(function(){return function(){Ce.forEach(function(pe){return clearTimeout(pe)}),Ce.splice(0,Ce.length)}},[]);var Te=function(ie){var Fe,Xe=ie.target,Ne=(Fe=Re.current)===null||Fe===void 0?void 0:Fe.getPopupElement();if(Ne&&Ne.contains(Xe)){var Le=setTimeout(function(){var Mt=Ce.indexOf(Le);if(Mt!==-1&&Ce.splice(Mt,1),ht(),!be&&!Ne.contains(document.activeElement)){var Ct;(Ct=Ie.current)===null||Ct===void 0||Ct.focus()}});Ce.push(Le)}for(var ct=arguments.length,rt=new Array(ct>1?ct-1:0),St=1;St<ct;St++)rt[St-1]=arguments[St];ve==null||ve.apply(void 0,[ie].concat(rt))},Bt=i.useState({}),st=de(Bt,2),on=st[1];function rn(){on({})}var Lt;Me&&(Lt=function(ie){X(ie)}),ai(function(){var pe;return[Ge.current,(pe=Re.current)===null||pe===void 0?void 0:pe.getPopupElement()]},ot,X,!!Me);var jt=i.useMemo(function(){return ce(ce({},e),{},{notFoundContent:v,open:Be,triggerOpen:ot,id:o,showSearch:we,multiple:me,toggleOpen:X})},[e,v,ot,Be,o,we,me,X]),Bn=!!_||b,Mn;Bn&&(Mn=i.createElement(tn,{className:le("".concat(r,"-arrow"),fe({},"".concat(r,"-arrow-loading"),b)),customizeIcon:_,customizeIconProps:{loading:b,searchValue:_e,open:Be,focused:je,showSearch:we}}));var Yo=function(){var ie;g==null||g(),(ie=Ie.current)===null||ie===void 0||ie.focus(),f([],{type:"clear",values:d}),$e("",!1,!1)},Pn=oi(r,Yo,d,V,G,C,_e,h),Qo=Pn.allowClear,Zo=Pn.clearIcon,ko=i.createElement(O,{ref:Oe}),Jo=le(r,l,fe(fe(fe(fe(fe(fe(fe(fe(fe(fe({},"".concat(r,"-focused"),je),"".concat(r,"-multiple"),me),"".concat(r,"-single"),!me),"".concat(r,"-allow-clear"),V),"".concat(r,"-show-arrow"),Bn),"".concat(r,"-disabled"),C),"".concat(r,"-loading"),b),"".concat(r,"-open"),Be),"".concat(r,"-customize-input"),qe),"".concat(r,"-show-search"),we)),Dn=i.createElement(yi,{ref:Re,disabled:C,prefixCls:r,visible:ot,popupElement:ko,animation:H,transitionName:j,dropdownStyle:Z,dropdownClassName:K,direction:c,dropdownMatchSelectWidth:te,dropdownRender:ge,dropdownAlign:ue,placement:ne,builtinPlacements:k,getPopupContainer:se,empty:p,getTriggerDOMNode:function(ie){return ye.current||ie},onPopupVisibleChange:Lt,onPopupMouseEnter:rn},Me?i.cloneElement(Me,{ref:Nt}):i.createElement(bi,Ae({},e,{domRef:ye,prefixCls:r,inputElement:qe,ref:Ie,id:o,prefix:q,showSearch:we,autoClearSearchValue:N,mode:h,activeDescendantId:P,tagRender:s,values:d,open:Be,onToggleOpen:X,activeValue:R,searchValue:_e,onSearch:$e,onSearchSubmit:Qe,onRemove:tt,tokenWithEnter:J,onInputBlur:Ze}))),an;return Me?an=Dn:an=i.createElement("div",Ae({className:Jo},He,{ref:Ge,onMouseDown:Te,onKeyDown:Ht,onKeyUp:Ft,onFocus:F,onBlur:ae}),i.createElement(xi,{visible:je&&!Be,values:d}),Dn,Mn,Qo&&Zo),i.createElement(yo.Provider,{value:jt},an)}),In=function(){return null};In.isSelectOptGroup=!0;var xn=function(){return null};xn.isSelectOption=!0;var Ro=i.forwardRef(function(e,t){var n=e.height,o=e.offsetY,r=e.offsetX,l=e.children,a=e.prefixCls,s=e.onInnerResize,c=e.innerProps,m=e.rtl,d=e.extra,f={},p={display:"flex",flexDirection:"column"};return o!==void 0&&(f={height:n,position:"relative",overflow:"hidden"},p=ce(ce({},p),{},fe(fe(fe(fe(fe({transform:"translateY(".concat(o,"px)")},m?"marginRight":"marginLeft",-r),"position","absolute"),"left",0),"right",0),"top",0))),i.createElement("div",{style:f},i.createElement(uo,{onResize:function(v){var g=v.offsetHeight;g&&s&&s()}},i.createElement("div",Ae({style:p,className:le(fe({},"".concat(a,"-holder-inner"),a)),ref:t},c),l,d)))});Ro.displayName="Filler";function Bi(e){var t=e.children,n=e.setRef,o=i.useCallback(function(r){n(r)},[]);return i.cloneElement(t,{ref:o})}function Mi(e,t,n,o,r,l,a,s){var c=s.getKey;return e.slice(t,n+1).map(function(m,d){var f=t+d,p=a(m,f,{style:{width:o},offsetX:r}),u=c(m);return i.createElement(Bi,{key:u,setRef:function(g){return l(m,g)}},p)})}function Pi(e,t,n){var o=e.length,r=t.length,l,a;if(o===0&&r===0)return null;o<r?(l=e,a=t):(l=t,a=e);var s={__EMPTY_ITEM__:!0};function c(v){return v!==void 0?n(v):s}for(var m=null,d=Math.abs(o-r)!==1,f=0;f<a.length;f+=1){var p=c(l[f]),u=c(a[f]);if(p!==u){m=f,d=d||p!==c(a[f+1]);break}}return m===null?null:{index:m,multiple:d}}function Di(e,t,n){var o=i.useState(e),r=de(o,2),l=r[0],a=r[1],s=i.useState(null),c=de(s,2),m=c[0],d=c[1];return i.useEffect(function(){var f=Pi(l||[],e||[],t);(f==null?void 0:f.index)!==void 0&&d(e[f.index]),a(e)},[e]),[m]}var An=(typeof navigator>"u"?"undefined":wt(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const Bo=function(e,t,n,o){var r=i.useRef(!1),l=i.useRef(null);function a(){clearTimeout(l.current),r.current=!0,l.current=setTimeout(function(){r.current=!1},50)}var s=i.useRef({top:e,bottom:t,left:n,right:o});return s.current.top=e,s.current.bottom=t,s.current.left=n,s.current.right=o,function(c,m){var d=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,f=c?m<0&&s.current.left||m>0&&s.current.right:m<0&&s.current.top||m>0&&s.current.bottom;return d&&f?(clearTimeout(l.current),r.current=!1):(!f||r.current)&&a(),!r.current&&f}};function zi(e,t,n,o,r,l,a){var s=i.useRef(0),c=i.useRef(null),m=i.useRef(null),d=i.useRef(!1),f=Bo(t,n,o,r);function p(b,$){if(it.cancel(c.current),!f(!1,$)){var S=b;if(!S._virtualHandled)S._virtualHandled=!0;else return;s.current+=$,m.current=$,An||S.preventDefault(),c.current=it(function(){var x=d.current?10:1;a(s.current*x,!1),s.current=0})}}function u(b,$){a($,!0),An||b.preventDefault()}var v=i.useRef(null),g=i.useRef(null);function h(b){if(e){it.cancel(g.current),g.current=it(function(){v.current=null},2);var $=b.deltaX,S=b.deltaY,x=b.shiftKey,w=$,I=S;(v.current==="sx"||!v.current&&x&&S&&!$)&&(w=S,I=0,v.current="sx");var R=Math.abs(w),y=Math.abs(I);v.current===null&&(v.current=l&&R>y?"x":"y"),v.current==="y"?p(b,I):u(b,w)}}function C(b){e&&(d.current=b.detail===m.current)}return[h,C]}function Ti(e,t,n,o){var r=i.useMemo(function(){return[new Map,[]]},[e,n.id,o]),l=de(r,2),a=l[0],s=l[1],c=function(d){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:d,p=a.get(d),u=a.get(f);if(p===void 0||u===void 0)for(var v=e.length,g=s.length;g<v;g+=1){var h,C=e[g],b=t(C);a.set(b,g);var $=(h=n.get(b))!==null&&h!==void 0?h:o;if(s[g]=(s[g-1]||0)+$,b===d&&(p=g),b===f&&(u=g),p!==void 0&&u!==void 0)break}return{top:s[p-1]||0,bottom:s[u]}};return c}var Ni=function(){function e(){ir(this,e),fe(this,"maps",void 0),fe(this,"id",0),fe(this,"diffKeys",new Set),this.maps=Object.create(null)}return rr(e,[{key:"set",value:function(n,o){this.maps[n]=o,this.id+=1,this.diffKeys.add(n)}},{key:"get",value:function(n){return this.maps[n]}},{key:"resetRecord",value:function(){this.diffKeys.clear()}},{key:"getRecord",value:function(){return this.diffKeys}}]),e}();function Wn(e){var t=parseFloat(e);return isNaN(t)?0:t}function Hi(e,t,n){var o=i.useState(0),r=de(o,2),l=r[0],a=r[1],s=i.useRef(new Map),c=i.useRef(new Ni),m=i.useRef(0);function d(){m.current+=1}function f(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;d();var v=function(){var C=!1;s.current.forEach(function(b,$){if(b&&b.offsetParent){var S=b.offsetHeight,x=getComputedStyle(b),w=x.marginTop,I=x.marginBottom,R=Wn(w),y=Wn(I),P=S+R+y;c.current.get($)!==P&&(c.current.set($,P),C=!0)}}),C&&a(function(b){return b+1})};if(u)v();else{m.current+=1;var g=m.current;Promise.resolve().then(function(){g===m.current&&v()})}}function p(u,v){var g=e(u);s.current.get(g),v?(s.current.set(g,v),f()):s.current.delete(g)}return i.useEffect(function(){return d},[]),[p,f,c.current,l]}var Vn=14/15;function Li(e,t,n){var o=i.useRef(!1),r=i.useRef(0),l=i.useRef(0),a=i.useRef(null),s=i.useRef(null),c,m=function(u){if(o.current){var v=Math.ceil(u.touches[0].pageX),g=Math.ceil(u.touches[0].pageY),h=r.current-v,C=l.current-g,b=Math.abs(h)>Math.abs(C);b?r.current=v:l.current=g;var $=n(b,b?h:C,!1,u);$&&u.preventDefault(),clearInterval(s.current),$&&(s.current=setInterval(function(){b?h*=Vn:C*=Vn;var S=Math.floor(b?h:C);(!n(b,S,!0)||Math.abs(S)<=.1)&&clearInterval(s.current)},16))}},d=function(){o.current=!1,c()},f=function(u){c(),u.touches.length===1&&!o.current&&(o.current=!0,r.current=Math.ceil(u.touches[0].pageX),l.current=Math.ceil(u.touches[0].pageY),a.current=u.target,a.current.addEventListener("touchmove",m,{passive:!1}),a.current.addEventListener("touchend",d,{passive:!0}))};c=function(){a.current&&(a.current.removeEventListener("touchmove",m),a.current.removeEventListener("touchend",d))},Pt(function(){return e&&t.current.addEventListener("touchstart",f,{passive:!0}),function(){var p;(p=t.current)===null||p===void 0||p.removeEventListener("touchstart",f),c(),clearInterval(s.current)}},[e])}function Fn(e){return Math.floor(Math.pow(e,.5))}function vn(e,t){var n="touches"in e?e.touches[0]:e;return n[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}function ji(e,t,n){i.useEffect(function(){var o=t.current;if(e&&o){var r=!1,l,a,s=function(){it.cancel(l)},c=function p(){s(),l=it(function(){n(a),p()})},m=function(u){if(!(u.target.draggable||u.button!==0)){var v=u;v._virtualHandled||(v._virtualHandled=!0,r=!0)}},d=function(){r=!1,s()},f=function(u){if(r){var v=vn(u,!1),g=o.getBoundingClientRect(),h=g.top,C=g.bottom;if(v<=h){var b=h-v;a=-Fn(b),c()}else if(v>=C){var $=v-C;a=Fn($),c()}else s()}};return o.addEventListener("mousedown",m),o.ownerDocument.addEventListener("mouseup",d),o.ownerDocument.addEventListener("mousemove",f),function(){o.removeEventListener("mousedown",m),o.ownerDocument.removeEventListener("mouseup",d),o.ownerDocument.removeEventListener("mousemove",f),s()}}},[e])}var _i=10;function Ai(e,t,n,o,r,l,a,s){var c=i.useRef(),m=i.useState(null),d=de(m,2),f=d[0],p=d[1];return Pt(function(){if(f&&f.times<_i){if(!e.current){p(function(G){return ce({},G)});return}l();var u=f.targetAlign,v=f.originAlign,g=f.index,h=f.offset,C=e.current.clientHeight,b=!1,$=u,S=null;if(C){for(var x=u||v,w=0,I=0,R=0,y=Math.min(t.length-1,g),P=0;P<=y;P+=1){var E=r(t[P]);I=w;var N=n.get(E);R=I+(N===void 0?o:N),w=R}for(var z=x==="top"?h:C-h,T=y;T>=0;T-=1){var D=r(t[T]),V=n.get(D);if(V===void 0){b=!0;break}if(z-=V,z<=0)break}switch(x){case"top":S=I-h;break;case"bottom":S=R-C+h;break;default:{var q=e.current.scrollTop,_=q+C;I<q?$="top":R>_&&($="bottom")}}S!==null&&a(S),S!==f.lastTop&&(b=!0)}b&&p(ce(ce({},f),{},{times:f.times+1,targetAlign:$,lastTop:S}))}},[f,e.current]),function(u){if(u==null){s();return}if(it.cancel(c.current),typeof u=="number")a(u);else if(u&&wt(u)==="object"){var v,g=u.align;"index"in u?v=u.index:v=t.findIndex(function(b){return r(b)===u.key});var h=u.offset,C=h===void 0?0:h;p({times:0,index:v,offset:C,originAlign:g})}}}var Xn=i.forwardRef(function(e,t){var n=e.prefixCls,o=e.rtl,r=e.scrollOffset,l=e.scrollRange,a=e.onStartMove,s=e.onStopMove,c=e.onScroll,m=e.horizontal,d=e.spinSize,f=e.containerSize,p=e.style,u=e.thumbStyle,v=e.showScrollBar,g=i.useState(!1),h=de(g,2),C=h[0],b=h[1],$=i.useState(null),S=de($,2),x=S[0],w=S[1],I=i.useState(null),R=de(I,2),y=R[0],P=R[1],E=!o,N=i.useRef(),z=i.useRef(),T=i.useState(v),D=de(T,2),V=D[0],q=D[1],_=i.useRef(),G=function(){v===!0||v===!1||(clearTimeout(_.current),q(!0),_.current=setTimeout(function(){q(!1)},3e3))},O=l-f||0,H=f-d||0,j=i.useMemo(function(){if(r===0||O===0)return 0;var A=r/O;return A*H},[r,O,H]),Z=function(B){B.stopPropagation(),B.preventDefault()},K=i.useRef({top:j,dragging:C,pageY:x,startTop:y});K.current={top:j,dragging:C,pageY:x,startTop:y};var te=function(B){b(!0),w(vn(B,m)),P(K.current.top),a(),B.stopPropagation(),B.preventDefault()};i.useEffect(function(){var A=function(W){W.preventDefault()},B=N.current,M=z.current;return B.addEventListener("touchstart",A,{passive:!1}),M.addEventListener("touchstart",te,{passive:!1}),function(){B.removeEventListener("touchstart",A),M.removeEventListener("touchstart",te)}},[]);var ge=i.useRef();ge.current=O;var ue=i.useRef();ue.current=H,i.useEffect(function(){if(C){var A,B=function(W){var ee=K.current,ve=ee.dragging,Se=ee.pageY,me=ee.startTop;it.cancel(A);var we=N.current.getBoundingClientRect(),He=f/(m?we.width:we.height);if(ve){var Ee=(vn(W,m)-Se)*He,De=me;!E&&m?De-=Ee:De+=Ee;var be=ge.current,Ke=ue.current,Ge=Ke?De/Ke:0,ye=Math.ceil(Ge*be);ye=Math.max(ye,0),ye=Math.min(ye,be),A=it(function(){c(ye,m)})}},M=function(){b(!1),s()};return window.addEventListener("mousemove",B,{passive:!0}),window.addEventListener("touchmove",B,{passive:!0}),window.addEventListener("mouseup",M,{passive:!0}),window.addEventListener("touchend",M,{passive:!0}),function(){window.removeEventListener("mousemove",B),window.removeEventListener("touchmove",B),window.removeEventListener("mouseup",M),window.removeEventListener("touchend",M),it.cancel(A)}}},[C]),i.useEffect(function(){return G(),function(){clearTimeout(_.current)}},[r]),i.useImperativeHandle(t,function(){return{delayHidden:G}});var ne="".concat(n,"-scrollbar"),k={position:"absolute",visibility:V?null:"hidden"},se={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return m?(k.height=8,k.left=0,k.right=0,k.bottom=0,se.height="100%",se.width=d,E?se.left=j:se.right=j):(k.width=8,k.top=0,k.bottom=0,E?k.right=0:k.left=0,se.width="100%",se.height=d,se.top=j),i.createElement("div",{ref:N,className:le(ne,fe(fe(fe({},"".concat(ne,"-horizontal"),m),"".concat(ne,"-vertical"),!m),"".concat(ne,"-visible"),V)),style:ce(ce({},k),p),onMouseDown:Z,onMouseMove:G},i.createElement("div",{ref:z,className:le("".concat(ne,"-thumb"),fe({},"".concat(ne,"-thumb-moving"),C)),style:ce(ce({},se),u),onMouseDown:te}))}),Wi=20;function Kn(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=e/t*e;return isNaN(n)&&(n=0),n=Math.max(n,Wi),Math.floor(n)}var Vi=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],Fi=[],Xi={overflowY:"auto",overflowAnchor:"none"};function Ki(e,t){var n=e.prefixCls,o=n===void 0?"rc-virtual-list":n,r=e.className,l=e.height,a=e.itemHeight,s=e.fullHeight,c=s===void 0?!0:s,m=e.style,d=e.data,f=e.children,p=e.itemKey,u=e.virtual,v=e.direction,g=e.scrollWidth,h=e.component,C=h===void 0?"div":h,b=e.onScroll,$=e.onVirtualScroll,S=e.onVisibleChange,x=e.innerProps,w=e.extraRender,I=e.styles,R=e.showScrollBar,y=R===void 0?"optional":R,P=It(e,Vi),E=i.useCallback(function(U){return typeof p=="function"?p(U):U==null?void 0:U[p]},[p]),N=Hi(E),z=de(N,4),T=z[0],D=z[1],V=z[2],q=z[3],_=!!(u!==!1&&l&&a),G=i.useMemo(function(){return Object.values(V.maps).reduce(function(U,F){return U+F},0)},[V.id,V.maps]),O=_&&d&&(Math.max(a*d.length,G)>l||!!g),H=v==="rtl",j=le(o,fe({},"".concat(o,"-rtl"),H),r),Z=d||Fi,K=i.useRef(),te=i.useRef(),ge=i.useRef(),ue=i.useState(0),ne=de(ue,2),k=ne[0],se=ne[1],A=i.useState(0),B=de(A,2),M=B[0],oe=B[1],W=i.useState(!1),ee=de(W,2),ve=ee[0],Se=ee[1],me=function(){Se(!0)},we=function(){Se(!1)},He={getKey:E};function Ee(U){se(function(F){var ae;typeof U=="function"?ae=U(F):ae=U;var Ce=Rt(ae);return K.current.scrollTop=Ce,Ce})}var De=i.useRef({start:0,end:Z.length}),be=i.useRef(),Ke=Di(Z,E),Ge=de(Ke,1),ye=Ge[0];be.current=ye;var Re=i.useMemo(function(){if(!_)return{scrollHeight:void 0,start:0,end:Z.length-1,offset:void 0};if(!O){var U;return{scrollHeight:((U=te.current)===null||U===void 0?void 0:U.offsetHeight)||0,start:0,end:Z.length-1,offset:void 0}}for(var F=0,ae,Ce,Te,Bt=Z.length,st=0;st<Bt;st+=1){var on=Z[st],rn=E(on),Lt=V.get(rn),jt=F+(Lt===void 0?a:Lt);jt>=k&&ae===void 0&&(ae=st,Ce=F),jt>k+l&&Te===void 0&&(Te=st),F=jt}return ae===void 0&&(ae=0,Ce=0,Te=Math.ceil(l/a)),Te===void 0&&(Te=Z.length-1),Te=Math.min(Te+1,Z.length-1),{scrollHeight:F,start:ae,end:Te,offset:Ce}},[O,_,k,Z,q,l]),Ie=Re.scrollHeight,Oe=Re.start,ze=Re.end,ke=Re.offset;De.current.start=Oe,De.current.end=ze,i.useLayoutEffect(function(){var U=V.getRecord();if(U.size===1){var F=Array.from(U)[0],ae=Z[Oe];if(ae){var Ce=E(ae);if(Ce===F){var Te=V.get(F),Bt=Te-a;Ee(function(st){return st+Bt})}}}V.resetRecord()},[Ie]);var We=i.useState({width:0,height:l}),je=de(We,2),xe=je[0],ht=je[1],_e=function(F){ht({width:F.offsetWidth,height:F.offsetHeight})},qe=i.useRef(),Me=i.useRef(),Nt=i.useMemo(function(){return Kn(xe.width,g)},[xe.width,g]),Ot=i.useMemo(function(){return Kn(xe.height,Ie)},[xe.height,Ie]),ut=Ie-l,bt=i.useRef(ut);bt.current=ut;function Rt(U){var F=U;return Number.isNaN(bt.current)||(F=Math.min(F,bt.current)),F=Math.max(F,0),F}var lt=k<=0,Ye=k>=ut,dt=M<=0,mt=M>=g,Be=Bo(lt,Ye,dt,mt),ft=function(){return{x:H?-M:M,y:k}},ot=i.useRef(ft()),X=Gt(function(U){if($){var F=ce(ce({},ft()),U);(ot.current.x!==F.x||ot.current.y!==F.y)&&($(F),ot.current=F)}});function J(U,F){var ae=U;F?(zn.flushSync(function(){oe(ae)}),X()):Ee(ae)}function Y(U){var F=U.currentTarget.scrollTop;F!==k&&Ee(F),b==null||b(U),X()}var Q=function(F){var ae=F,Ce=g?g-xe.width:0;return ae=Math.max(ae,0),ae=Math.min(ae,Ce),ae},he=Gt(function(U,F){F?(zn.flushSync(function(){oe(function(ae){var Ce=ae+(H?-U:U);return Q(Ce)})}),X()):Ee(function(ae){var Ce=ae+U;return Ce})}),$e=zi(_,lt,Ye,dt,mt,!!g,he),Qe=de($e,2),Ve=Qe[0],Je=Qe[1];Li(_,K,function(U,F,ae,Ce){var Te=Ce;return Be(U,F,ae)?!1:!Te||!Te._virtualHandled?(Te&&(Te._virtualHandled=!0),Ve({preventDefault:function(){},deltaX:U?F:0,deltaY:U?0:F}),!0):!1}),ji(O,K,function(U){Ee(function(F){return F+U})}),Pt(function(){function U(ae){var Ce=lt&&ae.detail<0,Te=Ye&&ae.detail>0;_&&!Ce&&!Te&&ae.preventDefault()}var F=K.current;return F.addEventListener("wheel",Ve,{passive:!1}),F.addEventListener("DOMMouseScroll",Je,{passive:!0}),F.addEventListener("MozMousePixelScroll",U,{passive:!1}),function(){F.removeEventListener("wheel",Ve),F.removeEventListener("DOMMouseScroll",Je),F.removeEventListener("MozMousePixelScroll",U)}},[_,lt,Ye]),Pt(function(){if(g){var U=Q(M);oe(U),X({x:U})}},[xe.width,g]);var Pe=function(){var F,ae;(F=qe.current)===null||F===void 0||F.delayHidden(),(ae=Me.current)===null||ae===void 0||ae.delayHidden()},et=Ai(K,Z,V,a,E,function(){return D(!0)},Ee,Pe);i.useImperativeHandle(t,function(){return{nativeElement:ge.current,getScrollInfo:ft,scrollTo:function(F){function ae(Ce){return Ce&&wt(Ce)==="object"&&("left"in Ce||"top"in Ce)}ae(F)?(F.left!==void 0&&oe(Q(F.left)),et(F.top)):et(F)}}}),Pt(function(){if(S){var U=Z.slice(Oe,ze+1);S(U,Z)}},[Oe,ze,Z]);var gt=Ti(Z,E,V,a),Ht=w==null?void 0:w({start:Oe,end:ze,virtual:O,offsetX:M,offsetY:ke,rtl:H,getSize:gt}),Ft=Mi(Z,Oe,ze,g,M,T,f,He),tt=null;l&&(tt=ce(fe({},c?"height":"maxHeight",l),Xi),_&&(tt.overflowY="hidden",g&&(tt.overflowX="hidden"),ve&&(tt.pointerEvents="none")));var Ze={};return H&&(Ze.dir="rtl"),i.createElement("div",Ae({ref:ge,style:ce(ce({},m),{},{position:"relative"}),className:j},Ze,P),i.createElement(uo,{onResize:_e},i.createElement(C,{className:"".concat(o,"-holder"),style:tt,ref:K,onScroll:Y,onMouseEnter:Pe},i.createElement(Ro,{prefixCls:o,height:Ie,offsetX:M,offsetY:ke,scrollWidth:g,onInnerResize:D,ref:te,innerProps:x,rtl:H,extra:Ht},Ft))),O&&Ie>l&&i.createElement(Xn,{ref:qe,prefixCls:o,scrollOffset:k,scrollRange:Ie,rtl:H,onScroll:J,onStartMove:me,onStopMove:we,spinSize:Ot,containerSize:xe.height,style:I==null?void 0:I.verticalScrollBar,thumbStyle:I==null?void 0:I.verticalScrollBarThumb,showScrollBar:y}),O&&g>xe.width&&i.createElement(Xn,{ref:Me,prefixCls:o,scrollOffset:M,scrollRange:g,rtl:H,onScroll:J,onStartMove:me,onStopMove:we,spinSize:Nt,containerSize:xe.width,horizontal:!0,style:I==null?void 0:I.horizontalScrollBar,thumbStyle:I==null?void 0:I.horizontalScrollBarThumb,showScrollBar:y}))}var Mo=i.forwardRef(Ki);Mo.displayName="List";function Gi(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}var qi=["disabled","title","children","style","className"];function Gn(e){return typeof e=="string"||typeof e=="number"}var Ui=function(t,n){var o=ri(),r=o.prefixCls,l=o.id,a=o.open,s=o.multiple,c=o.mode,m=o.searchValue,d=o.toggleOpen,f=o.notFoundContent,p=o.onPopupScroll,u=i.useContext(wn),v=u.maxCount,g=u.flattenOptions,h=u.onActiveValue,C=u.defaultActiveFirstOption,b=u.onSelect,$=u.menuItemSelectedIcon,S=u.rawValues,x=u.fieldNames,w=u.virtual,I=u.direction,R=u.listHeight,y=u.listItemHeight,P=u.optionRender,E="".concat(r,"-item"),N=ar(function(){return g},[a,g],function(A,B){return B[0]&&A[1]!==B[1]}),z=i.useRef(null),T=i.useMemo(function(){return s&&mn(v)&&(S==null?void 0:S.size)>=v},[s,v,S==null?void 0:S.size]),D=function(B){B.preventDefault()},V=function(B){var M;(M=z.current)===null||M===void 0||M.scrollTo(typeof B=="number"?{index:B}:B)},q=i.useCallback(function(A){return c==="combobox"?!1:S.has(A)},[c,Ue(S).toString(),S.size]),_=function(B){for(var M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,oe=N.length,W=0;W<oe;W+=1){var ee=(B+W*M+oe)%oe,ve=N[ee]||{},Se=ve.group,me=ve.data;if(!Se&&!(me!=null&&me.disabled)&&(q(me.value)||!T))return ee}return-1},G=i.useState(function(){return _(0)}),O=de(G,2),H=O[0],j=O[1],Z=function(B){var M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;j(B);var oe={source:M?"keyboard":"mouse"},W=N[B];if(!W){h(null,-1,oe);return}h(W.value,B,oe)};i.useEffect(function(){Z(C!==!1?_(0):-1)},[N.length,m]);var K=i.useCallback(function(A){return c==="combobox"?String(A).toLowerCase()===m.toLowerCase():S.has(A)},[c,m,Ue(S).toString(),S.size]);i.useEffect(function(){var A=setTimeout(function(){if(!s&&a&&S.size===1){var M=Array.from(S)[0],oe=N.findIndex(function(W){var ee=W.data;return ee.value===M});oe!==-1&&(Z(oe),V(oe))}});if(a){var B;(B=z.current)===null||B===void 0||B.scrollTo(void 0)}return function(){return clearTimeout(A)}},[a,m]);var te=function(B){B!==void 0&&b(B,{selected:!S.has(B)}),s||d(!1)};if(i.useImperativeHandle(n,function(){return{onKeyDown:function(B){var M=B.which,oe=B.ctrlKey;switch(M){case re.N:case re.P:case re.UP:case re.DOWN:{var W=0;if(M===re.UP?W=-1:M===re.DOWN?W=1:Gi()&&oe&&(M===re.N?W=1:M===re.P&&(W=-1)),W!==0){var ee=_(H+W,W);V(ee),Z(ee,!0)}break}case re.TAB:case re.ENTER:{var ve,Se=N[H];Se&&!(Se!=null&&(ve=Se.data)!==null&&ve!==void 0&&ve.disabled)&&!T?te(Se.value):te(void 0),a&&B.preventDefault();break}case re.ESC:d(!1),a&&B.stopPropagation()}},onKeyUp:function(){},scrollTo:function(B){V(B)}}}),N.length===0)return i.createElement("div",{role:"listbox",id:"".concat(l,"_list"),className:"".concat(E,"-empty"),onMouseDown:D},f);var ge=Object.keys(x).map(function(A){return x[A]}),ue=function(B){return B.label};function ne(A,B){var M=A.group;return{role:M?"presentation":"option",id:"".concat(l,"_list_").concat(B)}}var k=function(B){var M=N[B];if(!M)return null;var oe=M.data||{},W=oe.value,ee=M.group,ve=_t(oe,!0),Se=ue(M);return M?i.createElement("div",Ae({"aria-label":typeof Se=="string"&&!ee?Se:null},ve,{key:B},ne(M,B),{"aria-selected":K(W)}),W):null},se={role:"listbox",id:"".concat(l,"_list")};return i.createElement(i.Fragment,null,w&&i.createElement("div",Ae({},se,{style:{height:0,width:0,overflow:"hidden"}}),k(H-1),k(H),k(H+1)),i.createElement(Mo,{itemKey:"key",ref:z,data:N,height:R,itemHeight:y,fullHeight:!1,onMouseDown:D,onScroll:p,virtual:w,direction:I,innerProps:w?null:se},function(A,B){var M=A.group,oe=A.groupOption,W=A.data,ee=A.label,ve=A.value,Se=W.key;if(M){var me,we=(me=W.title)!==null&&me!==void 0?me:Gn(ee)?ee.toString():void 0;return i.createElement("div",{className:le(E,"".concat(E,"-group"),W.className),title:we},ee!==void 0?ee:Se)}var He=W.disabled,Ee=W.title;W.children;var De=W.style,be=W.className,Ke=It(W,qi),Ge=xt(Ke,ge),ye=q(ve),Re=He||!ye&&T,Ie="".concat(E,"-option"),Oe=le(E,Ie,be,fe(fe(fe(fe({},"".concat(Ie,"-grouped"),oe),"".concat(Ie,"-active"),H===B&&!Re),"".concat(Ie,"-disabled"),Re),"".concat(Ie,"-selected"),ye)),ze=ue(A),ke=!$||typeof $=="function"||ye,We=typeof ze=="number"?ze:ze||ve,je=Gn(We)?We.toString():void 0;return Ee!==void 0&&(je=Ee),i.createElement("div",Ae({},_t(Ge),w?{}:ne(A,B),{"aria-selected":K(ve),className:Oe,title:je,onMouseMove:function(){H===B||Re||Z(B)},onClick:function(){Re||te(ve)},style:De}),i.createElement("div",{className:"".concat(Ie,"-content")},typeof P=="function"?P(A,{index:B}):We),i.isValidElement($)||ye,ke&&i.createElement(tn,{className:"".concat(E,"-option-state"),customizeIcon:$,customizeIconProps:{value:ve,disabled:Re,isSelected:ye}},ye?"✓":null))}))},Yi=i.forwardRef(Ui);const Qi=function(e,t){var n=i.useRef({values:new Map,options:new Map}),o=i.useMemo(function(){var l=n.current,a=l.values,s=l.options,c=e.map(function(f){if(f.label===void 0){var p;return ce(ce({},f),{},{label:(p=a.get(f.value))===null||p===void 0?void 0:p.label})}return f}),m=new Map,d=new Map;return c.forEach(function(f){m.set(f.value,f),d.set(f.value,t.get(f.value)||s.get(f.value))}),n.current.values=m,n.current.options=d,c},[e,t]),r=i.useCallback(function(l){return t.get(l)||n.current.options.get(l)},[t]);return[o,r]};function ln(e,t){return xo(e).join("").toUpperCase().includes(t)}const Zi=function(e,t,n,o,r){return i.useMemo(function(){if(!n||o===!1)return e;var l=t.options,a=t.label,s=t.value,c=[],m=typeof o=="function",d=n.toUpperCase(),f=m?o:function(u,v){return r?ln(v[r],d):v[l]?ln(v[a!=="children"?a:"label"],d):ln(v[s],d)},p=m?function(u){return fn(u)}:function(u){return u};return e.forEach(function(u){if(u[l]){var v=f(n,p(u));if(v)c.push(u);else{var g=u[l].filter(function(h){return f(n,p(h))});g.length&&c.push(ce(ce({},u),{},fe({},l,g)))}return}f(n,p(u))&&c.push(u)}),c},[e,o,r,n,t])};var qn=0,ki=lr();function Ji(){var e;return ki?(e=qn,qn+=1):e="TEST_OR_SSR",e}function ea(e){var t=i.useState(),n=de(t,2),o=n[0],r=n[1];return i.useEffect(function(){r("rc_select_".concat(Ji()))},[]),e||o}var ta=["children","value"],na=["children"];function oa(e){var t=e,n=t.key,o=t.props,r=o.children,l=o.value,a=It(o,ta);return ce({key:n,value:l!==void 0?l:n,children:r},a)}function Po(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return Cn(e).map(function(n,o){if(!i.isValidElement(n)||!n.type)return null;var r=n,l=r.type.isSelectOptGroup,a=r.key,s=r.props,c=s.children,m=It(s,na);return t||!l?oa(n):ce(ce({key:"__RC_SELECT_GRP__".concat(a===null?o:a,"__"),label:a},m),{},{options:Po(c)})}).filter(function(n){return n})}var ra=function(t,n,o,r,l){return i.useMemo(function(){var a=t,s=!t;s&&(a=Po(n));var c=new Map,m=new Map,d=function(u,v,g){g&&typeof g=="string"&&u.set(v[g],v)},f=function p(u){for(var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,g=0;g<u.length;g+=1){var h=u[g];!h[o.options]||v?(c.set(h[o.value],h),d(m,h,o.label),d(m,h,r),d(m,h,l)):p(h[o.options],!0)}};return f(a),{options:a,valueOptions:c,labelOptions:m}},[t,n,o,r,l])};function Un(e){var t=i.useRef();t.current=e;var n=i.useCallback(function(){return t.current.apply(t,arguments)},[]);return n}var ia=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],aa=["inputValue"];function la(e){return!e||wt(e)!=="object"}var sa=i.forwardRef(function(e,t){var n=e.id,o=e.mode,r=e.prefixCls,l=r===void 0?"rc-select":r,a=e.backfill,s=e.fieldNames,c=e.inputValue,m=e.searchValue,d=e.onSearch,f=e.autoClearSearchValue,p=f===void 0?!0:f,u=e.onSelect,v=e.onDeselect,g=e.dropdownMatchSelectWidth,h=g===void 0?!0:g,C=e.filterOption,b=e.filterSort,$=e.optionFilterProp,S=e.optionLabelProp,x=e.options,w=e.optionRender,I=e.children,R=e.defaultActiveFirstOption,y=e.menuItemSelectedIcon,P=e.virtual,E=e.direction,N=e.listHeight,z=N===void 0?200:N,T=e.listItemHeight,D=T===void 0?20:T,V=e.labelRender,q=e.value,_=e.defaultValue,G=e.labelInValue,O=e.onChange,H=e.maxCount,j=It(e,ia),Z=ea(n),K=gn(o),te=!!(!x&&I),ge=i.useMemo(function(){return C===void 0&&o==="combobox"?!1:C},[C,o]),ue=i.useMemo(function(){return Oo(s,te)},[JSON.stringify(s),te]),ne=zt("",{value:m!==void 0?m:c,postState:function(J){return J||""}}),k=de(ne,2),se=k[0],A=k[1],B=ra(x,I,ue,$,S),M=B.valueOptions,oe=B.labelOptions,W=B.options,ee=i.useCallback(function(X){var J=xo(X);return J.map(function(Y){var Q,he,$e,Qe,Ve;if(la(Y))Q=Y;else{var Je;$e=Y.key,he=Y.label,Q=(Je=Y.value)!==null&&Je!==void 0?Je:$e}var Pe=M.get(Q);if(Pe){var et;he===void 0&&(he=Pe==null?void 0:Pe[S||ue.label]),$e===void 0&&($e=(et=Pe==null?void 0:Pe.key)!==null&&et!==void 0?et:Q),Qe=Pe==null?void 0:Pe.disabled,Ve=Pe==null?void 0:Pe.title}return{label:he,value:Q,key:$e,disabled:Qe,title:Ve}})},[ue,S,M]),ve=zt(_,{value:q}),Se=de(ve,2),me=Se[0],we=Se[1],He=i.useMemo(function(){var X,J=K&&me===null?[]:me,Y=ee(J);return o==="combobox"&&mi((X=Y[0])===null||X===void 0?void 0:X.value)?[]:Y},[me,ee,o,K]),Ee=Qi(He,M),De=de(Ee,2),be=De[0],Ke=De[1],Ge=i.useMemo(function(){if(!o&&be.length===1){var X=be[0];if(X.value===null&&(X.label===null||X.label===void 0))return[]}return be.map(function(J){var Y;return ce(ce({},J),{},{label:(Y=typeof V=="function"?V(J):J.label)!==null&&Y!==void 0?Y:J.value})})},[o,be,V]),ye=i.useMemo(function(){return new Set(be.map(function(X){return X.value}))},[be]);i.useEffect(function(){if(o==="combobox"){var X,J=(X=be[0])===null||X===void 0?void 0:X.value;A(di(J)?String(J):"")}},[be]);var Re=Un(function(X,J){var Y=J??X;return fe(fe({},ue.value,X),ue.label,Y)}),Ie=i.useMemo(function(){if(o!=="tags")return W;var X=Ue(W),J=function(Q){return M.has(Q)};return Ue(be).sort(function(Y,Q){return Y.value<Q.value?-1:1}).forEach(function(Y){var Q=Y.value;J(Q)||X.push(Re(Q,Y.label))}),X},[Re,W,M,be,o]),Oe=Zi(Ie,ue,se,ge,$),ze=i.useMemo(function(){return o!=="tags"||!se||Oe.some(function(X){return X[$||"value"]===se})||Oe.some(function(X){return X[ue.value]===se})?Oe:[Re(se)].concat(Ue(Oe))},[Re,$,o,Oe,se,ue]),ke=function X(J){var Y=Ue(J).sort(function(Q,he){return b(Q,he,{searchValue:se})});return Y.map(function(Q){return Array.isArray(Q.options)?ce(ce({},Q),{},{options:Q.options.length>0?X(Q.options):Q.options}):Q})},We=i.useMemo(function(){return b?ke(ze):ze},[ze,b,se]),je=i.useMemo(function(){return wi(We,{fieldNames:ue,childrenAsData:te})},[We,ue,te]),xe=function(J){var Y=ee(J);if(we(Y),O&&(Y.length!==be.length||Y.some(function($e,Qe){var Ve;return((Ve=be[Qe])===null||Ve===void 0?void 0:Ve.value)!==($e==null?void 0:$e.value)}))){var Q=G?Y:Y.map(function($e){return $e.value}),he=Y.map(function($e){return fn(Ke($e.value))});O(K?Q:Q[0],K?he:he[0])}},ht=i.useState(null),_e=de(ht,2),qe=_e[0],Me=_e[1],Nt=i.useState(0),Ot=de(Nt,2),ut=Ot[0],bt=Ot[1],Rt=R!==void 0?R:o!=="combobox",lt=i.useCallback(function(X,J){var Y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},Q=Y.source,he=Q===void 0?"keyboard":Q;bt(J),a&&o==="combobox"&&X!==null&&he==="keyboard"&&Me(String(X))},[a,o]),Ye=function(J,Y,Q){var he=function(){var tt,Ze=Ke(J);return[G?{label:Ze==null?void 0:Ze[ue.label],value:J,key:(tt=Ze==null?void 0:Ze.key)!==null&&tt!==void 0?tt:J}:J,fn(Ze)]};if(Y&&u){var $e=he(),Qe=de($e,2),Ve=Qe[0],Je=Qe[1];u(Ve,Je)}else if(!Y&&v&&Q!=="clear"){var Pe=he(),et=de(Pe,2),gt=et[0],Ht=et[1];v(gt,Ht)}},dt=Un(function(X,J){var Y,Q=K?J.selected:!0;Q?Y=K?[].concat(Ue(be),[X]):[X]:Y=be.filter(function(he){return he.value!==X}),xe(Y),Ye(X,Q),o==="combobox"?Me(""):(!gn||p)&&(A(""),Me(""))}),mt=function(J,Y){xe(J);var Q=Y.type,he=Y.values;(Q==="remove"||Q==="clear")&&he.forEach(function($e){Ye($e.value,!1,Q)})},Be=function(J,Y){if(A(J),Me(null),Y.source==="submit"){var Q=(J||"").trim();if(Q){var he=Array.from(new Set([].concat(Ue(ye),[Q])));xe(he),Ye(Q,!0),A("")}return}Y.source!=="blur"&&(o==="combobox"&&xe(J),d==null||d(J))},ft=function(J){var Y=J;o!=="tags"&&(Y=J.map(function(he){var $e=oe.get(he);return $e==null?void 0:$e.value}).filter(function(he){return he!==void 0}));var Q=Array.from(new Set([].concat(Ue(ye),Ue(Y))));xe(Q),Q.forEach(function(he){Ye(he,!0)})},ot=i.useMemo(function(){var X=P!==!1&&h!==!1;return ce(ce({},B),{},{flattenOptions:je,onActiveValue:lt,defaultActiveFirstOption:Rt,onSelect:dt,menuItemSelectedIcon:y,rawValues:ye,fieldNames:ue,virtual:X,direction:E,listHeight:z,listItemHeight:D,childrenAsData:te,maxCount:H,optionRender:w})},[H,B,je,lt,Rt,dt,y,ye,ue,P,h,E,z,D,te,w]);return i.createElement(wn.Provider,{value:ot},i.createElement(Ri,Ae({},j,{id:Z,prefixCls:l,ref:t,omitDomProps:aa,mode:o,displayValues:Ge,onDisplayValuesChange:mt,direction:E,searchValue:se,onSearch:Be,autoClearSearchValue:p,onSearchSplit:ft,dropdownMatchSelectWidth:h,OptionList:Yi,emptyOptions:!je.length,activeValue:qe,activeDescendantId:"".concat(Z,"_list_").concat(ut)})))}),En=sa;En.Option=xn;En.OptGroup=In;const ca=()=>{const[,e]=kt(),[t]=$n("Empty"),o=new Dt(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return i.createElement("svg",{style:o,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},i.createElement("title",null,(t==null?void 0:t.description)||"Empty"),i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("g",{transform:"translate(24 31.67)"},i.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),i.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),i.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),i.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),i.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),i.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),i.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},i.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),i.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},ua=()=>{const[,e]=kt(),[t]=$n("Empty"),{colorFill:n,colorFillTertiary:o,colorFillQuaternary:r,colorBgContainer:l}=e,{borderColor:a,shadowColor:s,contentColor:c}=i.useMemo(()=>({borderColor:new Dt(n).onBackground(l).toHexString(),shadowColor:new Dt(o).onBackground(l).toHexString(),contentColor:new Dt(r).onBackground(l).toHexString()}),[n,o,r,l]);return i.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},i.createElement("title",null,(t==null?void 0:t.description)||"Empty"),i.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},i.createElement("ellipse",{fill:s,cx:"32",cy:"33",rx:"32",ry:"7"}),i.createElement("g",{fillRule:"nonzero",stroke:a},i.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),i.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:c}))))},da=e=>{const{componentCls:t,margin:n,marginXS:o,marginXL:r,fontSize:l,lineHeight:a}=e;return{[t]:{marginInline:o,fontSize:l,lineHeight:a,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:o,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:n},"&-normal":{marginBlock:r,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:o,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}},ma=Et("Empty",e=>{const{componentCls:t,controlHeightLG:n,calc:o}=e,r=at(e,{emptyImgCls:`${t}-img`,emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()});return[da(r)]});var fa=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Do=i.createElement(ca,null),zo=i.createElement(ua,null),$t=e=>{const{className:t,rootClassName:n,prefixCls:o,image:r=Do,description:l,children:a,imageStyle:s,style:c,classNames:m,styles:d}=e,f=fa(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:p,direction:u,className:v,style:g,classNames:h,styles:C}=hn("empty"),b=p("empty",o),[$,S,x]=ma(b),[w]=$n("Empty"),I=typeof l<"u"?l:w==null?void 0:w.description,R=typeof I=="string"?I:"empty";let y=null;return typeof r=="string"?y=i.createElement("img",{alt:R,src:r}):y=r,$(i.createElement("div",Object.assign({className:le(S,x,b,v,{[`${b}-normal`]:r===zo,[`${b}-rtl`]:u==="rtl"},t,n,h.root,m==null?void 0:m.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},C.root),g),d==null?void 0:d.root),c)},f),i.createElement("div",{className:le(`${b}-image`,h.image,m==null?void 0:m.image),style:Object.assign(Object.assign(Object.assign({},s),C.image),d==null?void 0:d.image)},y),I&&i.createElement("div",{className:le(`${b}-description`,h.description,m==null?void 0:m.description),style:Object.assign(Object.assign({},C.description),d==null?void 0:d.description)},I),a&&i.createElement("div",{className:le(`${b}-footer`,h.footer,m==null?void 0:m.footer),style:Object.assign(Object.assign({},C.footer),d==null?void 0:d.footer)},a)))};$t.PRESENTED_IMAGE_DEFAULT=Do;$t.PRESENTED_IMAGE_SIMPLE=zo;const ga=e=>{const{componentName:t}=e,{getPrefixCls:n}=i.useContext(nt),o=n("empty");switch(t){case"Table":case"List":return yt.createElement($t,{image:$t.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return yt.createElement($t,{image:$t.PRESENTED_IMAGE_SIMPLE,className:`${o}-small`});case"Table.filter":return null;default:return yt.createElement($t,null)}},va=e=>{const n={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:e==="scroll"?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},n),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},n),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},n),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},n),{points:["br","tr"],offset:[0,-4]})}};function pa(e,t){return e||va(t)}const Yn=e=>{const{optionHeight:t,optionFontSize:n,optionLineHeight:o,optionPadding:r}=e;return{position:"relative",display:"block",minHeight:t,padding:r,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}},ha=e=>{const{antCls:t,componentCls:n}=e,o=`${n}-item`,r=`&${t}-slide-up-enter${t}-slide-up-enter-active`,l=`&${t}-slide-up-appear${t}-slide-up-appear-active`,a=`&${t}-slide-up-leave${t}-slide-up-leave-active`,s=`${n}-dropdown-placement-`,c=`${o}-option-selected`;return[{[`${n}-dropdown`]:Object.assign(Object.assign({},vt(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
          ${r}${s}bottomLeft,
          ${l}${s}bottomLeft
        `]:{animationName:bo},[`
          ${r}${s}topLeft,
          ${l}${s}topLeft,
          ${r}${s}topRight,
          ${l}${s}topRight
        `]:{animationName:ho},[`${a}${s}bottomLeft`]:{animationName:po},[`
          ${a}${s}topLeft,
          ${a}${s}topRight
        `]:{animationName:vo},"&-hidden":{display:"none"},[o]:Object.assign(Object.assign({},Yn(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},qt),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${o}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${o}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${o}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${o}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},Yn(e)),{color:e.colorTextDisabled})}),[`${c}:has(+ ${c})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${c}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},Tt(e,"slide-up"),Tt(e,"slide-down"),Ut(e,"move-up"),Ut(e,"move-down")]},ba=e=>{const{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:o,INTERNAL_FIXED_ITEM_MARGIN:r}=e,l=e.max(e.calc(n).sub(o).equal(),0),a=e.max(e.calc(l).sub(r).equal(),0);return{basePadding:l,containerPadding:a,itemHeight:L(t),itemLineHeight:L(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},Sa=e=>{const{multipleSelectItemHeight:t,selectHeight:n,lineWidth:o}=e;return e.calc(n).sub(t).div(2).sub(o).equal()},Ca=e=>{const{componentCls:t,iconCls:n,borderRadiusSM:o,motionDurationSlow:r,paddingXS:l,multipleItemColorDisabled:a,multipleItemBorderColorDisabled:s,colorIcon:c,colorIconHover:m,INTERNAL_FIXED_ITEM_MARGIN:d}=e;return{[`${t}-selection-overflow`]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:d,borderRadius:o,cursor:"default",transition:`font-size ${r}, line-height ${r}, height ${r}`,marginInlineEnd:e.calc(d).mul(2).equal(),paddingInlineStart:l,paddingInlineEnd:e.calc(l).div(2).equal(),[`${t}-disabled&`]:{color:a,borderColor:s,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(l).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},bn()),{display:"inline-flex",alignItems:"center",color:c,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${n}`]:{verticalAlign:"-0.2em"},"&:hover":{color:m}})}}}},$a=(e,t)=>{const{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:o}=e,r=`${n}-selection-overflow`,l=e.multipleSelectItemHeight,a=Sa(e),s=t?`${n}-${t}`:"",c=ba(e);return{[`${n}-multiple${s}`]:Object.assign(Object.assign({},Ca(e)),{[`${n}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:c.basePadding,paddingBlock:c.containerPadding,borderRadius:e.borderRadius,[`${n}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${L(o)} 0`,lineHeight:L(l),visibility:"hidden",content:'"\\a0"'}},[`${n}-selection-item`]:{height:c.itemHeight,lineHeight:L(c.itemLineHeight)},[`${n}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:L(l),marginBlock:o}},[`${n}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal()},[`${r}-item + ${r}-item,
        ${n}-prefix + ${n}-selection-wrap
      `]:{[`${n}-selection-search`]:{marginInlineStart:0},[`${n}-selection-placeholder`]:{insetInlineStart:0}},[`${r}-item-suffix`]:{minHeight:c.itemHeight,marginBlock:o},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(a).equal(),"\n          &-input,\n          &-mirror\n        ":{height:l,fontFamily:e.fontFamily,lineHeight:L(l),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function sn(e,t){const{componentCls:n}=e,o=t?`${n}-${t}`:"",r={[`${n}-multiple${o}`]:{fontSize:e.fontSize,[`${n}-selector`]:{[`${n}-show-search&`]:{cursor:"text"}},[`
        &${n}-show-arrow ${n}-selector,
        &${n}-allow-clear ${n}-selector
      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[$a(e,t),r]}const ya=e=>{const{componentCls:t}=e,n=at(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),o=at(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[sn(e),sn(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},sn(o,"lg")]};function cn(e,t){const{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:r}=e,l=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),a=t?`${n}-${t}`:"";return{[`${n}-single${a}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${n}-selector`]:Object.assign(Object.assign({},vt(e,!0)),{display:"flex",borderRadius:r,flex:"1 1 auto",[`${n}-selection-wrap:after`]:{lineHeight:L(l)},[`${n}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`
          ${n}-selection-item,
          ${n}-selection-placeholder
        `]:{display:"block",padding:0,lineHeight:L(l),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${n}-selection-item:empty:after`,`${n}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${n}-show-arrow ${n}-selection-item,
        &${n}-show-arrow ${n}-selection-search,
        &${n}-show-arrow ${n}-selection-placeholder
      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${L(o)}`,[`${n}-selection-search-input`]:{height:l,fontSize:e.fontSize},"&:after":{lineHeight:L(l)}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${L(o)}`,"&:after":{display:"none"}}}}}}}function wa(e){const{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[cn(e),cn(at(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${L(n)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},[`
            &${t}-show-arrow ${t}-selection-item,
            &${t}-show-arrow ${t}-selection-placeholder
          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},cn(at(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const Ia=e=>{const{fontSize:t,lineHeight:n,lineWidth:o,controlHeight:r,controlHeightSM:l,controlHeightLG:a,paddingXXS:s,controlPaddingHorizontal:c,zIndexPopupBase:m,colorText:d,fontWeightStrong:f,controlItemBgActive:p,controlItemBgHover:u,colorBgContainer:v,colorFillSecondary:g,colorBgContainerDisabled:h,colorTextDisabled:C,colorPrimaryHover:b,colorPrimary:$,controlOutline:S}=e,x=s*2,w=o*2,I=Math.min(r-x,r-w),R=Math.min(l-x,l-w),y=Math.min(a-x,a-w);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(s/2),zIndexPopup:m+50,optionSelectedColor:d,optionSelectedFontWeight:f,optionSelectedBg:p,optionActiveBg:u,optionPadding:`${(r-t*n)/2}px ${c}px`,optionFontSize:t,optionLineHeight:n,optionHeight:r,selectorBg:v,clearBg:v,singleItemHeightLG:a,multipleItemBg:g,multipleItemBorderColor:"transparent",multipleItemHeight:I,multipleItemHeightSM:R,multipleItemHeightLG:y,multipleSelectorBgDisabled:h,multipleItemColorDisabled:C,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(e.fontSize*1.25),hoverBorderColor:b,activeBorderColor:$,activeOutlineColor:S,selectAffixPadding:s}},To=(e,t)=>{const{componentCls:n,antCls:o,controlOutlineWidth:r}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{border:`${L(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${L(r)} ${t.activeOutlineColor}`,outline:0},[`${n}-prefix`]:{color:t.color}}}},Qn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},To(e,t))}),xa=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},To(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Qn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Qn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),No=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{background:t.bg,border:`${L(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{background:t.hoverBg},[`${n}-focused& ${n}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},Zn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},No(e,t))}),Ea=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},No(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),Zn(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),Zn(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),Oa=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${L(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),Ho=(e,t)=>{const{componentCls:n,antCls:o}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{borderWidth:`0 0 ${L(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${n}-disabled):not(${n}-customize-input):not(${o}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${n}-prefix`]:{color:t.color}}}},kn=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Ho(e,t))}),Ra=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},Ho(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),kn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),kn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),Ba=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},xa(e)),Ea(e)),Oa(e)),Ra(e))}),Ma=e=>{const{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},Pa=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none","-webkit-appearance":"none"}}}},Da=e=>{const{antCls:t,componentCls:n,inputPaddingHorizontalBase:o,iconCls:r}=e;return{[n]:Object.assign(Object.assign({},vt(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:Object.assign(Object.assign({},Ma(e)),Pa(e)),[`${n}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},qt),{[`> ${t}-typography`]:{display:"inline"}}),[`${n}-selection-placeholder`]:Object.assign(Object.assign({},qt),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:Object.assign(Object.assign({},bn()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[r]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${n}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:o,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorTextTertiary}},[`&:hover ${n}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}}),[`${n}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:e.calc(o).add(e.fontSize).add(e.paddingXS).equal()}}}}}},za=e=>{const{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},Da(e),wa(e),ya(e),ha(e),{[`${t}-rtl`]:{direction:"rtl"}},gr(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]},Ta=Et("Select",(e,t)=>{let{rootPrefixCls:n}=t;const o=at(e,{rootPrefixCls:n,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[za(o),Ba(o)]},Ia,{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});var Na={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"},Ha=function(t,n){return i.createElement(Jt,Ae({},t,{ref:n,icon:Na}))},La=i.forwardRef(Ha),ja={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},_a=function(t,n){return i.createElement(Jt,Ae({},t,{ref:n,icon:ja}))},Aa=i.forwardRef(_a);function Wa(e){let{suffixIcon:t,clearIcon:n,menuItemSelectedIcon:o,removeIcon:r,loading:l,multiple:a,hasFeedback:s,prefixCls:c,showSuffixIcon:m,feedbackIcon:d,showArrow:f,componentName:p}=e;const u=n??i.createElement(vr,null),v=b=>t===null&&!s&&!f?null:i.createElement(i.Fragment,null,m!==!1&&b,s&&d);let g=null;if(t!==void 0)g=v(t);else if(l)g=v(i.createElement(hr,{spin:!0}));else{const b=`${c}-suffix`;g=$=>{let{open:S,showSearch:x}=$;return v(S&&x?i.createElement(br,{className:b}):i.createElement(Aa,{className:b}))}}let h=null;o!==void 0?h=o:a?h=i.createElement(La,null):h=null;let C=null;return r!==void 0?C=r:C=i.createElement(pr,null),{clearIcon:u,suffixIcon:g,itemIcon:h,removeIcon:C}}function Va(e,t){return t!==void 0?t:e!==null}var Fa=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Lo="SECRET_COMBOBOX_MODE_DO_NOT_USE",Xa=(e,t)=>{var n;const{prefixCls:o,bordered:r,className:l,rootClassName:a,getPopupContainer:s,popupClassName:c,dropdownClassName:m,listHeight:d=256,placement:f,listItemHeight:p,size:u,disabled:v,notFoundContent:g,status:h,builtinPlacements:C,dropdownMatchSelectWidth:b,popupMatchSelectWidth:$,direction:S,style:x,allowClear:w,variant:I,dropdownStyle:R,transitionName:y,tagRender:P,maxCount:E,prefix:N}=e,z=Fa(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix"]),{getPopupContainer:T,getPrefixCls:D,renderEmpty:V,direction:q,virtual:_,popupMatchSelectWidth:G,popupOverflow:O}=i.useContext(nt),H=hn("select"),[,j]=kt(),Z=p??(j==null?void 0:j.controlHeight),K=D("select",o),te=D(),ge=S??q,{compactSize:ue,compactItemClassnames:ne}=mo(K,ge),[k,se]=Sr("select",I,r),A=At(K),[B,M,oe]=Ta(K,A),W=i.useMemo(()=>{const{mode:qe}=e;if(qe!=="combobox")return qe===Lo?"combobox":qe},[e.mode]),ee=W==="multiple"||W==="tags",ve=Va(e.suffixIcon,e.showArrow),Se=(n=$??b)!==null&&n!==void 0?n:G,{status:me,hasFeedback:we,isFormItemInput:He,feedbackIcon:Ee}=i.useContext(fo),De=yr(me,h);let be;g!==void 0?be=g:W==="combobox"?be=null:be=(V==null?void 0:V("Select"))||i.createElement(ga,{componentName:"Select"});const{suffixIcon:Ke,itemIcon:Ge,removeIcon:ye,clearIcon:Re}=Wa(Object.assign(Object.assign({},z),{multiple:ee,hasFeedback:we,feedbackIcon:Ee,showSuffixIcon:ve,prefixCls:K,componentName:"Select"})),Ie=w===!0?{clearIcon:Re}:w,Oe=xt(z,["suffixIcon","itemIcon"]),ze=le(c||m,{[`${K}-dropdown-${ge}`]:ge==="rtl"},a,oe,A,M),ke=go(qe=>{var Me;return(Me=u??ue)!==null&&Me!==void 0?Me:qe}),We=i.useContext(co),je=v??We,xe=le({[`${K}-lg`]:ke==="large",[`${K}-sm`]:ke==="small",[`${K}-rtl`]:ge==="rtl",[`${K}-${k}`]:se,[`${K}-in-form-item`]:He},Cr(K,De,we),ne,H.className,l,a,oe,A,M),ht=i.useMemo(()=>f!==void 0?f:ge==="rtl"?"bottomRight":"bottomLeft",[f,ge]),[_e]=yn("SelectLike",R==null?void 0:R.zIndex);return B(i.createElement(En,Object.assign({ref:t,virtual:_,showSearch:H.showSearch},Oe,{style:Object.assign(Object.assign({},H.style),x),dropdownMatchSelectWidth:Se,transitionName:$r(te,"slide-up",y),builtinPlacements:pa(C,O),listHeight:d,listItemHeight:Z,mode:W,prefixCls:K,placement:ht,direction:ge,prefix:N,suffixIcon:Ke,menuItemSelectedIcon:Ge,removeIcon:ye,allowClear:Ie,notFoundContent:be,className:xe,getPopupContainer:s||T,dropdownClassName:ze,disabled:je,dropdownStyle:Object.assign(Object.assign({},R),{zIndex:_e}),maxCount:ee?E:void 0,tagRender:ee?P:void 0})))},Wt=i.forwardRef(Xa),Ka=$o(Wt,"dropdownAlign");Wt.SECRET_COMBOBOX_MODE_DO_NOT_USE=Lo;Wt.Option=xn;Wt.OptGroup=In;Wt._InternalPanelDoNotUseOrYouWillBeFired=Ka;var Ga={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},qa=function(t,n){return i.createElement(Jt,Ae({},t,{ref:n,icon:Ga}))},pn=i.forwardRef(qa);const Ua=e=>typeof e!="object"&&typeof e!="function"||e===null;var Ya={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},Qa=function(t,n){return i.createElement(Jt,Ae({},t,{ref:n,icon:Ya}))},Za=i.forwardRef(Qa);const ka=i.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}}),Ja=e=>{const{antCls:t,componentCls:n,colorText:o,footerBg:r,headerHeight:l,headerPadding:a,headerColor:s,footerPadding:c,fontSize:m,bodyBg:d,headerBg:f}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:d,"&, *":{boxSizing:"border-box"},[`&${n}-has-sider`]:{flexDirection:"row",[`> ${n}, > ${n}-content`]:{width:0}},[`${n}-header, &${n}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${n}-header`]:{height:l,padding:a,color:s,lineHeight:L(l),background:f,[`${t}-menu`]:{lineHeight:"inherit"}},[`${n}-footer`]:{padding:c,color:o,fontSize:m,background:r},[`${n}-content`]:{flex:"auto",color:o,minHeight:0}}},jo=e=>{const{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:r,controlHeightSM:l,marginXXS:a,colorTextLightSolid:s,colorBgContainer:c}=e,m=o*1.25;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:n*2,headerPadding:`0 ${m}px`,headerColor:r,footerPadding:`${l}px ${m}px`,footerBg:t,siderBg:"#001529",triggerHeight:o+a*2,triggerBg:"#002140",triggerColor:s,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:c,lightTriggerBg:c,lightTriggerColor:r}},_o=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],Gl=Et("Layout",e=>[Ja(e)],jo,{deprecatedTokens:_o}),el=e=>{const{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:r,antCls:l,triggerHeight:a,triggerColor:s,triggerBg:c,headerHeight:m,zeroTriggerWidth:d,zeroTriggerHeight:f,borderRadiusLG:p,lightSiderBg:u,lightTriggerColor:v,lightTriggerBg:g,bodyBg:h}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:`all ${o}, background 0s`,"&-has-trigger":{paddingBottom:a},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${l}-menu${l}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:a,color:s,lineHeight:L(a),textAlign:"center",background:c,cursor:"pointer",transition:`all ${o}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:m,insetInlineEnd:e.calc(d).mul(-1).equal(),zIndex:1,width:d,height:f,color:s,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:`0 ${L(p)} ${L(p)} 0`,cursor:"pointer",transition:`background ${r} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${r}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(d).mul(-1).equal(),borderRadius:`${L(p)} 0 0 ${L(p)}`}},"&-light":{background:u,[`${t}-trigger`]:{color:v,background:g},[`${t}-zero-width-trigger`]:{color:v,background:g,border:`1px solid ${h}`,borderInlineStart:0}}}}},tl=Et(["Layout","Sider"],e=>[el(e)],jo,{deprecatedTokens:_o});var nl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Jn={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},ol=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),On=i.createContext({}),rl=(()=>{let e=0;return function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,`${t}${e}`}})(),ql=i.forwardRef((e,t)=>{const{prefixCls:n,className:o,trigger:r,children:l,defaultCollapsed:a=!1,theme:s="dark",style:c={},collapsible:m=!1,reverseArrow:d=!1,width:f=200,collapsedWidth:p=80,zeroWidthTriggerStyle:u,breakpoint:v,onCollapse:g,onBreakpoint:h}=e,C=nl(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:b}=i.useContext(ka),[$,S]=i.useState("collapsed"in e?e.collapsed:a),[x,w]=i.useState(!1);i.useEffect(()=>{"collapsed"in e&&S(e.collapsed)},[e.collapsed]);const I=(ne,k)=>{"collapsed"in e||S(ne),g==null||g(ne,k)},{getPrefixCls:R,direction:y}=i.useContext(nt),P=R("layout-sider",n),[E,N,z]=tl(P),T=i.useRef(null);T.current=ne=>{w(ne.matches),h==null||h(ne.matches),$!==ne.matches&&I(ne.matches,"responsive")},i.useEffect(()=>{function ne(se){return T.current(se)}let k;if(typeof window<"u"){const{matchMedia:se}=window;if(se&&v&&v in Jn){k=se(`screen and (max-width: ${Jn[v]})`);try{k.addEventListener("change",ne)}catch{k.addListener(ne)}ne(k)}}return()=>{try{k==null||k.removeEventListener("change",ne)}catch{k==null||k.removeListener(ne)}}},[v]),i.useEffect(()=>{const ne=rl("ant-sider-");return b.addSider(ne),()=>b.removeSider(ne)},[]);const D=()=>{I(!$,"clickTrigger")},V=xt(C,["collapsed"]),q=$?p:f,_=ol(q)?`${q}px`:String(q),G=parseFloat(String(p||0))===0?i.createElement("span",{onClick:D,className:le(`${P}-zero-width-trigger`,`${P}-zero-width-trigger-${d?"right":"left"}`),style:u},r||i.createElement(Za,null)):null,O=y==="rtl"==!d,Z={expanded:O?i.createElement(dn,null):i.createElement(pn,null),collapsed:O?i.createElement(pn,null):i.createElement(dn,null)}[$?"collapsed":"expanded"],K=r!==null?G||i.createElement("div",{className:`${P}-trigger`,onClick:D,style:{width:_}},r||Z):null,te=Object.assign(Object.assign({},c),{flex:`0 0 ${_}`,maxWidth:_,minWidth:_,width:_}),ge=le(P,`${P}-${s}`,{[`${P}-collapsed`]:!!$,[`${P}-has-trigger`]:m&&r!==null&&!G,[`${P}-below`]:!!x,[`${P}-zero-width`]:parseFloat(_)===0},o,N,z),ue=i.useMemo(()=>({siderCollapsed:$}),[$]);return E(i.createElement(On.Provider,{value:ue},i.createElement("aside",Object.assign({className:ge},V,{style:te,ref:t}),i.createElement("div",{className:`${P}-children`},l),m||x&&G?K:null)))}),Yt=i.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var il=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Ao=e=>{const{prefixCls:t,className:n,dashed:o}=e,r=il(e,["prefixCls","className","dashed"]),{getPrefixCls:l}=i.useContext(nt),a=l("menu",t),s=le({[`${a}-item-divider-dashed`]:!!o},n);return i.createElement(zr,Object.assign({className:s},r))},Wo=e=>{var t;const{className:n,children:o,icon:r,title:l,danger:a,extra:s}=e,{prefixCls:c,firstLevel:m,direction:d,disableMenuItemTitleTooltip:f,inlineCollapsed:p}=i.useContext(Yt),u=$=>{const S=o==null?void 0:o[0],x=i.createElement("span",{className:le(`${c}-title-content`,{[`${c}-title-content-with-extra`]:!!s||s===0})},o);return(!r||i.isValidElement(o)&&o.type==="span")&&o&&$&&m&&typeof S=="string"?i.createElement("div",{className:`${c}-inline-collapsed-noicon`},S.charAt(0)):x},{siderCollapsed:v}=i.useContext(On);let g=l;typeof l>"u"?g=m?o:"":l===!1&&(g="");const h={title:g};!v&&!p&&(h.title=null,h.open=!1);const C=Cn(o).length;let b=i.createElement(Tr,Object.assign({},xt(e,["title","icon","danger"]),{className:le({[`${c}-item-danger`]:a,[`${c}-item-only-child`]:(r?C+1:C)===1},n),title:typeof l=="string"?l:void 0}),en(r,{className:le(i.isValidElement(r)?(t=r.props)===null||t===void 0?void 0:t.className:"",`${c}-item-icon`)}),u(p));return f||(b=i.createElement(Ar,Object.assign({},h,{placement:d==="rtl"?"left":"right",classNames:{root:`${c}-inline-collapsed-tooltip`}}),b)),b};var al=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Qt=i.createContext(null),ll=i.forwardRef((e,t)=>{const{children:n}=e,o=al(e,["children"]),r=i.useContext(Qt),l=i.useMemo(()=>Object.assign(Object.assign({},r),o),[r,o.prefixCls,o.mode,o.selectable,o.rootClassName]),a=sr(n),s=so(t,a?cr(n):null);return i.createElement(Qt.Provider,{value:l},i.createElement(wr,{space:!0},a?i.cloneElement(n,{ref:s}):n))}),sl=e=>{const{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:r,lineWidth:l,lineType:a,itemPaddingInline:s}=e;return{[`${t}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${L(l)} ${a} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:s},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${n}`,`background ${n}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},cl=e=>{let{componentCls:t,menuArrowOffset:n,calc:o}=e;return{[`${t}-rtl`]:{direction:"rtl"},[`${t}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${t}-rtl${t}-vertical,
    ${t}-submenu-rtl ${t}-vertical`]:{[`${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${L(o(n).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${L(n)})`}}}}},eo=e=>Object.assign({},Sn(e)),to=(e,t)=>{const{componentCls:n,itemColor:o,itemSelectedColor:r,subMenuItemSelectedColor:l,groupTitleColor:a,itemBg:s,subMenuItemBg:c,itemSelectedBg:m,activeBarHeight:d,activeBarWidth:f,activeBarBorderWidth:p,motionDurationSlow:u,motionEaseInOut:v,motionEaseOut:g,itemPaddingInline:h,motionDurationMid:C,itemHoverColor:b,lineType:$,colorSplit:S,itemDisabledColor:x,dangerItemColor:w,dangerItemHoverColor:I,dangerItemSelectedColor:R,dangerItemActiveBg:y,dangerItemSelectedBg:P,popupBg:E,itemHoverBg:N,itemActiveBg:z,menuSubMenuBg:T,horizontalItemSelectedColor:D,horizontalItemSelectedBg:V,horizontalItemBorderRadius:q,horizontalItemHoverBg:_}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:o,background:s,[`&${n}-root:focus-visible`]:Object.assign({},eo(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:a}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:l},[`${n}-item, ${n}-submenu-title`]:{color:o,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},eo(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${x} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:b}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:N},"&:active":{backgroundColor:z}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:N},"&:active":{backgroundColor:z}}},[`${n}-item-danger`]:{color:w,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:I}},[`&${n}-item:active`]:{background:y}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:r,[`&${n}-item-danger`]:{color:R},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:m,[`&${n}-item-danger`]:{backgroundColor:P}},[`&${n}-submenu > ${n}`]:{backgroundColor:T},[`&${n}-popup > ${n}`]:{backgroundColor:E},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:E},[`&${n}-horizontal`]:Object.assign(Object.assign({},t==="dark"?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:p,marginTop:e.calc(p).mul(-1).equal(),marginBottom:0,borderRadius:q,"&::after":{position:"absolute",insetInline:h,bottom:0,borderBottom:`${L(d)} solid transparent`,transition:`border-color ${u} ${v}`,content:'""'},"&:hover, &-active, &-open":{background:_,"&::after":{borderBottomWidth:d,borderBottomColor:D}},"&-selected":{color:D,backgroundColor:V,"&:hover":{backgroundColor:V},"&::after":{borderBottomWidth:d,borderBottomColor:D}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${L(p)} ${$} ${S}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:c},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${L(f)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${C} ${g}`,`opacity ${C} ${g}`].join(","),content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:R}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${C} ${v}`,`opacity ${C} ${v}`].join(",")}}}}}},no=e=>{const{componentCls:t,itemHeight:n,itemMarginInline:o,padding:r,menuArrowSize:l,marginXS:a,itemMarginBlock:s,itemWidth:c,itemPaddingInline:m}=e,d=e.calc(l).add(r).add(a).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:L(n),paddingInline:m,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:s,width:c},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:L(n)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:d}}},ul=e=>{const{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:r,dropdownWidth:l,controlHeightLG:a,motionEaseOut:s,paddingXL:c,itemMarginInline:m,fontSizeLG:d,motionDurationFast:f,motionDurationSlow:p,paddingXS:u,boxShadowSecondary:v,collapsedWidth:g,collapsedIconSize:h}=e,C={height:o,lineHeight:L(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},no(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},no(e)),{boxShadow:v})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:l,maxHeight:`calc(100vh - ${L(e.calc(a).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${p}`,`background ${p}`,`padding ${f} ${s}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:C,[`& ${t}-item-group-title`]:{paddingInlineStart:c}},[`${t}-item`]:C}},{[`${t}-inline-collapsed`]:{width:g,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:d,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${L(e.calc(h).div(2).equal())} - ${L(m)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:h,lineHeight:L(o),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},qt),{paddingInline:u})}}]},oo=e=>{const{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:r,motionEaseOut:l,iconCls:a,iconSize:s,iconMarginInlineEnd:c}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${n}`,`background ${n}`,`padding calc(${n} + 0.1s) ${r}`].join(","),[`${t}-item-icon, ${a}`]:{minWidth:s,fontSize:s,transition:[`font-size ${o} ${l}`,`margin ${n} ${r}`,`color ${n}`].join(","),"+ span":{marginInlineStart:c,opacity:1,transition:[`opacity ${n} ${r}`,`margin ${n}`,`color ${n}`].join(",")}},[`${t}-item-icon`]:Object.assign({},bn()),[`&${t}-item-only-child`]:{[`> ${a}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},ro=e=>{const{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:r,menuArrowSize:l,menuArrowOffset:a}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:l,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${o}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(l).mul(.6).equal(),height:e.calc(l).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${n} ${o}`,`transform ${n} ${o}`,`top ${n} ${o}`,`color ${n} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${L(e.calc(a).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${L(a)})`}}}}},dl=e=>{const{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:r,motionDurationMid:l,motionEaseInOut:a,paddingXS:s,padding:c,colorSplit:m,lineWidth:d,zIndexPopup:f,borderRadiusLG:p,subMenuItemBorderRadius:u,menuArrowSize:v,menuArrowOffset:g,lineType:h,groupTitleLineHeight:C,groupTitleFontSize:b}=e;return[{"":{[n]:Object.assign(Object.assign({},Tn()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},vt(e)),Tn()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${L(s)} ${L(c)}`,fontSize:b,lineHeight:C,transition:`all ${r}`},[`&-horizontal ${n}-submenu`]:{transition:[`border-color ${r} ${a}`,`background ${r} ${a}`].join(",")},[`${n}-submenu, ${n}-submenu-inline`]:{transition:[`border-color ${r} ${a}`,`background ${r} ${a}`,`padding ${l} ${a}`].join(",")},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:[`background ${r} ${a}`,`padding ${r} ${a}`].join(",")},[`${n}-title-content`]:{transition:`color ${r}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:m,borderStyle:h,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),oo(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${L(e.calc(o).mul(2).equal())} ${L(c)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:f,borderRadius:p,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:p},oo(e)),ro(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:u},[`${n}-submenu-title::after`]:{transition:`transform ${r} ${a}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),ro(e)),{[`&-inline-collapsed ${n}-submenu-arrow,
        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${L(g)})`},"&::after":{transform:`rotate(45deg) translateX(${L(e.calc(g).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${L(e.calc(v).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${L(e.calc(g).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${L(g)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},ml=e=>{var t,n,o;const{colorPrimary:r,colorError:l,colorTextDisabled:a,colorErrorBg:s,colorText:c,colorTextDescription:m,colorBgContainer:d,colorFillAlter:f,colorFillContent:p,lineWidth:u,lineWidthBold:v,controlItemBgActive:g,colorBgTextHover:h,controlHeightLG:C,lineHeight:b,colorBgElevated:$,marginXXS:S,padding:x,fontSize:w,controlHeightSM:I,fontSizeLG:R,colorTextLightSolid:y,colorErrorHover:P}=e,E=(t=e.activeBarWidth)!==null&&t!==void 0?t:0,N=(n=e.activeBarBorderWidth)!==null&&n!==void 0?n:u,z=(o=e.itemMarginInline)!==null&&o!==void 0?o:e.marginXXS,T=new Dt(y).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:c,itemColor:c,colorItemTextHover:c,itemHoverColor:c,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:m,groupTitleColor:m,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:d,itemBg:d,colorItemBgHover:h,itemHoverBg:h,colorItemBgActive:p,itemActiveBg:g,colorSubItemBg:f,subMenuItemBg:f,colorItemBgSelected:g,itemSelectedBg:g,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:E,colorActiveBarHeight:v,activeBarHeight:v,colorActiveBarBorderSize:u,activeBarBorderWidth:N,colorItemTextDisabled:a,itemDisabledColor:a,colorDangerItemText:l,dangerItemColor:l,colorDangerItemTextHover:l,dangerItemHoverColor:l,colorDangerItemTextSelected:l,dangerItemSelectedColor:l,colorDangerItemBgActive:s,dangerItemActiveBg:s,colorDangerItemBgSelected:s,dangerItemSelectedBg:s,itemMarginInline:z,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:C,groupTitleLineHeight:b,collapsedWidth:C*2,popupBg:$,itemMarginBlock:S,itemPaddingInline:x,horizontalLineHeight:`${C*1.15}px`,iconSize:w,iconMarginInlineEnd:I-w,collapsedIconSize:R,groupTitleFontSize:w,darkItemDisabledColor:new Dt(y).setA(.25).toRgbString(),darkItemColor:T,darkDangerItemColor:l,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:y,darkItemSelectedBg:r,darkDangerItemSelectedBg:l,darkItemHoverBg:"transparent",darkGroupTitleColor:T,darkItemHoverColor:y,darkDangerItemHoverColor:P,darkDangerItemSelectedColor:y,darkDangerItemActiveBg:l,itemWidth:E?`calc(100% + ${N}px)`:`calc(100% - ${z*2}px)`}},fl=function(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return Et("Menu",r=>{const{colorBgElevated:l,controlHeightLG:a,fontSize:s,darkItemColor:c,darkDangerItemColor:m,darkItemBg:d,darkSubMenuItemBg:f,darkItemSelectedColor:p,darkItemSelectedBg:u,darkDangerItemSelectedBg:v,darkItemHoverBg:g,darkGroupTitleColor:h,darkItemHoverColor:C,darkItemDisabledColor:b,darkDangerItemHoverColor:$,darkDangerItemSelectedColor:S,darkDangerItemActiveBg:x,popupBg:w,darkPopupBg:I}=r,R=r.calc(s).div(7).mul(5).equal(),y=at(r,{menuArrowSize:R,menuHorizontalHeight:r.calc(a).mul(1.15).equal(),menuArrowOffset:r.calc(R).mul(.25).equal(),menuSubMenuBg:l,calc:r.calc,popupBg:w}),P=at(y,{itemColor:c,itemHoverColor:C,groupTitleColor:h,itemSelectedColor:p,subMenuItemSelectedColor:p,itemBg:d,popupBg:I,subMenuItemBg:f,itemActiveBg:"transparent",itemSelectedBg:u,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:g,itemDisabledColor:b,dangerItemColor:m,dangerItemHoverColor:$,dangerItemSelectedColor:S,dangerItemActiveBg:x,dangerItemSelectedBg:v,menuSubMenuBg:f,horizontalItemSelectedColor:p,horizontalItemSelectedBg:u});return[dl(y),sl(y),ul(y),to(y,"light"),to(P,"dark"),cl(y),Ir(y),Tt(y,"slide-up"),Tt(y,"slide-down"),Co(y,"zoom-big")]},ml,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)},Vo=e=>{var t;const{popupClassName:n,icon:o,title:r,theme:l}=e,a=i.useContext(Yt),{prefixCls:s,inlineCollapsed:c,theme:m}=a,d=Nr();let f;if(!o)f=c&&!d.length&&r&&typeof r=="string"?i.createElement("div",{className:`${s}-inline-collapsed-noicon`},r.charAt(0)):i.createElement("span",{className:`${s}-title-content`},r);else{const v=i.isValidElement(r)&&r.type==="span";f=i.createElement(i.Fragment,null,en(o,{className:le(i.isValidElement(o)?(t=o.props)===null||t===void 0?void 0:t.className:"",`${s}-item-icon`)}),v?r:i.createElement("span",{className:`${s}-title-content`},r))}const p=i.useMemo(()=>Object.assign(Object.assign({},a),{firstLevel:!1}),[a]),[u]=yn("Menu");return i.createElement(Yt.Provider,{value:p},i.createElement(Hr,Object.assign({},xt(e,["icon"]),{title:f,popupClassName:le(s,n,`${s}-${l||m}`),popupStyle:Object.assign({zIndex:u},e.popupStyle)})))};var gl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function un(e){return e===null||e===!1}const vl={item:Wo,submenu:Vo,divider:Ao},pl=i.forwardRef((e,t)=>{var n;const o=i.useContext(Qt),r=o||{},{getPrefixCls:l,getPopupContainer:a,direction:s,menu:c}=i.useContext(nt),m=l(),{prefixCls:d,className:f,style:p,theme:u="light",expandIcon:v,_internalDisableMenuItemTitleTooltip:g,inlineCollapsed:h,siderCollapsed:C,rootClassName:b,mode:$,selectable:S,onClick:x,overflowedIndicatorPopupClassName:w}=e,I=gl(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),R=xt(I,["collapsedWidth"]);(n=r.validator)===null||n===void 0||n.call(r,{mode:$});const y=Gt(function(){var j;x==null||x.apply(void 0,arguments),(j=r.onClick)===null||j===void 0||j.call(r)}),P=r.mode||$,E=S??r.selectable,N=h??C,z={horizontal:{motionName:`${m}-slide-up`},inline:xr(m),other:{motionName:`${m}-zoom-big`}},T=l("menu",d||r.prefixCls),D=At(T),[V,q,_]=fl(T,D,!o),G=le(`${T}-${u}`,c==null?void 0:c.className,f),O=i.useMemo(()=>{var j,Z;if(typeof v=="function"||un(v))return v||null;if(typeof r.expandIcon=="function"||un(r.expandIcon))return r.expandIcon||null;if(typeof(c==null?void 0:c.expandIcon)=="function"||un(c==null?void 0:c.expandIcon))return(c==null?void 0:c.expandIcon)||null;const K=(j=v??(r==null?void 0:r.expandIcon))!==null&&j!==void 0?j:c==null?void 0:c.expandIcon;return en(K,{className:le(`${T}-submenu-expand-icon`,i.isValidElement(K)?(Z=K.props)===null||Z===void 0?void 0:Z.className:void 0)})},[v,r==null?void 0:r.expandIcon,c==null?void 0:c.expandIcon,T]),H=i.useMemo(()=>({prefixCls:T,inlineCollapsed:N||!1,direction:s,firstLevel:!0,theme:u,mode:P,disableMenuItemTitleTooltip:g}),[T,N,s,g,u]);return V(i.createElement(Qt.Provider,{value:null},i.createElement(Yt.Provider,{value:H},i.createElement(Lr,Object.assign({getPopupContainer:a,overflowedIndicator:i.createElement(So,null),overflowedIndicatorPopupClassName:le(T,`${T}-${u}`,w),mode:P,selectable:E,onClick:y},R,{inlineCollapsed:N,style:Object.assign(Object.assign({},c==null?void 0:c.style),p),className:G,prefixCls:T,direction:s,defaultMotions:z,expandIcon:O,ref:t,rootClassName:le(b,q,r.rootClassName,_,D),_internalComponents:vl})))))}),Vt=i.forwardRef((e,t)=>{const n=i.useRef(null),o=i.useContext(On);return i.useImperativeHandle(t,()=>({menu:n.current,focus:r=>{var l;(l=n.current)===null||l===void 0||l.focus(r)}})),i.createElement(pl,Object.assign({ref:n},e,o))});Vt.Item=Wo;Vt.SubMenu=Vo;Vt.Divider=Ao;Vt.ItemGroup=jr;const hl=e=>{const{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:r}=e,l=`${n}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${n} ${l}`]:{[`&${l}-danger:not(${l}-disabled)`]:{color:o,"&:hover":{color:r,backgroundColor:o}}}}}},bl=e=>{const{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:r,sizePopupArrow:l,antCls:a,iconCls:s,motionDurationMid:c,paddingBlock:m,fontSize:d,dropdownEdgeChildPadding:f,colorTextDisabled:p,fontSizeIcon:u,controlPaddingHorizontal:v,colorBgElevated:g}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(l).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${a}-btn`]:{[`& > ${s}-down, & > ${a}-btn-icon > ${s}-down`]:{fontSize:u}},[`${t}-wrap`]:{position:"relative",[`${a}-btn > ${s}-down`]:{fontSize:u},[`${s}-down::before`]:{transition:`transform ${c}`}},[`${t}-wrap-open`]:{[`${s}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomLeft,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomLeft,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottom,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottom,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomRight,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:bo},[`&${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topLeft,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topLeft,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-top,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-top,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topRight,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topRight`]:{animationName:ho},[`&${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomLeft,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottom,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:po},[`&${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topLeft,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-top,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topRight`]:{animationName:vo}}},Wr(e,g,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},vt(e)),{[n]:Object.assign(Object.assign({padding:f,listStyleType:"none",backgroundColor:g,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},Nn(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${n}-item-group-title`]:{padding:`${L(m)} ${L(v)}`,color:e.colorTextDescription,transition:`all ${c}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${n}-item-icon`]:{minWidth:d,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${c}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${n}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${n}-item, ${n}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${L(m)} ${L(v)}`,color:e.colorText,fontWeight:"normal",fontSize:d,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${c}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},Nn(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:p,cursor:"not-allowed","&:hover":{color:p,backgroundColor:g,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${L(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:u,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${L(e.marginXS)}`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:e.calc(v).add(e.fontSizeSM).equal()},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:p,backgroundColor:g,cursor:"not-allowed"}},[`${n}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[Tt(e,"slide-up"),Tt(e,"slide-down"),Ut(e,"move-up"),Ut(e,"move-down"),Co(e,"zoom-big")]]},Sl=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},Vr({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),Fr(e)),Cl=Et("Dropdown",e=>{const{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:r}=e,l=at(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[bl(l),hl(l)]},Sl,{resetStyle:!1}),nn=e=>{var t;const{menu:n,arrow:o,prefixCls:r,children:l,trigger:a,disabled:s,dropdownRender:c,getPopupContainer:m,overlayClassName:d,rootClassName:f,overlayStyle:p,open:u,onOpenChange:v,visible:g,onVisibleChange:h,mouseEnterDelay:C=.15,mouseLeaveDelay:b=.1,autoAdjustOverflow:$=!0,placement:S="",overlay:x,transitionName:w}=e,{getPopupContainer:I,getPrefixCls:R,direction:y,dropdown:P}=i.useContext(nt);ur();const E=i.useMemo(()=>{const M=R();return w!==void 0?w:S.includes("top")?`${M}-slide-down`:`${M}-slide-up`},[R,S,w]),N=i.useMemo(()=>S?S.includes("Center")?S.slice(0,S.indexOf("Center")):S:y==="rtl"?"bottomRight":"bottomLeft",[S,y]),z=R("dropdown",r),T=At(z),[D,V,q]=Cl(z,T),[,_]=kt(),G=i.Children.only(Ua(l)?i.createElement("span",null,l):l),O=en(G,{className:le(`${z}-trigger`,{[`${z}-rtl`]:y==="rtl"},G.props.className),disabled:(t=G.props.disabled)!==null&&t!==void 0?t:s}),H=s?[]:a,j=!!(H!=null&&H.includes("contextMenu")),[Z,K]=zt(!1,{value:u??g}),te=Gt(M=>{v==null||v(M,{source:"trigger"}),h==null||h(M),K(M)}),ge=le(d,f,V,q,T,P==null?void 0:P.className,{[`${z}-rtl`]:y==="rtl"}),ue=Xr({arrowPointAtCenter:typeof o=="object"&&o.pointAtCenter,autoAdjustOverflow:$,offset:_.marginXXS,arrowWidth:o?_.sizePopupArrow:0,borderRadius:_.borderRadius}),ne=i.useCallback(()=>{n!=null&&n.selectable&&(n!=null&&n.multiple)||(v==null||v(!1,{source:"menu"}),K(!1))},[n==null?void 0:n.selectable,n==null?void 0:n.multiple]),k=()=>{let M;return n!=null&&n.items?M=i.createElement(Vt,Object.assign({},n)):typeof x=="function"?M=x():M=x,c&&(M=c(M)),M=i.Children.only(typeof M=="string"?i.createElement("span",null,M):M),i.createElement(ll,{prefixCls:`${z}-menu`,rootClassName:le(q,T),expandIcon:i.createElement("span",{className:`${z}-menu-submenu-arrow`},y==="rtl"?i.createElement(pn,{className:`${z}-menu-submenu-arrow-icon`}):i.createElement(dn,{className:`${z}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:ne,validator:oe=>{let{mode:W}=oe}},M)},[se,A]=yn("Dropdown",p==null?void 0:p.zIndex);let B=i.createElement(_r,Object.assign({alignPoint:j},xt(e,["rootClassName"]),{mouseEnterDelay:C,mouseLeaveDelay:b,visible:Z,builtinPlacements:ue,arrow:!!o,overlayClassName:ge,prefixCls:z,getPopupContainer:m||I,transitionName:E,trigger:H,overlay:k,placement:N,onVisibleChange:te,overlayStyle:Object.assign(Object.assign(Object.assign({},P==null?void 0:P.style),p),{zIndex:se})}),O);return se&&(B=i.createElement(Er.Provider,{value:A},B)),D(B)},$l=$o(nn,"align",void 0,"dropdown",e=>e),yl=e=>i.createElement($l,Object.assign({},e),i.createElement("span",null));nn._InternalPanelDoNotUseOrYouWillBeFired=yl;const Fo=i.createContext(null),wl=Fo.Provider,Xo=i.createContext(null),Il=Xo.Provider,xl=e=>{const{componentCls:t,antCls:n}=e,o=`${t}-group`;return{[o]:Object.assign(Object.assign({},vt(e)),{display:"inline-block",fontSize:0,[`&${o}-rtl`]:{direction:"rtl"},[`&${o}-block`]:{display:"flex"},[`${n}-badge ${n}-badge-count`]:{zIndex:1},[`> ${n}-badge:not(:first-child) > ${n}-button-wrapper`]:{borderInlineStart:"none"}})}},El=e=>{const{componentCls:t,wrapperMarginInlineEnd:n,colorPrimary:o,radioSize:r,motionDurationSlow:l,motionDurationMid:a,motionEaseInOutCirc:s,colorBgContainer:c,colorBorder:m,lineWidth:d,colorBgContainerDisabled:f,colorTextDisabled:p,paddingXS:u,dotColorDisabled:v,lineType:g,radioColor:h,radioBgColor:C,calc:b}=e,$=`${t}-inner`,x=b(r).sub(b(4).mul(2)),w=b(1).mul(r).equal({unit:!0});return{[`${t}-wrapper`]:Object.assign(Object.assign({},vt(e)),{display:"inline-flex",alignItems:"baseline",marginInlineStart:0,marginInlineEnd:n,cursor:"pointer","&:last-child":{marginInlineEnd:0},[`&${t}-wrapper-rtl`]:{direction:"rtl"},"&-disabled":{cursor:"not-allowed",color:e.colorTextDisabled},"&::after":{display:"inline-block",width:0,overflow:"hidden",content:'"\\a0"'},"&-block":{flex:1,justifyContent:"center"},[`${t}-checked::after`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"100%",border:`${L(d)} ${g} ${o}`,borderRadius:"50%",visibility:"hidden",opacity:0,content:'""'},[t]:Object.assign(Object.assign({},vt(e)),{position:"relative",display:"inline-block",outline:"none",cursor:"pointer",alignSelf:"center",borderRadius:"50%"}),[`${t}-wrapper:hover &,
        &:hover ${$}`]:{borderColor:o},[`${t}-input:focus-visible + ${$}`]:Object.assign({},Sn(e)),[`${t}:hover::after, ${t}-wrapper:hover &::after`]:{visibility:"visible"},[`${t}-inner`]:{"&::after":{boxSizing:"border-box",position:"absolute",insetBlockStart:"50%",insetInlineStart:"50%",display:"block",width:w,height:w,marginBlockStart:b(1).mul(r).div(-2).equal({unit:!0}),marginInlineStart:b(1).mul(r).div(-2).equal({unit:!0}),backgroundColor:h,borderBlockStart:0,borderInlineStart:0,borderRadius:w,transform:"scale(0)",opacity:0,transition:`all ${l} ${s}`,content:'""'},boxSizing:"border-box",position:"relative",insetBlockStart:0,insetInlineStart:0,display:"block",width:w,height:w,backgroundColor:c,borderColor:m,borderStyle:"solid",borderWidth:d,borderRadius:"50%",transition:`all ${a}`},[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0},[`${t}-checked`]:{[$]:{borderColor:o,backgroundColor:C,"&::after":{transform:`scale(${e.calc(e.dotSize).div(r).equal()})`,opacity:1,transition:`all ${l} ${s}`}}},[`${t}-disabled`]:{cursor:"not-allowed",[$]:{backgroundColor:f,borderColor:m,cursor:"not-allowed","&::after":{backgroundColor:v}},[`${t}-input`]:{cursor:"not-allowed"},[`${t}-disabled + span`]:{color:p,cursor:"not-allowed"},[`&${t}-checked`]:{[$]:{"&::after":{transform:`scale(${b(x).div(r).equal()})`}}}},[`span${t} + *`]:{paddingInlineStart:u,paddingInlineEnd:u}})}},Ol=e=>{const{buttonColor:t,controlHeight:n,componentCls:o,lineWidth:r,lineType:l,colorBorder:a,motionDurationSlow:s,motionDurationMid:c,buttonPaddingInline:m,fontSize:d,buttonBg:f,fontSizeLG:p,controlHeightLG:u,controlHeightSM:v,paddingXS:g,borderRadius:h,borderRadiusSM:C,borderRadiusLG:b,buttonCheckedBg:$,buttonSolidCheckedColor:S,colorTextDisabled:x,colorBgContainerDisabled:w,buttonCheckedBgDisabled:I,buttonCheckedColorDisabled:R,colorPrimary:y,colorPrimaryHover:P,colorPrimaryActive:E,buttonSolidCheckedBg:N,buttonSolidCheckedHoverBg:z,buttonSolidCheckedActiveBg:T,calc:D}=e;return{[`${o}-button-wrapper`]:{position:"relative",display:"inline-block",height:n,margin:0,paddingInline:m,paddingBlock:0,color:t,fontSize:d,lineHeight:L(D(n).sub(D(r).mul(2)).equal()),background:f,border:`${L(r)} ${l} ${a}`,borderBlockStartWidth:D(r).add(.02).equal(),borderInlineStartWidth:0,borderInlineEndWidth:r,cursor:"pointer",transition:[`color ${c}`,`background ${c}`,`box-shadow ${c}`].join(","),a:{color:t},[`> ${o}-button`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,zIndex:-1,width:"100%",height:"100%"},"&:not(:first-child)":{"&::before":{position:"absolute",insetBlockStart:D(r).mul(-1).equal(),insetInlineStart:D(r).mul(-1).equal(),display:"block",boxSizing:"content-box",width:1,height:"100%",paddingBlock:r,paddingInline:0,backgroundColor:a,transition:`background-color ${s}`,content:'""'}},"&:first-child":{borderInlineStart:`${L(r)} ${l} ${a}`,borderStartStartRadius:h,borderEndStartRadius:h},"&:last-child":{borderStartEndRadius:h,borderEndEndRadius:h},"&:first-child:last-child":{borderRadius:h},[`${o}-group-large &`]:{height:u,fontSize:p,lineHeight:L(D(u).sub(D(r).mul(2)).equal()),"&:first-child":{borderStartStartRadius:b,borderEndStartRadius:b},"&:last-child":{borderStartEndRadius:b,borderEndEndRadius:b}},[`${o}-group-small &`]:{height:v,paddingInline:D(g).sub(r).equal(),paddingBlock:0,lineHeight:L(D(v).sub(D(r).mul(2)).equal()),"&:first-child":{borderStartStartRadius:C,borderEndStartRadius:C},"&:last-child":{borderStartEndRadius:C,borderEndEndRadius:C}},"&:hover":{position:"relative",color:y},"&:has(:focus-visible)":Object.assign({},Sn(e)),[`${o}-inner, input[type='checkbox'], input[type='radio']`]:{width:0,height:0,opacity:0,pointerEvents:"none"},[`&-checked:not(${o}-button-wrapper-disabled)`]:{zIndex:1,color:y,background:$,borderColor:y,"&::before":{backgroundColor:y},"&:first-child":{borderColor:y},"&:hover":{color:P,borderColor:P,"&::before":{backgroundColor:P}},"&:active":{color:E,borderColor:E,"&::before":{backgroundColor:E}}},[`${o}-group-solid &-checked:not(${o}-button-wrapper-disabled)`]:{color:S,background:N,borderColor:N,"&:hover":{color:S,background:z,borderColor:z},"&:active":{color:S,background:T,borderColor:T}},"&-disabled":{color:x,backgroundColor:w,borderColor:a,cursor:"not-allowed","&:first-child, &:hover":{color:x,backgroundColor:w,borderColor:a}},[`&-disabled${o}-button-wrapper-checked`]:{color:R,backgroundColor:I,borderColor:a,boxShadow:"none"},"&-block":{flex:1,textAlign:"center"}}}},Rl=e=>{const{wireframe:t,padding:n,marginXS:o,lineWidth:r,fontSizeLG:l,colorText:a,colorBgContainer:s,colorTextDisabled:c,controlItemBgActiveDisabled:m,colorTextLightSolid:d,colorPrimary:f,colorPrimaryHover:p,colorPrimaryActive:u,colorWhite:v}=e,g=4,h=l,C=t?h-g*2:h-(g+r)*2;return{radioSize:h,dotSize:C,dotColorDisabled:c,buttonSolidCheckedColor:d,buttonSolidCheckedBg:f,buttonSolidCheckedHoverBg:p,buttonSolidCheckedActiveBg:u,buttonBg:s,buttonCheckedBg:s,buttonColor:a,buttonCheckedBgDisabled:m,buttonCheckedColorDisabled:c,buttonPaddingInline:n-r,wrapperMarginInlineEnd:o,radioColor:t?f:v,radioBgColor:t?s:f}},Ko=Et("Radio",e=>{const{controlOutline:t,controlOutlineWidth:n}=e,o=`0 0 0 ${L(n)} ${t}`,l=at(e,{radioFocusShadow:o,radioButtonFocusShadow:o});return[xl(l),El(l),Ol(l)]},Rl,{unitless:{radioSize:!0,dotSize:!0}});var Bl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Ml=(e,t)=>{var n,o;const r=i.useContext(Fo),l=i.useContext(Xo),{getPrefixCls:a,direction:s,radio:c}=i.useContext(nt),m=i.useRef(null),d=lo(t,m),{isFormItemInput:f}=i.useContext(fo),p=V=>{var q,_;(q=e.onChange)===null||q===void 0||q.call(e,V),(_=r==null?void 0:r.onChange)===null||_===void 0||_.call(r,V)},{prefixCls:u,className:v,rootClassName:g,children:h,style:C,title:b}=e,$=Bl(e,["prefixCls","className","rootClassName","children","style","title"]),S=a("radio",u),x=((r==null?void 0:r.optionType)||l)==="button",w=x?`${S}-button`:S,I=At(S),[R,y,P]=Ko(S,I),E=Object.assign({},$),N=i.useContext(co);r&&(E.name=r.name,E.onChange=p,E.checked=e.value===r.value,E.disabled=(n=E.disabled)!==null&&n!==void 0?n:r.disabled),E.disabled=(o=E.disabled)!==null&&o!==void 0?o:N;const z=le(`${w}-wrapper`,{[`${w}-wrapper-checked`]:E.checked,[`${w}-wrapper-disabled`]:E.disabled,[`${w}-wrapper-rtl`]:s==="rtl",[`${w}-wrapper-in-form-item`]:f,[`${w}-wrapper-block`]:!!(r!=null&&r.block)},c==null?void 0:c.className,v,g,y,P,I),[T,D]=Kr(E.onClick);return R(i.createElement(Or,{component:"Radio",disabled:E.disabled},i.createElement("label",{className:z,style:Object.assign(Object.assign({},c==null?void 0:c.style),C),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,title:b,onClick:T},i.createElement(Gr,Object.assign({},E,{className:le(E.className,{[Rr]:!x}),type:"radio",prefixCls:w,ref:d,onClick:D})),h!==void 0?i.createElement("span",{className:`${w}-label`},h):null)))},Zt=i.forwardRef(Ml),Pl=i.forwardRef((e,t)=>{const{getPrefixCls:n,direction:o}=i.useContext(nt),r=Br(),{prefixCls:l,className:a,rootClassName:s,options:c,buttonStyle:m="outline",disabled:d,children:f,size:p,style:u,id:v,optionType:g,name:h=r,defaultValue:C,value:b,block:$=!1,onChange:S,onMouseEnter:x,onMouseLeave:w,onFocus:I,onBlur:R}=e,[y,P]=zt(C,{value:b}),E=i.useCallback(j=>{const Z=y,K=j.target.value;"value"in e||P(K),K!==Z&&(S==null||S(j))},[y,P,S]),N=n("radio",l),z=`${N}-group`,T=At(N),[D,V,q]=Ko(N,T);let _=f;c&&c.length>0&&(_=c.map(j=>typeof j=="string"||typeof j=="number"?i.createElement(Zt,{key:j.toString(),prefixCls:N,disabled:d,value:j,checked:y===j},j):i.createElement(Zt,{key:`radio-group-value-options-${j.value}`,prefixCls:N,disabled:j.disabled||d,value:j.value,checked:y===j.value,title:j.title,style:j.style,id:j.id,required:j.required},j.label)));const G=go(p),O=le(z,`${z}-${m}`,{[`${z}-${G}`]:G,[`${z}-rtl`]:o==="rtl",[`${z}-block`]:$},a,s,V,q,T),H=i.useMemo(()=>({onChange:E,value:y,disabled:d,name:h,optionType:g,block:$}),[E,y,d,h,g,$]);return D(i.createElement("div",Object.assign({},_t(e,{aria:!0,data:!0}),{className:O,style:u,onMouseEnter:x,onMouseLeave:w,onFocus:I,onBlur:R,id:v,ref:t}),i.createElement(wl,{value:H},_)))}),Dl=i.memo(Pl);var zl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Tl=(e,t)=>{const{getPrefixCls:n}=i.useContext(nt),{prefixCls:o}=e,r=zl(e,["prefixCls"]),l=n("radio",o);return i.createElement(Il,{value:"button"},i.createElement(Zt,Object.assign({prefixCls:l},r,{type:"radio",ref:t})))},Nl=i.forwardRef(Tl),Rn=Zt;Rn.Button=Nl;Rn.Group=Dl;Rn.__ANT_RADIO=!0;function io(e){return["small","middle","large"].includes(e)}function ao(e){return e?typeof e=="number"&&!Number.isNaN(e):!1}const Go=yt.createContext({latestIndex:0}),Hl=Go.Provider,Ll=e=>{let{className:t,index:n,children:o,split:r,style:l}=e;const{latestIndex:a}=i.useContext(Go);return o==null?null:i.createElement(i.Fragment,null,i.createElement("div",{className:t,style:l},o),n<a&&r&&i.createElement("span",{className:`${t}-split`},r))};var jl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const _l=i.forwardRef((e,t)=>{var n;const{getPrefixCls:o,direction:r,size:l,className:a,style:s,classNames:c,styles:m}=hn("space"),{size:d=l??"small",align:f,className:p,rootClassName:u,children:v,direction:g="horizontal",prefixCls:h,split:C,style:b,wrap:$=!1,classNames:S,styles:x}=e,w=jl(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[I,R]=Array.isArray(d)?d:[d,d],y=io(R),P=io(I),E=ao(R),N=ao(I),z=Cn(v,{keepEmpty:!0}),T=f===void 0&&g==="horizontal"?"center":f,D=o("space",h),[V,q,_]=Mr(D),G=le(D,a,q,`${D}-${g}`,{[`${D}-rtl`]:r==="rtl",[`${D}-align-${T}`]:T,[`${D}-gap-row-${R}`]:y,[`${D}-gap-col-${I}`]:P},p,u,_),O=le(`${D}-item`,(n=S==null?void 0:S.item)!==null&&n!==void 0?n:c.item);let H=0;const j=z.map((te,ge)=>{var ue;te!=null&&(H=ge);const ne=(te==null?void 0:te.key)||`${O}-${ge}`;return i.createElement(Ll,{className:O,key:ne,index:ge,split:C,style:(ue=x==null?void 0:x.item)!==null&&ue!==void 0?ue:m.item},te)}),Z=i.useMemo(()=>({latestIndex:H}),[H]);if(z.length===0)return null;const K={};return $&&(K.flexWrap="wrap"),!P&&N&&(K.columnGap=I),!y&&E&&(K.rowGap=R),V(i.createElement("div",Object.assign({ref:t,className:G,style:Object.assign(Object.assign(Object.assign({},K),s),b)},w),i.createElement(Hl,{value:Z},j)))}),qo=_l;qo.Compact=Pr;var Al=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Uo=e=>{const{getPopupContainer:t,getPrefixCls:n,direction:o}=i.useContext(nt),{prefixCls:r,type:l="default",danger:a,disabled:s,loading:c,onClick:m,htmlType:d,children:f,className:p,menu:u,arrow:v,autoFocus:g,overlay:h,trigger:C,align:b,open:$,onOpenChange:S,placement:x,getPopupContainer:w,href:I,icon:R=i.createElement(So,null),title:y,buttonsRender:P=ne=>ne,mouseEnterDelay:E,mouseLeaveDelay:N,overlayClassName:z,overlayStyle:T,destroyPopupOnHide:D,dropdownRender:V}=e,q=Al(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","dropdownRender"]),_=n("dropdown",r),G=`${_}-button`,O={menu:u,arrow:v,autoFocus:g,align:b,disabled:s,trigger:s?[]:C,onOpenChange:S,getPopupContainer:w||t,mouseEnterDelay:E,mouseLeaveDelay:N,overlayClassName:z,overlayStyle:T,destroyPopupOnHide:D,dropdownRender:V},{compactSize:H,compactItemClassnames:j}=mo(_,o),Z=le(G,j,p);"overlay"in e&&(O.overlay=h),"open"in e&&(O.open=$),"placement"in e?O.placement=x:O.placement=o==="rtl"?"bottomLeft":"bottomRight";const K=i.createElement(Hn,{type:l,danger:a,disabled:s,loading:c,onClick:m,htmlType:d,href:I,title:y},f),te=i.createElement(Hn,{type:l,danger:a,icon:R}),[ge,ue]=P([K,te]);return i.createElement(qo.Compact,Object.assign({className:Z,size:H,block:!0},q),ge,i.createElement(nn,Object.assign({},O),ue))};Uo.__ANT_BUTTON=!0;const Wl=nn;Wl.Button=Uo;export{nn as D,$t as E,ka as L,Vt as M,ll as O,Aa as R,Wt as S,ql as a,On as b,Rn as c,Wl as d,Ca as e,ba as f,$o as g,Wa as h,Ut as i,qo as j,pn as k,Mo as l,ga as m,Gl as u,ni as w};
