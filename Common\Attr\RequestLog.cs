﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;

namespace zhiying_online.Common.Attr
{
    public class RequestLog : ActionFilterAttribute
    {
        private string ActionArguments { get; set; }

        /// <summary>
        /// 请求体中的所有值
        /// </summary>
        private string RequestBody { get; set; }

        private Stopwatch Stopwatch { get; set; }


        public override void OnActionExecuting(ActionExecutingContext context)
        {
            base.OnActionExecuting(context);

            // body后续添加了获取请求的请求体，如果在实际项目中不需要删除即可
            long contentLen = context.HttpContext.Request.ContentLength == null ? 0 : context.HttpContext.Request.ContentLength.Value;
            if (contentLen > 0)
            {
                //获取请求字符串
                var syncIOFeature = context.HttpContext.Features.Get<IHttpBodyControlFeature>();
                if (syncIOFeature != null)
                {
                    syncIOFeature.AllowSynchronousIO = true;
                }
                context.HttpContext.Request.EnableBuffering();
                RequestBody = new StreamReader(context.HttpContext.Request.Body).ReadToEnd();
                context.HttpContext.Request.Body.Seek(0, SeekOrigin.Begin);
            }

            //Form参数
            ActionArguments = "";
            if (context.HttpContext.Request.HasFormContentType)
            {
                foreach (var item in context.HttpContext.Request.Form)
                {
                    ActionArguments += item.Key + ":" + item.Value + ",";
                }
            }
            //Newtonsoft.Json.JsonConvert.SerializeObject(context.ActionArguments);

            Stopwatch = new Stopwatch();
            Stopwatch.Start();
        }

        public override void OnActionExecuted(ActionExecutedContext context)
        {
            base.OnActionExecuted(context);
            Stopwatch.Stop();
            string url = context.HttpContext.Request.Path + context.HttpContext.Request.QueryString;
            string method = context.HttpContext.Request.Method;
            string qs = ActionArguments;
            dynamic result = context.Result.GetType().Name == "EmptyResult" ? new { Value = "无返回结果" } : context.Result as dynamic;
            string res = "在返回结果前发生了异常";

            try
            {
                if (result != null)
                {
                    res = Newtonsoft.Json.JsonConvert.SerializeObject(result.Value);
                }
            }
            catch (System.Exception)
            {
                res = "日志未获取到结果，返回的数据无法序列化";
            }

            Logger.debug($"\n接口地址：{url}\n方式：{method} \n请求体：{RequestBody}\n参数：{qs}\n返回值：{res}\n耗时：{Stopwatch.Elapsed.TotalMilliseconds} 毫秒");
        }
    }
}
