﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Common.BLL;
using zhiying_online.Common.Jwt;

namespace zhiying_online.Controllers
{
    /// <summary>
    /// 查看报告
    /// </summary>
    [Route("[controller]/[action]")]
    public class ReportController : Controller
    {
        /// <summary>
        /// 查看报告
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        [JwtH5Attribute]
        [HttpPost]
        public IActionResult GetReportList(long? pid)
        {
            return Json(ReportHandle.GetReportList(pid));
        }
    }
}
