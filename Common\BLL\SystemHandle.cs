﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace zhiying_online.Common.BLL
{
    /// <summary>
    /// 获取配置信息
    /// </summary>
    public class SystemHandle
    {
        /// <summary>
        /// 获取配置信息
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static RetMsg GetSystemConfig(string type)
        {
            using (var db = new Model.zhiying_online())
            {
                return new RetMsg { code = 0, msg = "请求成功", result = db.system.Where(x => x.s_type == type).Select(x => x.s_result).FirstOrDefault() };
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="type"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        public static RetMsg SaveSystemConfig(string type, string result)
        {
            using (var db = new Model.zhiying_online())
            {
                var f = db.system.Where(x => x.s_type == type).FirstOrDefault();
                if (f == null)
                {
                    f = new Model.system
                    {
                        s_id = Tools.Get_Id(),
                        s_result = result,
                        s_time = DateTime.Now,
                        s_type = type
                    };
                    db.system.Add(f);
                } else
                {
                    f.s_result = result;
                }
                db.SaveChanges();
                return new RetMsg { code = 0, msg = "请求成功", result = db.system.Where(x => x.s_type == type).Select(x => x.s_result).FirstOrDefault() };
            }
        }
    }
}
