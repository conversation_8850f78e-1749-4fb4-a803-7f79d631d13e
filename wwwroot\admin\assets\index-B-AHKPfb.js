var gt=Object.defineProperty;var dt=(e,t,n)=>t in e?gt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var p=(e,t,n)=>dt(e,typeof t!="symbol"?t+"":t,n);import{l as ft,r as y,R as H,c as mt}from"./index-C0F_BgRl.js";import{l as tt,m as pt}from"./index-Cv9X8VLK.js";const bt=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},yt=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},St=(e,t)=>{const{prefixCls:n,componentCls:r,gridColumns:i}=e,s={};for(let o=i;o>=0;o--)o===0?(s[`${r}${t}-${o}`]={display:"none"},s[`${r}-push-${o}`]={insetInlineStart:"auto"},s[`${r}-pull-${o}`]={insetInlineEnd:"auto"},s[`${r}${t}-push-${o}`]={insetInlineStart:"auto"},s[`${r}${t}-pull-${o}`]={insetInlineEnd:"auto"},s[`${r}${t}-offset-${o}`]={marginInlineStart:0},s[`${r}${t}-order-${o}`]={order:0}):(s[`${r}${t}-${o}`]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:`0 0 ${o/i*100}%`,maxWidth:`${o/i*100}%`}],s[`${r}${t}-push-${o}`]={insetInlineStart:`${o/i*100}%`},s[`${r}${t}-pull-${o}`]={insetInlineEnd:`${o/i*100}%`},s[`${r}${t}-offset-${o}`]={marginInlineStart:`${o/i*100}%`},s[`${r}${t}-order-${o}`]={order:o});return s[`${r}${t}-flex`]={flex:`var(--${n}${t}-flex)`},s},T=(e,t)=>St(e,t),wt=(e,t,n)=>({[`@media (min-width: ${ft(t)})`]:Object.assign({},T(e,n))}),xt=()=>({}),vt=()=>({}),le=tt("Grid",bt,xt),$t=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),ue=tt("Grid",e=>{const t=pt(e,{gridColumns:24}),n=$t(t);return delete n.xs,[yt(t),T(t,""),T(t,"-xs"),Object.keys(n).map(r=>wt(t,n[r],`-${r}`)).reduce((r,i)=>Object.assign(Object.assign({},r),i),{})]},vt);var kt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"};const et=y.createContext({}),Ct={aliceblue:"9ehhb",antiquewhite:"9sgk7",aqua:"1ekf",aquamarine:"4zsno",azure:"9eiv3",beige:"9lhp8",bisque:"9zg04",black:"0",blanchedalmond:"9zhe5",blue:"73",blueviolet:"5e31e",brown:"6g016",burlywood:"8ouiv",cadetblue:"3qba8",chartreuse:"4zshs",chocolate:"87k0u",coral:"9yvyo",cornflowerblue:"3xael",cornsilk:"9zjz0",crimson:"8l4xo",cyan:"1ekf",darkblue:"3v",darkcyan:"rkb",darkgoldenrod:"776yz",darkgray:"6mbhl",darkgreen:"jr4",darkgrey:"6mbhl",darkkhaki:"7ehkb",darkmagenta:"5f91n",darkolivegreen:"3bzfz",darkorange:"9yygw",darkorchid:"5z6x8",darkred:"5f8xs",darksalmon:"9441m",darkseagreen:"5lwgf",darkslateblue:"2th1n",darkslategray:"1ugcv",darkslategrey:"1ugcv",darkturquoise:"14up",darkviolet:"5rw7n",deeppink:"9yavn",deepskyblue:"11xb",dimgray:"442g9",dimgrey:"442g9",dodgerblue:"16xof",firebrick:"6y7tu",floralwhite:"9zkds",forestgreen:"1cisi",fuchsia:"9y70f",gainsboro:"8m8kc",ghostwhite:"9pq0v",goldenrod:"8j4f4",gold:"9zda8",gray:"50i2o",green:"pa8",greenyellow:"6senj",grey:"50i2o",honeydew:"9eiuo",hotpink:"9yrp0",indianred:"80gnw",indigo:"2xcoy",ivory:"9zldc",khaki:"9edu4",lavenderblush:"9ziet",lavender:"90c8q",lawngreen:"4vk74",lemonchiffon:"9zkct",lightblue:"6s73a",lightcoral:"9dtog",lightcyan:"8s1rz",lightgoldenrodyellow:"9sjiq",lightgray:"89jo3",lightgreen:"5nkwg",lightgrey:"89jo3",lightpink:"9z6wx",lightsalmon:"9z2ii",lightseagreen:"19xgq",lightskyblue:"5arju",lightslategray:"4nwk9",lightslategrey:"4nwk9",lightsteelblue:"6wau6",lightyellow:"9zlcw",lime:"1edc",limegreen:"1zcxe",linen:"9shk6",magenta:"9y70f",maroon:"4zsow",mediumaquamarine:"40eju",mediumblue:"5p",mediumorchid:"79qkz",mediumpurple:"5r3rv",mediumseagreen:"2d9ip",mediumslateblue:"4tcku",mediumspringgreen:"1di2",mediumturquoise:"2uabw",mediumvioletred:"7rn9h",midnightblue:"z980",mintcream:"9ljp6",mistyrose:"9zg0x",moccasin:"9zfzp",navajowhite:"9zest",navy:"3k",oldlace:"9wq92",olive:"50hz4",olivedrab:"472ub",orange:"9z3eo",orangered:"9ykg0",orchid:"8iu3a",palegoldenrod:"9bl4a",palegreen:"5yw0o",paleturquoise:"6v4ku",palevioletred:"8k8lv",papayawhip:"9zi6t",peachpuff:"9ze0p",peru:"80oqn",pink:"9z8wb",plum:"8nba5",powderblue:"6wgdi",purple:"4zssg",rebeccapurple:"3zk49",red:"9y6tc",rosybrown:"7cv4f",royalblue:"2jvtt",saddlebrown:"5fmkz",salmon:"9rvci",sandybrown:"9jn1c",seagreen:"1tdnb",seashell:"9zje6",sienna:"6973h",silver:"7ir40",skyblue:"5arjf",slateblue:"45e4t",slategray:"4e100",slategrey:"4e100",snow:"9zke2",springgreen:"1egv",steelblue:"2r1kk",tan:"87yx8",teal:"pds",thistle:"8ggk8",tomato:"9yqfb",turquoise:"2j4r4",violet:"9b10u",wheat:"9ld4j",white:"9zldr",whitesmoke:"9lhpx",yellow:"9zl6o",yellowgreen:"61fzm"},d=Math.round;function R(e,t){const n=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],r=n.map(i=>parseFloat(i));for(let i=0;i<3;i+=1)r[i]=t(r[i]||0,n[i]||"",i);return n[3]?r[3]=n[3].includes("%")?r[3]/100:r[3]:r[3]=1,r}const D=(e,t,n)=>n===0?e:e/100;function $(e,t){const n=t||255;return e>n?n:e<0?0:e}class x{constructor(t){p(this,"isValid",!0);p(this,"r",0);p(this,"g",0);p(this,"b",0);p(this,"a",1);p(this,"_h");p(this,"_s");p(this,"_l");p(this,"_v");p(this,"_max");p(this,"_min");p(this,"_brightness");function n(r){return r[0]in t&&r[1]in t&&r[2]in t}if(t)if(typeof t=="string"){let i=function(s){return r.startsWith(s)};const r=t.trim();if(/^#?[A-F\d]{3,8}$/i.test(r))this.fromHexString(r);else if(i("rgb"))this.fromRgbString(r);else if(i("hsl"))this.fromHslString(r);else if(i("hsv")||i("hsb"))this.fromHsvString(r);else{const s=Ct[r.toLowerCase()];s&&this.fromHexString(parseInt(s,36).toString(16).padStart(6,"0"))}}else if(t instanceof x)this.r=t.r,this.g=t.g,this.b=t.b,this.a=t.a,this._h=t._h,this._s=t._s,this._l=t._l,this._v=t._v;else if(n("rgb"))this.r=$(t.r),this.g=$(t.g),this.b=$(t.b),this.a=typeof t.a=="number"?$(t.a,1):1;else if(n("hsl"))this.fromHsl(t);else if(n("hsv"))this.fromHsv(t);else throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(t))}setR(t){return this._sc("r",t)}setG(t){return this._sc("g",t)}setB(t){return this._sc("b",t)}setA(t){return this._sc("a",t,1)}setHue(t){const n=this.toHsv();return n.h=t,this._c(n)}getLuminance(){function t(s){const o=s/255;return o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4)}const n=t(this.r),r=t(this.g),i=t(this.b);return .2126*n+.7152*r+.0722*i}getHue(){if(typeof this._h>"u"){const t=this.getMax()-this.getMin();t===0?this._h=0:this._h=d(60*(this.r===this.getMax()?(this.g-this.b)/t+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/t+2:(this.r-this.g)/t+4))}return this._h}getSaturation(){if(typeof this._s>"u"){const t=this.getMax()-this.getMin();t===0?this._s=0:this._s=t/this.getMax()}return this._s}getLightness(){return typeof this._l>"u"&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return typeof this._v>"u"&&(this._v=this.getMax()/255),this._v}getBrightness(){return typeof this._brightness>"u"&&(this._brightness=(this.r*299+this.g*587+this.b*114)/1e3),this._brightness}darken(t=10){const n=this.getHue(),r=this.getSaturation();let i=this.getLightness()-t/100;return i<0&&(i=0),this._c({h:n,s:r,l:i,a:this.a})}lighten(t=10){const n=this.getHue(),r=this.getSaturation();let i=this.getLightness()+t/100;return i>1&&(i=1),this._c({h:n,s:r,l:i,a:this.a})}mix(t,n=50){const r=this._c(t),i=n/100,s=c=>(r[c]-this[c])*i+this[c],o={r:d(s("r")),g:d(s("g")),b:d(s("b")),a:d(s("a")*100)/100};return this._c(o)}tint(t=10){return this.mix({r:255,g:255,b:255,a:1},t)}shade(t=10){return this.mix({r:0,g:0,b:0,a:1},t)}onBackground(t){const n=this._c(t),r=this.a+n.a*(1-this.a),i=s=>d((this[s]*this.a+n[s]*n.a*(1-this.a))/r);return this._c({r:i("r"),g:i("g"),b:i("b"),a:r})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(t){return this.r===t.r&&this.g===t.g&&this.b===t.b&&this.a===t.a}clone(){return this._c(this)}toHexString(){let t="#";const n=(this.r||0).toString(16);t+=n.length===2?n:"0"+n;const r=(this.g||0).toString(16);t+=r.length===2?r:"0"+r;const i=(this.b||0).toString(16);if(t+=i.length===2?i:"0"+i,typeof this.a=="number"&&this.a>=0&&this.a<1){const s=d(this.a*255).toString(16);t+=s.length===2?s:"0"+s}return t}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const t=this.getHue(),n=d(this.getSaturation()*100),r=d(this.getLightness()*100);return this.a!==1?`hsla(${t},${n}%,${r}%,${this.a})`:`hsl(${t},${n}%,${r}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return this.a!==1?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(t,n,r){const i=this.clone();return i[t]=$(n,r),i}_c(t){return new this.constructor(t)}getMax(){return typeof this._max>"u"&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return typeof this._min>"u"&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(t){const n=t.replace("#","");function r(i,s){return parseInt(n[i]+n[s||i],16)}n.length<6?(this.r=r(0),this.g=r(1),this.b=r(2),this.a=n[3]?r(3)/255:1):(this.r=r(0,1),this.g=r(2,3),this.b=r(4,5),this.a=n[6]?r(6,7)/255:1)}fromHsl({h:t,s:n,l:r,a:i}){if(this._h=t%360,this._s=n,this._l=r,this.a=typeof i=="number"?i:1,n<=0){const f=d(r*255);this.r=f,this.g=f,this.b=f}let s=0,o=0,c=0;const l=t/60,a=(1-Math.abs(2*r-1))*n,u=a*(1-Math.abs(l%2-1));l>=0&&l<1?(s=a,o=u):l>=1&&l<2?(s=u,o=a):l>=2&&l<3?(o=a,c=u):l>=3&&l<4?(o=u,c=a):l>=4&&l<5?(s=u,c=a):l>=5&&l<6&&(s=a,c=u);const g=r-a/2;this.r=d((s+g)*255),this.g=d((o+g)*255),this.b=d((c+g)*255)}fromHsv({h:t,s:n,v:r,a:i}){this._h=t%360,this._s=n,this._v=r,this.a=typeof i=="number"?i:1;const s=d(r*255);if(this.r=s,this.g=s,this.b=s,n<=0)return;const o=t/60,c=Math.floor(o),l=o-c,a=d(r*(1-n)*255),u=d(r*(1-n*l)*255),g=d(r*(1-n*(1-l))*255);switch(c){case 0:this.g=g,this.b=a;break;case 1:this.r=u,this.b=a;break;case 2:this.r=a,this.b=g;break;case 3:this.r=a,this.g=u;break;case 4:this.r=g,this.g=a;break;case 5:default:this.g=a,this.b=u;break}}fromHsvString(t){const n=R(t,D);this.fromHsv({h:n[0],s:n[1],v:n[2],a:n[3]})}fromHslString(t){const n=R(t,D);this.fromHsl({h:n[0],s:n[1],l:n[2],a:n[3]})}fromRgbString(t){const n=R(t,(r,i)=>i.includes("%")?d(r/100*255):r);this.r=n[0],this.g=n[1],this.b=n[2],this.a=n[3]}}const M=2,W=.16,_t=.05,Mt=.05,Ht=.15,nt=5,rt=4,It=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function F(e,t,n){let r;return Math.round(e.h)>=60&&Math.round(e.h)<=240?r=n?Math.round(e.h)-M*t:Math.round(e.h)+M*t:r=n?Math.round(e.h)+M*t:Math.round(e.h)-M*t,r<0?r+=360:r>=360&&(r-=360),r}function U(e,t,n){if(e.h===0&&e.s===0)return e.s;let r;return n?r=e.s-W*t:t===rt?r=e.s+W:r=e.s+_t*t,r>1&&(r=1),n&&t===nt&&r>.1&&(r=.1),r<.06&&(r=.06),Math.round(r*100)/100}function J(e,t,n){let r;return n?r=e.v+Mt*t:r=e.v-Ht*t,r=Math.max(0,Math.min(1,r)),Math.round(r*100)/100}function jt(e,t={}){const n=[],r=new x(e),i=r.toHsv();for(let s=nt;s>0;s-=1){const o=new x({h:F(i,s,!0),s:U(i,s,!0),v:J(i,s,!0)});n.push(o)}n.push(r);for(let s=1;s<=rt;s+=1){const o=new x({h:F(i,s),s:U(i,s),v:J(i,s)});n.push(o)}return t.theme==="dark"?It.map(({index:s,amount:o})=>new x(t.backgroundColor||"#141414").mix(n[s],o).toHexString()):n.map(s=>s.toHexString())}const O=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];O.primary=O[5];function zt(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Rt(e,t){if(!e)return!1;if(e.contains)return e.contains(t);let n=t;for(;n;){if(n===e)return!0;n=n.parentNode}return!1}const Q="data-rc-order",X="data-rc-priority",Tt="rc-util-key",E=new Map;function st({mark:e}={}){return e?e.startsWith("data-")?e:`data-${e}`:Tt}function B(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function Ot(e){return e==="queue"?"prependQueue":e?"prepend":"append"}function G(e){return Array.from((E.get(e)||e).children).filter(t=>t.tagName==="STYLE")}function it(e,t={}){if(!zt())return null;const{csp:n,prepend:r,priority:i=0}=t,s=Ot(r),o=s==="prependQueue",c=document.createElement("style");c.setAttribute(Q,s),o&&i&&c.setAttribute(X,`${i}`),n!=null&&n.nonce&&(c.nonce=n==null?void 0:n.nonce),c.innerHTML=e;const l=B(t),{firstChild:a}=l;if(r){if(o){const u=(t.styles||G(l)).filter(g=>{if(!["prepend","prependQueue"].includes(g.getAttribute(Q)))return!1;const f=Number(g.getAttribute(X)||0);return i>=f});if(u.length)return l.insertBefore(c,u[u.length-1].nextSibling),c}l.insertBefore(c,a)}else l.appendChild(c);return c}function Et(e,t={}){let{styles:n}=t;return n||(n=G(B(t))),n.find(r=>r.getAttribute(st(t))===e)}function Nt(e,t){const n=E.get(e);if(!n||!Rt(document,n)){const r=it("",t),{parentNode:i}=r;E.set(e,i),e.removeChild(r)}}function qt(e,t,n={}){var l,a,u;const r=B(n),i=G(r),s={...n,styles:i};Nt(r,s);const o=Et(t,s);if(o)return(l=s.csp)!=null&&l.nonce&&o.nonce!==((a=s.csp)==null?void 0:a.nonce)&&(o.nonce=(u=s.csp)==null?void 0:u.nonce),o.innerHTML!==e&&(o.innerHTML=e),o;const c=it(e,s);return c.setAttribute(st(s),t),c}function ot(e){var t;return(t=e==null?void 0:e.getRootNode)==null?void 0:t.call(e)}function Lt(e){return ot(e)instanceof ShadowRoot}function At(e){return Lt(e)?ot(e):null}let N={};const Pt=e=>{};function Bt(e,t){}function Gt(e,t){}function Dt(){N={}}function at(e,t,n){!t&&!N[n]&&(e(!1,n),N[n]=!0)}function I(e,t){at(Bt,e,t)}function Wt(e,t){at(Gt,e,t)}I.preMessage=Pt;I.resetWarned=Dt;I.noteOnce=Wt;function Ft(e){return e.replace(/-(.)/g,(t,n)=>n.toUpperCase())}function Ut(e,t){I(e,`[@ant-design/icons] ${t}`)}function Y(e){return typeof e=="object"&&typeof e.name=="string"&&typeof e.theme=="string"&&(typeof e.icon=="object"||typeof e.icon=="function")}function K(e={}){return Object.keys(e).reduce((t,n)=>{const r=e[n];switch(n){case"class":t.className=r,delete t.class;break;default:delete t[n],t[Ft(n)]=r}return t},{})}function q(e,t,n){return n?H.createElement(e.tag,{key:t,...K(e.attrs),...n},(e.children||[]).map((r,i)=>q(r,`${t}-${e.tag}-${i}`))):H.createElement(e.tag,{key:t,...K(e.attrs)},(e.children||[]).map((r,i)=>q(r,`${t}-${e.tag}-${i}`)))}function ct(e){return jt(e)[0]}function lt(e){return e?Array.isArray(e)?e:[e]:[]}const Jt=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Qt=e=>{const{csp:t,prefixCls:n,layer:r}=y.useContext(et);let i=Jt;n&&(i=i.replace(/anticon/g,n)),r&&(i=`@layer ${r} {
${i}
}`),y.useEffect(()=>{const s=e.current,o=At(s);qt(i,"@ant-design-icons",{prepend:!r,csp:t,attachTo:o})},[])},k={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function Xt({primaryColor:e,secondaryColor:t}){k.primaryColor=e,k.secondaryColor=t||ct(e),k.calculated=!!t}function Yt(){return{...k}}const v=e=>{const{icon:t,className:n,onClick:r,style:i,primaryColor:s,secondaryColor:o,...c}=e,l=y.useRef();let a=k;if(s&&(a={primaryColor:s,secondaryColor:o||ct(s)}),Qt(l),Ut(Y(t),`icon should be icon definiton, but got ${t}`),!Y(t))return null;let u=t;return u&&typeof u.icon=="function"&&(u={...u,icon:u.icon(a.primaryColor,a.secondaryColor)}),q(u.icon,`svg-${u.name}`,{className:n,onClick:r,style:i,"data-icon":u.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",...c,ref:l})};v.displayName="IconReact";v.getTwoToneColors=Yt;v.setTwoToneColors=Xt;function ut(e){const[t,n]=lt(e);return v.setTwoToneColors({primaryColor:t,secondaryColor:n})}function Kt(){const e=v.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor}function L(){return L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},L.apply(this,arguments)}ut(O.primary);const j=y.forwardRef((e,t)=>{const{className:n,icon:r,spin:i,rotate:s,tabIndex:o,onClick:c,twoToneColor:l,...a}=e,{prefixCls:u="anticon",rootClassName:g}=y.useContext(et),f=mt(g,u,{[`${u}-${r.name}`]:!!r.name,[`${u}-spin`]:!!i||r.name==="loading"},n);let b=o;b===void 0&&c&&(b=-1);const C=s?{msTransform:`rotate(${s}deg)`,transform:`rotate(${s}deg)`}:void 0,[h,w]=lt(l);return y.createElement("span",L({role:"img","aria-label":r.name},a,{ref:t,tabIndex:b,onClick:c,className:f}),y.createElement(v,{icon:r,primaryColor:h,secondaryColor:w,style:C}))});j.displayName="AntdIcon";j.getTwoToneColor=Kt;j.setTwoToneColor=ut;function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}const Vt=(e,t)=>y.createElement(j,A({},e,{ref:t,icon:kt})),he=y.forwardRef(Vt),V=e=>{let t;const n=new Set,r=(a,u)=>{const g=typeof a=="function"?a(t):a;if(!Object.is(g,t)){const f=t;t=u??(typeof g!="object"||g===null)?g:Object.assign({},t,g),n.forEach(b=>b(t,f))}},i=()=>t,c={setState:r,getState:i,getInitialState:()=>l,subscribe:a=>(n.add(a),()=>n.delete(a))},l=t=e(r,i,c);return c},Zt=e=>e?V(e):V,te=e=>e;function ee(e,t=te){const n=H.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return H.useDebugValue(n),n}const Z=e=>{const t=Zt(e),n=r=>ee(t,r);return Object.assign(n,t),n},ne=e=>e?Z(e):Z;function re(e,t){let n;try{n=e()}catch{return}return{getItem:i=>{var s;const o=l=>l===null?null:JSON.parse(l,void 0),c=(s=n.getItem(i))!=null?s:null;return c instanceof Promise?c.then(o):o(c)},setItem:(i,s)=>n.setItem(i,JSON.stringify(s,void 0)),removeItem:i=>n.removeItem(i)}}const P=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return P(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return P(r)(n)}}}},se=(e,t)=>(n,r,i)=>{let s={storage:re(()=>localStorage),partialize:h=>h,version:0,merge:(h,w)=>({...w,...h}),...t},o=!1;const c=new Set,l=new Set;let a=s.storage;if(!a)return e((...h)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...h)},r,i);const u=()=>{const h=s.partialize({...r()});return a.setItem(s.name,{state:h,version:s.version})},g=i.setState;i.setState=(h,w)=>{g(h,w),u()};const f=e((...h)=>{n(...h),u()},r,i);i.getInitialState=()=>f;let b;const C=()=>{var h,w;if(!a)return;o=!1,c.forEach(m=>{var S;return m((S=r())!=null?S:f)});const _=((w=s.onRehydrateStorage)==null?void 0:w.call(s,(h=r())!=null?h:f))||void 0;return P(a.getItem.bind(a))(s.name).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==s.version){if(s.migrate){const S=s.migrate(m.state,m.version);return S instanceof Promise?S.then(z=>[!0,z]):[!0,S]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,m.state];return[!1,void 0]}).then(m=>{var S;const[z,ht]=m;if(b=s.merge(ht,(S=r())!=null?S:f),n(b,!0),z)return u()}).then(()=>{_==null||_(b,void 0),b=r(),o=!0,l.forEach(m=>m(b))}).catch(m=>{_==null||_(void 0,m)})};return i.persist={setOptions:h=>{s={...s,...h},h.storage&&(a=h.storage)},clearStorage:()=>{a==null||a.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>C(),hasHydrated:()=>o,onHydrate:h=>(c.add(h),()=>{c.delete(h)}),onFinishHydration:h=>(l.add(h),()=>{l.delete(h)})},s.skipHydration||C(),b||f},ie=se,ge=ne(ie(e=>({menu:"",token:"",setMenu:t=>e({menu:t}),setToken:t=>e({token:t})}),{name:"my-app-store"}));export{j as I,he as R,ue as a,le as b,$t as g,ge as u};
