﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zhiying_online.Common.Jwt;
using zhiying_online.Model;

namespace zhiying_online.Common.BLL
{
    /// <summary>
    /// 账号登录
    /// </summary>
    public class AccountHandle
    {
        /// <summary>
        /// 获取账号列表
        /// </summary>
        /// <returns></returns>
        public static RetMsg GetAccountList()
        {
            using (var db = new Model.zhiying_online())
            {
                var list = db.account.OrderByDescending(m => m.a_time).ToList();
                return new RetMsg { code = 0, msg = "请求成功", result = list.Select(m => new { id = m.a_id, name = m.a_name, time = m.a_time,accountid=m.a_accountid }) };
            }
        }

        /// <summary>
        /// 编辑账户
        /// </summary>
        /// <param name="id"></param>
        /// <param name="name"></param>
        /// <param name="accountid"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static RetMsg SaveAccount(long? id,string name,string accountid, string password)
        {
            using (var db = new Model.zhiying_online())
            {
                account _account = null;
                if (id.HasValue)
                {
                    _account = db.account.Find(id);
                    if (_account == null)
                    {
                        return new RetMsg { code = 1, msg = "账号不存在" };
                    }
                    _account.a_accountid = accountid;
                    _account.a_name = name;
                    _account.a_password = Tools.Encrypt(Tools.Encrypt(Tools.Encrypt(password + "_zyo")));
                }
                else
                {
                    _account = new account
                    {
                        a_id = Tools.Get_Id(),
                        a_accountid = accountid,
                        a_password = Tools.Encrypt(Tools.Encrypt(Tools.Encrypt(password + "_zyo"))),
                        a_name = name,
                        a_time = DateTime.Now
                    };
                    db.account.Add(_account);
                }
                db.SaveChanges();

                return new RetMsg { code = 0, msg = "请求成功" };
            }
        }

        /// <summary>
        /// 账号登录
        /// </summary>
        /// <param name="accountid"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static object Login(string accountid, string password)
        {
            using (var db = new Model.zhiying_online())
            {
                var jmpassword = Tools.Encrypt(Tools.Encrypt(Tools.Encrypt(password + "_zyo")));
                var _account = db.account.Where(m => m.a_accountid == accountid && m.a_password == jmpassword).FirstOrDefault();
                if (_account == null)
                {
                    return new RetMsg { code = 1, msg = "账号不存在" };
                }

                //登录成功分发accesstoken
                var Configuration = Tools.GetConfiguration();
                var ExpiresIn = 12 * 60 * 60;
                var securityKey = Configuration["JwtSecret"]; // 签名密钥
                var keyByteArray = Encoding.ASCII.GetBytes(securityKey);
                var signingKey = new SymmetricSecurityKey(keyByteArray);
                var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

                //定义角色
                var sessionid = Tools.Get_Id().ToString();
                var claim = new JwtHandle.ClaimModel
                {
                    bid = "0",
                    id = sessionid,
                    ip = Tools.GetUserIp(),
                    name = _account.a_name,
                    role = "admin",
                    usetype = "account",
                };

                //调用生成
                var token = JwtHandle.BuildJwtToken(new JwtHandle.PermissionRequirement(signingCredentials)
                {
                    claim = claim,
                    ExpiresIn = ExpiresIn,
                    issuer = "zhiying_online",
                    SecurityKey = securityKey,
                });


                //设置账号登录
                CacheManager.Default.Set_SlidingExpire("login_token_" + sessionid, token.access_token, TimeSpan.FromSeconds(ExpiresIn));


                return new { code = 0, msg = "请求成功", account = new { accountid = accountid, name = _account.a_name }, result = token };
            }
        }

        /// <summary>
        /// 设计登出
        /// </summary>
        /// <param name="accountid"></param>
        /// <returns></returns>
        public static RetMsg LogOut(string accountid)
        {
            CacheManager.Default.Remove("login_token_" + accountid);

            return new RetMsg { code = 0, msg = "请求成功" };
        }
    }
}
