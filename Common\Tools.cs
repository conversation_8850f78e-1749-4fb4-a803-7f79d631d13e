﻿using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;

namespace zhiying_online.Common
{
    public static class Tools
    {

        /// <summary>
        /// 超时时间
        /// </summary>
        public const int TIME_OUT = 5000;

        /// <summary>
        /// 获取id
        /// </summary>
        /// <param name="lenght"></param>
        /// <returns></returns>
        private static IdWorker worker = new IdWorker();
        public static long Get_Id()
        {
            return worker.nextId();
        }


        #region ========获取随机验证码========
        public static string RandomNumber(int min, int max)
        {
            Guid randSeedGuid = Guid.NewGuid();
            Random rand = new Random(BitConverter.ToInt32(randSeedGuid.ToByteArray(), 0));
            return rand.Next(min, max).ToString();
        }
        #endregion

        #region ========HTML请求========
        /// <summary>
        /// htmlGET同步请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="encoding"></param>
        /// <returns></returns>
        public static string HtmlGet(string url)
        {
            try
            {
                var wc = new WebClient();
                var data = wc.DownloadData(url);
                var str = Encoding.UTF8.GetString(data);
                return str;
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 获取json格式的数据
        /// </summary>
        /// <param name="Url"></param>
        /// <returns></returns>
        public static string HtmlGet_Json(string Url)
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(Url);
                request.Proxy = null;
                request.KeepAlive = false;
                request.Method = "GET";
                request.ContentType = "application/json; charset=UTF-8";
                request.AutomaticDecompression = DecompressionMethods.GZip;
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Stream myResponseStream = response.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.UTF8);
                string retString = myStreamReader.ReadToEnd();
                myStreamReader.Close();
                myResponseStream.Close();
                if (response != null)
                {
                    response.Close();
                }
                if (request != null)
                {
                    request.Abort();
                }
                return retString;
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// HttpPost同步请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="postdata"></param>
        /// <param name="encoding"></param>
        /// <returns></returns>
        public static string HtmlPost(string url, string postdata, Encoding encoding, string ContentType = "application/x-www-form-urlencoded")
        {
            try
            {
                var wc = new WebClient();
                wc.Headers.Add("Content-Type", ContentType);//采取POST方式必须加的header，如果改为GET方式的话就去掉这句话即可  
                var data = wc.UploadData(url, "POST", encoding.GetBytes(postdata));//得到返回字符流  
                var s = encoding.GetString(data);
                return s;
            }
            catch (Exception e)
            {
                Logger.debug("HtmlPost错误信息：" + Logger.GetExceptionStr(e));
                return "";
            }
        }

        /// <summary>
        /// 模拟form上传文件
        /// </summary>
        /// <param name="url"></param>
        /// <param name="fileName"></param>
        /// <param name="mstream"></param>
        /// <returns></returns>
        public static string UploadFile(string url, string fileName, Stream mstream)
        {
            var buffer = ReadFull(mstream); // 二进制文件

            string boundary = "---------------------------" + DateTime.Now.Ticks.ToString("x");
            string PREFIX = "--", LINE_END = "\r\n";

            StringBuilder sb = new StringBuilder();
            sb.Append(PREFIX + boundary + LINE_END);
            sb.Append("Content-Disposition: form-data; name=\"" + fileName + "\"; filename=\"" + fileName + "\"" + LINE_END);
            sb.Append("Content-Type: " + fileName + LINE_END + LINE_END);
            WebRequest request = WebRequest.Create(url);
            request.Method = "POST";
            request.ContentType = "multipart/form-data;boundary=" + boundary;
            Stream dataStream = request.GetRequestStream();
            dataStream.Write(Encoding.UTF8.GetBytes(sb.ToString()), 0, Encoding.UTF8.GetByteCount(sb.ToString()));
            dataStream.Write(buffer, 0, buffer.Length);
            dataStream.Write(Encoding.UTF8.GetBytes(LINE_END + PREFIX + boundary + PREFIX + LINE_END), 0, Encoding.UTF8.GetByteCount(LINE_END + PREFIX + boundary + PREFIX + LINE_END));
            dataStream.Close();

            WebResponse webResponse = request.GetResponse();
            Stream newStream = webResponse.GetResponseStream();

            StreamReader rdr = new StreamReader(newStream);
            var result = rdr.ReadToEnd();
            return result;
        }
        /// <summary>
        /// 流转二进制
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static byte[] ReadFull(Stream input)
        {
            byte[] buffer = new byte[16 * 1024];
            using (MemoryStream ms = new MemoryStream())
            {
                int read;
                while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
                {
                    ms.Write(buffer, 0, read);
                }
                return ms.ToArray();
            }
        }
        #endregion

        #region ========生成随机字母与数字=========
        /// <summary>
        /// 生成随机字母与数字
        /// </summary>
        /// <param name="IntStr">生成长度</param>
        /// <returns></returns>
        public static string GetStr(int Length)
        {
            return GetStr(Length, false);
        }
        /// <summary>
        /// 生成随机字母与数字
        /// </summary>
        /// <param name="Length">生成长度</param>
        /// <param name="Sleep">是否要在生成前将当前线程阻止以避免重复</param>
        /// <returns></returns>
        public static string GetStr(int Length, bool Sleep)
        {
            if (Sleep)
                System.Threading.Thread.Sleep(1);
            char[] Pattern = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' };
            string result = "";
            int n = Pattern.Length;
            Random random = new Random(~unchecked((int)DateTime.Now.Ticks));
            for (int i = 0; i < Length; i++)
            {
                int rnd = random.Next(0, n);
                result += Pattern[rnd];
            }
            return result;
        }
        /// <summary>
        /// 生成随机字母与数字
        /// </summary>
        /// <param name="IntStr">生成长度</param>
        /// <returns></returns>
        public static int GetStrs(int Length)
        {
            return GetStrs(Length, false);
        }
        /// <summary>
        /// 生成随机字母与数字
        /// </summary>
        /// <param name="Length">生成长度</param>
        /// <param name="Sleep">是否要在生成前将当前线程阻止以避免重复</param>
        /// <returns></returns>
        public static int GetStrs(int Length, bool Sleep)
        {
            if (Sleep)
                System.Threading.Thread.Sleep(3);
            int[] Pattern = new int[Length];
            for (int i = 0; i < Length; i++)
            {
                Pattern[i] = i;
            }
            int result = 0;
            int n = Pattern.Length;
            Random random = new Random(~unchecked((int)DateTime.Now.Ticks));
            int rnd = random.Next(0, n);
            result = Pattern[rnd];
            return result;
        }
        /// <summary>
        /// 生成纯数字
        /// </summary>
        /// <param name="Length"></param>
        /// <param name="Sleep"></param>
        /// <returns></returns>
        public static string GetNumber(int Length, bool Sleep)
        {
            if (Sleep)
                System.Threading.Thread.Sleep(3);
            char[] Pattern = new char[] { '2', '3', '4', '5', '6', '7', '8', '9', '0' };
            string result = "";
            int n = Pattern.Length;
            Random random = new Random(~unchecked((int)DateTime.Now.Ticks));
            for (int i = 0; i < Length; i++)
            {
                int rnd = random.Next(0, n);
                result += Pattern[rnd];
            }
            return result;
        }
        #endregion


        #region 获取客户端ip
        public static string GetUserIp()
        {
            var context = HelperHttpContext.Current;
            var ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ip))
            {
                ip = context.Connection.RemoteIpAddress.ToString();
            }
            return ip;
        }
        #endregion

        #region 获取本机局域网ip
        public static string GetLocalIPAddress()
        {
            System.Net.IPAddress[] addressList = Dns.GetHostEntry(Dns.GetHostName()).AddressList;
            string strNativeIP = "";
            string strServerIP = "";
            if (addressList.Length > 1)
            {
                strNativeIP = addressList[0].ToString();
                strServerIP = addressList[1].ToString();
            }
            else if (addressList.Length == 1)
            {
                strServerIP = addressList[0].ToString();
            }
            return strServerIP;
        }
        #endregion

        #region bool IsIPAddress(str1) 判断是否是IP格式
        /// <summary>
        /// 判断是否是IP地址格式 0.0.0.0
        /// </summary>
        /// <param name="str1">待判断的IP地址</param>
        /// <returns>true or false</returns>
        public static bool IsIPAddress(string str1)
        {
            if (str1 == null || str1 == string.Empty || str1.Length < 7 || str1.Length > 15) return false;

            string regformat = @"^\d{1,3}[\.]\d{1,3}[\.]\d{1,3}[\.]\d{1,3}$";

            Regex regex = new Regex(regformat, RegexOptions.IgnoreCase);
            return regex.IsMatch(str1);
        }
        #endregion

        #region 数组转SQL条件
        public static string ListToSqlstr(object[] list)
        {
            var sql = "";
            foreach (var item in list)
            {
                sql += "'" + item.ToString() + "',";
            }
            if (sql.Length > 0)
            {
                sql = sql.Substring(0, sql.Length - 1);
            }
            return sql;
        }
        #endregion

        #region 加解密
        /// <summary>
        /// 解密所有消息的基础方法
        /// </summary>
        /// <param name="sessionKey">储存在 SessionBag 中的当前用户 会话 SessionKey</param>
        /// <param name="encryptedData">接口返回数据中的 encryptedData 参数</param>
        /// <param name="iv">接口返回数据中的 iv 参数，对称解密算法初始向量</param>
        /// <returns></returns>
        public static string DecodeEncryptedData(string sessionKey, string encryptedData, string iv)
        {
            var aesCipher = Convert.FromBase64String(encryptedData);
            var aesKey = Convert.FromBase64String(sessionKey);
            var aesIV = Convert.FromBase64String(iv);

            var result = AES_Decrypt(encryptedData, aesIV, aesKey);
            var resultStr = Encoding.UTF8.GetString(result);
            return resultStr;
        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="Input"></param>
        /// <param name="Iv"></param>
        /// <param name="Key"></param>
        /// <returns></returns>
        private static byte[] AES_Decrypt(String Input, byte[] Iv, byte[] Key)
        {
            RijndaelManaged aes = new RijndaelManaged();
            aes.KeySize = 128;//原始：256
            aes.BlockSize = 128;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Key;
            aes.IV = Iv;
            var decrypt = aes.CreateDecryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    byte[] msg = new byte[xXml.Length + 32 - xXml.Length % 32];
                    Array.Copy(xXml, msg, xXml.Length);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = decode2(ms.ToArray());
            }
            return xBuff;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="decrypted"></param>
        /// <returns></returns>
        private static byte[] decode2(byte[] decrypted)
        {
            int pad = (int)decrypted[decrypted.Length - 1];
            if (pad < 1 || pad > 32)
            {
                pad = 0;
            }
            byte[] res = new byte[decrypted.Length - pad];
            Array.Copy(decrypted, 0, res, 0, decrypted.Length - pad);
            return res;
        }
        #endregion

        #region 时间操作
        public static DateTime BaseTime = new DateTime(1970, 1, 1);//Unix起始时间

        /// <summary>
        /// 转换微信DateTime时间到C#时间
        /// </summary>
        /// <param name="dateTimeFromXml">微信DateTime</param>
        /// <returns></returns>
        public static DateTime GetDateTimeFromXml(long dateTimeFromXml)
        {
            return BaseTime.AddTicks((dateTimeFromXml + 8 * 60 * 60) * 10000000);
        }
        /// <summary>
        /// 转换微信DateTime时间到C#时间
        /// </summary>
        /// <param name="dateTimeFromXml">微信DateTime</param>
        /// <returns></returns>
        public static DateTime GetDateTimeFromXml(string dateTimeFromXml)
        {
            return GetDateTimeFromXml(long.Parse(dateTimeFromXml));
        }

        /// <summary>
        /// 获取微信DateTime（UNIX时间戳）
        /// </summary>
        /// <param name="dateTime">时间</param>
        /// <returns></returns>
        public static long GetWeixinDateTime(DateTime dateTime)
        {
            return (dateTime.Ticks - BaseTime.Ticks) / 10000000 - 8 * 60 * 60;
        }
        /// <summary>
        /// 获取毫秒级时间戳
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public static long GetWeixinDateTimeHm(DateTime dateTime)
        {
            //北京时间相差8小时
            DateTime startTime = TimeZoneInfo.ConvertTime(new DateTime(1970, 1, 1, 8, 0, 0, 0), TimeZoneInfo.Local);
            long t = (dateTime.Ticks - startTime.Ticks) / 10000;
            return t;
        }
        #endregion

        #region 根据XML信息填充实实体
        /// <summary>
        /// 根据XML信息填充实实体
        /// </summary>
        /// <typeparam name="T">MessageBase为基类的类型，Response和Request都可以</typeparam>
        /// <param name="entity">实体</param>
        /// <param name="doc">XML</param>
        public static void FillEntityWithXml<T>(this T entity, XDocument doc)
        {
            var root = doc.Root;

            var props = entity.GetType().GetProperties();
            foreach (var prop in props)
            {
                var propName = prop.Name;
                if (root.Element(propName) != null)
                {
                    switch (prop.PropertyType.Name)
                    {
                        case "DateTime":
                            prop.SetValue(entity, GetDateTimeFromXml(root.Element(propName).Value), null);
                            break;
                        case "Boolean":
                            if (propName == "FuncFlag")
                            {
                                prop.SetValue(entity, root.Element(propName).Value == "1", null);
                            }
                            else
                            {
                                goto default;
                            }
                            break;
                        case "Int32":
                            prop.SetValue(entity, int.Parse(root.Element(propName).Value), null);
                            break;
                        case "Int64":
                            prop.SetValue(entity, long.Parse(root.Element(propName).Value), null);
                            break;
                        case "Double":
                            prop.SetValue(entity, double.Parse(root.Element(propName).Value), null);
                            break;

                        default:
                            prop.SetValue(entity, root.Element(propName).Value, null);
                            break;
                    }
                }
            }
        }
        #endregion

        #region MD5加密
        /// <summary>
        /// 加密(MD5)
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string Encrypt(string str)
        {
            var md5 = new MD5CryptoServiceProvider();

            byte[] t = md5.ComputeHash(Encoding.UTF8.GetBytes(str));

            var sb = new StringBuilder(32);

            for (int i = 0; i < t.Length; i++)
            {
                sb.Append(t[i].ToString("x").PadLeft(2, '0'));
            }
            return sb.ToString();
        }

        /// <summary>
        /// 加密
        /// </summary>
        /// <param name="str">需加密的字符串</param>
        /// <returns>加密后的字符</returns>
        public static string MD5ToBase64String(string str)
        {
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] MD5 = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(str));//MD5(注意UTF8编码)
            string result = Convert.ToBase64String(MD5, 0, MD5.Length);//Base64
            return result;
        }
        /// <summary>
        /// HMAC-SHA256签名
        /// </summary>
        /// <param name="secret"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string Hash_Hmac(string secret, string value)
        {
            //开始加密生成
            var encoding = new System.Text.UTF8Encoding();
            byte[] keyByte = encoding.GetBytes(secret);
            byte[] messageBytes = encoding.GetBytes(value.ToString());
            using (var hmacsha256 = new HMACSHA256(keyByte))
            {
                byte[] hashmessage = hmacsha256.ComputeHash(messageBytes);
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < hashmessage.Length; i++)
                {
                    builder.Append(hashmessage[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
        /// <summary>
        /// SHA256加密
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string SHA256Hash(string str)
        {
            byte[] data = Encoding.UTF8.GetBytes(str);
            SHA256 shaM = new SHA256Managed();
            var hashBytes = shaM.ComputeHash(data);
            return Convert.ToBase64String(hashBytes);
        }
        #endregion

        #region 读取本地配置JS文件
        /// <summary>
        /// 获取商家配置信息
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        /// 需要注意.net core 托管模式的路径
        public static T GetConfigJson<T>(string path)
        {
            var list = JsonConvert.DeserializeObject<T>(File.ReadAllText(AppContext.BaseDirectory + path));
            return list;
        }
        /// <summary>
        /// 保存商家配置信息
        /// </summary>
        /// <param name="path"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        /// 需要注意.net core 托管模式的路径
        public static void SaveConfigJson(string path, string config)
        {
            System.IO.File.WriteAllText(AppContext.BaseDirectory + path, config);
        }
        #endregion

        #region
        /// <summary>
        /// 根据完整文件路径获取FileStream
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static FileStream GetFileStream(string fileName)
        {
            FileStream fileStream = null;
            if (!string.IsNullOrEmpty(fileName) && File.Exists(fileName))
            {
                fileStream = new FileStream(fileName, FileMode.Open);
            }
            return fileStream;
        }
        #endregion

        #region 读取配置文件
        public static IConfiguration GetConfiguration()
        {
            IConfiguration Configuration = new ConfigurationBuilder().SetBasePath(AppContext.BaseDirectory).AddJsonFile("appsettings.json", optional: false, reloadOnChange: true).Build();
            return Configuration;
        }
        #endregion

        #region 信息脱敏
        /// <summary>
        /// 手机号脱敏
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        public static string MaskPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber) || phoneNumber.Length < 11)
                return phoneNumber;

            // 保留前3位和后4位，中间用****代替
            return phoneNumber.Substring(0, 3) + "****" + phoneNumber.Substring(7);
        }

        /// <summary>
        /// 身份证号脱敏
        /// </summary>
        /// <param name="idNumber"></param>
        /// <returns></returns>
        public static string MaskIdNumber(string idNumber)
        {
            if (string.IsNullOrEmpty(idNumber) || idNumber.Length < 18)
                return idNumber;

            // 保留前6位和后4位，中间用****代替
            return idNumber.Substring(0, 6) + "**********" + idNumber.Substring(14);
        }
        #endregion
    }
}
