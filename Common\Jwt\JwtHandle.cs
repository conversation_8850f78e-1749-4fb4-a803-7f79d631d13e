﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace zhiying_online.Common.Jwt
{
    public class JwtHandle
    {
        /// <summary>
        /// 参数对象
        /// </summary>
        public class PermissionRequirement : IAuthorizationRequirement
        {

            /// <summary>
            /// 发布者
            /// </summary>
            public string issuer { get; set; }

            /// <summary>
            /// 签名秘钥
            /// </summary>
            public string SecurityKey { get; set; }

            /// <summary>
            /// 过期时间(毫秒)
            /// </summary>
            public int ExpiresIn { get; set; }

            /// <summary>
            /// 账号信息
            /// </summary>
            public ClaimModel claim { get; set; }

            /// <summary>
            /// 签名验证
            /// </summary>
            public SigningCredentials SigningCredentials { get; set; }

            /// <summary>
            /// 构造
            /// </summary>
            /// <param name="signingCredentials">签名验证实体</param>
            public PermissionRequirement(SigningCredentials signingCredentials)
            {
                SigningCredentials = signingCredentials;
            }
        }

        /// <summary>
        /// 定义角色
        /// </summary>
        public class ClaimModel
        {
            /// <summary>
            /// 账号名称
            /// </summary>
            public string name { get; set; }

            /// <summary>
            /// 授权账号
            /// </summary>
            public string id { get; set; }

            /// <summary>
            /// 商家ID
            /// </summary>
            public string bid { get; set; }

            /// <summary>
            /// 对应账号类型 管理员 普通账号
            /// </summary>
            public string role { get; set; }

            /// <summary>
            /// 授权使用场景saas minapp openapi
            /// </summary>
            public string usetype { get; set; }

            /// <summary>
            /// 请求ip
            /// </summary>
            public string ip { get; set; }
        }

        /// <summary>
        /// 生产Token
        /// </summary>
        /// <param name="permissionRequirement"></param>
        /// <returns></returns>
        public static dynamic BuildJwtToken(PermissionRequirement permissionRequirement)
        {
            //定义角色
            var claims = new Claim[] {
                new Claim ("name",permissionRequirement.claim.name),
                new Claim ("id",permissionRequirement.claim.id),
                new Claim ("bid",permissionRequirement.claim.bid),
                new Claim ("role",permissionRequirement.claim.role),
                new Claim ("usetype",permissionRequirement.claim.usetype),
                new Claim ("ip",permissionRequirement.claim.ip),
            };

            //加密签名
            var keyByteArray = Encoding.ASCII.GetBytes(permissionRequirement.SecurityKey);
            var signingKey = new SymmetricSecurityKey(keyByteArray);
            permissionRequirement.SigningCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

            //定义时间和规则
            var now = DateTime.Now;
            var expirestime = now.AddSeconds(permissionRequirement.ExpiresIn);
            var jwt_token = new JwtSecurityToken(issuer: permissionRequirement.issuer, audience: permissionRequirement.claim.name, claims: claims, notBefore: now, expires: expirestime, signingCredentials: permissionRequirement.SigningCredentials);

            //生成token
            var encodedJwt_Token = new JwtSecurityTokenHandler().WriteToken(jwt_token);
            var responseTokenJson = new
            {
                access_token = encodedJwt_Token,
                expires_in = permissionRequirement.ExpiresIn,
                token_type = "Bearer"
            };

            //返回
            return responseTokenJson;
        }
    }
}
