﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Common;

namespace zhiying_online.Model
{
    /// <summary>
    /// 数据库模型
    /// </summary>
    public partial class zhiying_online : DbContext
    {
        public zhiying_online(bool read = false)
        {

        }

        public zhiying_online(DbContextOptions<zhiying_online> options)
            : base(options)
        {
        }

        public virtual DbSet<account> account { get; set; }


        public virtual DbSet<booking> booking { get; set; }


        public virtual DbSet<patient> patient { get; set; }

        public virtual DbSet<system> system { get; set; }

        public virtual DbSet<user> user { get; set; }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var Configuration = Tools.GetConfiguration();
                optionsBuilder.UseMySql($"Server={Configuration["Mysql:Server"]};Port={Configuration["Mysql:Port"]};Uid={Configuration["Mysql:Uid"]};Pwd={Configuration["Mysql:Pwd"]};Database={Configuration["Mysql:DataBase"]};", x => x.ServerVersion("8.0.25-mysql"));
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<account>(entity =>
            {
                entity.HasKey(e => e.a_id)
                  .HasName("PRIMARY");
            });

            modelBuilder.Entity<booking>(entity =>
            {
                entity.HasKey(e => e.b_id)
                  .HasName("PRIMARY");
            });

            modelBuilder.Entity<patient>(entity =>
            {
                entity.HasKey(e => e.p_id)
                  .HasName("PRIMARY");
            });

            modelBuilder.Entity<system>(entity =>
            {
                entity.HasKey(e => e.s_id)
                  .HasName("PRIMARY");
            });

            modelBuilder.Entity<user>(entity =>
            {
                entity.HasKey(e => e.u_id)
                  .HasName("PRIMARY");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
