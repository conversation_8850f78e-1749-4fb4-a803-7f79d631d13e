using log4net.Config;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using zhiying_online.Common;
using zhiying_online.Common.Attr;

namespace zhiying_online
{
    public class Startup
    {
        //服务名称
        public static string ServerName = Assembly.GetExecutingAssembly().GetName().Name.Replace("zhiying_online", "");


        // This method gets called by the runtime. Use this method to add services to the container.
        // For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=398940
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers().AddNewtonsoftJson(options =>
            {
                //数据格式首字母小写 不使用驼峰   小驼峰firstName  大驼峰 FirstName
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                //使用默认方式，不更改元数据的key的大小写
                //options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                // 忽略循环引用
                options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                // 设置时间格式
                options.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
                //忽略空值 不包含属性的null序列化
                options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                //long丢失精度，转string
                options.SerializerSettings.ContractResolver = new CustomContractResolver();
            });

            //添加httpcontext类
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            HelperHttpContext.serviceCollection = services;

            //注册全局异常监听
            services.AddMvc(options =>
            {
                options.Filters.Add<HttpGlobalExceptionFilter>();//全局注册错误异常
            });

            //注册全局sql过滤
            services.AddMvc(options =>
            {
                options.Filters.Add<SqlFilter>();
            });

            //接口文档服务
            //swagger文档
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "zhiying_online", Version = "V1" });
                //c.DocumentFilter<HiddenApiFilter>();//过滤的核心filter
                c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetExecutingAssembly().GetName().Name}.xml"), true); //添加控制器层注释（true表示显示控制器注释）
            });

            //获取客户端真实ip
            services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();
            });

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            // 允许所有跨域，cors是在ConfigureServices方法中配置的跨域策略名称
            app.UseMiddleware<CorsMiddleware>();

            app.UseSwagger();

            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint($"/swagger/v1/swagger.json", ServerName);
            });

            // 默认wwwroot配置
            app.UseDefaultFiles();
            // 启用静态文件服务
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthorization();
            app.UseForwardedHeaders();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            //注册log4net
            XmlConfigurator.ConfigureAndWatch(Common.Logger.repository, new FileInfo(AppContext.BaseDirectory + "log4net.config"));
        }
    }
}
