﻿using log4net;
using log4net.Repository;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;

namespace zhiying_online.Common
{
    public class Logger
    {
        private static string defaultNotifyEmail = "<EMAIL>";

        public static ILoggerRepository repository = LogManager.CreateRepository("NETCoreRepository");

        //OFF->FATAL->ERROR->WARN->INFO->DEBUG/ALL
        private static ILog logAll = log4net.LogManager.GetLogger(repository.Name, "All");
        private static ILog logErr = log4net.LogManager.GetLogger(repository.Name, "Error");
        private static List<string> listEmail = new List<string>();
        private static List<string> listPhone = new List<string>();

        private DateTime dtStopWatchStart;  // 计时器开始时间
        private DateTime dtStopWatchStop;   // 计时器结束时间

        #region 判断是否是合格的手机号码，1开头的11位数字
        public static bool IsMobilePhone(string phone)
        {
            Regex regex = new Regex("^1\\d{10}$");
            return regex.IsMatch(phone);
        }
        #endregion

        #region 判断是否是合格的邮件地址
        public static bool IsValidEmail(string email)
        {
            Regex regex = new Regex(@"^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$");
            return regex.IsMatch(email);
        }
        #endregion 

        #region 计时开始
        /// <summary>
        /// 计时开始
        /// </summary>
        public void StopWatchStart()
        {
            dtStopWatchStart = DateTime.Now;
        }
        #endregion

        #region 计时结束
        /// <summary>
        /// 计时结束，如果大于给定时间（毫秒），则给出错误日志
        /// </summary>
        /// <param name="iPeriod">限定完成时间，单位毫秒</param>
        /// <param name="strMessage">超出限定时间后给出的日志</param>
        public void StopWatchStop(int iPeriod, string strMessage)
        {
            dtStopWatchStop = DateTime.Now;
            int iTotal = (int)(dtStopWatchStop - dtStopWatchStart).TotalMilliseconds;
            if (iTotal > iPeriod)
            {
                error(strMessage);
            }
        }
        #endregion

        #region 添加邮件通知地址
        /// <summary>
        /// 添加邮件通知地址
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        public static bool AddNotifyEmail(string email)
        {
            bool b = false;
            if (!listEmail.Contains(email))
            {
                if (IsValidEmail(email))
                {
                    listEmail.Add(email);
                    b = true;
                }
            }
            return b;
        }
        #endregion

        #region 去除邮件通知地址
        /// <summary>
        /// 去除邮件通知地址
        /// </summary>
        /// <param name="email">邮件地址</param>
        /// <returns>true成功，false失败</returns>
        public static bool RemoveNotifyEmail(string email)
        {
            bool b = false;
            if (listEmail.Contains(email))
            {
                b = listEmail.Remove(email);
            }
            return b;
        }
        #endregion 

        #region 添加短信通知电话
        /// <summary>
        /// 添加短信通知电话
        /// </summary>
        /// <param name="phone">电话号码</param>
        /// <returns>true成功，false失败</returns>
        public static bool AddNotifyPhone(string phone)
        {
            bool b = false;

            if (!listPhone.Contains(phone))
            {
                if (IsMobilePhone(phone))
                {
                    listPhone.Add(phone);
                    b = true;
                }
            }
            return b;
        }
        #endregion

        #region 去除短信通知电话
        /// <summary>
        /// 去除短信通知电话
        /// </summary>
        /// <param name="phone">电话号码</param>
        /// <returns>true成功，false失败</returns>
        public static bool RemoveNotifyPhone(string phone)
        {
            bool b = false;
            if (listPhone.Contains(phone))
            {
                b = listPhone.Remove(phone);
            }
            return b;
        }
        #endregion 

        #region 群发邮件
        /// <summary>
        /// 发邮件
        /// </summary>
        /// <param name="title">邮件标题</param>
        /// <param name="content">邮件内容</param>
        public static void SendMail(string title, object content)
        {
            sendMail(title, content, new List<string>());
        }
        /// <summary>
        /// 群发邮件
        /// </summary>
        /// <param name="title">邮件标题</param>
        /// <param name="content">邮件内容</param>
        /// <param name="contacts">邮件列表</param>
        private static void sendMail(string title, object content, List<string> contacts)
        {
            if (contacts.Count == 0)
            {
                //MailHelper.SendMail(title, content.ToString(), defaultNotifyEmail);
            }
            else
            {
                foreach (var email in contacts)
                {
                    //MailHelper.SendMail(title, content.ToString(), email);
                }
            }
            return;
        }
        #endregion

        #region 群发短消息
        /// <summary>
        /// 群发短消息
        /// </summary>
        /// <param name="ext">不知道是个什么参数，必须送ext</param>
        /// <param name="message">群发的消息内容</param>
        /// <param name="phones">群发的电话号码列表</param>
        private static void sendSMS(string ext, object message, List<string> phones)
        {
#if SMS
            if (phones.Count == 0)
            {
                SMSHelper.Send(defaultNotifyPhone, message.ToString(), ext, SMSHelper.SMSType.漫道科技);
            }
            else
            {
                foreach (var phone in phones)
                {
                    SMSHelper.Send(phone, message.ToString(), ext, SMSHelper.SMSType.漫道科技);
                }
            }
#endif
        }
        #endregion

        #region 致命错误
        /// <summary>
        /// Fatal
        /// </summary>
        /// <param name="o"></param>
        public static void fatal(object o)
        {
            logErr.Fatal(o);
            sendMail("Fatal log", o, listEmail);
            sendSMS("ext", o, listPhone);
        }
        #endregion 

        #region 错误
        /// <summary>
        /// Error
        /// </summary>
        /// <param name="o"></param>
        public static void error(object o)
        {
            o = "TJCAW " + o + "\n";
            logErr.Error(o);
            sendMail("Fatal log", o, listEmail);
            sendSMS("ext", o, listPhone);
        }
        #endregion 

        #region 告警
        /// <summary>
        /// Warn
        /// </summary>
        /// <param name="o"></param>
        public static void warn(object o)
        {
            logAll.Warn(o);
        }
        #endregion 

        #region 信息
        /// <summary>
        /// Info
        /// </summary>
        /// <param name="o"></param>
        public static void info(object o)
        {
            logAll.Info(o);
        }
        #endregion 

        #region 调试
        /// <summary>
        /// Debug
        /// </summary>
        /// <param name="o"></param>
        public static void debug(object o)
        {
            o = "TJCAW " + o + "\n";
            logAll.Debug(o);
        }
        #endregion 

        #region 测试方法
        public static void SelfTest()
        {
            fatal("fatal log");
            error("error log");
            warn("warn log");
            info("info log");
            debug("debug log");
            bool b = false;
            b = AddNotifyEmail("<EMAIL>");
            error("error <NAME_EMAIL>");
            b = AddNotifyEmail("aaa");
            if (!b)
            {
                debug("测试错误邮件地址成功!");
            }
            b = AddNotifyPhone("13920776059");
            if (b)
            {
                debug("测试正确电话号码成功!");
            }
            b = AddNotifyPhone("111");
            if (!b)
            {
                debug("测试错误电话号码成功!");
            }

            // 测试性能监测
            Logger l = new Logger();
            l.StopWatchStart();
            l.StopWatchStop(1000, "不应该出现在日志中，如果出现则有错误");

            l.StopWatchStart();
            System.Threading.Thread.Sleep(1000);
            l.StopWatchStop(500, "性能监测报警成功！");
        }
        #endregion

        #region 异常错误输出
        /// <summary>
        /// 异常错误输出
        /// </summary>
        /// <param name="ex"></param>
        public static string GetExceptionStr(Exception ex)
        {
            return string.Format(
                "Message\n{0}\nSource\n{1}\nTargetSite\n{2}\nStackTrace\n{3}",
                ex.ToString(), ex.Source, ex.TargetSite, ex.StackTrace);
        }
        #endregion 
    }
}
