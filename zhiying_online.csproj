<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<!--创建xml文件-->
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<TargetFramework>netcoreapp3.1</TargetFramework>
		<UserSecretsId>5219c471-d693-43b7-8508-ffabaa8a5af0</UserSecretsId>
	</PropertyGroup>


	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>bin\Debug\netcoreapp3.1\zhiying_online.xml</DocumentationFile>
    <OutputPath>bin\Debug\netcoreapp3.1\</OutputPath>
  </PropertyGroup>


	<ItemGroup>
	  <None Include="wwwroot\wechat\assets\index-DWCUXl9t.js" />
	  <None Include="wwwroot\wechat\assets\index-VVeSPkJi.js" />
	  <None Include="wwwroot\wechat\assets\vendor-CKw6qmF3.js" />
	</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Log4NetCore" Version="1.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.3" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="5.0.1" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="3.2.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.6.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="zhiying_online.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
