<?xml version="1.0"?>
<doc>
    <assembly>
        <name>zhiying_online</name>
    </assembly>
    <members>
        <member name="T:zhiying_online.Common.Attr.HttpGlobalExceptionFilter">
            <summary>
            全局监听异常
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Attr.RequestLog.RequestBody">
            <summary>
            请求体中的所有值
            </summary>
        </member>
        <member name="T:zhiying_online.Common.Attr.SqlFilter">
            <summary>
            SQL 注入拦截
            </summary>
        </member>
        <member name="M:zhiying_online.Common.Attr.SqlFilter.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            sql防注入
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:zhiying_online.Common.Attr.SqlFilter.ProcessSqlStr(System.String,System.Int32)">
            <summary>
             分析用户请求是否正常
            </summary>
            <param name="Str">传入用户提交数据</param>
            <returns>返回是否含有SQL注入式攻击代码</returns>
        </member>
        <member name="T:zhiying_online.Common.BLL.AccountHandle">
            <summary>
            账号登录
            </summary>
        </member>
        <member name="M:zhiying_online.Common.BLL.AccountHandle.GetAccountList">
            <summary>
            获取账号列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.AccountHandle.SaveAccount(System.Nullable{System.Int64},System.String,System.String,System.String)">
            <summary>
            编辑账户
            </summary>
            <param name="id"></param>
            <param name="name"></param>
            <param name="accountid"></param>
            <param name="password"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.AccountHandle.Login(System.String,System.String)">
            <summary>
            账号登录
            </summary>
            <param name="accountid"></param>
            <param name="password"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.AccountHandle.LogOut(System.String)">
            <summary>
            设计登出
            </summary>
            <param name="accountid"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.BLL.BookingHandle">
            <summary>
            预约检查
            </summary>
        </member>
        <member name="M:zhiying_online.Common.BLL.BookingHandle.GetBookingList(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.Int32,System.Int32)">
            <summary>
            获取预约列表
            </summary>
            <param name="name"></param>
            <param name="phone"></param>
            <param name="idcard"></param>
            <param name="type"></param>
            <param name="star"></param>
            <param name="end"></param>
            <param name="status"></param>
            <param name="PageSize"></param>
            <param name="PageNo"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.BookingHandle.GetBookingListByPid(System.Nullable{System.Int64})">
            <summary>
            根据就诊人获取预约列表
            </summary>
            <param name="pid"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.BookingHandle.SaveBooking(System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int32,System.Nullable{System.DateTime})">
            <summary>
            保存预约
            </summary>
            <param name="uid"></param>
            <param name="pid"></param>
            <param name="type"></param>
            <param name="bookingtime"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.BookingHandle.EnableBooking(System.Nullable{System.Int64})">
            <summary>
            取消预约
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.BLL.PatientHandle">
            <summary>
            就诊人管理
            </summary>
        </member>
        <member name="M:zhiying_online.Common.BLL.PatientHandle.GetPatientList(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32)">
            <summary>
            获取就诊用户列表
            </summary>
            <param name="openid"></param>
            <param name="phone"></param>
            <param name="idcard"></param>
            <param name="star"></param>
            <param name="end"></param>
            <param name="PageSize"></param>
            <param name="PageNo"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.PatientHandle.GetGetPatientByUid(System.Int64)">
            <summary>
            获取诊用用户列表
            </summary>
            <param name="uid"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.PatientHandle.SavePatient(System.Nullable{System.Int64},System.Nullable{System.Int64},System.String,System.String,System.String,System.String)">
            <summary>
            保存就诊人
            </summary>
            <param name="id"></param>
            <param name="uid"></param>
            <param name="idcard"></param>
            <param name="phone"></param>
            <param name="name"></param>
            <param name="sex"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.PatientHandle.DelPatient(System.Nullable{System.Int64})">
            <summary>
            删除就诊人
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.BLL.ReportHandle">
            <summary>
            获取影片
            </summary>
        </member>
        <member name="T:zhiying_online.Common.BLL.SystemHandle">
            <summary>
            获取配置信息
            </summary>
        </member>
        <member name="M:zhiying_online.Common.BLL.SystemHandle.GetSystemConfig(System.String)">
            <summary>
            获取配置信息
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.SystemHandle.SaveSystemConfig(System.String,System.String)">
            <summary>
            保存配置
            </summary>
            <param name="type"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.BLL.UserHandle">
            <summary>
            获取用户信息
            </summary>
        </member>
        <member name="M:zhiying_online.Common.BLL.UserHandle.GetUserList(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32)">
            <summary>
            获取微信用户列表
            </summary>
            <param name="openid"></param>
            <param name="nickname"></param>
            <param name="star"></param>
            <param name="end"></param>
            <param name="PageSize"></param>
            <param name="PageNo"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.UserHandle.GetUserByID(System.Int64)">
            <summary>
            根据id获取用户
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.UserHandle.GetUserByOpenid(System.String)">
            <summary>
            根据openid获取用户
            </summary>
            <param name="openid"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.UserHandle.SaveUser(System.Nullable{System.Int64},System.String,System.String,System.String,System.String)">
            <summary>
            保存用户
            </summary>
            <param name="id"></param>
            <param name="openid"></param>
            <param name="unionid"></param>
            <param name="nickname"></param>
            <param name="headerimg"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.BLL.UserHandle.GetUserByCode(System.String)">
            <summary>
            根据code获取用户
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="F:zhiying_online.Common.CacheManager.dicLock">
            <summary>
            本地缓存锁
            </summary>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Lcok(System.String,System.Int32)">
            <summary>
            本地加锁机制防止重复
            </summary>
            <param name="key">key</param>
            <param name="timespan">锁时长，单位s</param>
            <returns>存在则返回0，不存在则返回1</returns>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Get``1(System.String)">
            <summary>
            取得缓存数据
            </summary>
            <typeparam name="T">类型值</typeparam>
            <param name="key">关键字</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Set_NotExpire``1(System.String,``0)">
            <summary>
            设置缓存(永不过期)
            </summary>
            <param name="key">关键字</param>
            <param name="value">缓存值</param>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Set_SlidingExpire``1(System.String,``0,System.TimeSpan)">
            <summary>
            设置缓存(滑动过期:超过一段时间不访问就会过期,一直访问就一直不过期)
            </summary>
            <param name="key">关键字</param>
            <param name="value">缓存值</param>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Set_AbsoluteExpire``1(System.String,``0,System.TimeSpan)">
            <summary>
            设置缓存(绝对时间过期:从缓存开始持续指定的时间段后就过期,无论有没有持续的访问)
            </summary>
            <param name="key">关键字</param>
            <param name="value">缓存值</param>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Set_SlidingAndAbsoluteExpire``1(System.String,``0,System.TimeSpan,System.TimeSpan)">
            <summary>
            设置缓存(绝对时间过期+滑动过期:比如滑动过期设置半小时,绝对过期时间设置2个小时，那么缓存开始后只要半小时内没有访问就会立马过期,如果半小时内有访问就会向后顺延半小时，但最多只能缓存2个小时)
            </summary>
            <param name="key">关键字</param>
            <param name="value">缓存值</param>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Remove(System.String)">
            <summary>
            移除缓存
            </summary>
            <param name="key">关键字</param>
        </member>
        <member name="M:zhiying_online.Common.CacheManager.Dispose">
            <summary>
            释放
            </summary>
        </member>
        <member name="M:zhiying_online.Common.CustomContractResolver.ResolveContractConverter(System.Type)">
            <summary>
            对长整型做处理
            </summary>
            <param name="objectType"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.DocumentationExtensions">
            <summary>
            DocumentationExtensions 
            </summary>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.GetDocumentation(System.Reflection.MethodInfo)">
            <summary>
            Provides the documentation comments for a specific method
            </summary>
            <param name="methodInfo">The MethodInfo (reflection data ) of the member to find documentation for</param>
            <returns>The XML fragment describing the method</returns>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.GetDocumentation(System.Reflection.MemberInfo)">
            <summary>
            Provides the documentation comments for a specific member
            </summary>
            <param name="memberInfo">The MemberInfo (reflection data) or the member to find documentation for</param>
            <returns>The XML fragment describing the member</returns>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.GetSummary(System.Reflection.MemberInfo)">
            <summary>
            Returns the Xml documenation summary comment for this member
            </summary>
            <param name="memberInfo"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.GetDocumentation(System.Type)">
            <summary>
            Provides the documentation comments for a specific type
            </summary>
            <param name="type">Type to find the documentation for</param>
            <returns>The XML fragment that describes the type</returns>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.GetSummary(System.Type)">
            <summary>
            Gets the summary portion of a type's documenation or returns an empty string if not available
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.GetSummary(System.Reflection.MethodInfo)">
            <summary>
            Gets the summary portion of a method documenation or returns an empty string if not available
            </summary>
            <param name="method"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.XmlFromName(System.Type,System.Char,System.String)">
            <summary>
            Obtains the XML Element that describes a reflection element by searching the 
            members for a member that has a name that describes the element.
            </summary>
            <param name="type">The type or parent type, used to fetch the assembly</param>
            <param name="prefix">The prefix as seen in the name attribute in the documentation XML</param>
            <param name="name">Where relevant, the full name qualifier for the element</param>
            <returns>The member that has a name that describes the specified reflection element</returns>
        </member>
        <member name="F:zhiying_online.Common.DocumentationExtensions.Cache">
            <summary>
            A cache used to remember Xml documentation for assemblies
            </summary>
        </member>
        <member name="F:zhiying_online.Common.DocumentationExtensions.FailCache">
            <summary>
            A cache used to store failure exceptions for assembly lookups
            </summary>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.XmlFromAssembly(System.Reflection.Assembly)">
            <summary>
            Obtains the documentation file for the specified assembly
            </summary>
            <param name="assembly">The assembly to find the XML document for</param>
            <returns>The XML document</returns>
            <remarks>This version uses a cache to preserve the assemblies, so that 
            the XML file is not loaded and parsed on every single lookup</remarks>
        </member>
        <member name="M:zhiying_online.Common.DocumentationExtensions.XmlFromAssemblyNonCached(System.Reflection.Assembly)">
            <summary>
            Loads and parses the documentation file for the specified assembly
            </summary>
            <param name="assembly">The assembly to find the XML document for</param>
            <returns>The XML document</returns>
        </member>
        <member name="T:zhiying_online.Common.IdWorker">
            <summary>
            雪花算法生成唯一id
            </summary>
        </member>
        <member name="M:zhiying_online.Common.IdWorker.#ctor">
            <summary>
            机器码
            </summary>
            <param name="workerId"></param>
        </member>
        <member name="M:zhiying_online.Common.IdWorker.tillNextMillis(System.Int64)">
            <summary>
            获取下一微秒时间戳
            </summary>
            <param name="lastTimestamp"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.IdWorker.timeGen">
            <summary>
            生成当前时间戳
            </summary>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.JsonConverterLong">
            <summary>
            long型转入前端丢失精度
            </summary>
        </member>
        <member name="T:zhiying_online.Common.Jwt.JwtAttribute">
            <summary>
            小程序授权专用验证
            </summary>
        </member>
        <member name="M:zhiying_online.Common.Jwt.JwtAttribute.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            h5授权accesstoken
            </summary>
            <param name="filterContext"></param>
        </member>
        <member name="M:zhiying_online.Common.Jwt.JwtAttribute.RetResult(zhiying_online.Common.Jwt.JwtAttribute.RetEnum,System.Object,System.String)">
            <summary>
            返回对象
            </summary>
            <param name="rte"></param>
            <param name="obj"></param>
            <param name="othermsg"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.Jwt.JwtH5Attribute">
            <summary>
            小程序授权专用验证
            </summary>
        </member>
        <member name="M:zhiying_online.Common.Jwt.JwtH5Attribute.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            h5授权accesstoken
            </summary>
            <param name="filterContext"></param>
        </member>
        <member name="M:zhiying_online.Common.Jwt.JwtH5Attribute.RetResult(zhiying_online.Common.Jwt.JwtH5Attribute.RetEnum,System.Object,System.String)">
            <summary>
            返回对象
            </summary>
            <param name="rte"></param>
            <param name="obj"></param>
            <param name="othermsg"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement">
            <summary>
            参数对象
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement.issuer">
            <summary>
            发布者
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement.SecurityKey">
            <summary>
            签名秘钥
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement.ExpiresIn">
            <summary>
            过期时间(毫秒)
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement.claim">
            <summary>
            账号信息
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement.SigningCredentials">
            <summary>
            签名验证
            </summary>
        </member>
        <member name="M:zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement.#ctor(Microsoft.IdentityModel.Tokens.SigningCredentials)">
            <summary>
            构造
            </summary>
            <param name="signingCredentials">签名验证实体</param>
        </member>
        <member name="T:zhiying_online.Common.Jwt.JwtHandle.ClaimModel">
            <summary>
            定义角色
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.ClaimModel.name">
            <summary>
            账号名称
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.ClaimModel.id">
            <summary>
            授权账号
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.ClaimModel.bid">
            <summary>
            商家ID
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.ClaimModel.role">
            <summary>
            对应账号类型 管理员 普通账号
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.ClaimModel.usetype">
            <summary>
            授权使用场景saas minapp openapi
            </summary>
        </member>
        <member name="P:zhiying_online.Common.Jwt.JwtHandle.ClaimModel.ip">
            <summary>
            请求ip
            </summary>
        </member>
        <member name="M:zhiying_online.Common.Jwt.JwtHandle.BuildJwtToken(zhiying_online.Common.Jwt.JwtHandle.PermissionRequirement)">
            <summary>
            生产Token
            </summary>
            <param name="permissionRequirement"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Logger.StopWatchStart">
            <summary>
            计时开始
            </summary>
        </member>
        <member name="M:zhiying_online.Common.Logger.StopWatchStop(System.Int32,System.String)">
            <summary>
            计时结束，如果大于给定时间（毫秒），则给出错误日志
            </summary>
            <param name="iPeriod">限定完成时间，单位毫秒</param>
            <param name="strMessage">超出限定时间后给出的日志</param>
        </member>
        <member name="M:zhiying_online.Common.Logger.AddNotifyEmail(System.String)">
            <summary>
            添加邮件通知地址
            </summary>
            <param name="email"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Logger.RemoveNotifyEmail(System.String)">
            <summary>
            去除邮件通知地址
            </summary>
            <param name="email">邮件地址</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:zhiying_online.Common.Logger.AddNotifyPhone(System.String)">
            <summary>
            添加短信通知电话
            </summary>
            <param name="phone">电话号码</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:zhiying_online.Common.Logger.RemoveNotifyPhone(System.String)">
            <summary>
            去除短信通知电话
            </summary>
            <param name="phone">电话号码</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:zhiying_online.Common.Logger.SendMail(System.String,System.Object)">
            <summary>
            发邮件
            </summary>
            <param name="title">邮件标题</param>
            <param name="content">邮件内容</param>
        </member>
        <member name="M:zhiying_online.Common.Logger.sendMail(System.String,System.Object,System.Collections.Generic.List{System.String})">
            <summary>
            群发邮件
            </summary>
            <param name="title">邮件标题</param>
            <param name="content">邮件内容</param>
            <param name="contacts">邮件列表</param>
        </member>
        <member name="M:zhiying_online.Common.Logger.sendSMS(System.String,System.Object,System.Collections.Generic.List{System.String})">
            <summary>
            群发短消息
            </summary>
            <param name="ext">不知道是个什么参数，必须送ext</param>
            <param name="message">群发的消息内容</param>
            <param name="phones">群发的电话号码列表</param>
        </member>
        <member name="M:zhiying_online.Common.Logger.fatal(System.Object)">
            <summary>
            Fatal
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:zhiying_online.Common.Logger.error(System.Object)">
            <summary>
            Error
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:zhiying_online.Common.Logger.warn(System.Object)">
            <summary>
            Warn
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:zhiying_online.Common.Logger.info(System.Object)">
            <summary>
            Info
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:zhiying_online.Common.Logger.debug(System.Object)">
            <summary>
            Debug
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:zhiying_online.Common.Logger.GetExceptionStr(System.Exception)">
            <summary>
            异常错误输出
            </summary>
            <param name="ex"></param>
        </member>
        <member name="T:zhiying_online.Common.RetMsg">
            <summary>
            统一返回对象
            </summary>
        </member>
        <member name="F:zhiying_online.Common.Tools.TIME_OUT">
            <summary>
            超时时间
            </summary>
        </member>
        <member name="F:zhiying_online.Common.Tools.worker">
            <summary>
            获取id
            </summary>
            <param name="lenght"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.HtmlGet(System.String)">
            <summary>
            htmlGET同步请求
            </summary>
            <param name="url"></param>
            <param name="encoding"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.HtmlGet_Json(System.String)">
            <summary>
            获取json格式的数据
            </summary>
            <param name="Url"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.HtmlPost(System.String,System.String,System.Text.Encoding,System.String)">
            <summary>
            HttpPost同步请求
            </summary>
            <param name="url"></param>
            <param name="postdata"></param>
            <param name="encoding"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.UploadFile(System.String,System.String,System.IO.Stream)">
            <summary>
            模拟form上传文件
            </summary>
            <param name="url"></param>
            <param name="fileName"></param>
            <param name="mstream"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.ReadFull(System.IO.Stream)">
            <summary>
            流转二进制
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetStr(System.Int32)">
            <summary>
            生成随机字母与数字
            </summary>
            <param name="IntStr">生成长度</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetStr(System.Int32,System.Boolean)">
            <summary>
            生成随机字母与数字
            </summary>
            <param name="Length">生成长度</param>
            <param name="Sleep">是否要在生成前将当前线程阻止以避免重复</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetStrs(System.Int32)">
            <summary>
            生成随机字母与数字
            </summary>
            <param name="IntStr">生成长度</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetStrs(System.Int32,System.Boolean)">
            <summary>
            生成随机字母与数字
            </summary>
            <param name="Length">生成长度</param>
            <param name="Sleep">是否要在生成前将当前线程阻止以避免重复</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetNumber(System.Int32,System.Boolean)">
            <summary>
            生成纯数字
            </summary>
            <param name="Length"></param>
            <param name="Sleep"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.IsIPAddress(System.String)">
            <summary>
            判断是否是IP地址格式 0.0.0.0
            </summary>
            <param name="str1">待判断的IP地址</param>
            <returns>true or false</returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.DecodeEncryptedData(System.String,System.String,System.String)">
            <summary>
            解密所有消息的基础方法
            </summary>
            <param name="sessionKey">储存在 SessionBag 中的当前用户 会话 SessionKey</param>
            <param name="encryptedData">接口返回数据中的 encryptedData 参数</param>
            <param name="iv">接口返回数据中的 iv 参数，对称解密算法初始向量</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.AES_Decrypt(System.String,System.Byte[],System.Byte[])">
            <summary>
            解密
            </summary>
            <param name="Input"></param>
            <param name="Iv"></param>
            <param name="Key"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.decode2(System.Byte[])">
            <summary>
            
            </summary>
            <param name="decrypted"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetDateTimeFromXml(System.Int64)">
            <summary>
            转换微信DateTime时间到C#时间
            </summary>
            <param name="dateTimeFromXml">微信DateTime</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetDateTimeFromXml(System.String)">
            <summary>
            转换微信DateTime时间到C#时间
            </summary>
            <param name="dateTimeFromXml">微信DateTime</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetWeixinDateTime(System.DateTime)">
            <summary>
            获取微信DateTime（UNIX时间戳）
            </summary>
            <param name="dateTime">时间</param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.FillEntityWithXml``1(``0,System.Xml.Linq.XDocument)">
            <summary>
            根据XML信息填充实实体
            </summary>
            <typeparam name="T">MessageBase为基类的类型，Response和Request都可以</typeparam>
            <param name="entity">实体</param>
            <param name="doc">XML</param>
        </member>
        <member name="M:zhiying_online.Common.Tools.Encrypt(System.String)">
            <summary>
            加密(MD5)
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.MD5ToBase64String(System.String)">
            <summary>
            加密
            </summary>
            <param name="str">需加密的字符串</param>
            <returns>加密后的字符</returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.Hash_Hmac(System.String,System.String)">
            <summary>
            HMAC-SHA256签名
            </summary>
            <param name="secret"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.SHA256Hash(System.String)">
            <summary>
            SHA256加密
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.GetConfigJson``1(System.String)">
            <summary>
            获取商家配置信息
            </summary>
            <param name="path"></param>
            <returns></returns>
            需要注意.net core 托管模式的路径
        </member>
        <member name="M:zhiying_online.Common.Tools.SaveConfigJson(System.String,System.String)">
            <summary>
            保存商家配置信息
            </summary>
            <param name="path"></param>
            <param name="config"></param>
            <returns></returns>
            需要注意.net core 托管模式的路径
        </member>
        <member name="M:zhiying_online.Common.Tools.GetFileStream(System.String)">
            <summary>
            根据完整文件路径获取FileStream
            </summary>
            <param name="fileName"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.MaskPhoneNumber(System.String)">
            <summary>
            手机号脱敏
            </summary>
            <param name="phoneNumber"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.Tools.MaskIdNumber(System.String)">
            <summary>
            身份证号脱敏
            </summary>
            <param name="idNumber"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Common.wechat.WechatHandles.WxJsonResult">
            <summary>
            统一返回码
            </summary>
        </member>
        <member name="T:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult">
            <summary>
            获取accesstoken
            </summary>
        </member>
        <member name="P:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult.access_token">
            <summary>
            接口调用凭证
            </summary>
        </member>
        <member name="P:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult.expires_in">
            <summary>
            access_token接口调用凭证超时时间，单位（秒）
            </summary>
        </member>
        <member name="P:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult.refresh_token">
            <summary>
            用户刷新access_token
            </summary>
        </member>
        <member name="P:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult.openid">
            <summary>
            授权用户唯一标识
            </summary>
        </member>
        <member name="P:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult.scope">
            <summary>
            用户授权的作用域，使用逗号（,）分隔
            </summary>
        </member>
        <member name="P:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult.authorizer_access_token">
            <summary>
            授权方令牌
            </summary>
        </member>
        <member name="P:zhiying_online.Common.wechat.WechatHandles.OAuthAccessTokenResult.authorizer_refresh_token">
            <summary>
            刷新令牌
            </summary>
        </member>
        <member name="T:zhiying_online.Common.wechat.WechatHandles.WxUserInfo">
            <summary>
            微信用户信息
            </summary>
        </member>
        <member name="M:zhiying_online.Common.wechat.WechatHandles.Get_Access_Token">
            <summary>
            获取accesstoken
            </summary>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.wechat.WechatHandles.GetOauthUrl(System.String,System.String)">
            <summary>
            获取授权的地址
            </summary>
            <param name="redirect_uri"></param>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.wechat.WechatHandles.GetOpenid(System.String)">
            <summary>
            code获取用户
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Common.wechat.WechatHandles.GetWxUserInfo(System.String)">
            <summary>
            获取用户新信息
            </summary>
            <param name="openid"></param>
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Controllers.AccountController">
            <summary>
            账号信息
            </summary>
        </member>
        <member name="M:zhiying_online.Controllers.AccountController.GetAccountList">
            <summary>
            获取账号列表
            </summary>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":{"id":"表id","name":"名称","time":"时间","accountid":"账号id"}}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.AccountController.SaveAccount(System.Nullable{System.Int64},System.String,System.String,System.String)">
            <summary>
            保存账号信息
            </summary>
            <param name="id">id</param>
            <param name="name">名称</param>
            <param name="accountid">账号登录id</param>
            <param name="password">密码</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.AccountController.Login(System.String,System.String)">
            <summary>
            账号登录
            </summary>
            <param name="accountid">账号登录id</param>
            <param name="password">密码</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","account":{"accountid":"账号id","name":"账号名称"},"token":{"access_token":"接口请求access_token","expires_in":"过期超时时间s","token_type":"token类型"}}</para>
            <para>请求失败：{"code":1,"msg":"账号不存在"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.AccountController.LogOut(System.String)">
            <summary>
            账号登出
            </summary>
            <param name="accountid"></param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Controllers.BookingController">
            <summary>
            预约
            </summary>
        </member>
        <member name="M:zhiying_online.Controllers.BookingController.GetBookingList(System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.Int32,System.Int32)">
            <summary>
            获取预约列表_ht
            </summary>
            <param name="name">预约人</param>
            <param name="phone">手机号</param>
            <param name="idcard">身份证</param>
            <param name="type">预约类型 1PET 2DR 3CT</param>
            <param name="star">预约开始时间</param>
            <param name="end">预约结束时间</param>
            <param name="status">状态 已取消 待检查 已完成</param>
            <param name="PageSize"></param>
            <param name="PageNo"></param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":[{"name":"预约人","phone":"就诊手机号","idcard":"就诊人身份证","id":"预约记录id","id":"预约记录id","uid":"微信用户ID","pid":"预约人ID","type":"预约类型","time":"发起预约时间","bookingtime":"预约时间","enabletime":"取消时间","status":"状态"}],"count":"数量"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.BookingController.GetBookingListByPid(System.Nullable{System.Int64})">
            <summary>
            根据就诊人获取预约列表
            </summary>
            <param name="pid">预约人ID</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":[{"name":"就诊人","phone":"就诊手机号","idcard":"就诊人身份证","id":"预约记录id","id":"预约记录id","uid":"微信用户ID","pid":"预约人ID","type":"预约类型","time":"发起预约时间","bookingtime":"预约时间","enabletime":"取消时间","status":"状态"}]}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.BookingController.SaveBooking(System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int32,System.Nullable{System.DateTime})">
            <summary>
            发起预约
            </summary>
            <param name="uid">微信用户ID</param>
            <param name="pid">就诊人ID</param>
            <param name="type">预约类型 1PET 2DR 3CT</param>
            <param name="bookingtime">预约时间</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.BookingController.EnableBooking(System.Nullable{System.Int64})">
            <summary>
            取消预约
            </summary>
            <param name="id">预约记录id</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Controllers.PatientController">
            <summary>
            就诊人
            </summary>
        </member>
        <member name="M:zhiying_online.Controllers.PatientController.GetPatientList(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32)">
            <summary>
            获取就诊人列表_ht
            </summary>
            <param name="openid">微信用户openid</param>
            <param name="phone">手机号</param>
            <param name="idcard">身份证号</param>
            <param name="star">添加开始时间</param>
            <param name="end">添加结束时间</param>
            <param name="PageSize"></param>
            <param name="PageNo"></param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":[{"id":"就诊人id等于后续的pid","uid":"微信用户ID","idcard":"身份证号","phone":"手机号","name":"就诊人名称","sex":"男，女","time":"就诊时间"}],"count":"数量"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.PatientController.GetGetPatientByUid(System.Int64)">
            <summary>
            根据用户获取就诊人列表
            </summary>
            <param name="uid"></param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":[{"id":"就诊人id等于后续的pid","uid":"微信用户ID","idcard":"身份证号","phone":"手机号","name":"就诊人名称","sex":"男，女","time":"就诊时间"}]}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.PatientController.SavePatient(System.Nullable{System.Int64},System.Nullable{System.Int64},System.String,System.String,System.String,System.String)">
            <summary>
            保存就诊人
            </summary>
            <param name="id">就诊人ID</param>
            <param name="uid">微信用户ID</param>
            <param name="idcard">身份证号</param>
            <param name="phone">手机号</param>
            <param name="name">就诊人</param>
            <param name="sex">性别</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.PatientController.DelPatient(System.Nullable{System.Int64})">
            <summary>
            删除就诊人
            </summary>
            <param name="id">就诊记录id</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Controllers.ReportController">
            <summary>
            查看报告
            </summary>
        </member>
        <member name="T:zhiying_online.Controllers.SystemController">
            <summary>
            系统配置
            </summary>
        </member>
        <member name="M:zhiying_online.Controllers.SystemController.GetSystemConfig(System.String)">
            <summary>
            获取系统配置
            </summary>
            <param name="type">配置类型自定义</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":"结果result"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.SystemController.SaveSystemConfig(System.String,System.String)">
            <summary>
            保存系统配置
            </summary>
            <param name="type">配置类型自定义，相同类型只可一次</param>
            <param name="result">配置规则</param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Controllers.UserController">
            <summary>
            微信用户信息
            </summary>
        </member>
        <member name="M:zhiying_online.Controllers.UserController.GetUserList(System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32)">
            <summary>
            获取用户列表_ht
            </summary>
            <param name="openid">微信openid</param>
            <param name="nickname">微信昵称</param>
            <param name="star">开始访问时间</param>
            <param name="end">结束访问时间</param>
            <param name="PageSize"></param>
            <param name="PageNo"></param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":[{"id":"微信用户ID","openid":"openid","headerimg":"头像（未接入）","nickname":"头像（未接入）","unionid":"unionid(未接入)","time":"时间"}],"count":"数量"}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.UserController.GetUserByID(System.Int64)">
            <summary>
            根据id获取用户
            </summary>
            <param name="id"></param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":{"id":"微信用户ID","openid":"openid","headerimg":"头像（未接入）","nickname":"头像（未接入）","unionid":"unionid(未接入)","time":"时间"}}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="M:zhiying_online.Controllers.UserController.GetUserByCode(System.String)">
            <summary>
            code获取用户信息
            </summary>
            <param name="code"></param>
            <response code="200">
            <para>请求成功：{"code":0,"msg":"请求成功","result":{"id":"微信用户ID","openid":"openid","headerimg":"头像（未接入）","nickname":"头像（未接入）","unionid":"unionid(未接入)","time":"时间"},"token":{"access_token":"接口请求access_token","expires_in":"过期超时时间s","token_type":"token类型"}}</para>
            <para>请求失败：{"code":1,"msg":"请求失败"}</para>
            </response >
            <returns></returns>
        </member>
        <member name="T:zhiying_online.Model.zhiying_online">
            <summary>
            数据库模型
            </summary>
        </member>
    </members>
</doc>
