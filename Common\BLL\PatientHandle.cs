﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Model;

namespace zhiying_online.Common.BLL
{
    /// <summary>
    /// 就诊人管理
    /// </summary>
    public class PatientHandle
    {
        /// <summary>
        /// 获取就诊用户列表
        /// </summary>
        /// <param name="openid"></param>
        /// <param name="phone"></param>
        /// <param name="idcard"></param>
        /// <param name="star"></param>
        /// <param name="end"></param>
        /// <param name="PageSize"></param>
        /// <param name="PageNo"></param>
        /// <returns></returns>
        public static RetMsg GetPatientList(string openid, string phone, string idcard, DateTime? star, DateTime? end, int PageSize = 10, int PageNo = 1)
        {
            using (var db = new Model.zhiying_online())
            {
                IEnumerable<patient> list = db.patient.Where(m => !m.p_del.HasValue);
                if (!string.IsNullOrEmpty(openid))
                {
                    var user = UserHandle.GetUserByOpenid(openid);
                    if (user == null)
                    {
                        return new RetMsg { code = 0, msg = "请求成功", count = 0, result = new List<object> { } };
                    }
                    list = list.Where(m => m.p_uid == user.u_id);
                }
                if (!string.IsNullOrEmpty(phone))
                {
                    list = list.Where(m => m.p_phone == phone);
                }
                if (!string.IsNullOrEmpty(idcard))
                {
                    list = list.Where(m => m.p_idcard == idcard);
                }
                if (star.HasValue && end.HasValue)
                {
                    list = list.Where(m => m.p_time >= star && m.p_time <= end);
                }
                var count = list.Count();
                list = list.OrderByDescending(m => m.p_time).Skip((PageNo - 1) * PageSize).Take(PageSize).ToList();

                return new RetMsg
                {
                    code = 0,
                    msg = "请求成功",
                    count = count,
                    result = list.Select(m => new
                    {
                        id = m.p_id,
                        uid = m.p_uid,
                        idcard = m.p_idcard,
                        phone = m.p_phone,
                        name = m.p_name,
                        sex = m.p_sex,
                        time = m.p_time,
                        birthday = m.p_birthday
                    })
                };
            }
        }

        /// <summary>
        /// 获取诊用用户列表
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public static RetMsg GetGetPatientByUid(long uid)
        {
            using (var db = new Model.zhiying_online())
            {
                var plist = db.patient.Where(x => x.p_uid == uid && !x.p_del.HasValue).OrderByDescending(m => m.p_time).ToList();
                return new RetMsg
                {
                    code = 0,
                    msg = "请求成功",
                    result = plist.Select(m => new
                    {
                        id = m.p_id,
                        uid = m.p_uid,
                        idcard = m.p_idcard,//Tools.MaskIdNumber(m.p_idcard),
                        phone = m.p_phone,//Tools.MaskPhoneNumber(m.p_phone),
                        name = m.p_name,
                        sex = m.p_sex,
                        time = m.p_time,
                        birthday = m.p_birthday
                    })
                };
            }
        }

        /// <summary>
        /// 保存就诊人
        /// </summary>
        /// <param name="id"></param>
        /// <param name="uid"></param>
        /// <param name="idcard"></param>
        /// <param name="phone"></param>
        /// <param name="name"></param>
        /// <param name="sex"></param>
        /// <returns></returns>
        public static RetMsg SavePatient(long? id, long? uid, string idcard, string phone, string name, string sex,DateTime? birthday)
        {
            using (var db = new Model.zhiying_online())
            {
                //判断防止重复添加
                var up = db.patient.Where(m => m.p_uid == uid && m.p_idcard == idcard && !m.p_del.HasValue).FirstOrDefault();
                if (up != null)
                {
                    if (up.p_id != id)
                    {
                        return new RetMsg { code = 0, msg = "您已添加过此就诊人" };
                    }
                }


                patient _p = null;
                if (id.HasValue)
                {
                    _p = db.patient.Find(id.Value);
                    _p.p_idcard = idcard;
                    _p.p_phone = phone;
                    _p.p_name = name;
                    _p.p_sex = sex;
                    _p.p_birthday = birthday;
                }
                else
                {
                    _p = new patient
                    {
                        p_uid = uid,
                        p_idcard = idcard,
                        p_phone = phone,
                        p_name = name,
                        p_sex = sex,
                        p_time = DateTime.Now,
                        p_id = Tools.Get_Id(),
                        p_birthday = birthday
                    };
                    db.patient.Add(_p);
                }
                db.SaveChanges();
                return new RetMsg { code = 0, msg = "请求成功" };
            }
        }

        /// <summary>
        /// 删除就诊人
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static RetMsg DelPatient(long? id)
        {
            using (var db = new Model.zhiying_online())
            {
                patient _p = db.patient.Find(id);
                _p.p_del = true;
                db.SaveChanges();
                return new RetMsg { code = 0, msg = "请求成功" };
            }
        }
    }
}
