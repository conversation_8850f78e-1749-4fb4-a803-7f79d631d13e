﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Common.BLL;
using zhiying_online.Common.Jwt;

namespace zhiying_online.Controllers
{
    /// <summary>
    /// 微信用户信息
    /// </summary>
    [Route("[controller]/[action]")]
    public class UserController : Controller
    {
        /// <summary>
        /// 获取用户列表_ht
        /// </summary>
        /// <param name="openid">微信openid</param>
        /// <param name="nickname">微信昵称</param>
        /// <param name="star">开始访问时间</param>
        /// <param name="end">结束访问时间</param>
        /// <param name="PageSize"></param>
        /// <param name="PageNo"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":[{"id":"微信用户ID","openid":"openid","headerimg":"头像（未接入）","nickname":"头像（未接入）","unionid":"unionid(未接入)","time":"时间"}],"count":"数量"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtAttribute]
        public IActionResult GetUserList(string openid, string nickname, DateTime? star, DateTime? end, int PageSize = 10, int PageNo = 1)
        {
            return Json(UserHandle.GetUserList(openid, nickname, star, end, PageSize, PageNo));
        }

        /// <summary>
        /// 根据id获取用户
        /// </summary>
        /// <param name="id"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":{"id":"微信用户ID","openid":"openid","headerimg":"头像（未接入）","nickname":"头像（未接入）","unionid":"unionid(未接入)","time":"时间"}}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetUserByID(long id)
        {
            return Json(new { code = 0, msg = "请求成功", result = UserHandle.GetUserByID(id) });
        }

        /// <summary>
        /// code获取用户信息
        /// </summary>
        /// <param name="code"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":{"id":"微信用户ID","openid":"openid","headerimg":"头像（未接入）","nickname":"头像（未接入）","unionid":"unionid(未接入)","time":"时间"},"token":{"access_token":"接口请求access_token","expires_in":"过期超时时间s","token_type":"token类型"}}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetUserByCode(string code)
        {
            return Json(UserHandle.GetUserByCode(code));
        }

        /// <summary>
        /// openid获取用户信息
        /// </summary>
        /// <param name="openid"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":{"id":"微信用户ID","openid":"openid","headerimg":"头像（未接入）","nickname":"头像（未接入）","unionid":"unionid(未接入)","time":"时间"},"token":{"access_token":"接口请求access_token","expires_in":"过期超时时间s","token_type":"token类型"}}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetUserInfoByOpenid(string openid)
        {
            return Json(UserHandle.GetUserInfoByOpenid(openid));
        }
    }
}
