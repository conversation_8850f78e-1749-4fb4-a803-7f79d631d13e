import{r as d,_ as Oe,a as ce,R as $,c as k,b as se,d as ne,u as at,e as Ce,f as Ld,g as fl,h as gn,C as jn,i as ea,j as Fd,D as _d,k as hl,l as A,m as dt,n as <PERSON>r,o as Ue,p as zd,q as Qo,s as Hd,t as Wd,v as qd,w as _n,x as on,y as tn,z as bn,A as Gd,B as Ud,E as ml,F as gl,G as lr,H as wt,I as Nr,J as ho,K as Xd,L as Jo,M as Kd,N as Yd,T as pl,O as St,P as V,Q as Zn,S as vl,U as Zd,V as Qd}from"./index-C0F_BgRl.js";import{c as bl,a as yl,u as Dt,g as Cl,C as cr,b as Jd,d as xl,e as ef,i as tf,K as Sl,B as nn,f as wl,p as Et,h as El,j as <PERSON>e,P as Ol,R as ei,k as zn,l as gt,m as pt,n as yn,o as $l,q as Dn,z as Pl,r as rn,s as nf,t as rf,v as of,w as af,x as sf,y as lf,A as cf,D as xr,E as uf,F as df,G as ti,H as ur,I as Tl,J as rt,L as ff,M as hf,N as mf,O as gf,Q as pf,S as vf,T as Rl,U as Ml,V as bf,W as yf,X as Cf,Y as xf,Z as Sf,_ as wf,$ as Ef,a0 as Of,a1 as Nl,a2 as jl,a3 as $f,a4 as mo,a5 as Pf,a6 as ni,a7 as Qn,a8 as Tf,a9 as jr,aa as Dl,ab as ut,ac as nr,ad as Rf,ae as Il}from"./index-Cv9X8VLK.js";import{S as Al}from"./Skeleton-Cxkqevfo.js";import{D as Mf,R as Vl,S as go,g as Nf,a as kl,u as Bl,L as jf,b as Df,w as If,c as zt,M as Af,d as Vf}from"./index-bqnPHK9F.js";import{i as Ll,u as kf,r as Bf,g as Lf,a as Ff,b as _f,P as zf,T as dr}from"./useBubbleLock-ftt3IjSJ.js";import{S as Hf}from"./index-Di_KhKWu.js";import{g as Wf,I as kt,u as qf,R as Gf}from"./index-B-AHKPfb.js";import"./EllipsisOutlined-DUbZADlg.js";function Uf(){const[e,t]=d.useState([]),n=d.useCallback(r=>(t(o=>[].concat(Oe(o),[r])),()=>{t(o=>o.filter(i=>i!==r))}),[]);return[e,n]}function Xf(e){var t="touches"in e?e.touches[0]:e,n=document.documentElement.scrollLeft||document.body.scrollLeft||window.pageXOffset,r=document.documentElement.scrollTop||document.body.scrollTop||window.pageYOffset;return{pageX:t.pageX-n,pageY:t.pageY-r}}function Fl(e){var t=e.targetRef,n=e.containerRef,r=e.direction,o=e.onDragChange,i=e.onDragChangeComplete,a=e.calculate,s=e.color,l=e.disabledDrag,u=d.useState({x:0,y:0}),c=ce(u,2),f=c[0],m=c[1],h=d.useRef(null),b=d.useRef(null);d.useEffect(function(){m(a())},[s]),d.useEffect(function(){return function(){document.removeEventListener("mousemove",h.current),document.removeEventListener("mouseup",b.current),document.removeEventListener("touchmove",h.current),document.removeEventListener("touchend",b.current),h.current=null,b.current=null}},[]);var g=function(x){var S=Xf(x),w=S.pageX,E=S.pageY,O=n.current.getBoundingClientRect(),T=O.x,P=O.y,N=O.width,D=O.height,j=t.current.getBoundingClientRect(),M=j.width,I=j.height,L=M/2,R=I/2,F=Math.max(0,Math.min(w-T,N))-L,_=Math.max(0,Math.min(E-P,D))-R,Q={x:F,y:r==="x"?f.y:_};if(M===0&&I===0||M!==I)return!1;o==null||o(Q)},v=function(x){x.preventDefault(),g(x)},y=function(x){x.preventDefault(),document.removeEventListener("mousemove",h.current),document.removeEventListener("mouseup",b.current),document.removeEventListener("touchmove",h.current),document.removeEventListener("touchend",b.current),h.current=null,b.current=null,i==null||i()},p=function(x){document.removeEventListener("mousemove",h.current),document.removeEventListener("mouseup",b.current),!l&&(g(x),document.addEventListener("mousemove",v),document.addEventListener("mouseup",y),document.addEventListener("touchmove",v),document.addEventListener("touchend",y),h.current=v,b.current=y)};return[f,p]}var _l=function(t){var n=t.size,r=n===void 0?"default":n,o=t.color,i=t.prefixCls;return $.createElement("div",{className:k("".concat(i,"-handler"),se({},"".concat(i,"-handler-sm"),r==="small")),style:{backgroundColor:o}})},zl=function(t){var n=t.children,r=t.style,o=t.prefixCls;return $.createElement("div",{className:"".concat(o,"-palette"),style:ne({position:"relative"},r)},n)},Hl=d.forwardRef(function(e,t){var n=e.children,r=e.x,o=e.y;return $.createElement("div",{ref:t,style:{position:"absolute",left:"".concat(r,"%"),top:"".concat(o,"%"),zIndex:1,transform:"translate(-50%, -50%)"}},n)}),Kf=function(t){var n=t.color,r=t.onChange,o=t.prefixCls,i=t.onChangeComplete,a=t.disabled,s=d.useRef(),l=d.useRef(),u=d.useRef(n),c=at(function(g){var v=bl({offset:g,targetRef:l,containerRef:s,color:n});u.current=v,r(v)}),f=Fl({color:n,containerRef:s,targetRef:l,calculate:function(){return yl(n)},onDragChange:c,onDragChangeComplete:function(){return i==null?void 0:i(u.current)},disabledDrag:a}),m=ce(f,2),h=m[0],b=m[1];return $.createElement("div",{ref:s,className:"".concat(o,"-select"),onMouseDown:b,onTouchStart:b},$.createElement(zl,{prefixCls:o},$.createElement(Hl,{x:h.x,y:h.y,ref:l},$.createElement(_l,{color:n.toRgbString(),prefixCls:o})),$.createElement("div",{className:"".concat(o,"-saturation"),style:{backgroundColor:"hsl(".concat(n.toHsb().h,",100%, 50%)"),backgroundImage:"linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))"}})))},Yf=function(t,n){var r=Dt(t,{value:n}),o=ce(r,2),i=o[0],a=o[1],s=d.useMemo(function(){return Cl(i)},[i]);return[s,a]},Zf=function(t){var n=t.colors,r=t.children,o=t.direction,i=o===void 0?"to right":o,a=t.type,s=t.prefixCls,l=d.useMemo(function(){return n.map(function(u,c){var f=Cl(u);return a==="alpha"&&c===n.length-1&&(f=new cr(f.setA(1))),f.toRgbString()}).join(",")},[n,a]);return $.createElement("div",{className:"".concat(s,"-gradient"),style:{position:"absolute",inset:0,background:"linear-gradient(".concat(i,", ").concat(l,")")}},r)},Qf=function(t){var n=t.prefixCls,r=t.colors,o=t.disabled,i=t.onChange,a=t.onChangeComplete,s=t.color,l=t.type,u=d.useRef(),c=d.useRef(),f=d.useRef(s),m=function(S){return l==="hue"?S.getHue():S.a*100},h=at(function(x){var S=bl({offset:x,targetRef:c,containerRef:u,color:s,type:l});f.current=S,i(m(S))}),b=Fl({color:s,targetRef:c,containerRef:u,calculate:function(){return yl(s,l)},onDragChange:h,onDragChangeComplete:function(){a(m(f.current))},direction:"x",disabledDrag:o}),g=ce(b,2),v=g[0],y=g[1],p=$.useMemo(function(){if(l==="hue"){var x=s.toHsb();x.s=1,x.b=1,x.a=1;var S=new cr(x);return S}return s},[s,l]),C=$.useMemo(function(){return r.map(function(x){return"".concat(x.color," ").concat(x.percent,"%")})},[r]);return $.createElement("div",{ref:u,className:k("".concat(n,"-slider"),"".concat(n,"-slider-").concat(l)),onMouseDown:y,onTouchStart:y},$.createElement(zl,{prefixCls:n},$.createElement(Hl,{x:v.x,y:v.y,ref:c},$.createElement(_l,{size:"small",color:p.toHexString(),prefixCls:n})),$.createElement(Zf,{colors:C,type:l,prefixCls:n})))};function Jf(e){return d.useMemo(function(){var t=e||{},n=t.slider;return[n||Qf]},[e])}var eh=[{color:"rgb(255, 0, 0)",percent:0},{color:"rgb(255, 255, 0)",percent:17},{color:"rgb(0, 255, 0)",percent:33},{color:"rgb(0, 255, 255)",percent:50},{color:"rgb(0, 0, 255)",percent:67},{color:"rgb(255, 0, 255)",percent:83},{color:"rgb(255, 0, 0)",percent:100}],th=d.forwardRef(function(e,t){var n=e.value,r=e.defaultValue,o=e.prefixCls,i=o===void 0?Jd:o,a=e.onChange,s=e.onChangeComplete,l=e.className,u=e.style,c=e.panelRender,f=e.disabledAlpha,m=f===void 0?!1:f,h=e.disabled,b=h===void 0?!1:h,g=e.components,v=Jf(g),y=ce(v,1),p=y[0],C=Yf(r||ef,n),x=ce(C,2),S=x[0],w=x[1],E=d.useMemo(function(){return S.setA(1).toRgbString()},[S]),O=function(_,Q){n||w(_),a==null||a(_,Q)},T=function(_){return new cr(S.setHue(_))},P=function(_){return new cr(S.setA(_/100))},N=function(_){O(T(_),{type:"hue",value:_})},D=function(_){O(P(_),{type:"alpha",value:_})},j=function(_){s&&s(T(_))},M=function(_){s&&s(P(_))},I=k("".concat(i,"-panel"),l,se({},"".concat(i,"-panel-disabled"),b)),L={prefixCls:i,disabled:b,color:S},R=$.createElement($.Fragment,null,$.createElement(Kf,Ce({onChange:O},L,{onChangeComplete:s})),$.createElement("div",{className:"".concat(i,"-slider-container")},$.createElement("div",{className:k("".concat(i,"-slider-group"),se({},"".concat(i,"-slider-group-disabled-alpha"),m))},$.createElement(p,Ce({},L,{type:"hue",colors:eh,min:0,max:359,value:S.getHue(),onChange:N,onChangeComplete:j})),!m&&$.createElement(p,Ce({},L,{type:"alpha",colors:[{percent:0,color:"rgba(255, 0, 4, 0)"},{percent:100,color:E}],min:0,max:100,value:S.a*100,onChange:D,onChangeComplete:M}))),$.createElement(xl,{color:S.toRgbString(),prefixCls:i})));return $.createElement("div",{className:I,style:u,ref:t},typeof c=="function"?c(R):R)});const nh=new Sl("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),rh=new Sl("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),oh=function(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const{antCls:n}=e,r=`${n}-fade`,o=t?"&":"";return[tf(r,nh,rh,e.motionDurationMid,t),{[`
        ${o}${r}-enter,
        ${o}${r}-appear
      `]:{opacity:0,animationTimingFunction:"linear"},[`${o}${r}-leave`]:{animationTimingFunction:"linear"}}]};function Dr(e){return!!(e!=null&&e.then)}const Wl=e=>{const{type:t,children:n,prefixCls:r,buttonProps:o,close:i,autoFocus:a,emitEvent:s,isSilent:l,quitOnNullishReturnValue:u,actionFn:c}=e,f=d.useRef(!1),m=d.useRef(null),[h,b]=Ld(!1),g=function(){i==null||i.apply(void 0,arguments)};d.useEffect(()=>{let p=null;return a&&(p=setTimeout(()=>{var C;(C=m.current)===null||C===void 0||C.focus({preventScroll:!0})})),()=>{p&&clearTimeout(p)}},[]);const v=p=>{Dr(p)&&(b(!0),p.then(function(){b(!1,!0),g.apply(void 0,arguments),f.current=!1},C=>{if(b(!1,!0),f.current=!1,!(l!=null&&l()))return Promise.reject(C)}))},y=p=>{if(f.current)return;if(f.current=!0,!c){g();return}let C;if(s){if(C=c(p),u&&!Dr(C)){f.current=!1,g(p);return}}else if(c.length)C=c(i),f.current=!1;else if(C=c(),!Dr(C)){g();return}v(C)};return d.createElement(nn,Object.assign({},wl(t),{onClick:y,loading:h,prefixCls:r},o,{ref:m}),n)},Hn=$.createContext({}),{Provider:ql}=Hn,ta=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:n,isSilent:r,mergedOkCancel:o,rootPrefixCls:i,close:a,onCancel:s,onConfirm:l}=d.useContext(Hn);return o?$.createElement(Wl,{isSilent:r,actionFn:s,close:function(){a==null||a.apply(void 0,arguments),l==null||l(!1)},autoFocus:e==="cancel",buttonProps:t,prefixCls:`${i}-btn`},n):null},na=()=>{const{autoFocusButton:e,close:t,isSilent:n,okButtonProps:r,rootPrefixCls:o,okTextLocale:i,okType:a,onConfirm:s,onOk:l}=d.useContext(Hn);return $.createElement(Wl,{isSilent:n,type:a||"primary",actionFn:l,close:function(){t==null||t.apply(void 0,arguments),s==null||s(!0)},autoFocus:e==="ok",buttonProps:r,prefixCls:`${o}-btn`},i)};var Gl=d.createContext({});function ra(e,t,n){var r=t;return!r&&n&&(r="".concat(e,"-").concat(n)),r}function oa(e,t){var n=e["page".concat(t?"Y":"X","Offset")],r="scroll".concat(t?"Top":"Left");if(typeof n!="number"){var o=e.document;n=o.documentElement[r],typeof n!="number"&&(n=o.body[r])}return n}function ih(e){var t=e.getBoundingClientRect(),n={left:t.left,top:t.top},r=e.ownerDocument,o=r.defaultView||r.parentWindow;return n.left+=oa(o),n.top+=oa(o,!0),n}const ah=d.memo(function(e){var t=e.children;return t},function(e,t){var n=t.shouldUpdate;return!n});var sh={width:0,height:0,overflow:"hidden",outline:"none"},lh={outline:"none"},Ul=$.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,o=e.style,i=e.title,a=e.ariaId,s=e.footer,l=e.closable,u=e.closeIcon,c=e.onClose,f=e.children,m=e.bodyStyle,h=e.bodyProps,b=e.modalRender,g=e.onMouseDown,v=e.onMouseUp,y=e.holderRef,p=e.visible,C=e.forceRender,x=e.width,S=e.height,w=e.classNames,E=e.styles,O=$.useContext(Gl),T=O.panel,P=fl(y,T),N=d.useRef(),D=d.useRef();$.useImperativeHandle(t,function(){return{focus:function(){var G;(G=N.current)===null||G===void 0||G.focus({preventScroll:!0})},changeActive:function(G){var Y=document,J=Y.activeElement;G&&J===D.current?N.current.focus({preventScroll:!0}):!G&&J===N.current&&D.current.focus({preventScroll:!0})}}});var j={};x!==void 0&&(j.width=x),S!==void 0&&(j.height=S);var M=s?$.createElement("div",{className:k("".concat(n,"-footer"),w==null?void 0:w.footer),style:ne({},E==null?void 0:E.footer)},s):null,I=i?$.createElement("div",{className:k("".concat(n,"-header"),w==null?void 0:w.header),style:ne({},E==null?void 0:E.header)},$.createElement("div",{className:"".concat(n,"-title"),id:a},i)):null,L=d.useMemo(function(){return gn(l)==="object"&&l!==null?l:l?{closeIcon:u??$.createElement("span",{className:"".concat(n,"-close-x")})}:{}},[l,u,n]),R=Et(L,!0),F=gn(l)==="object"&&l.disabled,_=l?$.createElement("button",Ce({type:"button",onClick:c,"aria-label":"Close"},R,{className:"".concat(n,"-close"),disabled:F}),L.closeIcon):null,Q=$.createElement("div",{className:k("".concat(n,"-content"),w==null?void 0:w.content),style:E==null?void 0:E.content},_,I,$.createElement("div",Ce({className:k("".concat(n,"-body"),w==null?void 0:w.body),style:ne(ne({},m),E==null?void 0:E.body)},h),f),M);return $.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":i?a:null,"aria-modal":"true",ref:P,style:ne(ne({},o),j),className:k(n,r),onMouseDown:g,onMouseUp:v},$.createElement("div",{ref:N,tabIndex:0,style:lh},$.createElement(ah,{shouldUpdate:p||C},b?b(Q):Q)),$.createElement("div",{tabIndex:0,ref:D,style:sh}))}),Xl=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.title,o=e.style,i=e.className,a=e.visible,s=e.forceRender,l=e.destroyOnClose,u=e.motionName,c=e.ariaId,f=e.onVisibleChanged,m=e.mousePosition,h=d.useRef(),b=d.useState(),g=ce(b,2),v=g[0],y=g[1],p={};v&&(p.transformOrigin=v);function C(){var x=ih(h.current);y(m&&(m.x||m.y)?"".concat(m.x-x.left,"px ").concat(m.y-x.top,"px"):"")}return d.createElement(jn,{visible:a,onVisibleChanged:f,onAppearPrepare:C,onEnterPrepare:C,forceRender:s,motionName:u,removeOnLeave:l,ref:h},function(x,S){var w=x.className,E=x.style;return d.createElement(Ul,Ce({},e,{ref:t,title:r,ariaId:c,prefixCls:n,holderRef:S,style:ne(ne(ne({},E),o),p),className:k(i,w)}))})});Xl.displayName="Content";var ch=function(t){var n=t.prefixCls,r=t.style,o=t.visible,i=t.maskProps,a=t.motionName,s=t.className;return d.createElement(jn,{key:"mask",visible:o,motionName:a,leavedClassName:"".concat(n,"-mask-hidden")},function(l,u){var c=l.className,f=l.style;return d.createElement("div",Ce({ref:u,style:ne(ne({},f),r),className:k("".concat(n,"-mask"),c,s)},i))})},uh=function(t){var n=t.prefixCls,r=n===void 0?"rc-dialog":n,o=t.zIndex,i=t.visible,a=i===void 0?!1:i,s=t.keyboard,l=s===void 0?!0:s,u=t.focusTriggerAfterClose,c=u===void 0?!0:u,f=t.wrapStyle,m=t.wrapClassName,h=t.wrapProps,b=t.onClose,g=t.afterOpenChange,v=t.afterClose,y=t.transitionName,p=t.animation,C=t.closable,x=C===void 0?!0:C,S=t.mask,w=S===void 0?!0:S,E=t.maskTransitionName,O=t.maskAnimation,T=t.maskClosable,P=T===void 0?!0:T,N=t.maskStyle,D=t.maskProps,j=t.rootClassName,M=t.classNames,I=t.styles,L=d.useRef(),R=d.useRef(),F=d.useRef(),_=d.useState(a),Q=ce(_,2),z=Q[0],G=Q[1],Y=El();function J(){ea(R.current,document.activeElement)||(L.current=document.activeElement)}function U(){if(!ea(R.current,document.activeElement)){var le;(le=F.current)===null||le===void 0||le.focus()}}function H(le){if(le)U();else{if(G(!1),w&&L.current&&c){try{L.current.focus({preventScroll:!0})}catch{}L.current=null}z&&(v==null||v())}g==null||g(le)}function W(le){b==null||b(le)}var B=d.useRef(!1),K=d.useRef(),re=function(){clearTimeout(K.current),B.current=!0},q=function(){K.current=setTimeout(function(){B.current=!1})},oe=null;P&&(oe=function(pe){B.current?B.current=!1:R.current===pe.target&&W(pe)});function ue(le){if(l&&le.keyCode===Ee.ESC){le.stopPropagation(),W(le);return}a&&le.keyCode===Ee.TAB&&F.current.changeActive(!le.shiftKey)}d.useEffect(function(){a&&(G(!0),J())},[a]),d.useEffect(function(){return function(){clearTimeout(K.current)}},[]);var Se=ne(ne(ne({zIndex:o},f),I==null?void 0:I.wrapper),{},{display:z?null:"none"});return d.createElement("div",Ce({className:k("".concat(r,"-root"),j)},Et(t,{data:!0})),d.createElement(ch,{prefixCls:r,visible:w&&a,motionName:ra(r,E,O),style:ne(ne({zIndex:o},N),I==null?void 0:I.mask),maskProps:D,className:M==null?void 0:M.mask}),d.createElement("div",Ce({tabIndex:-1,onKeyDown:ue,className:k("".concat(r,"-wrap"),m,M==null?void 0:M.wrapper),ref:R,onClick:oe,style:Se},h),d.createElement(Xl,Ce({},t,{onMouseDown:re,onMouseUp:q,ref:F,closable:x,ariaId:Y,prefixCls:r,visible:a&&z,onClose:W,onVisibleChanged:H,motionName:ra(r,y,p)}))))},Kl=function(t){var n=t.visible,r=t.getContainer,o=t.forceRender,i=t.destroyOnClose,a=i===void 0?!1:i,s=t.afterClose,l=t.panelRef,u=d.useState(n),c=ce(u,2),f=c[0],m=c[1],h=d.useMemo(function(){return{panel:l}},[l]);return d.useEffect(function(){n&&m(!0)},[n]),!o&&a&&!f?null:d.createElement(Gl.Provider,{value:h},d.createElement(Ol,{open:n||o||f,autoDestroy:!1,getContainer:r,autoLock:n||f},d.createElement(uh,Ce({},t,{destroyOnClose:a,afterClose:function(){s==null||s(),m(!1)}}))))};Kl.displayName="Dialog";function fr(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function ia(e){const{closable:t,closeIcon:n}=e||{};return $.useMemo(()=>{if(!t&&(t===!1||n===!1||n===null))return!1;if(t===void 0&&n===void 0)return null;let r={closeIcon:typeof n!="boolean"&&n!==null?n:void 0};return t&&typeof t=="object"&&(r=Object.assign(Object.assign({},r),t)),r},[t,n])}function aa(){const e={};for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.forEach(o=>{o&&Object.keys(o).forEach(i=>{o[i]!==void 0&&(e[i]=o[i])})}),e}const dh={};function Yl(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:dh;const r=ia(e),o=ia(t),i=typeof r!="boolean"?!!(r!=null&&r.disabled):!1,a=$.useMemo(()=>Object.assign({closeIcon:$.createElement(ei,null)},n),[n]),s=$.useMemo(()=>r===!1?!1:r?aa(a,o,r):o===!1?!1:o?aa(a,o):a.closable?a:!1,[r,o,a]);return $.useMemo(()=>{if(s===!1)return[!1,null,i];const{closeIconRender:l}=a,{closeIcon:u}=s;let c=u;if(c!=null){l&&(c=l(u));const f=Et(s,!0);Object.keys(f).length&&(c=$.isValidElement(c)?$.cloneElement(c,f):$.createElement("span",Object.assign({},f),c))}return[!0,c,i]},[s,a])}const fh=()=>Fd()&&window.document.documentElement;function sa(){}const hh=d.createContext({add:sa,remove:sa});function Zl(e){const t=d.useContext(hh),n=d.useRef(null);return at(o=>{if(o){const i=e?o.querySelector(e):o;t.add(i),n.current=i}else t.remove(n.current)})}const la=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:n}=d.useContext(Hn);return $.createElement(nn,Object.assign({onClick:n},e),t)},ca=()=>{const{confirmLoading:e,okButtonProps:t,okType:n,okTextLocale:r,onOk:o}=d.useContext(Hn);return $.createElement(nn,Object.assign({},wl(n),{loading:e,onClick:o},t),r)};function Ql(e,t){return $.createElement("span",{className:`${e}-close-x`},t||$.createElement(ei,{className:`${e}-close-icon`}))}const Jl=e=>{const{okText:t,okType:n="primary",cancelText:r,confirmLoading:o,onOk:i,onCancel:a,okButtonProps:s,cancelButtonProps:l,footer:u}=e,[c]=zn("Modal",hl()),f=t||(c==null?void 0:c.okText),m=r||(c==null?void 0:c.cancelText),h={confirmLoading:o,okButtonProps:s,cancelButtonProps:l,okTextLocale:f,cancelTextLocale:m,okType:n,onOk:i,onCancel:a},b=$.useMemo(()=>h,Oe(Object.values(h)));let g;return typeof u=="function"||typeof u>"u"?(g=$.createElement($.Fragment,null,$.createElement(la,null),$.createElement(ca,null)),typeof u=="function"&&(g=u(g,{OkBtn:ca,CancelBtn:la})),g=$.createElement(ql,{value:b},g)):g=u,$.createElement(_d,{disabled:!1},g)};function ua(e){return{position:e,inset:0}}const mh=e=>{const{componentCls:t,antCls:n}=e;return[{[`${t}-root`]:{[`${t}${n}-zoom-enter, ${t}${n}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${n}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},ua("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},ua("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:oh(e)}]},gh=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${A(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},dt(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${A(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:A(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},Cr(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${A(e.borderRadiusLG)} ${A(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${A(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,
          ${t}-body,
          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},ph=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},vh=e=>{const{componentCls:t}=e,n=Wf(e);delete n.xs;const r=Object.keys(n).map(o=>({[`@media (min-width: ${A(n[o])})`]:{width:`var(--${t.replace(".","")}-${o}-width)`}}));return{[`${t}-root`]:{[t]:[{width:`var(--${t.replace(".","")}-xs-width)`}].concat(Oe(r))}}},ec=e=>{const t=e.padding,n=e.fontSizeHeading5,r=e.lineHeightHeading5;return pt(e,{modalHeaderHeight:e.calc(e.calc(r).mul(n).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},tc=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${A(e.paddingMD)} ${A(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${A(e.padding)} ${A(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${A(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${A(e.paddingXS)} ${A(e.padding)}`:0,footerBorderTop:e.wireframe?`${A(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${A(e.borderRadiusLG)} ${A(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${A(e.padding*2)} ${A(e.padding*2)} ${A(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM}),nc=gt("Modal",e=>{const t=ec(e);return[gh(t),ph(t),mh(t),Ll(t,"zoom"),vh(t)]},tc,{unitless:{titleLineHeight:!0}});var bh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let po;const yh=e=>{po={x:e.pageX,y:e.pageY},setTimeout(()=>{po=null},100)};fh()&&document.documentElement.addEventListener("click",yh,!0);const rc=e=>{var t;const{getPopupContainer:n,getPrefixCls:r,direction:o,modal:i}=d.useContext(Ue),a=H=>{const{onCancel:W}=e;W==null||W(H)},s=H=>{const{onOk:W}=e;W==null||W(H)},{prefixCls:l,className:u,rootClassName:c,open:f,wrapClassName:m,centered:h,getContainer:b,focusTriggerAfterClose:g=!0,style:v,visible:y,width:p=520,footer:C,classNames:x,styles:S,children:w,loading:E}=e,O=bh(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading"]),T=r("modal",l),P=r(),N=yn(T),[D,j,M]=nc(T,N),I=k(m,{[`${T}-centered`]:h??(i==null?void 0:i.centered),[`${T}-wrap-rtl`]:o==="rtl"}),L=C!==null&&!E?d.createElement(Jl,Object.assign({},e,{onOk:s,onCancel:a})):null,[R,F,_]=Yl(fr(e),fr(i),{closable:!0,closeIcon:d.createElement(ei,{className:`${T}-close-icon`}),closeIconRender:H=>Ql(T,H)}),Q=Zl(`.${T}-content`),[z,G]=$l("Modal",O.zIndex),[Y,J]=d.useMemo(()=>p&&typeof p=="object"?[void 0,p]:[p,void 0],[p]),U=d.useMemo(()=>{const H={};return J&&Object.keys(J).forEach(W=>{const B=J[W];B!==void 0&&(H[`--${T}-${W}-width`]=typeof B=="number"?`${B}px`:B)}),H},[J]);return D(d.createElement(Dn,{form:!0,space:!0},d.createElement(Pl.Provider,{value:G},d.createElement(Kl,Object.assign({width:Y},O,{zIndex:z,getContainer:b===void 0?n:b,prefixCls:T,rootClassName:k(j,c,M,N),footer:L,visible:f??y,mousePosition:(t=O.mousePosition)!==null&&t!==void 0?t:po,onClose:a,closable:R&&{disabled:_,closeIcon:F},closeIcon:F,focusTriggerAfterClose:g,transitionName:rn(P,"zoom",e.transitionName),maskTransitionName:rn(P,"fade",e.maskTransitionName),className:k(j,u,i==null?void 0:i.className),style:Object.assign(Object.assign(Object.assign({},i==null?void 0:i.style),v),U),classNames:Object.assign(Object.assign(Object.assign({},i==null?void 0:i.classNames),x),{wrapper:k(I,x==null?void 0:x.wrapper)}),styles:Object.assign(Object.assign({},i==null?void 0:i.styles),S),panelRef:Q}),E?d.createElement(Al,{active:!0,title:!1,paragraph:{rows:4},className:`${T}-body-skeleton`}):w))))},Ch=e=>{const{componentCls:t,titleFontSize:n,titleLineHeight:r,modalConfirmIconSize:o,fontSize:i,lineHeight:a,modalTitleHeight:s,fontHeight:l,confirmBodyPadding:u}=e,c=`${t}-confirm`;return{[c]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${c}-body-wrapper`]:Object.assign({},zd()),[`&${t} ${t}-body`]:{padding:u},[`${c}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:o,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(l).sub(o).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(s).sub(o).equal()).div(2).equal()}},[`${c}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS,maxWidth:`calc(100% - ${A(e.marginSM)})`},[`${e.iconCls} + ${c}-paragraph`]:{maxWidth:`calc(100% - ${A(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${c}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:n,lineHeight:r},[`${c}-content`]:{color:e.colorText,fontSize:i,lineHeight:a},[`${c}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${c}-error ${c}-body > ${e.iconCls}`]:{color:e.colorError},[`${c}-warning ${c}-body > ${e.iconCls},
        ${c}-confirm ${c}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${c}-info ${c}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${c}-success ${c}-body > ${e.iconCls}`]:{color:e.colorSuccess}}},xh=nf(["Modal","confirm"],e=>{const t=ec(e);return[Ch(t)]},tc,{order:-1e3});var Sh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function oc(e){const{prefixCls:t,icon:n,okText:r,cancelText:o,confirmPrefixCls:i,type:a,okCancel:s,footer:l,locale:u}=e,c=Sh(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let f=n;if(!n&&n!==null)switch(a){case"info":f=d.createElement(lf,null);break;case"success":f=d.createElement(sf,null);break;case"error":f=d.createElement(af,null);break;default:f=d.createElement(of,null)}const m=s??a==="confirm",h=e.autoFocusButton===null?!1:e.autoFocusButton||"ok",[b]=zn("Modal"),g=u||b,v=r||(m?g==null?void 0:g.okText:g==null?void 0:g.justOkText),y=o||(g==null?void 0:g.cancelText),p=Object.assign({autoFocusButton:h,cancelTextLocale:y,okTextLocale:v,mergedOkCancel:m},c),C=d.useMemo(()=>p,Oe(Object.values(p))),x=d.createElement(d.Fragment,null,d.createElement(ta,null),d.createElement(na,null)),S=e.title!==void 0&&e.title!==null,w=`${i}-body`;return d.createElement("div",{className:`${i}-body-wrapper`},d.createElement("div",{className:k(w,{[`${w}-has-title`]:S})},f,d.createElement("div",{className:`${i}-paragraph`},S&&d.createElement("span",{className:`${i}-title`},e.title),d.createElement("div",{className:`${i}-content`},e.content))),l===void 0||typeof l=="function"?d.createElement(ql,{value:C},d.createElement("div",{className:`${i}-btns`},typeof l=="function"?l(x,{OkBtn:na,CancelBtn:ta}):x)):l,d.createElement(xh,{prefixCls:t}))}const wh=e=>{const{close:t,zIndex:n,maskStyle:r,direction:o,prefixCls:i,wrapClassName:a,rootPrefixCls:s,bodyStyle:l,closable:u=!1,onConfirm:c,styles:f}=e,m=`${i}-confirm`,h=e.width||416,b=e.style||{},g=e.mask===void 0?!0:e.mask,v=e.maskClosable===void 0?!1:e.maskClosable,y=k(m,`${m}-${e.type}`,{[`${m}-rtl`]:o==="rtl"},e.className),[,p]=Hd(),C=d.useMemo(()=>n!==void 0?n:p.zIndexPopupBase+rf,[n,p]);return d.createElement(rc,Object.assign({},e,{className:y,wrapClassName:k({[`${m}-centered`]:!!e.centered},a),onCancel:()=>{t==null||t({triggerCancel:!0}),c==null||c(!1)},title:"",footer:null,transitionName:rn(s||"","zoom",e.transitionName),maskTransitionName:rn(s||"","fade",e.maskTransitionName),mask:g,maskClosable:v,style:b,styles:Object.assign({body:l,mask:r},f),width:h,zIndex:C,closable:u}),d.createElement(oc,Object.assign({},e,{confirmPrefixCls:m})))},ic=e=>{const{rootPrefixCls:t,iconPrefixCls:n,direction:r,theme:o}=e;return d.createElement(Qo,{prefixCls:t,iconPrefixCls:n,direction:r,theme:o},d.createElement(wh,Object.assign({},e)))},Ut=[];let ac="";function sc(){return ac}const Eh=e=>{var t,n;const{prefixCls:r,getContainer:o,direction:i}=e,a=hl(),s=d.useContext(Ue),l=sc()||s.getPrefixCls(),u=r||`${l}-modal`;let c=o;return c===!1&&(c=void 0),$.createElement(ic,Object.assign({},e,{rootPrefixCls:l,prefixCls:u,iconPrefixCls:s.iconPrefixCls,theme:s.theme,direction:i??s.direction,locale:(n=(t=s.locale)===null||t===void 0?void 0:t.Modal)!==null&&n!==void 0?n:a,getContainer:c}))};function Wn(e){const t=Wd(),n=document.createDocumentFragment();let r=Object.assign(Object.assign({},e),{close:l,open:!0}),o,i;function a(){for(var c,f=arguments.length,m=new Array(f),h=0;h<f;h++)m[h]=arguments[h];if(m.some(v=>v==null?void 0:v.triggerCancel)){var g;(c=e.onCancel)===null||c===void 0||(g=c).call.apply(g,[e,()=>{}].concat(Oe(m.slice(1))))}for(let v=0;v<Ut.length;v++)if(Ut[v]===l){Ut.splice(v,1);break}i()}function s(c){clearTimeout(o),o=setTimeout(()=>{const f=t.getPrefixCls(void 0,sc()),m=t.getIconPrefixCls(),h=t.getTheme(),b=$.createElement(Eh,Object.assign({},c));i=cf()($.createElement(Qo,{prefixCls:f,iconPrefixCls:m,theme:h},t.holderRender?t.holderRender(b):b),n)})}function l(){for(var c=arguments.length,f=new Array(c),m=0;m<c;m++)f[m]=arguments[m];r=Object.assign(Object.assign({},r),{open:!1,afterClose:()=>{typeof e.afterClose=="function"&&e.afterClose(),a.apply(this,f)}}),r.visible&&delete r.visible,s(r)}function u(c){typeof c=="function"?r=c(r):r=Object.assign(Object.assign({},r),c),s(r)}return s(r),Ut.push(l),{destroy:l,update:u}}function lc(e){return Object.assign(Object.assign({},e),{type:"warning"})}function cc(e){return Object.assign(Object.assign({},e),{type:"info"})}function uc(e){return Object.assign(Object.assign({},e),{type:"success"})}function dc(e){return Object.assign(Object.assign({},e),{type:"error"})}function fc(e){return Object.assign(Object.assign({},e),{type:"confirm"})}function Oh(e){let{rootPrefixCls:t}=e;ac=t}var $h=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ph=(e,t)=>{var n,{afterClose:r,config:o}=e,i=$h(e,["afterClose","config"]);const[a,s]=d.useState(!0),[l,u]=d.useState(o),{direction:c,getPrefixCls:f}=d.useContext(Ue),m=f("modal"),h=f(),b=()=>{var p;r(),(p=l.afterClose)===null||p===void 0||p.call(l)},g=function(){var p;s(!1);for(var C=arguments.length,x=new Array(C),S=0;S<C;S++)x[S]=arguments[S];if(x.some(O=>O==null?void 0:O.triggerCancel)){var E;(p=l.onCancel)===null||p===void 0||(E=p).call.apply(E,[l,()=>{}].concat(Oe(x.slice(1))))}};d.useImperativeHandle(t,()=>({destroy:g,update:p=>{u(C=>Object.assign(Object.assign({},C),p))}}));const v=(n=l.okCancel)!==null&&n!==void 0?n:l.type==="confirm",[y]=zn("Modal",qd.Modal);return d.createElement(ic,Object.assign({prefixCls:m,rootPrefixCls:h},l,{close:g,open:a,afterClose:b,okText:l.okText||(v?y==null?void 0:y.okText:y==null?void 0:y.justOkText),direction:l.direction||c,cancelText:l.cancelText||(y==null?void 0:y.cancelText)},i))},Th=d.forwardRef(Ph);let da=0;const Rh=d.memo(d.forwardRef((e,t)=>{const[n,r]=Uf();return d.useImperativeHandle(t,()=>({patchElement:r}),[]),d.createElement(d.Fragment,null,n)}));function Mh(){const e=d.useRef(null),[t,n]=d.useState([]);d.useEffect(()=>{t.length&&(Oe(t).forEach(a=>{a()}),n([]))},[t]);const r=d.useCallback(i=>function(s){var l;da+=1;const u=d.createRef();let c;const f=new Promise(v=>{c=v});let m=!1,h;const b=d.createElement(Th,{key:`modal-${da}`,config:i(s),ref:u,afterClose:()=>{h==null||h()},isSilent:()=>m,onConfirm:v=>{c(v)}});return h=(l=e.current)===null||l===void 0?void 0:l.patchElement(b),h&&Ut.push(h),{destroy:()=>{function v(){var y;(y=u.current)===null||y===void 0||y.destroy()}u.current?v():n(y=>[].concat(Oe(y),[v]))},update:v=>{function y(){var p;(p=u.current)===null||p===void 0||p.update(v)}u.current?y():n(p=>[].concat(Oe(p),[y]))},then:v=>(m=!0,f.then(v))}},[]);return[d.useMemo(()=>({info:r(cc),success:r(uc),error:r(dc),warning:r(lc),confirm:r(fc)}),[]),d.createElement(Rh,{key:"modal-holder",ref:e})]}const vo=d.createContext({}),Nh=e=>{const{antCls:t,componentCls:n,iconCls:r,avatarBg:o,avatarColor:i,containerSize:a,containerSizeLG:s,containerSizeSM:l,textFontSize:u,textFontSizeLG:c,textFontSizeSM:f,borderRadius:m,borderRadiusLG:h,borderRadiusSM:b,lineWidth:g,lineType:v}=e,y=(p,C,x)=>({width:p,height:p,borderRadius:"50%",[`&${n}-square`]:{borderRadius:x},[`&${n}-icon`]:{fontSize:C,[`> ${r}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},dt(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:i,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:o,border:`${A(g)} ${v} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),y(a,u,m)),{"&-lg":Object.assign({},y(s,c,h)),"&-sm":Object.assign({},y(l,f,b)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},jh=e=>{const{componentCls:t,groupBorderColor:n,groupOverlapping:r,groupSpace:o}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:r}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:o}}}},Dh=e=>{const{controlHeight:t,controlHeightLG:n,controlHeightSM:r,fontSize:o,fontSizeLG:i,fontSizeXL:a,fontSizeHeading3:s,marginXS:l,marginXXS:u,colorBorderBg:c}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:r,textFontSize:Math.round((i+a)/2),textFontSizeLG:s,textFontSizeSM:o,groupSpace:u,groupOverlapping:-l,groupBorderColor:c}},hc=gt("Avatar",e=>{const{colorTextLightSolid:t,colorTextPlaceholder:n}=e,r=pt(e,{avatarBg:n,avatarColor:t});return[Nh(r),jh(r)]},Dh);var Ih=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ah=(e,t)=>{const[n,r]=d.useState(1),[o,i]=d.useState(!1),[a,s]=d.useState(!0),l=d.useRef(null),u=d.useRef(null),c=_n(t,l),{getPrefixCls:f,avatar:m}=d.useContext(Ue),h=d.useContext(vo),b=()=>{if(!u.current||!l.current)return;const B=u.current.offsetWidth,K=l.current.offsetWidth;if(B!==0&&K!==0){const{gap:re=4}=e;re*2<K&&r(K-re*2<B?(K-re*2)/B:1)}};d.useEffect(()=>{i(!0)},[]),d.useEffect(()=>{s(!0),r(1)},[e.src]),d.useEffect(b,[e.gap]);const g=()=>{const{onError:B}=e;(B==null?void 0:B())!==!1&&s(!1)},{prefixCls:v,shape:y,size:p,src:C,srcSet:x,icon:S,className:w,rootClassName:E,alt:O,draggable:T,children:P,crossOrigin:N}=e,D=Ih(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),j=xr(B=>{var K,re;return(re=(K=p??(h==null?void 0:h.size))!==null&&K!==void 0?K:B)!==null&&re!==void 0?re:"default"}),M=Object.keys(typeof j=="object"?j||{}:{}).some(B=>["xs","sm","md","lg","xl","xxl"].includes(B)),I=kf(M),L=d.useMemo(()=>{if(typeof j!="object")return{};const B=Bf.find(re=>I[re]),K=j[B];return K?{width:K,height:K,fontSize:K&&(S||P)?K/2:18}:{}},[I,j]),R=f("avatar",v),F=yn(R),[_,Q,z]=hc(R,F),G=k({[`${R}-lg`]:j==="large",[`${R}-sm`]:j==="small"}),Y=d.isValidElement(C),J=y||(h==null?void 0:h.shape)||"circle",U=k(R,G,m==null?void 0:m.className,`${R}-${J}`,{[`${R}-image`]:Y||C&&a,[`${R}-icon`]:!!S},z,F,w,E,Q),H=typeof j=="number"?{width:j,height:j,fontSize:S?j/2:18}:{};let W;if(typeof C=="string"&&a)W=d.createElement("img",{src:C,draggable:T,srcSet:x,onError:g,alt:O,crossOrigin:N});else if(Y)W=C;else if(S)W=S;else if(o||n!==1){const B=`scale(${n})`,K={msTransform:B,WebkitTransform:B,transform:B};W=d.createElement(uf,{onResize:b},d.createElement("span",{className:`${R}-string`,ref:u,style:Object.assign({},K)},P))}else W=d.createElement("span",{className:`${R}-string`,style:{opacity:0},ref:u},P);return delete D.onError,delete D.gap,_(d.createElement("span",Object.assign({},D,{style:Object.assign(Object.assign(Object.assign(Object.assign({},H),L),m==null?void 0:m.style),D.style),className:U,ref:c}),W))},mc=d.forwardRef(Ah),hr=e=>e?typeof e=="function"?e():e:null,Vh=e=>{const{componentCls:t,popoverColor:n,titleMinWidth:r,fontWeightStrong:o,innerPadding:i,boxShadowSecondary:a,colorTextHeading:s,borderRadiusLG:l,zIndexPopup:u,titleMarginBottom:c,colorBgElevated:f,popoverBg:m,titleBorderBottom:h,innerContentPadding:b,titlePadding:g}=e;return[{[t]:Object.assign(Object.assign({},dt(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:u,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":f,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:l,boxShadow:a,padding:i},[`${t}-title`]:{minWidth:r,marginBottom:c,color:s,fontWeight:o,borderBottom:h,padding:g},[`${t}-inner-content`]:{color:n,padding:b}})},Lf(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},kh=e=>{const{componentCls:t}=e;return{[t]:df.map(n=>{const r=e[`${n}6`];return{[`&${t}-${n}`]:{"--antd-arrow-background-color":r,[`${t}-inner`]:{backgroundColor:r},[`${t}-arrow`]:{background:"transparent"}}}})}},Bh=e=>{const{lineWidth:t,controlHeight:n,fontHeight:r,padding:o,wireframe:i,zIndexPopupBase:a,borderRadiusLG:s,marginXS:l,lineType:u,colorSplit:c,paddingSM:f}=e,m=n-r,h=m/2,b=m/2-t,g=o;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:a+30},Ff(e)),_f({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:l,titlePadding:i?`${h}px ${g}px ${b}px`:0,titleBorderBottom:i?`${t}px ${u} ${c}`:"none",innerContentPadding:i?`${f}px ${g}px`:0})},gc=gt("Popover",e=>{const{colorBgElevated:t,colorText:n}=e,r=pt(e,{popoverBg:t,popoverColor:n});return[Vh(r),kh(r),Ll(r,"zoom-big")]},Bh,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Lh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const pc=e=>{let{title:t,content:n,prefixCls:r}=e;return!t&&!n?null:d.createElement(d.Fragment,null,t&&d.createElement("div",{className:`${r}-title`},t),n&&d.createElement("div",{className:`${r}-inner-content`},n))},Fh=e=>{const{hashId:t,prefixCls:n,className:r,style:o,placement:i="top",title:a,content:s,children:l}=e,u=hr(a),c=hr(s),f=k(t,n,`${n}-pure`,`${n}-placement-${i}`,r);return d.createElement("div",{className:f,style:o},d.createElement("div",{className:`${n}-arrow`}),d.createElement(zf,Object.assign({},e,{className:t,prefixCls:n}),l||d.createElement(pc,{prefixCls:n,title:u,content:c})))},_h=e=>{const{prefixCls:t,className:n}=e,r=Lh(e,["prefixCls","className"]),{getPrefixCls:o}=d.useContext(Ue),i=o("popover",t),[a,s,l]=gc(i);return a(d.createElement(Fh,Object.assign({},r,{prefixCls:i,hashId:s,className:k(n,l)})))};var zh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Hh=d.forwardRef((e,t)=>{var n,r;const{prefixCls:o,title:i,content:a,overlayClassName:s,placement:l="top",trigger:u="hover",children:c,mouseEnterDelay:f=.1,mouseLeaveDelay:m=.1,onOpenChange:h,overlayStyle:b={},styles:g,classNames:v}=e,y=zh(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:p,className:C,style:x,classNames:S,styles:w}=on("popover"),E=p("popover",o),[O,T,P]=gc(E),N=p(),D=k(s,T,P,C,S.root,v==null?void 0:v.root),j=k(S.body,v==null?void 0:v.body),[M,I]=Dt(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(r=e.defaultOpen)!==null&&r!==void 0?r:e.defaultVisible}),L=(z,G)=>{I(z,!0),h==null||h(z,G)},R=z=>{z.keyCode===Ee.ESC&&L(!1,z)},F=z=>{L(z)},_=hr(i),Q=hr(a);return O(d.createElement(dr,Object.assign({placement:l,trigger:u,mouseEnterDelay:f,mouseLeaveDelay:m},y,{prefixCls:E,classNames:{root:D,body:j},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},w.root),x),b),g==null?void 0:g.root),body:Object.assign(Object.assign({},w.body),g==null?void 0:g.body)},ref:t,open:M,onOpenChange:F,overlay:_||Q?d.createElement(pc,{prefixCls:E,title:_,content:Q}):null,transitionName:rn(N,"zoom-big",y.transitionName),"data-popover-inject":!0}),ti(c,{onKeyDown:z=>{var G,Y;d.isValidElement(c)&&((Y=c==null?void 0:(G=c.props).onKeyDown)===null||Y===void 0||Y.call(G,z)),R(z)}})))}),ri=Hh;ri._InternalPanelDoNotUseOrYouWillBeFired=_h;const fa=e=>{const{size:t,shape:n}=d.useContext(vo),r=d.useMemo(()=>({size:e.size||t,shape:e.shape||n}),[e.size,e.shape,t,n]);return d.createElement(vo.Provider,{value:r},e.children)},Wh=e=>{var t,n,r,o;const{getPrefixCls:i,direction:a}=d.useContext(Ue),{prefixCls:s,className:l,rootClassName:u,style:c,maxCount:f,maxStyle:m,size:h,shape:b,maxPopoverPlacement:g,maxPopoverTrigger:v,children:y,max:p}=e,C=i("avatar",s),x=`${C}-group`,S=yn(C),[w,E,O]=hc(C,S),T=k(x,{[`${x}-rtl`]:a==="rtl"},O,S,l,u,E),P=ur(y).map((j,M)=>ti(j,{key:`avatar-key-${M}`})),N=(p==null?void 0:p.count)||f,D=P.length;if(N&&N<D){const j=P.slice(0,N),M=P.slice(N,D),I=(p==null?void 0:p.style)||m,L=((t=p==null?void 0:p.popover)===null||t===void 0?void 0:t.trigger)||v||"hover",R=((n=p==null?void 0:p.popover)===null||n===void 0?void 0:n.placement)||g||"top",F=Object.assign(Object.assign({content:M},p==null?void 0:p.popover),{classNames:{root:k(`${x}-popover`,(o=(r=p==null?void 0:p.popover)===null||r===void 0?void 0:r.classNames)===null||o===void 0?void 0:o.root)},placement:R,trigger:L});return j.push(d.createElement(ri,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},F),d.createElement(mc,{style:I},`+${D-N}`))),w(d.createElement(fa,{shape:b,size:h},d.createElement("div",{className:T,style:c},j)))}return w(d.createElement(fa,{shape:b,size:h},d.createElement("div",{className:T,style:c},P)))},vc=mc;vc.Group=Wh;const Sr=e=>{let{children:t}=e;const{getPrefixCls:n}=d.useContext(Ue),r=n("breadcrumb");return d.createElement("li",{className:`${r}-separator`,"aria-hidden":"true"},t===""?t:t||"/")};Sr.__ANT_BREADCRUMB_SEPARATOR=!0;var qh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Gh(e,t){if(e.title===void 0||e.title===null)return null;const n=Object.keys(t).join("|");return typeof e.title=="object"?e.title:String(e.title).replace(new RegExp(`:(${n})`,"g"),(r,o)=>t[o]||r)}function bc(e,t,n,r){if(n==null)return null;const{className:o,onClick:i}=t,a=qh(t,["className","onClick"]),s=Object.assign(Object.assign({},Et(a,{data:!0,aria:!0})),{onClick:i});return r!==void 0?d.createElement("a",Object.assign({},s,{className:k(`${e}-link`,o),href:r}),n):d.createElement("span",Object.assign({},s,{className:k(`${e}-link`,o)}),n)}function Uh(e,t){return(r,o,i,a,s)=>{if(t)return t(r,o,i,a);const l=Gh(r,o);return bc(e,r,l,s)}}var bo=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const yc=e=>{const{prefixCls:t,separator:n="/",children:r,menu:o,overlay:i,dropdownProps:a,href:s}=e,u=(c=>{if(o||i){const f=Object.assign({},a);if(o){const m=o||{},{items:h}=m,b=bo(m,["items"]);f.menu=Object.assign(Object.assign({},b),{items:h==null?void 0:h.map((g,v)=>{var{key:y,title:p,label:C,path:x}=g,S=bo(g,["key","title","label","path"]);let w=C??p;return x&&(w=d.createElement("a",{href:`${s}${x}`},w)),Object.assign(Object.assign({},S),{key:y??v,label:w})})})}else i&&(f.overlay=i);return d.createElement(Mf,Object.assign({placement:"bottom"},f),d.createElement("span",{className:`${t}-overlay-link`},c,d.createElement(Vl,null)))}return c})(r);return u!=null?d.createElement(d.Fragment,null,d.createElement("li",null,u),n&&d.createElement(Sr,null,n)):null},Cc=e=>{const{prefixCls:t,children:n,href:r}=e,o=bo(e,["prefixCls","children","href"]),{getPrefixCls:i}=d.useContext(Ue),a=i("breadcrumb",t);return d.createElement(yc,Object.assign({},o,{prefixCls:a}),bc(a,o,n,r))};Cc.__ANT_BREADCRUMB_ITEM=!0;const Xh=e=>{const{componentCls:t,iconCls:n,calc:r}=e;return{[t]:Object.assign(Object.assign({},dt(e)),{color:e.itemColor,fontSize:e.fontSize,[n]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:`color ${e.motionDurationMid}`,padding:`0 ${A(e.paddingXXS)}`,borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:r(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},Cr(e)),"li:last-child":{color:e.lastItemColor},[`${t}-separator`]:{marginInline:e.separatorMargin,color:e.separatorColor},[`${t}-link`]:{[`
          > ${n} + span,
          > ${n} + a
        `]:{marginInlineStart:e.marginXXS}},[`${t}-overlay-link`]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:`0 ${A(e.paddingXXS)}`,marginInline:r(e.marginXXS).mul(-1).equal(),[`> ${n}`]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${e.componentCls}-rtl`]:{direction:"rtl"}})}},Kh=e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS}),Yh=gt("Breadcrumb",e=>{const t=pt(e,{});return Xh(t)},Kh);var ha=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Zh(e){const{breadcrumbName:t,children:n}=e,r=ha(e,["breadcrumbName","children"]),o=Object.assign({title:t},r);return n&&(o.menu={items:n.map(i=>{var{breadcrumbName:a}=i,s=ha(i,["breadcrumbName"]);return Object.assign(Object.assign({},s),{title:a})})}),o}function Qh(e,t){return d.useMemo(()=>e||(t?t.map(Zh):null),[e,t])}var Jh=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const em=(e,t)=>{if(t===void 0)return t;let n=(t||"").replace(/^\//,"");return Object.keys(e).forEach(r=>{n=n.replace(`:${r}`,e[r])}),n},oi=e=>{const{prefixCls:t,separator:n="/",style:r,className:o,rootClassName:i,routes:a,items:s,children:l,itemRender:u,params:c={}}=e,f=Jh(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:m,direction:h,breadcrumb:b}=d.useContext(Ue);let g;const v=m("breadcrumb",t),[y,p,C]=Yh(v),x=Qh(s,a),S=Uh(v,u);if(x&&x.length>0){const O=[],T=s||a;g=x.map((P,N)=>{const{path:D,key:j,type:M,menu:I,overlay:L,onClick:R,className:F,separator:_,dropdownProps:Q}=P,z=em(c,D);z!==void 0&&O.push(z);const G=j??N;if(M==="separator")return d.createElement(Sr,{key:G},_);const Y={},J=N===x.length-1;I?Y.menu=I:L&&(Y.overlay=L);let{href:U}=P;return O.length&&z!==void 0&&(U=`#/${O.join("/")}`),d.createElement(yc,Object.assign({key:G},Y,Et(P,{data:!0,aria:!0}),{className:F,dropdownProps:Q,href:U,separator:J?"":n,onClick:R,prefixCls:v}),S(P,c,T,O,U))})}else if(l){const O=ur(l).length;g=ur(l).map((T,P)=>{if(!T)return T;const N=P===O-1;return ti(T,{separator:N?"":n,key:P})})}const w=k(v,b==null?void 0:b.className,{[`${v}-rtl`]:h==="rtl"},o,i,p,C),E=Object.assign(Object.assign({},b==null?void 0:b.style),r);return y(d.createElement("nav",Object.assign({className:w,style:E},f),d.createElement("ol",null,g)))};oi.Item=Cc;oi.Separator=Sr;const tm=e=>{const{componentCls:t,sizePaddingEdgeHorizontal:n,colorSplit:r,lineWidth:o,textPaddingInline:i,orientationMargin:a,verticalMarginInline:s}=e;return{[t]:Object.assign(Object.assign({},dt(e)),{borderBlockStart:`${A(o)} solid ${r}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:s,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${A(o)} solid ${r}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${A(e.dividerHorizontalGutterMargin)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${A(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${r}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${A(o)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${a} * 100%)`},"&::after":{width:`calc(100% - ${a} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${a} * 100%)`},"&::after":{width:`calc(${a} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:i},"&-dashed":{background:"none",borderColor:r,borderStyle:"dashed",borderWidth:`${A(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:r,borderStyle:"dotted",borderWidth:`${A(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:n}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:n}}})}},nm=e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),rm=gt("Divider",e=>{const t=pt(e,{dividerHorizontalWithTextGutterMargin:e.margin,dividerHorizontalGutterMargin:e.marginLG,sizePaddingEdgeHorizontal:0});return[tm(t)]},nm,{unitless:{orientationMargin:!0}});var om=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const yo=e=>{const{getPrefixCls:t,direction:n,className:r,style:o}=on("divider"),{prefixCls:i,type:a="horizontal",orientation:s="center",orientationMargin:l,className:u,rootClassName:c,children:f,dashed:m,variant:h="solid",plain:b,style:g}=e,v=om(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),y=t("divider",i),[p,C,x]=rm(y),S=!!f,w=d.useMemo(()=>s==="left"?n==="rtl"?"end":"start":s==="right"?n==="rtl"?"start":"end":s,[n,s]),E=w==="start"&&l!=null,O=w==="end"&&l!=null,T=k(y,r,C,x,`${y}-${a}`,{[`${y}-with-text`]:S,[`${y}-with-text-${w}`]:S,[`${y}-dashed`]:!!m,[`${y}-${h}`]:h!=="solid",[`${y}-plain`]:!!b,[`${y}-rtl`]:n==="rtl",[`${y}-no-default-orientation-margin-start`]:E,[`${y}-no-default-orientation-margin-end`]:O},u,c),P=d.useMemo(()=>typeof l=="number"?l:/^\d+$/.test(l)?Number(l):l,[l]),N={marginInlineStart:E?P:void 0,marginInlineEnd:O?P:void 0};return p(d.createElement("div",Object.assign({className:T,style:Object.assign(Object.assign({},o),g)},v,{role:"separator"}),f&&a!=="vertical"&&d.createElement("span",{className:`${y}-inner-text`,style:N},f)))};var ma=function(t,n){if(!t)return null;var r={left:t.offsetLeft,right:t.parentElement.clientWidth-t.clientWidth-t.offsetLeft,width:t.clientWidth,top:t.offsetTop,bottom:t.parentElement.clientHeight-t.clientHeight-t.offsetTop,height:t.clientHeight};return n?{left:0,right:0,width:0,top:r.top,bottom:r.bottom,height:r.height}:{left:r.left,right:r.right,width:r.width,top:0,bottom:0,height:0}},ct=function(t){return t!==void 0?"".concat(t,"px"):void 0};function im(e){var t=e.prefixCls,n=e.containerRef,r=e.value,o=e.getValueIndex,i=e.motionName,a=e.onMotionStart,s=e.onMotionEnd,l=e.direction,u=e.vertical,c=u===void 0?!1:u,f=d.useRef(null),m=d.useState(r),h=ce(m,2),b=h[0],g=h[1],v=function(I){var L,R=o(I),F=(L=n.current)===null||L===void 0?void 0:L.querySelectorAll(".".concat(t,"-item"))[R];return(F==null?void 0:F.offsetParent)&&F},y=d.useState(null),p=ce(y,2),C=p[0],x=p[1],S=d.useState(null),w=ce(S,2),E=w[0],O=w[1];tn(function(){if(b!==r){var M=v(b),I=v(r),L=ma(M,c),R=ma(I,c);g(r),x(L),O(R),M&&I?a():s()}},[r]);var T=d.useMemo(function(){if(c){var M;return ct((M=C==null?void 0:C.top)!==null&&M!==void 0?M:0)}return ct(l==="rtl"?-(C==null?void 0:C.right):C==null?void 0:C.left)},[c,l,C]),P=d.useMemo(function(){if(c){var M;return ct((M=E==null?void 0:E.top)!==null&&M!==void 0?M:0)}return ct(l==="rtl"?-(E==null?void 0:E.right):E==null?void 0:E.left)},[c,l,E]),N=function(){return c?{transform:"translateY(var(--thumb-start-top))",height:"var(--thumb-start-height)"}:{transform:"translateX(var(--thumb-start-left))",width:"var(--thumb-start-width)"}},D=function(){return c?{transform:"translateY(var(--thumb-active-top))",height:"var(--thumb-active-height)"}:{transform:"translateX(var(--thumb-active-left))",width:"var(--thumb-active-width)"}},j=function(){x(null),O(null),s()};return!C||!E?null:d.createElement(jn,{visible:!0,motionName:i,motionAppear:!0,onAppearStart:N,onAppearActive:D,onVisibleChanged:j},function(M,I){var L=M.className,R=M.style,F=ne(ne({},R),{},{"--thumb-start-left":T,"--thumb-start-width":ct(C==null?void 0:C.width),"--thumb-active-left":P,"--thumb-active-width":ct(E==null?void 0:E.width),"--thumb-start-top":T,"--thumb-start-height":ct(C==null?void 0:C.height),"--thumb-active-top":P,"--thumb-active-height":ct(E==null?void 0:E.height)}),_={ref:_n(f,I),style:F,className:k("".concat(t,"-thumb"),L)};return d.createElement("div",_)})}var am=["prefixCls","direction","vertical","options","disabled","defaultValue","value","name","onChange","className","motionName"];function sm(e){if(typeof e.title<"u")return e.title;if(gn(e.label)!=="object"){var t;return(t=e.label)===null||t===void 0?void 0:t.toString()}}function lm(e){return e.map(function(t){if(gn(t)==="object"&&t!==null){var n=sm(t);return ne(ne({},t),{},{title:n})}return{label:t==null?void 0:t.toString(),title:t==null?void 0:t.toString(),value:t}})}var cm=function(t){var n=t.prefixCls,r=t.className,o=t.disabled,i=t.checked,a=t.label,s=t.title,l=t.value,u=t.name,c=t.onChange,f=t.onFocus,m=t.onBlur,h=t.onKeyDown,b=t.onKeyUp,g=t.onMouseDown,v=function(p){o||c(p,l)};return d.createElement("label",{className:k(r,se({},"".concat(n,"-item-disabled"),o)),onMouseDown:g},d.createElement("input",{name:u,className:"".concat(n,"-item-input"),type:"radio",disabled:o,checked:i,onChange:v,onFocus:f,onBlur:m,onKeyDown:h,onKeyUp:b}),d.createElement("div",{className:"".concat(n,"-item-label"),title:s,"aria-selected":i},a))},um=d.forwardRef(function(e,t){var n,r,o=e.prefixCls,i=o===void 0?"rc-segmented":o,a=e.direction,s=e.vertical,l=e.options,u=l===void 0?[]:l,c=e.disabled,f=e.defaultValue,m=e.value,h=e.name,b=e.onChange,g=e.className,v=g===void 0?"":g,y=e.motionName,p=y===void 0?"thumb-motion":y,C=bn(e,am),x=d.useRef(null),S=d.useMemo(function(){return _n(x,t)},[x,t]),w=d.useMemo(function(){return lm(u)},[u]),E=Dt((n=w[0])===null||n===void 0?void 0:n.value,{value:m,defaultValue:f}),O=ce(E,2),T=O[0],P=O[1],N=d.useState(!1),D=ce(N,2),j=D[0],M=D[1],I=function(oe,ue){P(ue),b==null||b(ue)},L=Tl(C,["children"]),R=d.useState(!1),F=ce(R,2),_=F[0],Q=F[1],z=d.useState(!1),G=ce(z,2),Y=G[0],J=G[1],U=function(){J(!0)},H=function(){J(!1)},W=function(){Q(!1)},B=function(oe){oe.key==="Tab"&&Q(!0)},K=function(oe){var ue=w.findIndex(function(he){return he.value===T}),Se=w.length,le=(ue+oe+Se)%Se,pe=w[le];pe&&(P(pe.value),b==null||b(pe.value))},re=function(oe){switch(oe.key){case"ArrowLeft":case"ArrowUp":K(-1);break;case"ArrowRight":case"ArrowDown":K(1);break}};return d.createElement("div",Ce({role:"radiogroup","aria-label":"segmented control",tabIndex:c?void 0:0},L,{className:k(i,(r={},se(r,"".concat(i,"-rtl"),a==="rtl"),se(r,"".concat(i,"-disabled"),c),se(r,"".concat(i,"-vertical"),s),r),v),ref:S}),d.createElement("div",{className:"".concat(i,"-group")},d.createElement(im,{vertical:s,prefixCls:i,value:T,containerRef:x,motionName:"".concat(i,"-").concat(p),direction:a,getValueIndex:function(oe){return w.findIndex(function(ue){return ue.value===oe})},onMotionStart:function(){M(!0)},onMotionEnd:function(){M(!1)}}),w.map(function(q){var oe;return d.createElement(cm,Ce({},q,{name:h,key:q.value,prefixCls:i,className:k(q.className,"".concat(i,"-item"),(oe={},se(oe,"".concat(i,"-item-selected"),q.value===T&&!j),se(oe,"".concat(i,"-item-focused"),Y&&_&&q.value===T),oe)),checked:q.value===T,onChange:I,onFocus:U,onBlur:H,onKeyDown:re,onKeyUp:B,onMouseDown:W,disabled:!!c||!!q.disabled}))})))}),dm=um;function ga(e,t){return{[`${e}, ${e}:hover, ${e}:focus`]:{color:t.colorTextDisabled,cursor:"not-allowed"}}}function pa(e){return{backgroundColor:e.itemSelectedBg,boxShadow:e.boxShadowTertiary}}const fm=Object.assign({overflow:"hidden"},Ud),hm=e=>{const{componentCls:t}=e,n=e.calc(e.controlHeight).sub(e.calc(e.trackPadding).mul(2)).equal(),r=e.calc(e.controlHeightLG).sub(e.calc(e.trackPadding).mul(2)).equal(),o=e.calc(e.controlHeightSM).sub(e.calc(e.trackPadding).mul(2)).equal();return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},dt(e)),{display:"inline-block",padding:e.trackPadding,color:e.itemColor,background:e.trackBg,borderRadius:e.borderRadius,transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`}),Cr(e)),{[`${t}-group`]:{position:"relative",display:"flex",alignItems:"stretch",justifyItems:"flex-start",flexDirection:"row",width:"100%"},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-vertical`]:{[`${t}-group`]:{flexDirection:"column"},[`${t}-thumb`]:{width:"100%",height:0,padding:`0 ${A(e.paddingXXS)}`}},[`&${t}-block`]:{display:"flex"},[`&${t}-block ${t}-item`]:{flex:1,minWidth:0},[`${t}-item`]:{position:"relative",textAlign:"center",cursor:"pointer",transition:`color ${e.motionDurationMid} ${e.motionEaseInOut}`,borderRadius:e.borderRadiusSM,transform:"translateZ(0)","&-selected":Object.assign(Object.assign({},pa(e)),{color:e.itemSelectedColor}),"&-focused":Object.assign({},Gd(e)),"&::after":{content:'""',position:"absolute",zIndex:-1,width:"100%",height:"100%",top:0,insetInlineStart:0,borderRadius:"inherit",opacity:0,transition:`opacity ${e.motionDurationMid}`,pointerEvents:"none"},[`&:hover:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemHoverBg}},[`&:active:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemActiveBg}},"&-label":Object.assign({minHeight:n,lineHeight:A(n),padding:`0 ${A(e.segmentedPaddingHorizontal)}`},fm),"&-icon + *":{marginInlineStart:e.calc(e.marginSM).div(2).equal()},"&-input":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:0,opacity:0,pointerEvents:"none"}},[`${t}-thumb`]:Object.assign(Object.assign({},pa(e)),{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:"100%",padding:`${A(e.paddingXXS)} 0`,borderRadius:e.borderRadiusSM,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, height ${e.motionDurationSlow} ${e.motionEaseInOut}`,[`& ~ ${t}-item:not(${t}-item-selected):not(${t}-item-disabled)::after`]:{backgroundColor:"transparent"}}),[`&${t}-lg`]:{borderRadius:e.borderRadiusLG,[`${t}-item-label`]:{minHeight:r,lineHeight:A(r),padding:`0 ${A(e.segmentedPaddingHorizontal)}`,fontSize:e.fontSizeLG},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadius}},[`&${t}-sm`]:{borderRadius:e.borderRadiusSM,[`${t}-item-label`]:{minHeight:o,lineHeight:A(o),padding:`0 ${A(e.segmentedPaddingHorizontalSM)}`},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadiusXS}}}),ga(`&-disabled ${t}-item`,e)),ga(`${t}-item-disabled`,e)),{[`${t}-thumb-motion-appear-active`]:{transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, width ${e.motionDurationSlow} ${e.motionEaseInOut}`,willChange:"transform, width"},[`&${t}-shape-round`]:{borderRadius:9999,[`${t}-item, ${t}-thumb`]:{borderRadius:9999}}})}},mm=e=>{const{colorTextLabel:t,colorText:n,colorFillSecondary:r,colorBgElevated:o,colorFill:i,lineWidthBold:a,colorBgLayout:s}=e;return{trackPadding:a,trackBg:s,itemColor:t,itemHoverColor:n,itemHoverBg:r,itemSelectedBg:o,itemActiveBg:i,itemSelectedColor:n}},gm=gt("Segmented",e=>{const{lineWidth:t,calc:n}=e,r=pt(e,{segmentedPaddingHorizontal:n(e.controlPaddingHorizontal).sub(t).equal(),segmentedPaddingHorizontalSM:n(e.controlPaddingHorizontalSM).sub(t).equal()});return[hm(r)]},mm);var va=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function pm(e){return typeof e=="object"&&!!(e!=null&&e.icon)}const vm=d.forwardRef((e,t)=>{const n=El(),{prefixCls:r,className:o,rootClassName:i,block:a,options:s=[],size:l="middle",style:u,vertical:c,shape:f="default",name:m=n}=e,h=va(e,["prefixCls","className","rootClassName","block","options","size","style","vertical","shape","name"]),{getPrefixCls:b,direction:g,className:v,style:y}=on("segmented"),p=b("segmented",r),[C,x,S]=gm(p),w=xr(l),E=d.useMemo(()=>s.map(P=>{if(pm(P)){const{icon:N,label:D}=P,j=va(P,["icon","label"]);return Object.assign(Object.assign({},j),{label:d.createElement(d.Fragment,null,d.createElement("span",{className:`${p}-item-icon`},N),D&&d.createElement("span",null,D))})}return P}),[s,p]),O=k(o,i,v,{[`${p}-block`]:a,[`${p}-sm`]:w==="small",[`${p}-lg`]:w==="large",[`${p}-vertical`]:c,[`${p}-shape-${f}`]:f==="round"},x,S),T=Object.assign(Object.assign({},y),u);return C(d.createElement(dm,Object.assign({},h,{name:m,className:O,style:T,options:E,ref:t,prefixCls:p,direction:g,vertical:c})))}),bm=vm,xc=$.createContext({}),Sc=$.createContext({}),wc=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=()=>{if(r&&n&&!n.cleared){const i=n.toHsb();i.a=0;const a=rt(i);a.cleared=!0,r(a)}};return $.createElement("div",{className:`${t}-clear`,onClick:o})},Ec="hex",Oc="rgb",$c="hsb";var ym={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},Cm=function(t,n){return d.createElement(ff,Ce({},t,{ref:n,icon:ym}))},xm=d.forwardRef(Cm);function Co(){return typeof BigInt=="function"}function Pc(e){return!e&&e!==0&&!Number.isNaN(e)||!String(e).trim()}function Zt(e){var t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),t.startsWith(".")&&(t="0".concat(t));var r=t||"0",o=r.split("."),i=o[0]||"0",a=o[1]||"0";i==="0"&&a==="0"&&(n=!1);var s=n?"-":"";return{negative:n,negativeStr:s,trimStr:r,integerStr:i,decimalStr:a,fullStr:"".concat(s).concat(r)}}function ii(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function Xt(e){var t=String(e);if(ii(e)){var n=Number(t.slice(t.indexOf("e-")+2)),r=t.match(/\.(\d+)/);return r!=null&&r[1]&&(n+=r[1].length),n}return t.includes(".")&&ai(t)?t.length-t.indexOf(".")-1:0}function wr(e){var t=String(e);if(ii(e)){if(e>Number.MAX_SAFE_INTEGER)return String(Co()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(Co()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(Xt(t))}return Zt(t).fullStr}function ai(e){return typeof e=="number"?!Number.isNaN(e):e?/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e):!1}var Sm=function(){function e(t){if(gl(this,e),se(this,"origin",""),se(this,"negative",void 0),se(this,"integer",void 0),se(this,"decimal",void 0),se(this,"decimalLen",void 0),se(this,"empty",void 0),se(this,"nan",void 0),Pc(t)){this.empty=!0;return}if(this.origin=String(t),t==="-"||Number.isNaN(t)){this.nan=!0;return}var n=t;if(ii(n)&&(n=Number(n)),n=typeof n=="string"?n:wr(n),ai(n)){var r=Zt(n);this.negative=r.negative;var o=r.trimStr.split(".");this.integer=BigInt(o[0]);var i=o[1]||"0";this.decimal=BigInt(i),this.decimalLen=i.length}else this.nan=!0}return ml(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(n){var r="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(n,"0"));return BigInt(r)}},{key:"negate",value:function(){var n=new e(this.toString());return n.negative=!n.negative,n}},{key:"cal",value:function(n,r,o){var i=Math.max(this.getDecimalStr().length,n.getDecimalStr().length),a=this.alignDecimal(i),s=n.alignDecimal(i),l=r(a,s).toString(),u=o(i),c=Zt(l),f=c.negativeStr,m=c.trimStr,h="".concat(f).concat(m.padStart(u+1,"0"));return new e("".concat(h.slice(0,-u),".").concat(h.slice(-u)))}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var r=new e(n);return r.isInvalidate()?this:this.cal(r,function(o,i){return o+i},function(o){return o})}},{key:"multi",value:function(n){var r=new e(n);return this.isInvalidate()||r.isInvalidate()?new e(NaN):this.cal(r,function(o,i){return o*i},function(o){return o*2})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(n){return this.toString()===(n==null?void 0:n.toString())}},{key:"lessEquals",value:function(n){return this.add(n.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return n?this.isInvalidate()?"":Zt("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),wm=function(){function e(t){if(gl(this,e),se(this,"origin",""),se(this,"number",void 0),se(this,"empty",void 0),Pc(t)){this.empty=!0;return}this.origin=String(t),this.number=Number(t)}return ml(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var r=Number(n);if(Number.isNaN(r))return this;var o=this.number+r;if(o>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(o<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var i=Math.max(Xt(this.number),Xt(r));return new e(o.toFixed(i))}},{key:"multi",value:function(n){var r=Number(n);if(this.isInvalidate()||Number.isNaN(r))return new e(NaN);var o=this.number*r;if(o>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(o<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var i=Math.max(Xt(this.number),Xt(r));return new e(o.toFixed(i))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(n){return this.toNumber()===(n==null?void 0:n.toNumber())}},{key:"lessEquals",value:function(n){return this.add(n.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return n?this.isInvalidate()?"":wr(this.number):this.origin}}]),e}();function ot(e){return Co()?new Sm(e):new wm(e)}function rr(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e==="")return"";var o=Zt(e),i=o.negativeStr,a=o.integerStr,s=o.decimalStr,l="".concat(t).concat(s),u="".concat(i).concat(a);if(n>=0){var c=Number(s[n]);if(c>=5&&!r){var f=ot(e).add("".concat(i,"0.").concat("0".repeat(n)).concat(10-c));return rr(f.toString(),t,n,r)}return n===0?u:"".concat(u).concat(t).concat(s.padEnd(n,"0").slice(0,n))}return l===".0"?u:"".concat(u).concat(l)}function Em(e,t){return typeof Proxy<"u"&&e?new Proxy(e,{get:function(r,o){if(t[o])return t[o];var i=r[o];return typeof i=="function"?i.bind(r):i}}):e}function Om(e,t){var n=d.useRef(null);function r(){try{var i=e.selectionStart,a=e.selectionEnd,s=e.value,l=s.substring(0,i),u=s.substring(a);n.current={start:i,end:a,value:s,beforeTxt:l,afterTxt:u}}catch{}}function o(){if(e&&n.current&&t)try{var i=e.value,a=n.current,s=a.beforeTxt,l=a.afterTxt,u=a.start,c=i.length;if(i.startsWith(s))c=s.length;else if(i.endsWith(l))c=i.length-n.current.afterTxt.length;else{var f=s[u-1],m=i.indexOf(f,u-1);m!==-1&&(c=m+1)}e.setSelectionRange(c,c)}catch(h){lr(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(h.message))}}return[r,o]}var $m=function(){var t=d.useState(!1),n=ce(t,2),r=n[0],o=n[1];return tn(function(){o(hf())},[]),r},Pm=200,Tm=600;function Rm(e){var t=e.prefixCls,n=e.upNode,r=e.downNode,o=e.upDisabled,i=e.downDisabled,a=e.onStep,s=d.useRef(),l=d.useRef([]),u=d.useRef();u.current=a;var c=function(){clearTimeout(s.current)},f=function(C,x){C.preventDefault(),c(),u.current(x);function S(){u.current(x),s.current=setTimeout(S,Pm)}s.current=setTimeout(S,Tm)};d.useEffect(function(){return function(){c(),l.current.forEach(function(p){return wt.cancel(p)})}},[]);var m=$m();if(m)return null;var h="".concat(t,"-handler"),b=k(h,"".concat(h,"-up"),se({},"".concat(h,"-up-disabled"),o)),g=k(h,"".concat(h,"-down"),se({},"".concat(h,"-down-disabled"),i)),v=function(){return l.current.push(wt(c))},y={unselectable:"on",role:"button",onMouseUp:v,onMouseLeave:v};return d.createElement("div",{className:"".concat(h,"-wrap")},d.createElement("span",Ce({},y,{onMouseDown:function(C){f(C,!0)},"aria-label":"Increase Value","aria-disabled":o,className:b}),n||d.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),d.createElement("span",Ce({},y,{onMouseDown:function(C){f(C,!1)},"aria-label":"Decrease Value","aria-disabled":i,className:g}),r||d.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function ba(e){var t=typeof e=="number"?wr(e):Zt(e).fullStr,n=t.includes(".");return n?Zt(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}const Mm=function(){var e=d.useRef(0),t=function(){wt.cancel(e.current)};return d.useEffect(function(){return t},[]),function(n){t(),e.current=wt(function(){n()})}};var Nm=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],jm=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],ya=function(t,n){return t||n.isEmpty()?n.toString():n.toNumber()},Ca=function(t){var n=ot(t);return n.isInvalidate()?null:n},Dm=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.className,o=e.style,i=e.min,a=e.max,s=e.step,l=s===void 0?1:s,u=e.defaultValue,c=e.value,f=e.disabled,m=e.readOnly,h=e.upHandler,b=e.downHandler,g=e.keyboard,v=e.changeOnWheel,y=v===void 0?!1:v,p=e.controls,C=p===void 0?!0:p;e.classNames;var x=e.stringMode,S=e.parser,w=e.formatter,E=e.precision,O=e.decimalSeparator,T=e.onChange,P=e.onInput,N=e.onPressEnter,D=e.onStep,j=e.changeOnBlur,M=j===void 0?!0:j,I=e.domRef,L=bn(e,Nm),R="".concat(n,"-input"),F=d.useRef(null),_=d.useState(!1),Q=ce(_,2),z=Q[0],G=Q[1],Y=d.useRef(!1),J=d.useRef(!1),U=d.useRef(!1),H=d.useState(function(){return ot(c??u)}),W=ce(H,2),B=W[0],K=W[1];function re(X){c===void 0&&K(X)}var q=d.useCallback(function(X,Z){if(!Z)return E>=0?E:Math.max(Xt(X),Xt(l))},[E,l]),oe=d.useCallback(function(X){var Z=String(X);if(S)return S(Z);var fe=Z;return O&&(fe=fe.replace(O,".")),fe.replace(/[^\w.-]+/g,"")},[S,O]),ue=d.useRef(""),Se=d.useCallback(function(X,Z){if(w)return w(X,{userTyping:Z,input:String(ue.current)});var fe=typeof X=="number"?wr(X):X;if(!Z){var ae=q(fe,Z);if(ai(fe)&&(O||ae>=0)){var Je=O||".";fe=rr(fe,Je,ae)}}return fe},[w,q,O]),le=d.useState(function(){var X=u??c;return B.isInvalidate()&&["string","number"].includes(gn(X))?Number.isNaN(X)?"":X:Se(B.toString(),!1)}),pe=ce(le,2),he=pe[0],je=pe[1];ue.current=he;function we(X,Z){je(Se(X.isInvalidate()?X.toString(!1):X.toString(!Z),Z))}var De=d.useMemo(function(){return Ca(a)},[a,E]),ie=d.useMemo(function(){return Ca(i)},[i,E]),me=d.useMemo(function(){return!De||!B||B.isInvalidate()?!1:De.lessEquals(B)},[De,B]),xe=d.useMemo(function(){return!ie||!B||B.isInvalidate()?!1:B.lessEquals(ie)},[ie,B]),de=Om(F.current,z),ve=ce(de,2),Ve=ve[0],qe=ve[1],ke=function(Z){return De&&!Z.lessEquals(De)?De:ie&&!ie.lessEquals(Z)?ie:null},Ie=function(Z){return!ke(Z)},_e=function(Z,fe){var ae=Z,Je=Ie(ae)||ae.isEmpty();if(!ae.isEmpty()&&!fe&&(ae=ke(ae)||ae,Je=!0),!m&&!f&&Je){var Ft=ae.toString(),bt=q(Ft,fe);return bt>=0&&(ae=ot(rr(Ft,".",bt)),Ie(ae)||(ae=ot(rr(Ft,".",bt,!0)))),ae.equals(B)||(re(ae),T==null||T(ae.isEmpty()?null:ya(x,ae)),c===void 0&&we(ae,fe)),ae}return B},Xe=Mm(),Me=function X(Z){if(Ve(),ue.current=Z,je(Z),!J.current){var fe=oe(Z),ae=ot(fe);ae.isNaN()||_e(ae,!0)}P==null||P(Z),Xe(function(){var Je=Z;S||(Je=Z.replace(/。/g,".")),Je!==Z&&X(Je)})},Be=function(){J.current=!0},Lt=function(){J.current=!1,Me(F.current.value)},vt=function(Z){Me(Z.target.value)},be=function(Z){var fe;if(!(Z&&me||!Z&&xe)){Y.current=!1;var ae=ot(U.current?ba(l):l);Z||(ae=ae.negate());var Je=(B||ot(0)).add(ae.toString()),Ft=_e(Je,!1);D==null||D(ya(x,Ft),{offset:U.current?ba(l):l,type:Z?"up":"down"}),(fe=F.current)===null||fe===void 0||fe.focus()}},Ae=function(Z){var fe=ot(oe(he)),ae;fe.isNaN()?ae=_e(B,Z):ae=_e(fe,Z),c!==void 0?we(B,!1):ae.isNaN()||we(ae,!1)},Ke=function(){Y.current=!0},Kn=function(Z){var fe=Z.key,ae=Z.shiftKey;Y.current=!0,U.current=ae,fe==="Enter"&&(J.current||(Y.current=!1),Ae(!1),N==null||N(Z)),g!==!1&&!J.current&&["Up","ArrowUp","Down","ArrowDown"].includes(fe)&&(be(fe==="Up"||fe==="ArrowUp"),Z.preventDefault())},lt=function(){Y.current=!1,U.current=!1};d.useEffect(function(){if(y&&z){var X=function(ae){be(ae.deltaY<0),ae.preventDefault()},Z=F.current;if(Z)return Z.addEventListener("wheel",X,{passive:!1}),function(){return Z.removeEventListener("wheel",X)}}});var Tr=function(){M&&Ae(!1),G(!1),Y.current=!1};return Nr(function(){B.isInvalidate()||we(B,!1)},[E,w]),Nr(function(){var X=ot(c);K(X);var Z=ot(oe(he));(!X.equals(Z)||!Y.current||w)&&we(X,Y.current)},[c]),Nr(function(){w&&qe()},[he]),d.createElement("div",{ref:I,className:k(n,r,se(se(se(se(se({},"".concat(n,"-focused"),z),"".concat(n,"-disabled"),f),"".concat(n,"-readonly"),m),"".concat(n,"-not-a-number"),B.isNaN()),"".concat(n,"-out-of-range"),!B.isInvalidate()&&!Ie(B))),style:o,onFocus:function(){G(!0)},onBlur:Tr,onKeyDown:Kn,onKeyUp:lt,onCompositionStart:Be,onCompositionEnd:Lt,onBeforeInput:Ke},C&&d.createElement(Rm,{prefixCls:n,upNode:h,downNode:b,upDisabled:me,downDisabled:xe,onStep:be}),d.createElement("div",{className:"".concat(R,"-wrap")},d.createElement("input",Ce({autoComplete:"off",role:"spinbutton","aria-valuemin":i,"aria-valuemax":a,"aria-valuenow":B.isInvalidate()?null:B.toString(),step:l},L,{ref:_n(F,t),className:R,value:he,onChange:vt,disabled:f,readOnly:m}))))}),Im=d.forwardRef(function(e,t){var n=e.disabled,r=e.style,o=e.prefixCls,i=o===void 0?"rc-input-number":o,a=e.value,s=e.prefix,l=e.suffix,u=e.addonBefore,c=e.addonAfter,f=e.className,m=e.classNames,h=bn(e,jm),b=d.useRef(null),g=d.useRef(null),v=d.useRef(null),y=function(C){v.current&&gf(v.current,C)};return d.useImperativeHandle(t,function(){return Em(v.current,{focus:y,nativeElement:b.current.nativeElement||g.current})}),d.createElement(mf,{className:f,triggerFocus:y,prefixCls:i,value:a,disabled:n,style:r,prefix:s,suffix:l,addonAfter:c,addonBefore:u,classNames:m,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:b},d.createElement(Dm,Ce({prefixCls:i,disabled:n,ref:v,domRef:g,className:m==null?void 0:m.input},h)))});const Am=e=>{var t;const n=(t=e.handleVisible)!==null&&t!==void 0?t:"auto",r=e.controlHeightSM-e.lineWidth*2;return Object.assign(Object.assign({},pf(e)),{controlWidth:90,handleWidth:r,handleFontSize:e.fontSize/2,handleVisible:n,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new ho(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:n===!0?1:0,handleVisibleWidth:n===!0?r:0})},xa=(e,t)=>{let{componentCls:n,borderRadiusSM:r,borderRadiusLG:o}=e;const i=t==="lg"?o:r;return{[`&-${t}`]:{[`${n}-handler-wrap`]:{borderStartEndRadius:i,borderEndEndRadius:i},[`${n}-handler-up`]:{borderStartEndRadius:i},[`${n}-handler-down`]:{borderEndEndRadius:i}}}},Vm=e=>{const{componentCls:t,lineWidth:n,lineType:r,borderRadius:o,inputFontSizeSM:i,inputFontSizeLG:a,controlHeightLG:s,controlHeightSM:l,colorError:u,paddingInlineSM:c,paddingBlockSM:f,paddingBlockLG:m,paddingInlineLG:h,colorTextDescription:b,motionDurationMid:g,handleHoverColor:v,handleOpacity:y,paddingInline:p,paddingBlock:C,handleBg:x,handleActiveBg:S,colorTextDisabled:w,borderRadiusSM:E,borderRadiusLG:O,controlWidth:T,handleBorderColor:P,filledHandleBg:N,lineHeightLG:D,calc:j}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},dt(e)),Ml(e)),{display:"inline-block",width:T,margin:0,padding:0,borderRadius:o}),bf(e,{[`${t}-handler-wrap`]:{background:x,[`${t}-handler-down`]:{borderBlockStart:`${A(n)} ${r} ${P}`}}})),yf(e,{[`${t}-handler-wrap`]:{background:N,[`${t}-handler-down`]:{borderBlockStart:`${A(n)} ${r} ${P}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:x}}})),Cf(e,{[`${t}-handler-wrap`]:{background:x,[`${t}-handler-down`]:{borderBlockStart:`${A(n)} ${r} ${P}`}}})),xf(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:a,lineHeight:D,borderRadius:O,[`input${t}-input`]:{height:j(s).sub(j(n).mul(2)).equal(),padding:`${A(m)} ${A(h)}`}},"&-sm":{padding:0,fontSize:i,borderRadius:E,[`input${t}-input`]:{height:j(l).sub(j(n).mul(2)).equal(),padding:`${A(f)} ${A(c)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:u}}},"&-group":Object.assign(Object.assign(Object.assign({},dt(e)),wf(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:O,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:E}}},Ef(e)),Of(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},dt(e)),{width:"100%",padding:`${A(C)} ${A(p)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:o,outline:0,transition:`all ${g} linear`,appearance:"textfield",fontSize:"inherit"}),Sf(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,webkitAppearance:"none",appearance:"none"}})},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[t]:Object.assign(Object.assign(Object.assign({[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:y,height:"100%",borderStartStartRadius:0,borderStartEndRadius:o,borderEndEndRadius:o,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${g}`,overflow:"hidden",[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:b,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${A(n)} ${r} ${P}`,transition:`all ${g} linear`,"&:active":{background:S},"&:hover":{height:"60%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{color:v}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},Xd()),{color:b,transition:`all ${g} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:o},[`${t}-handler-down`]:{borderEndEndRadius:o}},xa(e,"lg")),xa(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`
          ${t}-handler-up-disabled,
          ${t}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${t}-handler-up-disabled:hover &-handler-up-inner,
          ${t}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:w}})}]},km=e=>{const{componentCls:t,paddingBlock:n,paddingInline:r,inputAffixPadding:o,controlWidth:i,borderRadiusLG:a,borderRadiusSM:s,paddingInlineLG:l,paddingInlineSM:u,paddingBlockLG:c,paddingBlockSM:f,motionDurationMid:m}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${A(n)} 0`}},Ml(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:i,padding:0,paddingInlineStart:r,"&-lg":{borderRadius:a,paddingInlineStart:l,[`input${t}-input`]:{padding:`${A(c)} 0`}},"&-sm":{borderRadius:s,paddingInlineStart:u,[`input${t}-input`]:{padding:`${A(f)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:o},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:r,marginInlineStart:o,transition:`margin ${m}`}},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${t}-affix-wrapper-without-controls):hover ${t}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(r).equal()}})}},Bm=gt("InputNumber",e=>{const t=pt(e,vf(e));return[Vm(t),km(t),Rl(t)]},Am,{unitless:{handleOpacity:!0}});var Lm=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Tc=d.forwardRef((e,t)=>{const{getPrefixCls:n,direction:r}=d.useContext(Ue),o=d.useRef(null);d.useImperativeHandle(t,()=>o.current);const{className:i,rootClassName:a,size:s,disabled:l,prefixCls:u,addonBefore:c,addonAfter:f,prefix:m,suffix:h,bordered:b,readOnly:g,status:v,controls:y,variant:p}=e,C=Lm(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),x=n("input-number",u),S=yn(x),[w,E,O]=Bm(x,S),{compactSize:T,compactItemClassnames:P}=Nl(x,r);let N=d.createElement(xm,{className:`${x}-handler-up-inner`}),D=d.createElement(Vl,{className:`${x}-handler-down-inner`});const j=typeof y=="boolean"?y:void 0;typeof y=="object"&&(N=typeof y.upIcon>"u"?N:d.createElement("span",{className:`${x}-handler-up-inner`},y.upIcon),D=typeof y.downIcon>"u"?D:d.createElement("span",{className:`${x}-handler-down-inner`},y.downIcon));const{hasFeedback:M,status:I,isFormItemInput:L,feedbackIcon:R}=d.useContext(jl),F=Pf(I,v),_=xr(B=>{var K;return(K=s??T)!==null&&K!==void 0?K:B}),Q=d.useContext(Jo),z=l??Q,[G,Y]=$f("inputNumber",p,b),J=M&&d.createElement(d.Fragment,null,R),U=k({[`${x}-lg`]:_==="large",[`${x}-sm`]:_==="small",[`${x}-rtl`]:r==="rtl",[`${x}-in-form-item`]:L},E),H=`${x}-group`,W=d.createElement(Im,Object.assign({ref:o,disabled:z,className:k(O,S,i,a,P),upHandler:N,downHandler:D,prefixCls:x,readOnly:g,controls:j,prefix:m,suffix:J||h,addonBefore:c&&d.createElement(Dn,{form:!0,space:!0},c),addonAfter:f&&d.createElement(Dn,{form:!0,space:!0},f),classNames:{input:U,variant:k({[`${x}-${G}`]:Y},mo(x,F,M)),affixWrapper:k({[`${x}-affix-wrapper-sm`]:_==="small",[`${x}-affix-wrapper-lg`]:_==="large",[`${x}-affix-wrapper-rtl`]:r==="rtl",[`${x}-affix-wrapper-without-controls`]:y===!1},E),wrapper:k({[`${H}-rtl`]:r==="rtl"},E),groupWrapper:k({[`${x}-group-wrapper-sm`]:_==="small",[`${x}-group-wrapper-lg`]:_==="large",[`${x}-group-wrapper-rtl`]:r==="rtl",[`${x}-group-wrapper-${G}`]:Y},mo(`${x}-group-wrapper`,F,M),E)}},C));return w(W)}),mr=Tc,Fm=e=>d.createElement(Qo,{theme:{components:{InputNumber:{handleVisible:!0}}}},d.createElement(Tc,Object.assign({},e)));mr._InternalPanelDoNotUseOrYouWillBeFired=Fm;const Qt=e=>{let{prefixCls:t,min:n=0,max:r=100,value:o,onChange:i,className:a,formatter:s}=e;const l=`${t}-steppers`,[u,c]=d.useState(o);return d.useEffect(()=>{Number.isNaN(o)||c(o)},[o]),$.createElement(mr,{className:k(l,a),min:n,max:r,value:u,formatter:s,size:"small",onChange:f=>{o||c(f||0),i==null||i(f)}})},_m=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-alpha-input`,[i,a]=d.useState(rt(n||"#000"));d.useEffect(()=>{n&&a(n)},[n]);const s=l=>{const u=i.toHsb();u.a=(l||0)/100;const c=rt(u);n||a(c),r==null||r(c)};return $.createElement(Qt,{value:ni(i),prefixCls:t,formatter:l=>`${l}%`,className:o,onChange:s})},zm=/(^#[\da-f]{6}$)|(^#[\da-f]{8}$)/i,Hm=e=>zm.test(`#${e}`),Wm=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-hex-input`,[i,a]=d.useState(()=>n?Qn(n.toHexString()):void 0);d.useEffect(()=>{n&&a(Qn(n.toHexString()))},[n]);const s=l=>{const u=l.target.value;a(Qn(u)),Hm(Qn(u,!0))&&(r==null||r(rt(u)))};return $.createElement(Tf,{className:o,value:i,prefix:"#",onChange:s,size:"small"})},qm=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-hsb-input`,[i,a]=d.useState(rt(n||"#000"));d.useEffect(()=>{n&&a(n)},[n]);const s=(l,u)=>{const c=i.toHsb();c[u]=u==="h"?l:(l||0)/100;const f=rt(c);n||a(f),r==null||r(f)};return $.createElement("div",{className:o},$.createElement(Qt,{max:360,min:0,value:Number(i.toHsb().h),prefixCls:t,className:o,formatter:l=>jr(l||0).toString(),onChange:l=>s(Number(l),"h")}),$.createElement(Qt,{max:100,min:0,value:Number(i.toHsb().s)*100,prefixCls:t,className:o,formatter:l=>`${jr(l||0)}%`,onChange:l=>s(Number(l),"s")}),$.createElement(Qt,{max:100,min:0,value:Number(i.toHsb().b)*100,prefixCls:t,className:o,formatter:l=>`${jr(l||0)}%`,onChange:l=>s(Number(l),"b")}))},Gm=e=>{let{prefixCls:t,value:n,onChange:r}=e;const o=`${t}-rgb-input`,[i,a]=d.useState(rt(n||"#000"));d.useEffect(()=>{n&&a(n)},[n]);const s=(l,u)=>{const c=i.toRgb();c[u]=l||0;const f=rt(c);n||a(f),r==null||r(f)};return $.createElement("div",{className:o},$.createElement(Qt,{max:255,min:0,value:Number(i.toRgb().r),prefixCls:t,className:o,onChange:l=>s(Number(l),"r")}),$.createElement(Qt,{max:255,min:0,value:Number(i.toRgb().g),prefixCls:t,className:o,onChange:l=>s(Number(l),"g")}),$.createElement(Qt,{max:255,min:0,value:Number(i.toRgb().b),prefixCls:t,className:o,onChange:l=>s(Number(l),"b")}))},Um=[Ec,$c,Oc].map(e=>({value:e,label:e.toUpperCase()})),Xm=e=>{const{prefixCls:t,format:n,value:r,disabledAlpha:o,onFormatChange:i,onChange:a,disabledFormat:s}=e,[l,u]=Dt(Ec,{value:n,onChange:i}),c=`${t}-input`,f=h=>{u(h)},m=d.useMemo(()=>{const h={value:r,prefixCls:t,onChange:a};switch(l){case $c:return $.createElement(qm,Object.assign({},h));case Oc:return $.createElement(Gm,Object.assign({},h));default:return $.createElement(Wm,Object.assign({},h))}},[l,t,r,a]);return $.createElement("div",{className:`${c}-container`},!s&&$.createElement(go,{value:l,variant:"borderless",getPopupContainer:h=>h,popupMatchSelectWidth:68,placement:"bottomRight",onChange:f,className:`${t}-format-select`,size:"small",options:Um}),$.createElement("div",{className:c},m),!o&&$.createElement(_m,{prefixCls:t,value:r,onChange:a}))};function xo(e,t,n){return(e-t)/(n-t)}function si(e,t,n,r){var o=xo(t,n,r),i={};switch(e){case"rtl":i.right="".concat(o*100,"%"),i.transform="translateX(50%)";break;case"btt":i.bottom="".concat(o*100,"%"),i.transform="translateY(50%)";break;case"ttb":i.top="".concat(o*100,"%"),i.transform="translateY(-50%)";break;default:i.left="".concat(o*100,"%"),i.transform="translateX(-50%)";break}return i}function qt(e,t){return Array.isArray(e)?e[t]:e}var an=d.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),Rc=d.createContext({}),Km=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],Sa=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.value,o=e.valueIndex,i=e.onStartMove,a=e.onDelete,s=e.style,l=e.render,u=e.dragging,c=e.draggingDelete,f=e.onOffsetChange,m=e.onChangeComplete,h=e.onFocus,b=e.onMouseEnter,g=bn(e,Km),v=d.useContext(an),y=v.min,p=v.max,C=v.direction,x=v.disabled,S=v.keyboard,w=v.range,E=v.tabIndex,O=v.ariaLabelForHandle,T=v.ariaLabelledByForHandle,P=v.ariaRequired,N=v.ariaValueTextFormatterForHandle,D=v.styles,j=v.classNames,M="".concat(n,"-handle"),I=function(U){x||i(U,o)},L=function(U){h==null||h(U,o)},R=function(U){b(U,o)},F=function(U){if(!x&&S){var H=null;switch(U.which||U.keyCode){case Ee.LEFT:H=C==="ltr"||C==="btt"?-1:1;break;case Ee.RIGHT:H=C==="ltr"||C==="btt"?1:-1;break;case Ee.UP:H=C!=="ttb"?1:-1;break;case Ee.DOWN:H=C!=="ttb"?-1:1;break;case Ee.HOME:H="min";break;case Ee.END:H="max";break;case Ee.PAGE_UP:H=2;break;case Ee.PAGE_DOWN:H=-2;break;case Ee.BACKSPACE:case Ee.DELETE:a(o);break}H!==null&&(U.preventDefault(),f(H,o))}},_=function(U){switch(U.which||U.keyCode){case Ee.LEFT:case Ee.RIGHT:case Ee.UP:case Ee.DOWN:case Ee.HOME:case Ee.END:case Ee.PAGE_UP:case Ee.PAGE_DOWN:m==null||m();break}},Q=si(C,r,y,p),z={};if(o!==null){var G;z={tabIndex:x?null:qt(E,o),role:"slider","aria-valuemin":y,"aria-valuemax":p,"aria-valuenow":r,"aria-disabled":x,"aria-label":qt(O,o),"aria-labelledby":qt(T,o),"aria-required":qt(P,o),"aria-valuetext":(G=qt(N,o))===null||G===void 0?void 0:G(r),"aria-orientation":C==="ltr"||C==="rtl"?"horizontal":"vertical",onMouseDown:I,onTouchStart:I,onFocus:L,onMouseEnter:R,onKeyDown:F,onKeyUp:_}}var Y=d.createElement("div",Ce({ref:t,className:k(M,se(se(se({},"".concat(M,"-").concat(o+1),o!==null&&w),"".concat(M,"-dragging"),u),"".concat(M,"-dragging-delete"),c),j.handle),style:ne(ne(ne({},Q),s),D.handle)},z,g));return l&&(Y=l(Y,{index:o,prefixCls:n,value:r,dragging:u,draggingDelete:c})),Y}),Ym=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],Zm=d.forwardRef(function(e,t){var n=e.prefixCls,r=e.style,o=e.onStartMove,i=e.onOffsetChange,a=e.values,s=e.handleRender,l=e.activeHandleRender,u=e.draggingIndex,c=e.draggingDelete,f=e.onFocus,m=bn(e,Ym),h=d.useRef({}),b=d.useState(!1),g=ce(b,2),v=g[0],y=g[1],p=d.useState(-1),C=ce(p,2),x=C[0],S=C[1],w=function(N){S(N),y(!0)},E=function(N,D){w(D),f==null||f(N)},O=function(N,D){w(D)};d.useImperativeHandle(t,function(){return{focus:function(N){var D;(D=h.current[N])===null||D===void 0||D.focus()},hideHelp:function(){Kd.flushSync(function(){y(!1)})}}});var T=ne({prefixCls:n,onStartMove:o,onOffsetChange:i,render:s,onFocus:E,onMouseEnter:O},m);return d.createElement(d.Fragment,null,a.map(function(P,N){var D=u===N;return d.createElement(Sa,Ce({ref:function(M){M?h.current[N]=M:delete h.current[N]},dragging:D,draggingDelete:D&&c,style:qt(r,N),key:N,value:P,valueIndex:N},T))}),l&&v&&d.createElement(Sa,Ce({key:"a11y"},T,{value:a[x],valueIndex:null,dragging:u!==-1,draggingDelete:c,render:l,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))}),Qm=function(t){var n=t.prefixCls,r=t.style,o=t.children,i=t.value,a=t.onClick,s=d.useContext(an),l=s.min,u=s.max,c=s.direction,f=s.includedStart,m=s.includedEnd,h=s.included,b="".concat(n,"-text"),g=si(c,i,l,u);return d.createElement("span",{className:k(b,se({},"".concat(b,"-active"),h&&f<=i&&i<=m)),style:ne(ne({},g),r),onMouseDown:function(y){y.stopPropagation()},onClick:function(){a(i)}},o)},Jm=function(t){var n=t.prefixCls,r=t.marks,o=t.onClick,i="".concat(n,"-mark");return r.length?d.createElement("div",{className:i},r.map(function(a){var s=a.value,l=a.style,u=a.label;return d.createElement(Qm,{key:s,prefixCls:i,style:l,value:s,onClick:o},u)})):null},eg=function(t){var n=t.prefixCls,r=t.value,o=t.style,i=t.activeStyle,a=d.useContext(an),s=a.min,l=a.max,u=a.direction,c=a.included,f=a.includedStart,m=a.includedEnd,h="".concat(n,"-dot"),b=c&&f<=r&&r<=m,g=ne(ne({},si(u,r,s,l)),typeof o=="function"?o(r):o);return b&&(g=ne(ne({},g),typeof i=="function"?i(r):i)),d.createElement("span",{className:k(h,se({},"".concat(h,"-active"),b)),style:g})},tg=function(t){var n=t.prefixCls,r=t.marks,o=t.dots,i=t.style,a=t.activeStyle,s=d.useContext(an),l=s.min,u=s.max,c=s.step,f=d.useMemo(function(){var m=new Set;if(r.forEach(function(b){m.add(b.value)}),o&&c!==null)for(var h=l;h<=u;)m.add(h),h+=c;return Array.from(m)},[l,u,c,o,r]);return d.createElement("div",{className:"".concat(n,"-step")},f.map(function(m){return d.createElement(eg,{prefixCls:n,key:m,value:m,style:i,activeStyle:a})}))},wa=function(t){var n=t.prefixCls,r=t.style,o=t.start,i=t.end,a=t.index,s=t.onStartMove,l=t.replaceCls,u=d.useContext(an),c=u.direction,f=u.min,m=u.max,h=u.disabled,b=u.range,g=u.classNames,v="".concat(n,"-track"),y=xo(o,f,m),p=xo(i,f,m),C=function(E){!h&&s&&s(E,-1)},x={};switch(c){case"rtl":x.right="".concat(y*100,"%"),x.width="".concat(p*100-y*100,"%");break;case"btt":x.bottom="".concat(y*100,"%"),x.height="".concat(p*100-y*100,"%");break;case"ttb":x.top="".concat(y*100,"%"),x.height="".concat(p*100-y*100,"%");break;default:x.left="".concat(y*100,"%"),x.width="".concat(p*100-y*100,"%")}var S=l||k(v,se(se({},"".concat(v,"-").concat(a+1),a!==null&&b),"".concat(n,"-track-draggable"),s),g.track);return d.createElement("div",{className:S,style:ne(ne({},x),r),onMouseDown:C,onTouchStart:C})},ng=function(t){var n=t.prefixCls,r=t.style,o=t.values,i=t.startPoint,a=t.onStartMove,s=d.useContext(an),l=s.included,u=s.range,c=s.min,f=s.styles,m=s.classNames,h=d.useMemo(function(){if(!u){if(o.length===0)return[];var g=i??c,v=o[0];return[{start:Math.min(g,v),end:Math.max(g,v)}]}for(var y=[],p=0;p<o.length-1;p+=1)y.push({start:o[p],end:o[p+1]});return y},[o,u,i,c]);if(!l)return null;var b=h!=null&&h.length&&(m.tracks||f.tracks)?d.createElement(wa,{index:null,prefixCls:n,start:h[0].start,end:h[h.length-1].end,replaceCls:k(m.tracks,"".concat(n,"-tracks")),style:f.tracks}):null;return d.createElement(d.Fragment,null,b,h.map(function(g,v){var y=g.start,p=g.end;return d.createElement(wa,{index:v,prefixCls:n,style:ne(ne({},qt(r,v)),f.track),start:y,end:p,key:v,onStartMove:a})}))},rg=130;function Ea(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}function og(e,t,n,r,o,i,a,s,l,u,c){var f=d.useState(null),m=ce(f,2),h=m[0],b=m[1],g=d.useState(-1),v=ce(g,2),y=v[0],p=v[1],C=d.useState(!1),x=ce(C,2),S=x[0],w=x[1],E=d.useState(n),O=ce(E,2),T=O[0],P=O[1],N=d.useState(n),D=ce(N,2),j=D[0],M=D[1],I=d.useRef(null),L=d.useRef(null),R=d.useRef(null),F=d.useContext(Rc),_=F.onDragStart,Q=F.onDragChange;tn(function(){y===-1&&P(n)},[n,y]),d.useEffect(function(){return function(){document.removeEventListener("mousemove",I.current),document.removeEventListener("mouseup",L.current),R.current&&(R.current.removeEventListener("touchmove",I.current),R.current.removeEventListener("touchend",L.current))}},[]);var z=function(H,W,B){W!==void 0&&b(W),P(H);var K=H;B&&(K=H.filter(function(re,q){return q!==y})),a(K),Q&&Q({rawValues:H,deleteIndex:B?y:-1,draggingIndex:y,draggingValue:W})},G=at(function(U,H,W){if(U===-1){var B=j[0],K=j[j.length-1],re=r-B,q=o-K,oe=H*(o-r);oe=Math.max(oe,re),oe=Math.min(oe,q);var ue=i(B+oe);oe=ue-B;var Se=j.map(function(je){return je+oe});z(Se)}else{var le=(o-r)*H,pe=Oe(T);pe[U]=j[U];var he=l(pe,le,U,"dist");z(he.values,he.value,W)}}),Y=function(H,W,B){H.stopPropagation();var K=B||n,re=K[W];p(W),b(re),M(K),P(K),w(!1);var q=Ea(H),oe=q.pageX,ue=q.pageY,Se=!1;_&&_({rawValues:K,draggingIndex:W,draggingValue:re});var le=function(je){je.preventDefault();var we=Ea(je),De=we.pageX,ie=we.pageY,me=De-oe,xe=ie-ue,de=e.current.getBoundingClientRect(),ve=de.width,Ve=de.height,qe,ke;switch(t){case"btt":qe=-xe/Ve,ke=me;break;case"ttb":qe=xe/Ve,ke=me;break;case"rtl":qe=-me/ve,ke=xe;break;default:qe=me/ve,ke=xe}Se=u?Math.abs(ke)>rg&&c<T.length:!1,w(Se),G(W,qe,Se)},pe=function he(je){je.preventDefault(),document.removeEventListener("mouseup",he),document.removeEventListener("mousemove",le),R.current&&(R.current.removeEventListener("touchmove",I.current),R.current.removeEventListener("touchend",L.current)),I.current=null,L.current=null,R.current=null,s(Se),p(-1),w(!1)};document.addEventListener("mouseup",pe),document.addEventListener("mousemove",le),H.currentTarget.addEventListener("touchend",pe),H.currentTarget.addEventListener("touchmove",le),I.current=le,L.current=pe,R.current=H.currentTarget},J=d.useMemo(function(){var U=Oe(n).sort(function(re,q){return re-q}),H=Oe(T).sort(function(re,q){return re-q}),W={};H.forEach(function(re){W[re]=(W[re]||0)+1}),U.forEach(function(re){W[re]=(W[re]||0)-1});var B=u?1:0,K=Object.values(W).reduce(function(re,q){return re+Math.abs(q)},0);return K<=B?T:n},[n,T,u]);return[y,h,S,J,Y]}function ig(e,t,n,r,o,i){var a=d.useCallback(function(h){return Math.max(e,Math.min(t,h))},[e,t]),s=d.useCallback(function(h){if(n!==null){var b=e+Math.round((a(h)-e)/n)*n,g=function(C){return(String(C).split(".")[1]||"").length},v=Math.max(g(n),g(t),g(e)),y=Number(b.toFixed(v));return e<=y&&y<=t?y:null}return null},[n,e,t,a]),l=d.useCallback(function(h){var b=a(h),g=r.map(function(p){return p.value});n!==null&&g.push(s(h)),g.push(e,t);var v=g[0],y=t-e;return g.forEach(function(p){var C=Math.abs(b-p);C<=y&&(v=p,y=C)}),v},[e,t,r,n,a,s]),u=function h(b,g,v){var y=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit";if(typeof g=="number"){var p,C=b[v],x=C+g,S=[];r.forEach(function(P){S.push(P.value)}),S.push(e,t),S.push(s(C));var w=g>0?1:-1;y==="unit"?S.push(s(C+w*n)):S.push(s(x)),S=S.filter(function(P){return P!==null}).filter(function(P){return g<0?P<=C:P>=C}),y==="unit"&&(S=S.filter(function(P){return P!==C}));var E=y==="unit"?C:x;p=S[0];var O=Math.abs(p-E);if(S.forEach(function(P){var N=Math.abs(P-E);N<O&&(p=P,O=N)}),p===void 0)return g<0?e:t;if(y==="dist")return p;if(Math.abs(g)>1){var T=Oe(b);return T[v]=p,h(T,g-w,v,y)}return p}else{if(g==="min")return e;if(g==="max")return t}},c=function(b,g,v){var y=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",p=b[v],C=u(b,g,v,y);return{value:C,changed:C!==p}},f=function(b){return i===null&&b===0||typeof i=="number"&&b<i},m=function(b,g,v){var y=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"unit",p=b.map(l),C=p[v],x=u(p,g,v,y);if(p[v]=x,o===!1){var S=i||0;v>0&&p[v-1]!==C&&(p[v]=Math.max(p[v],p[v-1]+S)),v<p.length-1&&p[v+1]!==C&&(p[v]=Math.min(p[v],p[v+1]-S))}else if(typeof i=="number"||i===null){for(var w=v+1;w<p.length;w+=1)for(var E=!0;f(p[w]-p[w-1])&&E;){var O=c(p,1,w);p[w]=O.value,E=O.changed}for(var T=v;T>0;T-=1)for(var P=!0;f(p[T]-p[T-1])&&P;){var N=c(p,-1,T-1);p[T-1]=N.value,P=N.changed}for(var D=p.length-1;D>0;D-=1)for(var j=!0;f(p[D]-p[D-1])&&j;){var M=c(p,-1,D-1);p[D-1]=M.value,j=M.changed}for(var I=0;I<p.length-1;I+=1)for(var L=!0;f(p[I+1]-p[I])&&L;){var R=c(p,1,I+1);p[I+1]=R.value,L=R.changed}}return{value:p[v],values:p}};return[l,m]}function ag(e){return d.useMemo(function(){if(e===!0||!e)return[!!e,!1,!1,0];var t=e.editable,n=e.draggableTrack,r=e.minCount,o=e.maxCount;return[!0,t,!t&&n,r||0,o]},[e])}var sg=d.forwardRef(function(e,t){var n=e.prefixCls,r=n===void 0?"rc-slider":n,o=e.className,i=e.style,a=e.classNames,s=e.styles,l=e.id,u=e.disabled,c=u===void 0?!1:u,f=e.keyboard,m=f===void 0?!0:f,h=e.autoFocus,b=e.onFocus,g=e.onBlur,v=e.min,y=v===void 0?0:v,p=e.max,C=p===void 0?100:p,x=e.step,S=x===void 0?1:x,w=e.value,E=e.defaultValue,O=e.range,T=e.count,P=e.onChange,N=e.onBeforeChange,D=e.onAfterChange,j=e.onChangeComplete,M=e.allowCross,I=M===void 0?!0:M,L=e.pushable,R=L===void 0?!1:L,F=e.reverse,_=e.vertical,Q=e.included,z=Q===void 0?!0:Q,G=e.startPoint,Y=e.trackStyle,J=e.handleStyle,U=e.railStyle,H=e.dotStyle,W=e.activeDotStyle,B=e.marks,K=e.dots,re=e.handleRender,q=e.activeHandleRender,oe=e.track,ue=e.tabIndex,Se=ue===void 0?0:ue,le=e.ariaLabelForHandle,pe=e.ariaLabelledByForHandle,he=e.ariaRequired,je=e.ariaValueTextFormatterForHandle,we=d.useRef(null),De=d.useRef(null),ie=d.useMemo(function(){return _?F?"ttb":"btt":F?"rtl":"ltr"},[F,_]),me=ag(O),xe=ce(me,5),de=xe[0],ve=xe[1],Ve=xe[2],qe=xe[3],ke=xe[4],Ie=d.useMemo(function(){return isFinite(y)?y:0},[y]),_e=d.useMemo(function(){return isFinite(C)?C:100},[C]),Xe=d.useMemo(function(){return S!==null&&S<=0?1:S},[S]),Me=d.useMemo(function(){return typeof R=="boolean"?R?Xe:!1:R>=0?R:!1},[R,Xe]),Be=d.useMemo(function(){return Object.keys(B||{}).map(function(ge){var ee=B[ge],ye={value:Number(ge)};return ee&&gn(ee)==="object"&&!d.isValidElement(ee)&&("label"in ee||"style"in ee)?(ye.style=ee.style,ye.label=ee.label):ye.label=ee,ye}).filter(function(ge){var ee=ge.label;return ee||typeof ee=="number"}).sort(function(ge,ee){return ge.value-ee.value})},[B]),Lt=ig(Ie,_e,Xe,Be,I,Me),vt=ce(Lt,2),be=vt[0],Ae=vt[1],Ke=Dt(E,{value:w}),Kn=ce(Ke,2),lt=Kn[0],Tr=Kn[1],X=d.useMemo(function(){var ge=lt==null?[]:Array.isArray(lt)?lt:[lt],ee=ce(ge,1),ye=ee[0],$e=ye===void 0?Ie:ye,Le=lt===null?[]:[$e];if(de){if(Le=Oe(ge),T||lt===void 0){var Pt=T>=0?T+1:2;for(Le=Le.slice(0,Pt);Le.length<Pt;){var yt;Le.push((yt=Le[Le.length-1])!==null&&yt!==void 0?yt:Ie)}}Le.sort(function(Ct,xt){return Ct-xt})}return Le.forEach(function(Ct,xt){Le[xt]=be(Ct)}),Le},[lt,de,Ie,T,be]),Z=function(ee){return de?ee:ee[0]},fe=at(function(ge){var ee=Oe(ge).sort(function(ye,$e){return ye-$e});P&&!Yd(ee,X,!0)&&P(Z(ee)),Tr(ee)}),ae=at(function(ge){ge&&we.current.hideHelp();var ee=Z(X);D==null||D(ee),lr(!D,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),j==null||j(ee)}),Je=function(ee){if(!(c||!ve||X.length<=qe)){var ye=Oe(X);ye.splice(ee,1),N==null||N(Z(ye)),fe(ye);var $e=Math.max(0,ee-1);we.current.hideHelp(),we.current.focus($e)}},Ft=og(De,ie,X,Ie,_e,be,fe,ae,Ae,ve,qe),bt=ce(Ft,5),Hi=bt[0],Nd=bt[1],jd=bt[2],Rr=bt[3],Wi=bt[4],qi=function(ee,ye){if(!c){var $e=Oe(X),Le=0,Pt=0,yt=_e-Ie;X.forEach(function(Tt,Yn){var Ji=Math.abs(ee-Tt);Ji<=yt&&(yt=Ji,Le=Yn),Tt<ee&&(Pt=Yn)});var Ct=Le;ve&&yt!==0&&(!ke||X.length<ke)?($e.splice(Pt+1,0,ee),Ct=Pt+1):$e[Le]=ee,de&&!X.length&&T===void 0&&$e.push(ee);var xt=Z($e);if(N==null||N(xt),fe($e),ye){var _t,sn;(_t=document.activeElement)===null||_t===void 0||(sn=_t.blur)===null||sn===void 0||sn.call(_t),we.current.focus(Ct),Wi(ye,Ct,$e)}else D==null||D(xt),lr(!D,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),j==null||j(xt)}},Dd=function(ee){ee.preventDefault();var ye=De.current.getBoundingClientRect(),$e=ye.width,Le=ye.height,Pt=ye.left,yt=ye.top,Ct=ye.bottom,xt=ye.right,_t=ee.clientX,sn=ee.clientY,Tt;switch(ie){case"btt":Tt=(Ct-sn)/Le;break;case"ttb":Tt=(sn-yt)/Le;break;case"rtl":Tt=(xt-_t)/$e;break;default:Tt=(_t-Pt)/$e}var Yn=Ie+Tt*(_e-Ie);qi(be(Yn),ee)},Id=d.useState(null),Gi=ce(Id,2),Mr=Gi[0],Ui=Gi[1],Ad=function(ee,ye){if(!c){var $e=Ae(X,ee,ye);N==null||N(Z(X)),fe($e.values),Ui($e.value)}};d.useEffect(function(){if(Mr!==null){var ge=X.indexOf(Mr);ge>=0&&we.current.focus(ge)}Ui(null)},[Mr]);var Vd=d.useMemo(function(){return Ve&&Xe===null?!1:Ve},[Ve,Xe]),Xi=at(function(ge,ee){Wi(ge,ee),N==null||N(Z(X))}),Ki=Hi!==-1;d.useEffect(function(){if(!Ki){var ge=X.lastIndexOf(Nd);we.current.focus(ge)}},[Ki]);var wn=d.useMemo(function(){return Oe(Rr).sort(function(ge,ee){return ge-ee})},[Rr]),kd=d.useMemo(function(){return de?[wn[0],wn[wn.length-1]]:[Ie,wn[0]]},[wn,de,Ie]),Yi=ce(kd,2),Zi=Yi[0],Qi=Yi[1];d.useImperativeHandle(t,function(){return{focus:function(){we.current.focus(0)},blur:function(){var ee,ye=document,$e=ye.activeElement;(ee=De.current)!==null&&ee!==void 0&&ee.contains($e)&&($e==null||$e.blur())}}}),d.useEffect(function(){h&&we.current.focus(0)},[]);var Bd=d.useMemo(function(){return{min:Ie,max:_e,direction:ie,disabled:c,keyboard:m,step:Xe,included:z,includedStart:Zi,includedEnd:Qi,range:de,tabIndex:Se,ariaLabelForHandle:le,ariaLabelledByForHandle:pe,ariaRequired:he,ariaValueTextFormatterForHandle:je,styles:s||{},classNames:a||{}}},[Ie,_e,ie,c,m,Xe,z,Zi,Qi,de,Se,le,pe,he,je,s,a]);return d.createElement(an.Provider,{value:Bd},d.createElement("div",{ref:De,className:k(r,o,se(se(se(se({},"".concat(r,"-disabled"),c),"".concat(r,"-vertical"),_),"".concat(r,"-horizontal"),!_),"".concat(r,"-with-marks"),Be.length)),style:i,onMouseDown:Dd,id:l},d.createElement("div",{className:k("".concat(r,"-rail"),a==null?void 0:a.rail),style:ne(ne({},U),s==null?void 0:s.rail)}),oe!==!1&&d.createElement(ng,{prefixCls:r,style:Y,values:X,startPoint:G,onStartMove:Vd?Xi:void 0}),d.createElement(tg,{prefixCls:r,marks:Be,dots:K,style:H,activeStyle:W}),d.createElement(Zm,{ref:we,prefixCls:r,style:J,values:Rr,draggingIndex:Hi,draggingDelete:jd,onStartMove:Xi,onOffsetChange:Ad,onFocus:b,onBlur:g,handleRender:re,activeHandleRender:q,onChangeComplete:ae,onDelete:ve?Je:void 0}),d.createElement(Jm,{prefixCls:r,marks:Be,onClick:qi})))});const Mc=d.createContext({}),Oa=d.forwardRef((e,t)=>{const{open:n,draggingDelete:r}=e,o=d.useRef(null),i=n&&!r,a=d.useRef(null);function s(){wt.cancel(a.current),a.current=null}function l(){a.current=wt(()=>{var u;(u=o.current)===null||u===void 0||u.forceAlign(),a.current=null})}return d.useEffect(()=>(i?l():s(),s),[i,e.title]),d.createElement(dr,Object.assign({ref:_n(o,t)},e,{open:i}))}),lg=e=>{const{componentCls:t,antCls:n,controlSize:r,dotSize:o,marginFull:i,marginPart:a,colorFillContentHover:s,handleColorDisabled:l,calc:u,handleSize:c,handleSizeHover:f,handleActiveColor:m,handleActiveOutlineColor:h,handleLineWidth:b,handleLineWidthHover:g,motionDurationMid:v}=e;return{[t]:Object.assign(Object.assign({},dt(e)),{position:"relative",height:r,margin:`${A(a)} ${A(i)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${A(i)} ${A(a)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${v}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${v}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:s},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${A(b)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:c,height:c,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:u(b).mul(-1).equal(),insetBlockStart:u(b).mul(-1).equal(),width:u(c).add(u(b).mul(2)).equal(),height:u(c).add(u(b).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:c,height:c,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${A(b)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`
            inset-inline-start ${v},
            inset-block-start ${v},
            width ${v},
            height ${v},
            box-shadow ${v},
            outline ${v}
          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:u(f).sub(c).div(2).add(g).mul(-1).equal(),insetBlockStart:u(f).sub(c).div(2).add(g).mul(-1).equal(),width:u(f).add(u(g).mul(2)).equal(),height:u(f).add(u(g).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${A(g)} ${m}`,outline:`6px solid ${h}`,width:f,height:f,insetInlineStart:e.calc(c).sub(f).div(2).equal(),insetBlockStart:e.calc(c).sub(f).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:o,height:o,backgroundColor:e.colorBgElevated,border:`${A(b)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`
          ${t}-dot
        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:c,height:c,boxShadow:`0 0 0 ${A(b)} ${l}`,insetInlineStart:0,insetBlockStart:0},[`
          ${t}-mark-text,
          ${t}-dot
        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${n}-tooltip-inner`]:{minWidth:"unset"}})}},Nc=(e,t)=>{const{componentCls:n,railSize:r,handleSize:o,dotSize:i,marginFull:a,calc:s}=e,l=t?"paddingBlock":"paddingInline",u=t?"width":"height",c=t?"height":"width",f=t?"insetBlockStart":"insetInlineStart",m=t?"top":"insetInlineStart",h=s(r).mul(3).sub(o).div(2).equal(),b=s(o).sub(r).div(2).equal(),g=t?{borderWidth:`${A(b)} 0`,transform:`translateY(${A(s(b).mul(-1).equal())})`}:{borderWidth:`0 ${A(b)}`,transform:`translateX(${A(e.calc(b).mul(-1).equal())})`};return{[l]:r,[c]:s(r).mul(3).equal(),[`${n}-rail`]:{[u]:"100%",[c]:r},[`${n}-track,${n}-tracks`]:{[c]:r},[`${n}-track-draggable`]:Object.assign({},g),[`${n}-handle`]:{[f]:h},[`${n}-mark`]:{insetInlineStart:0,top:0,[m]:s(r).mul(3).add(t?0:a).equal(),[u]:"100%"},[`${n}-step`]:{insetInlineStart:0,top:0,[m]:r,[u]:"100%",[c]:r},[`${n}-dot`]:{position:"absolute",[f]:s(r).sub(i).div(2).equal()}}},cg=e=>{const{componentCls:t,marginPartWithMark:n}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},Nc(e,!0)),{[`&${t}-with-marks`]:{marginBottom:n}})}},ug=e=>{const{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},Nc(e,!1)),{height:"100%"})}},dg=e=>{const n=e.controlHeightLG/4,r=e.controlHeightSM/2,o=e.lineWidth+1,i=e.lineWidth+1*1.5,a=e.colorPrimary,s=new ho(a).setA(.2).toRgbString();return{controlSize:n,railSize:4,handleSize:n,handleSizeHover:r,dotSize:8,handleLineWidth:o,handleLineWidthHover:i,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:a,handleActiveOutlineColor:s,handleColorDisabled:new ho(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}},fg=gt("Slider",e=>{const t=pt(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[lg(t),cg(t),ug(t)]},dg);function Ir(){const[e,t]=d.useState(!1),n=d.useRef(null),r=()=>{wt.cancel(n.current)},o=i=>{r(),i?t(i):n.current=wt(()=>{t(i)})};return d.useEffect(()=>r,[]),[e,o]}var hg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function mg(e,t){return e||e===null?e:t||t===null?t:n=>typeof n=="number"?n.toString():""}const gg=$.forwardRef((e,t)=>{const{prefixCls:n,range:r,className:o,rootClassName:i,style:a,disabled:s,tooltipPrefixCls:l,tipFormatter:u,tooltipVisible:c,getTooltipPopupContainer:f,tooltipPlacement:m,tooltip:h={},onChangeComplete:b,classNames:g,styles:v}=e,y=hg(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:p}=e,{getPrefixCls:C,direction:x,className:S,style:w,classNames:E,styles:O,getPopupContainer:T}=on("slider"),P=$.useContext(Jo),N=s??P,{handleRender:D,direction:j}=$.useContext(Mc),I=(j||x)==="rtl",[L,R]=Ir(),[F,_]=Ir(),Q=Object.assign({},h),{open:z,placement:G,getPopupContainer:Y,prefixCls:J,formatter:U}=Q,H=z??c,W=(L||F)&&H!==!1,B=mg(U,u),[K,re]=Ir(),q=de=>{b==null||b(de),re(!1)},oe=(de,ve)=>de||(ve?I?"left":"right":"top"),ue=C("slider",n),[Se,le,pe]=fg(ue),he=k(o,S,E.root,g==null?void 0:g.root,i,{[`${ue}-rtl`]:I,[`${ue}-lock`]:K},le,pe);I&&!y.vertical&&(y.reverse=!y.reverse),$.useEffect(()=>{const de=()=>{wt(()=>{_(!1)},1)};return document.addEventListener("mouseup",de),()=>{document.removeEventListener("mouseup",de)}},[]);const je=r&&!H,we=D||((de,ve)=>{const{index:Ve}=ve,qe=de.props;function ke(Me,Be,Lt){var vt,be,Ae,Ke;Lt&&((be=(vt=y)[Me])===null||be===void 0||be.call(vt,Be)),(Ke=(Ae=qe)[Me])===null||Ke===void 0||Ke.call(Ae,Be)}const Ie=Object.assign(Object.assign({},qe),{onMouseEnter:Me=>{R(!0),ke("onMouseEnter",Me)},onMouseLeave:Me=>{R(!1),ke("onMouseLeave",Me)},onMouseDown:Me=>{_(!0),re(!0),ke("onMouseDown",Me)},onFocus:Me=>{var Be;_(!0),(Be=y.onFocus)===null||Be===void 0||Be.call(y,Me),ke("onFocus",Me,!0)},onBlur:Me=>{var Be;_(!1),(Be=y.onBlur)===null||Be===void 0||Be.call(y,Me),ke("onBlur",Me,!0)}}),_e=$.cloneElement(de,Ie),Xe=(!!H||W)&&B!==null;return je?_e:$.createElement(Oa,Object.assign({},Q,{prefixCls:C("tooltip",J??l),title:B?B(ve.value):"",open:Xe,placement:oe(G??m,p),key:Ve,classNames:{root:`${ue}-tooltip`},getPopupContainer:Y||f||T}),_e)}),De=je?(de,ve)=>{const Ve=$.cloneElement(de,{style:Object.assign(Object.assign({},de.props.style),{visibility:"hidden"})});return $.createElement(Oa,Object.assign({},Q,{prefixCls:C("tooltip",J??l),title:B?B(ve.value):"",open:B!==null&&W,placement:oe(G??m,p),key:"tooltip",classNames:{root:`${ue}-tooltip`},getPopupContainer:Y||f||T,draggingDelete:ve.draggingDelete}),Ve)}:void 0,ie=Object.assign(Object.assign(Object.assign(Object.assign({},O.root),w),v==null?void 0:v.root),a),me=Object.assign(Object.assign({},O.tracks),v==null?void 0:v.tracks),xe=k(E.tracks,g==null?void 0:g.tracks);return Se($.createElement(sg,Object.assign({},y,{classNames:Object.assign({handle:k(E.handle,g==null?void 0:g.handle),rail:k(E.rail,g==null?void 0:g.rail),track:k(E.track,g==null?void 0:g.track)},xe?{tracks:xe}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},O.handle),v==null?void 0:v.handle),rail:Object.assign(Object.assign({},O.rail),v==null?void 0:v.rail),track:Object.assign(Object.assign({},O.track),v==null?void 0:v.track)},Object.keys(me).length?{tracks:me}:{}),step:y.step,range:r,className:he,style:ie,disabled:N,ref:t,prefixCls:ue,handleRender:we,activeHandleRender:De,onChangeComplete:q})))});var pg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const jc=e=>{const{prefixCls:t,colors:n,type:r,color:o,range:i=!1,className:a,activeIndex:s,onActive:l,onDragStart:u,onDragChange:c,onKeyDelete:f}=e,m=pg(e,["prefixCls","colors","type","color","range","className","activeIndex","onActive","onDragStart","onDragChange","onKeyDelete"]),h=Object.assign(Object.assign({},m),{track:!1}),b=d.useMemo(()=>`linear-gradient(90deg, ${n.map(w=>`${w.color} ${w.percent}%`).join(", ")})`,[n]),g=d.useMemo(()=>!o||!r?null:r==="alpha"?o.toRgbString():`hsl(${o.toHsb().h}, 100%, 50%)`,[o,r]),v=at(u),y=at(c),p=d.useMemo(()=>({onDragStart:v,onDragChange:y}),[]),C=at((S,w)=>{const{onFocus:E,style:O,className:T,onKeyDown:P}=S.props,N=Object.assign({},O);return r==="gradient"&&(N.background=Dl(n,w.value)),d.cloneElement(S,{onFocus:D=>{l==null||l(w.index),E==null||E(D)},style:N,className:k(T,{[`${t}-slider-handle-active`]:s===w.index}),onKeyDown:D=>{(D.key==="Delete"||D.key==="Backspace")&&f&&f(w.index),P==null||P(D)}})}),x=d.useMemo(()=>({direction:"ltr",handleRender:C}),[]);return d.createElement(Mc.Provider,{value:x},d.createElement(Rc.Provider,{value:p},d.createElement(gg,Object.assign({},h,{className:k(a,`${t}-slider`),tooltip:{open:!1},range:{editable:i,minCount:2},styles:{rail:{background:b},handle:g?{background:g}:{}},classNames:{rail:`${t}-slider-rail`,handle:`${t}-slider-handle`}}))))},vg=e=>{const{value:t,onChange:n,onChangeComplete:r}=e,o=a=>n(a[0]),i=a=>r(a[0]);return d.createElement(jc,Object.assign({},e,{value:[t],onChange:o,onChangeComplete:i}))};function $a(e){return Oe(e).sort((t,n)=>t.percent-n.percent)}const bg=e=>{const{prefixCls:t,mode:n,onChange:r,onChangeComplete:o,onActive:i,activeIndex:a,onGradientDragging:s,colors:l}=e,u=n==="gradient",c=d.useMemo(()=>l.map(y=>({percent:y.percent,color:y.color.toRgbString()})),[l]),f=d.useMemo(()=>c.map(y=>y.percent),[c]),m=d.useRef(c),h=y=>{let{rawValues:p,draggingIndex:C,draggingValue:x}=y;if(p.length>c.length){const S=Dl(c,x),w=Oe(c);w.splice(C,0,{percent:x,color:S}),m.current=w}else m.current=c;s(!0),r(new ut($a(m.current)),!0)},b=y=>{let{deleteIndex:p,draggingIndex:C,draggingValue:x}=y,S=Oe(m.current);p!==-1?S.splice(p,1):(S[C]=Object.assign(Object.assign({},S[C]),{percent:x}),S=$a(S)),r(new ut(S),!0)},g=y=>{const p=Oe(c);p.splice(y,1);const C=new ut(p);r(C),o(C)},v=y=>{o(new ut(c)),a>=y.length&&i(y.length-1),s(!1)};return u?d.createElement(jc,{min:0,max:100,prefixCls:t,className:`${t}-gradient-slider`,colors:c,color:null,value:f,range:!0,onChangeComplete:v,disabled:!1,type:"gradient",activeIndex:a,onActive:i,onDragStart:h,onDragChange:b,onKeyDelete:g}):null},yg=d.memo(bg);var Cg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const xg={slider:vg},Pa=()=>{const e=d.useContext(xc),{mode:t,onModeChange:n,modeOptions:r,prefixCls:o,allowClear:i,value:a,disabledAlpha:s,onChange:l,onClear:u,onChangeComplete:c,activeIndex:f,gradientDragging:m}=e,h=Cg(e,["mode","onModeChange","modeOptions","prefixCls","allowClear","value","disabledAlpha","onChange","onClear","onChangeComplete","activeIndex","gradientDragging"]),b=$.useMemo(()=>a.cleared?[{percent:0,color:new ut("")},{percent:100,color:new ut("")}]:a.getColors(),[a]),g=!a.isGradient(),[v,y]=$.useState(a);tn(()=>{var M;g||y((M=b[f])===null||M===void 0?void 0:M.color)},[m,f]);const p=$.useMemo(()=>{var M;return g?a:m?v:(M=b[f])===null||M===void 0?void 0:M.color},[a,f,g,v,m]),[C,x]=$.useState(p),[S,w]=$.useState(0),E=C!=null&&C.equals(p)?p:C;tn(()=>{x(p)},[S,p==null?void 0:p.toHexString()]);const O=(M,I)=>{let L=rt(M);if(a.cleared){const F=L.toRgb();if(!F.r&&!F.g&&!F.b&&I){const{type:_,value:Q=0}=I;L=new ut({h:_==="hue"?Q:0,s:1,b:1,a:_==="alpha"?Q/100:1})}else L=nr(L)}if(t==="single")return L;const R=Oe(b);return R[f]=Object.assign(Object.assign({},R[f]),{color:L}),new ut(R)},T=(M,I,L)=>{const R=O(M,L);x(R.isGradient()?R.getColors()[f].color:R),l(R,I)},P=(M,I)=>{c(O(M,I)),w(L=>L+1)},N=M=>{l(O(M))};let D=null;const j=r.length>1;return(i||j)&&(D=$.createElement("div",{className:`${o}-operation`},j&&$.createElement(bm,{size:"small",options:r,value:t,onChange:n}),$.createElement(wc,Object.assign({prefixCls:o,value:a,onChange:M=>{l(M),u==null||u()}},h)))),$.createElement($.Fragment,null,D,$.createElement(yg,Object.assign({},e,{colors:b})),$.createElement(th,{prefixCls:o,value:E==null?void 0:E.toHsb(),disabledAlpha:s,onChange:(M,I)=>{T(M,!0,I)},onChangeComplete:(M,I)=>{P(M,I)},components:xg}),$.createElement(Xm,Object.assign({value:p,onChange:N,prefixCls:o,disabledAlpha:s},h)))},Ta=()=>{const{prefixCls:e,value:t,presets:n,onChange:r}=d.useContext(Sc);return Array.isArray(n)?$.createElement(Rf,{value:t,presets:n,prefixCls:e,onChange:r}):null},Sg=e=>{const{prefixCls:t,presets:n,panelRender:r,value:o,onChange:i,onClear:a,allowClear:s,disabledAlpha:l,mode:u,onModeChange:c,modeOptions:f,onChangeComplete:m,activeIndex:h,onActive:b,format:g,onFormatChange:v,gradientDragging:y,onGradientDragging:p,disabledFormat:C}=e,x=`${t}-inner`,S=$.useMemo(()=>({prefixCls:t,value:o,onChange:i,onClear:a,allowClear:s,disabledAlpha:l,mode:u,onModeChange:c,modeOptions:f,onChangeComplete:m,activeIndex:h,onActive:b,format:g,onFormatChange:v,gradientDragging:y,onGradientDragging:p,disabledFormat:C}),[t,o,i,a,s,l,u,c,f,m,h,b,g,v,y,p,C]),w=$.useMemo(()=>({prefixCls:t,value:o,presets:n,onChange:i}),[t,o,n,i]),E=$.createElement("div",{className:`${x}-content`},$.createElement(Pa,null),Array.isArray(n)&&$.createElement(yo,null),$.createElement(Ta,null));return $.createElement(xc.Provider,{value:S},$.createElement(Sc.Provider,{value:w},$.createElement("div",{className:x},typeof r=="function"?r(E,{components:{Picker:Pa,Presets:Ta}}):E)))};var wg=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Eg=d.forwardRef((e,t)=>{const{color:n,prefixCls:r,open:o,disabled:i,format:a,className:s,showText:l,activeIndex:u}=e,c=wg(e,["color","prefixCls","open","disabled","format","className","showText","activeIndex"]),f=`${r}-trigger`,m=`${f}-text`,h=`${m}-cell`,[b]=zn("ColorPicker"),g=$.useMemo(()=>{if(!l)return"";if(typeof l=="function")return l(n);if(n.cleared)return b.transparent;if(n.isGradient())return n.getColors().map((C,x)=>{const S=u!==-1&&u!==x;return $.createElement("span",{key:x,className:k(h,S&&`${h}-inactive`)},C.color.toRgbString()," ",C.percent,"%")});const y=n.toHexString().toUpperCase(),p=ni(n);switch(a){case"rgb":return n.toRgbString();case"hsb":return n.toHsbString();default:return p<100?`${y.slice(0,7)},${p}%`:y}},[n,a,l,u]),v=d.useMemo(()=>n.cleared?$.createElement(wc,{prefixCls:r}):$.createElement(xl,{prefixCls:r,color:n.toCssString()}),[n,r]);return $.createElement("div",Object.assign({ref:t,className:k(f,s,{[`${f}-active`]:o,[`${f}-disabled`]:i})},Et(c)),v,l&&$.createElement("div",{className:m},g))});function Og(e,t,n){const[r]=zn("ColorPicker"),[o,i]=Dt(e,{value:t}),[a,s]=d.useState("single"),[l,u]=d.useMemo(()=>{const g=(Array.isArray(n)?n:[n]).filter(C=>C);g.length||g.push("single");const v=new Set(g),y=[],p=(C,x)=>{v.has(C)&&y.push({label:x,value:C})};return p("single",r.singleColor),p("gradient",r.gradientColor),[y,v]},[n]),[c,f]=d.useState(null),m=at(g=>{f(g),i(g)}),h=d.useMemo(()=>{const g=rt(o||"");return g.equals(c)?c:g},[o,c]),b=d.useMemo(()=>{var g;return u.has(a)?a:(g=l[0])===null||g===void 0?void 0:g.value},[u,a,l]);return d.useEffect(()=>{s(h.isGradient()?"gradient":"single")},[h]),[h,m,b,s,l]}const Dc=(e,t)=>({backgroundImage:`conic-gradient(${t} 0 25%, transparent 0 50%, ${t} 0 75%, transparent 0)`,backgroundSize:`${e} ${e}`}),Ra=(e,t)=>{const{componentCls:n,borderRadiusSM:r,colorPickerInsetShadow:o,lineWidth:i,colorFillSecondary:a}=e;return{[`${n}-color-block`]:Object.assign(Object.assign({position:"relative",borderRadius:r,width:t,height:t,boxShadow:o,flex:"none"},Dc("50%",e.colorFillSecondary)),{[`${n}-color-block-inner`]:{width:"100%",height:"100%",boxShadow:`inset 0 0 0 ${A(i)} ${a}`,borderRadius:"inherit"}})}},$g=e=>{const{componentCls:t,antCls:n,fontSizeSM:r,lineHeightSM:o,colorPickerAlphaInputWidth:i,marginXXS:a,paddingXXS:s,controlHeightSM:l,marginXS:u,fontSizeIcon:c,paddingXS:f,colorTextPlaceholder:m,colorPickerInputNumberHandleWidth:h,lineWidth:b}=e;return{[`${t}-input-container`]:{display:"flex",[`${t}-steppers${n}-input-number`]:{fontSize:r,lineHeight:o,[`${n}-input-number-input`]:{paddingInlineStart:s,paddingInlineEnd:0},[`${n}-input-number-handler-wrap`]:{width:h}},[`${t}-steppers${t}-alpha-input`]:{flex:`0 0 ${A(i)}`,marginInlineStart:a},[`${t}-format-select${n}-select`]:{marginInlineEnd:u,width:"auto","&-single":{[`${n}-select-selector`]:{padding:0,border:0},[`${n}-select-arrow`]:{insetInlineEnd:0},[`${n}-select-selection-item`]:{paddingInlineEnd:e.calc(c).add(a).equal(),fontSize:r,lineHeight:A(l)},[`${n}-select-item-option-content`]:{fontSize:r,lineHeight:o},[`${n}-select-dropdown`]:{[`${n}-select-item`]:{minHeight:"auto"}}}},[`${t}-input`]:{gap:a,alignItems:"center",flex:1,width:0,[`${t}-hsb-input,${t}-rgb-input`]:{display:"flex",gap:a,alignItems:"center"},[`${t}-steppers`]:{flex:1},[`${t}-hex-input${n}-input-affix-wrapper`]:{flex:1,padding:`0 ${A(f)}`,[`${n}-input`]:{fontSize:r,textTransform:"uppercase",lineHeight:A(e.calc(l).sub(e.calc(b).mul(2)).equal())},[`${n}-input-prefix`]:{color:m}}}}}},Pg=e=>{const{componentCls:t,controlHeightLG:n,borderRadiusSM:r,colorPickerInsetShadow:o,marginSM:i,colorBgElevated:a,colorFillSecondary:s,lineWidthBold:l,colorPickerHandlerSize:u}=e;return{userSelect:"none",[`${t}-select`]:{[`${t}-palette`]:{minHeight:e.calc(n).mul(4).equal(),overflow:"hidden",borderRadius:r},[`${t}-saturation`]:{position:"absolute",borderRadius:"inherit",boxShadow:o,inset:0},marginBottom:i},[`${t}-handler`]:{width:u,height:u,border:`${A(l)} solid ${a}`,position:"relative",borderRadius:"50%",cursor:"pointer",boxShadow:`${o}, 0 0 0 1px ${s}`}}},Tg=e=>{const{componentCls:t,antCls:n,colorTextQuaternary:r,paddingXXS:o,colorPickerPresetColorSize:i,fontSizeSM:a,colorText:s,lineHeightSM:l,lineWidth:u,borderRadius:c,colorFill:f,colorWhite:m,marginXXS:h,paddingXS:b,fontHeightSM:g}=e;return{[`${t}-presets`]:{[`${n}-collapse-item > ${n}-collapse-header`]:{padding:0,[`${n}-collapse-expand-icon`]:{height:g,color:r,paddingInlineEnd:o}},[`${n}-collapse`]:{display:"flex",flexDirection:"column",gap:h},[`${n}-collapse-item > ${n}-collapse-content > ${n}-collapse-content-box`]:{padding:`${A(b)} 0`},"&-label":{fontSize:a,color:s,lineHeight:l},"&-items":{display:"flex",flexWrap:"wrap",gap:e.calc(h).mul(1.5).equal(),[`${t}-presets-color`]:{position:"relative",cursor:"pointer",width:i,height:i,"&::before":{content:'""',pointerEvents:"none",width:e.calc(i).add(e.calc(u).mul(4)).equal(),height:e.calc(i).add(e.calc(u).mul(4)).equal(),position:"absolute",top:e.calc(u).mul(-2).equal(),insetInlineStart:e.calc(u).mul(-2).equal(),borderRadius:c,border:`${A(u)} solid transparent`,transition:`border-color ${e.motionDurationMid} ${e.motionEaseInBack}`},"&:hover::before":{borderColor:f},"&::after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.calc(i).div(13).mul(5).equal(),height:e.calc(i).div(13).mul(8).equal(),border:`${A(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`},[`&${t}-presets-color-checked`]:{"&::after":{opacity:1,borderColor:m,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`transform ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`},[`&${t}-presets-color-bright`]:{"&::after":{borderColor:"rgba(0, 0, 0, 0.45)"}}}}},"&-empty":{fontSize:a,color:r}}}},Rg=e=>{const{componentCls:t,colorPickerInsetShadow:n,colorBgElevated:r,colorFillSecondary:o,lineWidthBold:i,colorPickerHandlerSizeSM:a,colorPickerSliderHeight:s,marginSM:l,marginXS:u}=e,c=e.calc(a).sub(e.calc(i).mul(2).equal()).equal(),f=e.calc(a).add(e.calc(i).mul(2).equal()).equal(),m={"&:after":{transform:"scale(1)",boxShadow:`${n}, 0 0 0 1px ${e.colorPrimaryActive}`}};return{[`${t}-slider`]:[Dc(A(s),e.colorFillSecondary),{margin:0,padding:0,height:s,borderRadius:e.calc(s).div(2).equal(),"&-rail":{height:s,borderRadius:e.calc(s).div(2).equal(),boxShadow:n},[`& ${t}-slider-handle`]:{width:c,height:c,top:0,borderRadius:"100%","&:before":{display:"block",position:"absolute",background:"transparent",left:{_skip_check_:!0,value:"50%"},top:"50%",transform:"translate(-50%, -50%)",width:f,height:f,borderRadius:"100%"},"&:after":{width:a,height:a,border:`${A(i)} solid ${r}`,boxShadow:`${n}, 0 0 0 1px ${o}`,outline:"none",insetInlineStart:e.calc(i).mul(-1).equal(),top:e.calc(i).mul(-1).equal(),background:"transparent",transition:"none"},"&:focus":m}}],[`${t}-slider-container`]:{display:"flex",gap:l,marginBottom:l,[`${t}-slider-group`]:{flex:1,flexDirection:"column",justifyContent:"space-between",display:"flex","&-disabled-alpha":{justifyContent:"center"}}},[`${t}-gradient-slider`]:{marginBottom:u,[`& ${t}-slider-handle`]:{"&:after":{transform:"scale(0.8)"},"&-active, &:focus":m}}}},So=(e,t,n)=>({borderInlineEndWidth:e.lineWidth,borderColor:t,boxShadow:`0 0 0 ${A(e.controlOutlineWidth)} ${n}`,outline:0}),Mg=e=>{const{componentCls:t}=e;return{"&-rtl":{[`${t}-presets-color`]:{"&::after":{direction:"ltr"}},[`${t}-clear`]:{"&::after":{direction:"ltr"}}}}},Ma=(e,t,n)=>{const{componentCls:r,borderRadiusSM:o,lineWidth:i,colorSplit:a,colorBorder:s,red6:l}=e;return{[`${r}-clear`]:Object.assign(Object.assign({width:t,height:t,borderRadius:o,border:`${A(i)} solid ${a}`,position:"relative",overflow:"hidden",cursor:"inherit",transition:`all ${e.motionDurationFast}`},n),{"&::after":{content:'""',position:"absolute",insetInlineEnd:e.calc(i).mul(-1).equal(),top:e.calc(i).mul(-1).equal(),display:"block",width:40,height:2,transformOrigin:"calc(100% - 1px) 1px",transform:"rotate(-45deg)",backgroundColor:l},"&:hover":{borderColor:s}})}},Ng=e=>{const{componentCls:t,colorError:n,colorWarning:r,colorErrorHover:o,colorWarningHover:i,colorErrorOutline:a,colorWarningOutline:s}=e;return{[`&${t}-status-error`]:{borderColor:n,"&:hover":{borderColor:o},[`&${t}-trigger-active`]:Object.assign({},So(e,n,a))},[`&${t}-status-warning`]:{borderColor:r,"&:hover":{borderColor:i},[`&${t}-trigger-active`]:Object.assign({},So(e,r,s))}}},jg=e=>{const{componentCls:t,controlHeightLG:n,controlHeightSM:r,controlHeight:o,controlHeightXS:i,borderRadius:a,borderRadiusSM:s,borderRadiusXS:l,borderRadiusLG:u,fontSizeLG:c}=e;return{[`&${t}-lg`]:{minWidth:n,minHeight:n,borderRadius:u,[`${t}-color-block, ${t}-clear`]:{width:o,height:o,borderRadius:a},[`${t}-trigger-text`]:{fontSize:c}},[`&${t}-sm`]:{minWidth:r,minHeight:r,borderRadius:s,[`${t}-color-block, ${t}-clear`]:{width:i,height:i,borderRadius:l},[`${t}-trigger-text`]:{lineHeight:A(i)}}}},Dg=e=>{const{antCls:t,componentCls:n,colorPickerWidth:r,colorPrimary:o,motionDurationMid:i,colorBgElevated:a,colorTextDisabled:s,colorText:l,colorBgContainerDisabled:u,borderRadius:c,marginXS:f,marginSM:m,controlHeight:h,controlHeightSM:b,colorBgTextActive:g,colorPickerPresetColorSize:v,colorPickerPreviewSize:y,lineWidth:p,colorBorder:C,paddingXXS:x,fontSize:S,colorPrimaryHover:w,controlOutline:E}=e;return[{[n]:Object.assign({[`${n}-inner`]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({"&-content":{display:"flex",flexDirection:"column",width:r,[`& > ${t}-divider`]:{margin:`${A(m)} 0 ${A(f)}`}},[`${n}-panel`]:Object.assign({},Pg(e))},Rg(e)),Ra(e,y)),$g(e)),Tg(e)),Ma(e,v,{marginInlineStart:"auto"})),{[`${n}-operation`]:{display:"flex",justifyContent:"space-between",marginBottom:f}}),"&-trigger":Object.assign(Object.assign(Object.assign(Object.assign({minWidth:h,minHeight:h,borderRadius:c,border:`${A(p)} solid ${C}`,cursor:"pointer",display:"inline-flex",alignItems:"flex-start",justifyContent:"center",transition:`all ${i}`,background:a,padding:e.calc(x).sub(p).equal(),[`${n}-trigger-text`]:{marginInlineStart:f,marginInlineEnd:e.calc(f).sub(e.calc(x).sub(p)).equal(),fontSize:S,color:l,alignSelf:"center","&-cell":{"&:not(:last-child):after":{content:'", "'},"&-inactive":{color:s}}},"&:hover":{borderColor:w},[`&${n}-trigger-active`]:Object.assign({},So(e,o,E)),"&-disabled":{color:s,background:u,cursor:"not-allowed","&:hover":{borderColor:g},[`${n}-trigger-text`]:{color:s}}},Ma(e,b)),Ra(e,b)),Ng(e)),jg(e))},Mg(e))},Rl(e,{focusElCls:`${n}-trigger-active`})]},Ig=gt("ColorPicker",e=>{const{colorTextQuaternary:t,marginSM:n}=e,r=8,o=pt(e,{colorPickerWidth:234,colorPickerHandlerSize:16,colorPickerHandlerSizeSM:12,colorPickerAlphaInputWidth:44,colorPickerInputNumberHandleWidth:16,colorPickerPresetColorSize:24,colorPickerInsetShadow:`inset 0 0 1px 0 ${t}`,colorPickerSliderHeight:r,colorPickerPreviewSize:e.calc(r).mul(2).add(n).equal()});return[Dg(o)]});var Ag=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Gt=e=>{const{mode:t,value:n,defaultValue:r,format:o,defaultFormat:i,allowClear:a=!1,presets:s,children:l,trigger:u="click",open:c,disabled:f,placement:m="bottomLeft",arrow:h=!0,panelRender:b,showText:g,style:v,className:y,size:p,rootClassName:C,prefixCls:x,styles:S,disabledAlpha:w=!1,onFormatChange:E,onChange:O,onClear:T,onOpenChange:P,onChangeComplete:N,getPopupContainer:D,autoAdjustOverflow:j=!0,destroyTooltipOnHide:M,disabledFormat:I}=e,L=Ag(e,["mode","value","defaultValue","format","defaultFormat","allowClear","presets","children","trigger","open","disabled","placement","arrow","panelRender","showText","style","className","size","rootClassName","prefixCls","styles","disabledAlpha","onFormatChange","onChange","onClear","onOpenChange","onChangeComplete","getPopupContainer","autoAdjustOverflow","destroyTooltipOnHide","disabledFormat"]),{getPrefixCls:R,direction:F,colorPicker:_}=d.useContext(Ue),Q=d.useContext(Jo),z=f??Q,[G,Y]=Dt(!1,{value:c,postState:be=>!z&&be,onChange:P}),[J,U]=Dt(o,{value:o,defaultValue:i,onChange:E}),H=R("color-picker",x),[W,B,K,re,q]=Og(r,n,t),oe=d.useMemo(()=>ni(W)<100,[W]),[ue,Se]=$.useState(null),le=be=>{if(N){let Ae=rt(be);w&&oe&&(Ae=nr(be)),N(Ae)}},pe=(be,Ae)=>{let Ke=rt(be);w&&oe&&(Ke=nr(Ke)),B(Ke),Se(null),O&&O(Ke,Ke.toCssString()),Ae||le(Ke)},[he,je]=$.useState(0),[we,De]=$.useState(!1),ie=be=>{if(re(be),be==="single"&&W.isGradient())je(0),pe(new ut(W.getColors()[0].color)),Se(W);else if(be==="gradient"&&!W.isGradient()){const Ae=oe?nr(W):W;pe(new ut(ue||[{percent:0,color:Ae},{percent:100,color:Ae}]))}},{status:me}=$.useContext(jl),{compactSize:xe,compactItemClassnames:de}=Nl(H,F),ve=xr(be=>{var Ae;return(Ae=p??xe)!==null&&Ae!==void 0?Ae:be}),Ve=yn(H),[qe,ke,Ie]=Ig(H,Ve),_e={[`${H}-rtl`]:F},Xe=k(C,Ie,Ve,_e),Me=k(mo(H,me),{[`${H}-sm`]:ve==="small",[`${H}-lg`]:ve==="large"},de,_==null?void 0:_.className,Xe,y,ke),Be=k(H,Xe),Lt={open:G,trigger:u,placement:m,arrow:h,rootClassName:C,getPopupContainer:D,autoAdjustOverflow:j,destroyTooltipOnHide:M},vt=Object.assign(Object.assign({},_==null?void 0:_.style),v);return qe($.createElement(ri,Object.assign({style:S==null?void 0:S.popup,styles:{body:S==null?void 0:S.popupOverlayInner},onOpenChange:be=>{(!be||!z)&&Y(be)},content:$.createElement(Dn,{form:!0},$.createElement(Sg,{mode:K,onModeChange:ie,modeOptions:q,prefixCls:H,value:W,allowClear:a,disabled:z,disabledAlpha:w,presets:s,panelRender:b,format:J,onFormatChange:U,onChange:pe,onChangeComplete:le,onClear:T,activeIndex:he,onActive:je,gradientDragging:we,onGradientDragging:De,disabledFormat:I})),classNames:{root:Be}},Lt),l||$.createElement(Eg,Object.assign({activeIndex:G?he:-1,open:G,className:Me,style:vt,prefixCls:H,disabled:z,showText:g,format:J},L,{color:W}))))},Vg=Nf(Gt,void 0,e=>Object.assign(Object.assign({},e),{placement:"bottom",autoAdjustOverflow:!1}),"color-picker",e=>e);Gt._InternalPanelDoNotUseOrYouWillBeFired=Vg;var Na=d.createContext(null),Ic=d.createContext({}),kg=["prefixCls","className","containerRef"],Bg=function(t){var n=t.prefixCls,r=t.className,o=t.containerRef,i=bn(t,kg),a=d.useContext(Ic),s=a.panel,l=fl(s,o);return d.createElement("div",Ce({className:k("".concat(n,"-content"),r),role:"dialog",ref:l},Et(t,{aria:!0}),{"aria-modal":"true"},i))};function ja(e){return typeof e=="string"&&String(Number(e))===e?(lr(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var Da={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function Lg(e,t){var n,r,o,i=e.prefixCls,a=e.open,s=e.placement,l=e.inline,u=e.push,c=e.forceRender,f=e.autoFocus,m=e.keyboard,h=e.classNames,b=e.rootClassName,g=e.rootStyle,v=e.zIndex,y=e.className,p=e.id,C=e.style,x=e.motion,S=e.width,w=e.height,E=e.children,O=e.mask,T=e.maskClosable,P=e.maskMotion,N=e.maskClassName,D=e.maskStyle,j=e.afterOpenChange,M=e.onClose,I=e.onMouseEnter,L=e.onMouseOver,R=e.onMouseLeave,F=e.onClick,_=e.onKeyDown,Q=e.onKeyUp,z=e.styles,G=e.drawerRender,Y=d.useRef(),J=d.useRef(),U=d.useRef();d.useImperativeHandle(t,function(){return Y.current});var H=function(me){var xe=me.keyCode,de=me.shiftKey;switch(xe){case Ee.TAB:{if(xe===Ee.TAB){if(!de&&document.activeElement===U.current){var ve;(ve=J.current)===null||ve===void 0||ve.focus({preventScroll:!0})}else if(de&&document.activeElement===J.current){var Ve;(Ve=U.current)===null||Ve===void 0||Ve.focus({preventScroll:!0})}}break}case Ee.ESC:{M&&m&&(me.stopPropagation(),M(me));break}}};d.useEffect(function(){if(a&&f){var ie;(ie=Y.current)===null||ie===void 0||ie.focus({preventScroll:!0})}},[a]);var W=d.useState(!1),B=ce(W,2),K=B[0],re=B[1],q=d.useContext(Na),oe;typeof u=="boolean"?oe=u?{}:{distance:0}:oe=u||{};var ue=(n=(r=(o=oe)===null||o===void 0?void 0:o.distance)!==null&&r!==void 0?r:q==null?void 0:q.pushDistance)!==null&&n!==void 0?n:180,Se=d.useMemo(function(){return{pushDistance:ue,push:function(){re(!0)},pull:function(){re(!1)}}},[ue]);d.useEffect(function(){if(a){var ie;q==null||(ie=q.push)===null||ie===void 0||ie.call(q)}else{var me;q==null||(me=q.pull)===null||me===void 0||me.call(q)}},[a]),d.useEffect(function(){return function(){var ie;q==null||(ie=q.pull)===null||ie===void 0||ie.call(q)}},[]);var le=O&&d.createElement(jn,Ce({key:"mask"},P,{visible:a}),function(ie,me){var xe=ie.className,de=ie.style;return d.createElement("div",{className:k("".concat(i,"-mask"),xe,h==null?void 0:h.mask,N),style:ne(ne(ne({},de),D),z==null?void 0:z.mask),onClick:T&&a?M:void 0,ref:me})}),pe=typeof x=="function"?x(s):x,he={};if(K&&ue)switch(s){case"top":he.transform="translateY(".concat(ue,"px)");break;case"bottom":he.transform="translateY(".concat(-ue,"px)");break;case"left":he.transform="translateX(".concat(ue,"px)");break;default:he.transform="translateX(".concat(-ue,"px)");break}s==="left"||s==="right"?he.width=ja(S):he.height=ja(w);var je={onMouseEnter:I,onMouseOver:L,onMouseLeave:R,onClick:F,onKeyDown:_,onKeyUp:Q},we=d.createElement(jn,Ce({key:"panel"},pe,{visible:a,forceRender:c,onVisibleChanged:function(me){j==null||j(me)},removeOnLeave:!1,leavedClassName:"".concat(i,"-content-wrapper-hidden")}),function(ie,me){var xe=ie.className,de=ie.style,ve=d.createElement(Bg,Ce({id:p,containerRef:me,prefixCls:i,className:k(y,h==null?void 0:h.content),style:ne(ne({},C),z==null?void 0:z.content)},Et(e,{aria:!0}),je),E);return d.createElement("div",Ce({className:k("".concat(i,"-content-wrapper"),h==null?void 0:h.wrapper,xe),style:ne(ne(ne({},he),de),z==null?void 0:z.wrapper)},Et(e,{data:!0})),G?G(ve):ve)}),De=ne({},g);return v&&(De.zIndex=v),d.createElement(Na.Provider,{value:Se},d.createElement("div",{className:k(i,"".concat(i,"-").concat(s),b,se(se({},"".concat(i,"-open"),a),"".concat(i,"-inline"),l)),style:De,tabIndex:-1,ref:Y,onKeyDown:H},le,d.createElement("div",{tabIndex:0,ref:J,style:Da,"aria-hidden":"true","data-sentinel":"start"}),we,d.createElement("div",{tabIndex:0,ref:U,style:Da,"aria-hidden":"true","data-sentinel":"end"})))}var Fg=d.forwardRef(Lg),_g=function(t){var n=t.open,r=n===void 0?!1:n,o=t.prefixCls,i=o===void 0?"rc-drawer":o,a=t.placement,s=a===void 0?"right":a,l=t.autoFocus,u=l===void 0?!0:l,c=t.keyboard,f=c===void 0?!0:c,m=t.width,h=m===void 0?378:m,b=t.mask,g=b===void 0?!0:b,v=t.maskClosable,y=v===void 0?!0:v,p=t.getContainer,C=t.forceRender,x=t.afterOpenChange,S=t.destroyOnClose,w=t.onMouseEnter,E=t.onMouseOver,O=t.onMouseLeave,T=t.onClick,P=t.onKeyDown,N=t.onKeyUp,D=t.panelRef,j=d.useState(!1),M=ce(j,2),I=M[0],L=M[1],R=d.useState(!1),F=ce(R,2),_=F[0],Q=F[1];tn(function(){Q(!0)},[]);var z=_?r:!1,G=d.useRef(),Y=d.useRef();tn(function(){z&&(Y.current=document.activeElement)},[z]);var J=function(K){var re;if(L(K),x==null||x(K),!K&&Y.current&&!((re=G.current)!==null&&re!==void 0&&re.contains(Y.current))){var q;(q=Y.current)===null||q===void 0||q.focus({preventScroll:!0})}},U=d.useMemo(function(){return{panel:D}},[D]);if(!C&&!I&&!z&&S)return null;var H={onMouseEnter:w,onMouseOver:E,onMouseLeave:O,onClick:T,onKeyDown:P,onKeyUp:N},W=ne(ne({},t),{},{open:z,prefixCls:i,placement:s,autoFocus:u,keyboard:f,width:h,mask:g,maskClosable:y,inline:p===!1,afterOpenChange:J,ref:G},H);return d.createElement(Ic.Provider,{value:U},d.createElement(Ol,{open:z||C||I,autoDestroy:!1,getContainer:p,autoLock:g&&(z||I)},d.createElement(Fg,W)))};const Ac=e=>{var t,n;const{prefixCls:r,title:o,footer:i,extra:a,loading:s,onClose:l,headerStyle:u,bodyStyle:c,footerStyle:f,children:m,classNames:h,styles:b}=e,g=on("drawer"),v=d.useCallback(S=>d.createElement("button",{type:"button",onClick:l,"aria-label":"Close",className:`${r}-close`},S),[l]),[y,p]=Yl(fr(e),fr(g),{closable:!0,closeIconRender:v}),C=d.useMemo(()=>{var S,w;return!o&&!y?null:d.createElement("div",{style:Object.assign(Object.assign(Object.assign({},(S=g.styles)===null||S===void 0?void 0:S.header),u),b==null?void 0:b.header),className:k(`${r}-header`,{[`${r}-header-close-only`]:y&&!o&&!a},(w=g.classNames)===null||w===void 0?void 0:w.header,h==null?void 0:h.header)},d.createElement("div",{className:`${r}-header-title`},p,o&&d.createElement("div",{className:`${r}-title`},o)),a&&d.createElement("div",{className:`${r}-extra`},a))},[y,p,a,u,r,o]),x=d.useMemo(()=>{var S,w;if(!i)return null;const E=`${r}-footer`;return d.createElement("div",{className:k(E,(S=g.classNames)===null||S===void 0?void 0:S.footer,h==null?void 0:h.footer),style:Object.assign(Object.assign(Object.assign({},(w=g.styles)===null||w===void 0?void 0:w.footer),f),b==null?void 0:b.footer)},i)},[i,f,r]);return d.createElement(d.Fragment,null,C,d.createElement("div",{className:k(`${r}-body`,h==null?void 0:h.body,(t=g.classNames)===null||t===void 0?void 0:t.body),style:Object.assign(Object.assign(Object.assign({},(n=g.styles)===null||n===void 0?void 0:n.body),c),b==null?void 0:b.body)},s?d.createElement(Al,{active:!0,title:!1,paragraph:{rows:5},className:`${r}-body-skeleton`}):m),x)},zg=e=>{const t="100%";return{left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`}[e]},Vc=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),kc=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},Vc({opacity:e},{opacity:1})),Hg=(e,t)=>[kc(.7,t),Vc({transform:zg(e)},{transform:"none"})],Wg=e=>{const{componentCls:t,motionDurationSlow:n}=e;return{[t]:{[`${t}-mask-motion`]:kc(0,n),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce((r,o)=>Object.assign(Object.assign({},r),{[`&-${o}`]:Hg(o,n)}),{})}}},qg=e=>{const{borderRadiusSM:t,componentCls:n,zIndexPopup:r,colorBgMask:o,colorBgElevated:i,motionDurationSlow:a,motionDurationMid:s,paddingXS:l,padding:u,paddingLG:c,fontSizeLG:f,lineHeightLG:m,lineWidth:h,lineType:b,colorSplit:g,marginXS:v,colorIcon:y,colorIconHover:p,colorBgTextHover:C,colorBgTextActive:x,colorText:S,fontWeightStrong:w,footerPaddingBlock:E,footerPaddingInline:O,calc:T}=e,P=`${n}-content-wrapper`;return{[n]:{position:"fixed",inset:0,zIndex:r,pointerEvents:"none",color:S,"&-pure":{position:"relative",background:i,display:"flex",flexDirection:"column",[`&${n}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${n}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${n}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${n}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${n}-mask`]:{position:"absolute",inset:0,zIndex:r,background:o,pointerEvents:"auto"},[P]:{position:"absolute",zIndex:r,maxWidth:"100vw",transition:`all ${a}`,"&-hidden":{display:"none"}},[`&-left > ${P}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${P}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${P}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${P}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${n}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:i,pointerEvents:"auto"},[`${n}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${A(u)} ${A(c)}`,fontSize:f,lineHeight:m,borderBottom:`${A(h)} ${b} ${g}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${n}-extra`]:{flex:"none"},[`${n}-close`]:Object.assign({display:"inline-flex",width:T(f).add(l).equal(),height:T(f).add(l).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:v,color:y,fontWeight:w,fontSize:f,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${s}`,textRendering:"auto","&:hover":{color:p,backgroundColor:C,textDecoration:"none"},"&:active":{backgroundColor:x}},Cr(e)),[`${n}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:f,lineHeight:m},[`${n}-body`]:{flex:1,minWidth:0,minHeight:0,padding:c,overflow:"auto",[`${n}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${n}-footer`]:{flexShrink:0,padding:`${A(E)} ${A(O)}`,borderTop:`${A(h)} ${b} ${g}`},"&-rtl":{direction:"rtl"}}}},Gg=e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding}),Bc=gt("Drawer",e=>{const t=pt(e,{});return[qg(t),Wg(t)]},Gg);var Lc=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ug={distance:180},Fc=e=>{const{rootClassName:t,width:n,height:r,size:o="default",mask:i=!0,push:a=Ug,open:s,afterOpenChange:l,onClose:u,prefixCls:c,getContainer:f,style:m,className:h,visible:b,afterVisibleChange:g,maskStyle:v,drawerStyle:y,contentWrapperStyle:p}=e,C=Lc(e,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle"]),{getPopupContainer:x,getPrefixCls:S,direction:w,className:E,style:O,classNames:T,styles:P}=on("drawer"),N=S("drawer",c),[D,j,M]=Bc(N),I=f===void 0&&x?()=>x(document.body):f,L=k({"no-mask":!i,[`${N}-rtl`]:w==="rtl"},t,j,M),R=d.useMemo(()=>n??(o==="large"?736:378),[n,o]),F=d.useMemo(()=>r??(o==="large"?736:378),[r,o]),_={motionName:rn(N,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},Q=H=>({motionName:rn(N,`panel-motion-${H}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500}),z=Zl(),[G,Y]=$l("Drawer",C.zIndex),{classNames:J={},styles:U={}}=C;return D(d.createElement(Dn,{form:!0,space:!0},d.createElement(Pl.Provider,{value:Y},d.createElement(_g,Object.assign({prefixCls:N,onClose:u,maskMotion:_,motion:Q},C,{classNames:{mask:k(J.mask,T.mask),content:k(J.content,T.content),wrapper:k(J.wrapper,T.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},U.mask),v),P.mask),content:Object.assign(Object.assign(Object.assign({},U.content),y),P.content),wrapper:Object.assign(Object.assign(Object.assign({},U.wrapper),p),P.wrapper)},open:s??b,mask:i,push:a,width:R,height:F,style:Object.assign(Object.assign({},O),m),className:k(E,h),rootClassName:L,getContainer:I,afterOpenChange:l??g,panelRef:z,zIndex:G}),d.createElement(Ac,Object.assign({prefixCls:N},C,{onClose:u}))))))},Xg=e=>{const{prefixCls:t,style:n,className:r,placement:o="right"}=e,i=Lc(e,["prefixCls","style","className","placement"]),{getPrefixCls:a}=d.useContext(Ue),s=a("drawer",t),[l,u,c]=Bc(s),f=k(s,`${s}-pure`,`${s}-${o}`,u,c,r);return l(d.createElement("div",{className:f,style:n},d.createElement(Ac,Object.assign({prefixCls:s},i))))};Fc._InternalPanelDoNotUseOrYouWillBeFired=Xg;var Kg={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"}}]},name:"file-text",theme:"outlined"};function Yg(e,t,n){return typeof n=="boolean"?n:e.length?!0:ur(t).some(o=>o.type===kl)}var _c=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Er(e){let{suffixCls:t,tagName:n,displayName:r}=e;return o=>d.forwardRef((a,s)=>d.createElement(o,Object.assign({ref:s,suffixCls:t,tagName:n},a)))}const li=d.forwardRef((e,t)=>{const{prefixCls:n,suffixCls:r,className:o,tagName:i}=e,a=_c(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=d.useContext(Ue),l=s("layout",n),[u,c,f]=Bl(l),m=r?`${l}-${r}`:l;return u(d.createElement(i,Object.assign({className:k(n||m,o,c,f),ref:t},a)))}),Zg=d.forwardRef((e,t)=>{const{direction:n}=d.useContext(Ue),[r,o]=d.useState([]),{prefixCls:i,className:a,rootClassName:s,children:l,hasSider:u,tagName:c,style:f}=e,m=_c(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),h=Tl(m,["suffixCls"]),{getPrefixCls:b,className:g,style:v}=on("layout"),y=b("layout",i),p=Yg(r,l,u),[C,x,S]=Bl(y),w=k(y,{[`${y}-has-sider`]:p,[`${y}-rtl`]:n==="rtl"},g,a,s,x,S),E=d.useMemo(()=>({siderHook:{addSider:O=>{o(T=>[].concat(Oe(T),[O]))},removeSider:O=>{o(T=>T.filter(P=>P!==O))}}}),[]);return C(d.createElement(jf.Provider,{value:E},d.createElement(c,Object.assign({ref:t,className:w,style:Object.assign(Object.assign({},v),f)},h),l)))}),Qg=Er({tagName:"div",displayName:"Layout"})(Zg),Jg=Er({suffixCls:"header",tagName:"header",displayName:"Header"})(li),ep=Er({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(li),tp=Er({suffixCls:"content",tagName:"main",displayName:"Content"})(li),It=Qg;It.Header=Jg;It.Footer=ep;It.Content=tp;It.Sider=kl;It._InternalSiderContext=Df;var np=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const rp=e=>{const{prefixCls:t,className:n,closeIcon:r,closable:o,type:i,title:a,children:s,footer:l}=e,u=np(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:c}=d.useContext(Ue),f=c(),m=t||c("modal"),h=yn(f),[b,g,v]=nc(m,h),y=`${m}-confirm`;let p={};return i?p={closable:o??!1,title:"",footer:"",children:d.createElement(oc,Object.assign({},e,{prefixCls:m,confirmPrefixCls:y,rootPrefixCls:f,content:s}))}:p={closable:o??!0,title:a,footer:l!==null&&d.createElement(Jl,Object.assign({},e)),children:s},b(d.createElement(Ul,Object.assign({prefixCls:m,className:k(g,`${m}-pure-panel`,i&&y,i&&`${y}-${i}`,n,v,h)},u,{closeIcon:Ql(m,r),closable:o},p)))},op=If(rp);function zc(e){return Wn(lc(e))}const st=rc;st.useModal=Mh;st.info=function(t){return Wn(cc(t))};st.success=function(t){return Wn(uc(t))};st.error=function(t){return Wn(dc(t))};st.warning=zc;st.warn=zc;st.confirm=function(t){return Wn(fc(t))};st.destroyAll=function(){for(;Ut.length;){const t=Ut.pop();t&&t()}};st.config=Oh;st._InternalPanelDoNotUseOrYouWillBeFired=op;var ip={icon:function(t,n){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M551.9 513c19.6 0 35.9-14.2 39.3-32.8A40.02 40.02 0 01552 512a40 40 0 01-40-39.4v.5c0 22 17.9 39.9 39.9 39.9zM752 687.8l-.3-.3c-29-17.5-62.3-26.8-97-26.8-44.9 0-87.2 15.7-121 43.8a256.27 256.27 0 01-164.9 59.9c-41.2 0-81-9.8-116.7-28L210.5 844h603l-59.9-155.2-1.6-1z",fill:n}},{tag:"path",attrs:{d:"M879 824.9L696.3 352V178H768v-68H256v68h71.7v174L145 824.9c-2.8 7.4-4.3 15.2-4.3 23.1 0 35.3 28.7 64 64 64h614.6c7.9 0 15.7-1.5 23.1-4.3 33-12.7 49.4-49.8 36.6-82.8zM395.7 364.7V180h232.6v184.7L719.2 600c-20.7-5.3-42.1-8-63.9-8-61.2 0-119.2 21.5-165.3 60a188.78 188.78 0 01-121.3 43.9c-32.7 0-64.1-8.3-91.8-23.7l118.8-307.5zM210.5 844l41.6-107.6.1-.2c35.7 18.1 75.4 27.8 116.6 27.8 61.2 0 119.2-21.5 165.3-60 33.9-28.2 76.3-43.9 121.3-43.9 35 0 68.4 9.5 97.6 27.1l.6 1.6L813.5 844h-603z",fill:t}},{tag:"path",attrs:{d:"M552 512c19.3 0 35.4-13.6 39.2-31.8.6-2.7.8-5.4.8-8.2 0-22.1-17.9-40-40-40s-40 17.9-40 40v.6a40 40 0 0040 39.4z",fill:t}}]}},name:"experiment",theme:"twotone"},ap={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M391 240.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L200 146.3a8.03 8.03 0 00-11.3 0l-42.4 42.3a8.03 8.03 0 000 11.3L280 333.6l-43.9 43.9a8.01 8.01 0 004.7 13.6L401 410c5.1.6 9.5-3.7 8.9-8.9L391 240.9zm10.1 373.2L240.8 633c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L146.3 824a8.03 8.03 0 000 11.3l42.4 42.3c3.1 3.1 8.2 3.1 11.3 0L333.7 744l43.7 43.7A8.01 8.01 0 00391 783l18.9-160.1c.6-5.1-3.7-9.4-8.8-8.8zm221.8-204.2L783.2 391c6.6-.8 9.4-8.9 4.7-13.6L744 333.6 877.7 200c3.1-3.1 3.1-8.2 0-11.3l-42.4-42.3a8.03 8.03 0 00-11.3 0L690.3 279.9l-43.7-43.7a8.01 8.01 0 00-13.6 4.7L614.1 401c-.6 5.2 3.7 9.5 8.8 8.9zM744 690.4l43.9-43.9a8.01 8.01 0 00-4.7-13.6L623 614c-5.1-.6-9.5 3.7-8.9 8.9L633 783.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L824 877.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3L744 690.4z"}}]},name:"fullscreen-exit",theme:"outlined"},sp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M290 236.4l43.9-43.9a8.01 8.01 0 00-4.7-13.6L169 160c-5.1-.6-9.5 3.7-8.9 8.9L179 329.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L370 423.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3L290 236.4zm352.7 187.3c3.1 3.1 8.2 3.1 11.3 0l133.7-133.6 43.7 43.7a8.01 8.01 0 0013.6-4.7L863.9 169c.6-5.1-3.7-9.5-8.9-8.9L694.8 179c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L600.3 370a8.03 8.03 0 000 11.3l42.4 42.4zM845 694.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L654 600.3a8.03 8.03 0 00-11.3 0l-42.4 42.3a8.03 8.03 0 000 11.3L734 787.6l-43.9 43.9a8.01 8.01 0 004.7 13.6L855 864c5.1.6 9.5-3.7 8.9-8.9L845 694.9zm-463.7-94.6a8.03 8.03 0 00-11.3 0L236.3 733.9l-43.7-43.7a8.01 8.01 0 00-13.6 4.7L160.1 855c-.6 5.1 3.7 9.5 8.9 8.9L329.2 845c6.6-.8 9.4-8.9 4.7-13.6L290 787.6 423.7 654c3.1-3.1 3.1-8.2 0-11.3l-42.4-42.4z"}}]},name:"fullscreen",theme:"outlined"},lp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M608 112c-167.9 0-304 136.1-304 304 0 70.3 23.9 135 63.9 186.5l-41.1 41.1-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-44.9 44.9-62.3-62.3a8.15 8.15 0 00-11.4 0l-39.8 39.8a8.15 8.15 0 000 11.4l62.3 62.3-65.3 65.3a8.03 8.03 0 000 11.3l42.3 42.3c3.1 3.1 8.2 3.1 11.3 0l253.6-253.6A304.06 304.06 0 00608 720c167.9 0 304-136.1 304-304S775.9 112 608 112zm161.2 465.2C726.2 620.3 668.9 644 608 644c-60.9 0-118.2-23.7-161.2-66.8-43.1-43-66.8-100.3-66.8-161.2 0-60.9 23.7-118.2 66.8-161.2 43-43.1 100.3-66.8 161.2-66.8 60.9 0 118.2 23.7 161.2 66.8 43.1 43 66.8 100.3 66.8 161.2 0 60.9-23.7 118.2-66.8 161.2z"}}]},name:"key",theme:"outlined"},cp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"},up={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"},dp={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"};const Ia={color:"primary",variant:"outlined"},fp=({open:e,onCancel:t})=>{const{theme:n,setTheme:r,primary:o,setPrimary:i,borderColor:a,setBorderColor:s,bgContainer:l,setBgContainer:u,fontColor:c,setFontColor:f,Link:m,setLink:h,fontSize:b,setFontSize:g,borderRadius:v,setBorderRadius:y,isButtonSolid:p,setIsButtonSolid:C,buttonShadow:x,setButtonShadow:S,buttonFontWeight:w,setButtonFontWeight:E,buttonSize:O,setButtonSize:T}=d.useContext(pl),P=R=>r(R),N=R=>i(R.toHexString()),D=R=>R&&g(R),j=R=>R&&y(R),M=R=>h(R.toHexString()),I=St.localGet("custom-theme"),L=()=>{const R={theme:n,primary:o,borderColor:a,bgContainer:l,fontColor:c,Link:m,fontSize:b,borderRadius:v,isButtonSolid:p,buttonShadow:x,buttonFontWeight:w,buttonSize:O};I?St.localSet("custom-theme",{...I,...R}):St.localSet("custom-theme",R),Il.success("主题设置已保存"),t()};return V.jsxs(Fc,{title:"主题配置",onClose:t,open:e,children:[V.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:16},children:[V.jsxs("div",{children:[V.jsx("span",{children:"主题模式："}),V.jsxs(zt.Group,{value:n,onChange:R=>P(R.target.value),children:[V.jsx(zt.Button,{value:"light",children:"浅色"}),V.jsx(zt.Button,{value:"dark",children:"深色"})]})]}),V.jsxs("div",{children:[V.jsx("span",{children:"主题颜色："}),V.jsx(Gt,{showText:!0,value:o,onChange:N,presets:[{label:"预设颜色",colors:["#1692ff","#52c41a","#f5222d","#fa8c16","#13c2c2"]}]})]}),V.jsxs("div",{children:[V.jsx("span",{children:"超链接颜色："}),V.jsx(Gt,{showText:!0,value:m,onChange:M,presets:[{label:"预设颜色",colors:["#1692ff","#722ed1","#eb2f96","#fa541c"]}]})]}),V.jsxs("div",{children:[V.jsx("span",{children:"字体大小："}),V.jsx(mr,{min:12,max:20,value:b,onChange:D,suffix:"px"})]}),V.jsxs("div",{children:[V.jsx("span",{children:"圆角大小："}),V.jsx(mr,{min:0,max:16,value:v,onChange:j,suffix:"px"})]}),V.jsxs("div",{children:[V.jsx("span",{children:"边框颜色："}),V.jsx(Gt,{showText:!0,value:a,onChange:R=>s(R.toHexString())})]}),V.jsxs("div",{children:[V.jsx("span",{children:"字体颜色："}),V.jsx(Gt,{showText:!0,value:c,onChange:R=>f(R.toHexString())})]}),V.jsxs("div",{children:[V.jsx("span",{children:"容器背景色："}),V.jsx(Gt,{showText:!0,value:l,onChange:R=>u(R.toHexString())})]}),V.jsx(yo,{children:"按钮样式"}),V.jsxs("div",{children:[V.jsx("span",{children:"按钮是否实心："}),V.jsx(Hf,{checked:p,onChange:C})]}),V.jsxs("div",{children:[V.jsx("span",{children:"按钮阴影："}),V.jsx(go,{style:{width:180},value:x,onChange:S,options:[{label:"无",value:"none"},{label:"浅阴影",value:"0 2px 4px rgba(0,0,0,0.08)"},{label:"中等阴影",value:"0 2px 8px rgba(0,0,0,0.15)"},{label:"深阴影",value:"0 4px 16px rgba(0,0,0,0.2)"}]})]}),V.jsxs("div",{children:[V.jsx("span",{children:"按钮字重："}),V.jsx(go,{style:{width:180},value:w,onChange:E,options:[{label:"正常 (400)",value:400},{label:"中等 (500)",value:500},{label:"加粗 (600)",value:600},{label:"超粗 (700)",value:700}]})]}),V.jsxs("div",{children:[V.jsx("span",{children:"按钮尺寸："}),V.jsxs(zt.Group,{value:O,onChange:R=>T(R.target.value),children:[V.jsx(zt.Button,{value:"small",children:"小"}),V.jsx(zt.Button,{value:"middle",children:"中"}),V.jsx(zt.Button,{value:"large",children:"大"})]})]})]}),V.jsx(yo,{}),V.jsxs("div",{style:{textAlign:"right"},children:[V.jsx(nn,{onClick:()=>{i("#1692ff"),h("#1692ff"),s(n==="dark"?"#3E3E3E":"#d9d9d9"),u(n==="dark"?"#1f1f1f":"#fafafa"),f(n==="dark"?"#e0e0e0":"#1f1f1f"),g(14),y(6),C(!0),S("none"),E(500),T("middle")},children:"重置默认"}),V.jsx(nn,{type:"primary",onClick:L,style:{marginLeft:8},children:"确定"})]})]})};function Aa(e,t){const n=[];function r(o,i=[]){for(const a of o){const s=[...i,a];if(a.key===t){n.push(...s);return}a.children&&r(a.children,s)}}return r(e),n.map(o=>({title:o.label}))}function wo(){return wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},wo.apply(this,arguments)}const hp=(e,t)=>d.createElement(kt,wo({},e,{ref:t,icon:ip})),mp=d.forwardRef(hp);function Eo(){return Eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Eo.apply(this,arguments)}const gp=(e,t)=>d.createElement(kt,Eo({},e,{ref:t,icon:Kg})),Ar=d.forwardRef(gp);function Oo(){return Oo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Oo.apply(this,arguments)}const pp=(e,t)=>d.createElement(kt,Oo({},e,{ref:t,icon:ap})),vp=d.forwardRef(pp);function $o(){return $o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$o.apply(this,arguments)}const bp=(e,t)=>d.createElement(kt,$o({},e,{ref:t,icon:sp})),yp=d.forwardRef(bp);function Po(){return Po=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Po.apply(this,arguments)}const Cp=(e,t)=>d.createElement(kt,Po({},e,{ref:t,icon:lp})),xp=d.forwardRef(Cp);function To(){return To=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},To.apply(this,arguments)}const Sp=(e,t)=>d.createElement(kt,To({},e,{ref:t,icon:cp})),wp=d.forwardRef(Sp);function Ro(){return Ro=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ro.apply(this,arguments)}const Ep=(e,t)=>d.createElement(kt,Ro({},e,{ref:t,icon:up})),Op=d.forwardRef(Ep);function Mo(){return Mo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Mo.apply(this,arguments)}const $p=(e,t)=>d.createElement(kt,Mo({},e,{ref:t,icon:dp})),Pp=d.forwardRef($p),Hc=d.createContext({});function Tp(e){const t=d.useRef(null);return t.current===null&&(t.current=e()),t.current}const ci=typeof window<"u",Rp=ci?d.useLayoutEffect:d.useEffect,ui=d.createContext(null);function di(e,t){e.indexOf(t)===-1&&e.push(t)}function fi(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const Ot=(e,t,n)=>n>t?t:n<e?e:n;let hi=()=>{};const $t={},Wc=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function qc(e){return typeof e=="object"&&e!==null}const Gc=e=>/^0[^.\s]+$/u.test(e);function mi(e){let t;return()=>(t===void 0&&(t=e()),t)}const nt=e=>e,Mp=(e,t)=>n=>t(e(n)),qn=(...e)=>e.reduce(Mp),In=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r};class gi{constructor(){this.subscriptions=[]}add(t){return di(this.subscriptions,t),()=>fi(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const a=this.subscriptions[i];a&&a(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const ft=e=>e*1e3,ht=e=>e/1e3;function Uc(e,t){return t?e*(1e3/t):0}const Xc=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Np=1e-7,jp=12;function Dp(e,t,n,r,o){let i,a,s=0;do a=t+(n-t)/2,i=Xc(a,r,o)-e,i>0?n=a:t=a;while(Math.abs(i)>Np&&++s<jp);return a}function Gn(e,t,n,r){if(e===t&&n===r)return nt;const o=i=>Dp(i,0,1,e,n);return i=>i===0||i===1?i:Xc(o(i),t,r)}const Kc=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Yc=e=>t=>1-e(1-t),Zc=Gn(.33,1.53,.69,.99),pi=Yc(Zc),Qc=Kc(pi),Jc=e=>(e*=2)<1?.5*pi(e):.5*(2-Math.pow(2,-10*(e-1))),vi=e=>1-Math.sin(Math.acos(e)),eu=Yc(vi),tu=Kc(vi),Ip=Gn(.42,0,1,1),Ap=Gn(0,0,.58,1),nu=Gn(.42,0,.58,1),Vp=e=>Array.isArray(e)&&typeof e[0]!="number",ru=e=>Array.isArray(e)&&typeof e[0]=="number",kp={linear:nt,easeIn:Ip,easeInOut:nu,easeOut:Ap,circIn:vi,circInOut:tu,circOut:eu,backIn:pi,backInOut:Qc,backOut:Zc,anticipate:Jc},Bp=e=>typeof e=="string",Va=e=>{if(ru(e)){hi(e.length===4);const[t,n,r,o]=e;return Gn(t,n,r,o)}else if(Bp(e))return kp[e];return e},Jn=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ka={value:null};function Lp(e,t){let n=new Set,r=new Set,o=!1,i=!1;const a=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(f){a.has(f)&&(c.schedule(f),e()),l++,f(s)}const c={schedule:(f,m=!1,h=!1)=>{const g=h&&o?n:r;return m&&a.add(f),g.has(f)||g.add(f),f},cancel:f=>{r.delete(f),a.delete(f)},process:f=>{if(s=f,o){i=!0;return}o=!0,[n,r]=[r,n],n.forEach(u),t&&ka.value&&ka.value.frameloop[t].push(l),l=0,n.clear(),o=!1,i&&(i=!1,c.process(f))}};return c}const Fp=40;function ou(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,a=Jn.reduce((C,x)=>(C[x]=Lp(i,t?x:void 0),C),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:c,update:f,preRender:m,render:h,postRender:b}=a,g=()=>{const C=$t.useManualTiming?o.timestamp:performance.now();n=!1,$t.useManualTiming||(o.delta=r?1e3/60:Math.max(Math.min(C-o.timestamp,Fp),1)),o.timestamp=C,o.isProcessing=!0,s.process(o),l.process(o),u.process(o),c.process(o),f.process(o),m.process(o),h.process(o),b.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(g))},v=()=>{n=!0,r=!0,o.isProcessing||e(g)};return{schedule:Jn.reduce((C,x)=>{const S=a[x];return C[x]=(w,E=!1,O=!1)=>(n||v(),S.schedule(w,E,O)),C},{}),cancel:C=>{for(let x=0;x<Jn.length;x++)a[Jn[x]].cancel(C)},state:o,steps:a}}const{schedule:Te,cancel:At,state:Fe,steps:Vr}=ou(typeof requestAnimationFrame<"u"?requestAnimationFrame:nt,!0);let or;function _p(){or=void 0}const Ze={now:()=>(or===void 0&&Ze.set(Fe.isProcessing||$t.useManualTiming?Fe.timestamp:performance.now()),or),set:e=>{or=e,queueMicrotask(_p)}},iu=e=>t=>typeof t=="string"&&t.startsWith(e),bi=iu("--"),zp=iu("var(--"),yi=e=>zp(e)?Hp.test(e.split("/*")[0].trim()):!1,Hp=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Cn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},An={...Cn,transform:e=>Ot(0,1,e)},er={...Cn,default:1},Pn=e=>Math.round(e*1e5)/1e5,Ci=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Wp(e){return e==null}const qp=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,xi=(e,t)=>n=>!!(typeof n=="string"&&qp.test(n)&&n.startsWith(e)||t&&!Wp(n)&&Object.prototype.hasOwnProperty.call(n,t)),au=(e,t,n)=>r=>{if(typeof r!="string")return r;const[o,i,a,s]=r.match(Ci);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(a),alpha:s!==void 0?parseFloat(s):1}},Gp=e=>Ot(0,255,e),kr={...Cn,transform:e=>Math.round(Gp(e))},Kt={test:xi("rgb","red"),parse:au("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+kr.transform(e)+", "+kr.transform(t)+", "+kr.transform(n)+", "+Pn(An.transform(r))+")"};function Up(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const No={test:xi("#"),parse:Up,transform:Kt.transform},Un=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),jt=Un("deg"),mt=Un("%"),te=Un("px"),Xp=Un("vh"),Kp=Un("vw"),Ba={...mt,parse:e=>mt.parse(e)/100,transform:e=>mt.transform(e*100)},cn={test:xi("hsl","hue"),parse:au("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+mt.transform(Pn(t))+", "+mt.transform(Pn(n))+", "+Pn(An.transform(r))+")"},He={test:e=>Kt.test(e)||No.test(e)||cn.test(e),parse:e=>Kt.test(e)?Kt.parse(e):cn.test(e)?cn.parse(e):No.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Kt.transform(e):cn.transform(e)},Yp=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Zp(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(Ci))==null?void 0:t.length)||0)+(((n=e.match(Yp))==null?void 0:n.length)||0)>0}const su="number",lu="color",Qp="var",Jp="var(",La="${}",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Vn(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let i=0;const s=t.replace(ev,l=>(He.test(l)?(r.color.push(i),o.push(lu),n.push(He.parse(l))):l.startsWith(Jp)?(r.var.push(i),o.push(Qp),n.push(l)):(r.number.push(i),o.push(su),n.push(parseFloat(l))),++i,La)).split(La);return{values:n,split:s,indexes:r,types:o}}function cu(e){return Vn(e).values}function uu(e){const{split:t,types:n}=Vn(e),r=t.length;return o=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],o[a]!==void 0){const s=n[a];s===su?i+=Pn(o[a]):s===lu?i+=He.transform(o[a]):i+=o[a]}return i}}const tv=e=>typeof e=="number"?0:e;function nv(e){const t=cu(e);return uu(e)(t.map(tv))}const Vt={test:Zp,parse:cu,createTransformer:uu,getAnimatableNone:nv};function Br(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function rv({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,a=0;if(!t)o=i=a=n;else{const s=n<.5?n*(1+t):n+t-n*t,l=2*n-s;o=Br(l,s,e+1/3),i=Br(l,s,e),a=Br(l,s,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(a*255),alpha:r}}function gr(e,t){return n=>n>0?t:e}const Pe=(e,t,n)=>e+(t-e)*n,Lr=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},ov=[No,Kt,cn],iv=e=>ov.find(t=>t.test(e));function Fa(e){const t=iv(e);if(!t)return!1;let n=t.parse(e);return t===cn&&(n=rv(n)),n}const _a=(e,t)=>{const n=Fa(e),r=Fa(t);if(!n||!r)return gr(e,t);const o={...n};return i=>(o.red=Lr(n.red,r.red,i),o.green=Lr(n.green,r.green,i),o.blue=Lr(n.blue,r.blue,i),o.alpha=Pe(n.alpha,r.alpha,i),Kt.transform(o))},jo=new Set(["none","hidden"]);function av(e,t){return jo.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function sv(e,t){return n=>Pe(e,t,n)}function Si(e){return typeof e=="number"?sv:typeof e=="string"?yi(e)?gr:He.test(e)?_a:uv:Array.isArray(e)?du:typeof e=="object"?He.test(e)?_a:lv:gr}function du(e,t){const n=[...e],r=n.length,o=e.map((i,a)=>Si(i)(i,t[a]));return i=>{for(let a=0;a<r;a++)n[a]=o[a](i);return n}}function lv(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=Si(e[o])(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}}function cv(e,t){const n=[],r={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const i=t.types[o],a=e.indexes[i][r[i]],s=e.values[a]??0;n[o]=s,r[i]++}return n}const uv=(e,t)=>{const n=Vt.createTransformer(t),r=Vn(e),o=Vn(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?jo.has(e)&&!o.values.length||jo.has(t)&&!r.values.length?av(e,t):qn(du(cv(r,o),o.values),n):gr(e,t)};function fu(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?Pe(e,t,n):Si(e)(e,t)}const dv=e=>{const t=({timestamp:n})=>e(n);return{start:(n=!0)=>Te.update(t,n),stop:()=>At(t),now:()=>Fe.isProcessing?Fe.timestamp:Ze.now()}},hu=(e,t,n=10)=>{let r="";const o=Math.max(Math.round(t/n),2);for(let i=0;i<o;i++)r+=e(i/(o-1))+", ";return`linear(${r.substring(0,r.length-2)})`},pr=2e4;function wi(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<pr;)t+=n,r=e.next(t);return t>=pr?1/0:t}function fv(e,t=100,n){const r=n({...e,keyframes:[0,t]}),o=Math.min(wi(r),pr);return{type:"keyframes",ease:i=>r.next(o*i).value/t,duration:ht(o)}}const hv=5;function mu(e,t,n){const r=Math.max(t-hv,0);return Uc(n-e(r),t-r)}const Re={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},za=.001;function mv({duration:e=Re.duration,bounce:t=Re.bounce,velocity:n=Re.velocity,mass:r=Re.mass}){let o,i,a=1-t;a=Ot(Re.minDamping,Re.maxDamping,a),e=Ot(Re.minDuration,Re.maxDuration,ht(e)),a<1?(o=u=>{const c=u*a,f=c*e,m=c-n,h=Do(u,a),b=Math.exp(-f);return za-m/h*b},i=u=>{const f=u*a*e,m=f*n+n,h=Math.pow(a,2)*Math.pow(u,2)*e,b=Math.exp(-f),g=Do(Math.pow(u,2),a);return(-o(u)+za>0?-1:1)*((m-h)*b)/g}):(o=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-.001+c*f},i=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const s=5/e,l=pv(o,i,s);if(e=ft(e),isNaN(l))return{stiffness:Re.stiffness,damping:Re.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:a*2*Math.sqrt(r*u),duration:e}}}const gv=12;function pv(e,t,n){let r=n;for(let o=1;o<gv;o++)r=r-e(r)/t(r);return r}function Do(e,t){return e*Math.sqrt(1-t*t)}const vv=["duration","bounce"],bv=["stiffness","damping","mass"];function Ha(e,t){return t.some(n=>e[n]!==void 0)}function yv(e){let t={velocity:Re.velocity,stiffness:Re.stiffness,damping:Re.damping,mass:Re.mass,isResolvedFromDuration:!1,...e};if(!Ha(e,bv)&&Ha(e,vv))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),o=r*r,i=2*Ot(.05,1,1-(e.bounce||0))*Math.sqrt(o);t={...t,mass:Re.mass,stiffness:o,damping:i}}else{const n=mv(e);t={...t,...n,mass:Re.mass},t.isResolvedFromDuration=!0}return t}function vr(e=Re.visualDuration,t=Re.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:o}=n;const i=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],s={done:!1,value:i},{stiffness:l,damping:u,mass:c,duration:f,velocity:m,isResolvedFromDuration:h}=yv({...n,velocity:-ht(n.velocity||0)}),b=m||0,g=u/(2*Math.sqrt(l*c)),v=a-i,y=ht(Math.sqrt(l/c)),p=Math.abs(v)<5;r||(r=p?Re.restSpeed.granular:Re.restSpeed.default),o||(o=p?Re.restDelta.granular:Re.restDelta.default);let C;if(g<1){const S=Do(y,g);C=w=>{const E=Math.exp(-g*y*w);return a-E*((b+g*y*v)/S*Math.sin(S*w)+v*Math.cos(S*w))}}else if(g===1)C=S=>a-Math.exp(-y*S)*(v+(b+y*v)*S);else{const S=y*Math.sqrt(g*g-1);C=w=>{const E=Math.exp(-g*y*w),O=Math.min(S*w,300);return a-E*((b+g*y*v)*Math.sinh(O)+S*v*Math.cosh(O))/S}}const x={calculatedDuration:h&&f||null,next:S=>{const w=C(S);if(h)s.done=S>=f;else{let E=S===0?b:0;g<1&&(E=S===0?ft(b):mu(C,S,w));const O=Math.abs(E)<=r,T=Math.abs(a-w)<=o;s.done=O&&T}return s.value=s.done?a:w,s},toString:()=>{const S=Math.min(wi(x),pr),w=hu(E=>x.next(S*E).value,S,30);return S+"ms "+w},toTransition:()=>{}};return x}vr.applyToOptions=e=>{const t=fv(e,100,vr);return e.ease=t.ease,e.duration=ft(t.duration),e.type="keyframes",e};function Io({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){const f=e[0],m={done:!1,value:f},h=O=>s!==void 0&&O<s||l!==void 0&&O>l,b=O=>s===void 0?l:l===void 0||Math.abs(s-O)<Math.abs(l-O)?s:l;let g=n*t;const v=f+g,y=a===void 0?v:a(v);y!==v&&(g=y-f);const p=O=>-g*Math.exp(-O/r),C=O=>y+p(O),x=O=>{const T=p(O),P=C(O);m.done=Math.abs(T)<=u,m.value=m.done?y:P};let S,w;const E=O=>{h(m.value)&&(S=O,w=vr({keyframes:[m.value,b(m.value)],velocity:mu(C,O,m.value),damping:o,stiffness:i,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:O=>{let T=!1;return!w&&S===void 0&&(T=!0,x(O),E(O)),S!==void 0&&O>=S?w.next(O-S):(!T&&x(O),m)}}}function Cv(e,t,n){const r=[],o=n||$t.mix||fu,i=e.length-1;for(let a=0;a<i;a++){let s=o(e[a],e[a+1]);if(t){const l=Array.isArray(t)?t[a]||nt:t;s=qn(l,s)}r.push(s)}return r}function xv(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(hi(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const a=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=Cv(t,r,o),l=s.length,u=c=>{if(a&&c<e[0])return t[0];let f=0;if(l>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const m=In(e[f],e[f+1],c);return s[f](m)};return n?c=>u(Ot(e[0],e[i-1],c)):u}function Sv(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=In(0,t,r);e.push(Pe(n,1,o))}}function wv(e){const t=[0];return Sv(t,e.length-1),t}function Ev(e,t){return e.map(n=>n*t)}function Ov(e,t){return e.map(()=>t||nu).splice(0,e.length-1)}function Tn({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=Vp(r)?r.map(Va):Va(r),i={done:!1,value:t[0]},a=Ev(n&&n.length===t.length?n:wv(t),e),s=xv(a,t,{ease:Array.isArray(o)?o:Ov(t,o)});return{calculatedDuration:e,next:l=>(i.value=s(l),i.done=l>=e,i)}}const $v=e=>e!==null;function Ei(e,{repeat:t,repeatType:n="loop"},r,o=1){const i=e.filter($v),s=o<0||t&&n!=="loop"&&t%2===1?0:i.length-1;return!s||r===void 0?i[s]:r}const Pv={decay:Io,inertia:Io,tween:Tn,keyframes:Tn,spring:vr};function gu(e){typeof e.type=="string"&&(e.type=Pv[e.type])}class Oi{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,n){return this.finished.then(t,n)}}const Tv=e=>e/100;class $i extends Oi{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(n=!0)=>{var r,o;if(n){const{motionValue:i}=this.options;i&&i.updatedAt!==Ze.now()&&this.tick(Ze.now())}this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(o=(r=this.options).onStop)==null||o.call(r))},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;gu(t);const{type:n=Tn,repeat:r=0,repeatDelay:o=0,repeatType:i,velocity:a=0}=t;let{keyframes:s}=t;const l=n||Tn;l!==Tn&&typeof s[0]!="number"&&(this.mixKeyframes=qn(Tv,fu(s[0],s[1])),s=[0,100]);const u=l({...t,keyframes:s});i==="mirror"&&(this.mirroredGenerator=l({...t,keyframes:[...s].reverse(),velocity:-a})),u.calculatedDuration===null&&(u.calculatedDuration=wi(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+o,this.totalDuration=this.resolvedDuration*(r+1)-o,this.generator=u}updateTime(t){const n=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(t,n=!1){const{generator:r,totalDuration:o,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:l}=this;if(this.startTime===null)return r.next(0);const{delay:u=0,keyframes:c,repeat:f,repeatType:m,repeatDelay:h,type:b,onUpdate:g,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-o/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),p=this.playbackSpeed>=0?y<0:y>o;this.currentTime=Math.max(y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=o);let C=this.currentTime,x=r;if(f){const O=Math.min(this.currentTime,o)/s;let T=Math.floor(O),P=O%1;!P&&O>=1&&(P=1),P===1&&T--,T=Math.min(T,f+1),!!(T%2)&&(m==="reverse"?(P=1-P,h&&(P-=h/s)):m==="mirror"&&(x=a)),C=Ot(0,1,P)*s}const S=p?{done:!1,value:c[0]}:x.next(C);i&&(S.value=i(S.value));let{done:w}=S;!p&&l!==null&&(w=this.playbackSpeed>=0?this.currentTime>=o:this.currentTime<=0);const E=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&w);return E&&b!==Io&&(S.value=Ei(c,this.options,v,this.speed)),g&&g(S.value),E&&this.finish(),S}then(t,n){return this.finished.then(t,n)}get duration(){return ht(this.calculatedDuration)}get time(){return ht(this.currentTime)}set time(t){var n;t=ft(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(Ze.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=ht(this.currentTime))}play(){var o,i;if(this.isStopped)return;const{driver:t=dv,startTime:n}=this.options;this.driver||(this.driver=t(a=>this.tick(a))),(i=(o=this.options).onPlay)==null||i.call(o);const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=n??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Ze.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(t=this.options).onComplete)==null||n.call(t)}cancel(){var t,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(t=this.options).onCancel)==null||n.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),t.observe(this)}}function Rv(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const Yt=e=>e*180/Math.PI,Ao=e=>{const t=Yt(Math.atan2(e[1],e[0]));return Vo(t)},Mv={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Ao,rotateZ:Ao,skewX:e=>Yt(Math.atan(e[1])),skewY:e=>Yt(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Vo=e=>(e=e%360,e<0&&(e+=360),e),Wa=Ao,qa=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),Ga=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),Nv={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:qa,scaleY:Ga,scale:e=>(qa(e)+Ga(e))/2,rotateX:e=>Vo(Yt(Math.atan2(e[6],e[5]))),rotateY:e=>Vo(Yt(Math.atan2(-e[2],e[0]))),rotateZ:Wa,rotate:Wa,skewX:e=>Yt(Math.atan(e[4])),skewY:e=>Yt(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function ko(e){return e.includes("scale")?1:0}function Bo(e,t){if(!e||e==="none")return ko(t);const n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,o;if(n)r=Nv,o=n;else{const s=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=Mv,o=s}if(!o)return ko(t);const i=r[t],a=o[1].split(",").map(Dv);return typeof i=="function"?i(a):a[i]}const jv=(e,t)=>{const{transform:n="none"}=getComputedStyle(e);return Bo(n,t)};function Dv(e){return parseFloat(e.trim())}const xn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Sn=new Set(xn),Ua=e=>e===Cn||e===te,Iv=new Set(["x","y","z"]),Av=xn.filter(e=>!Iv.has(e));function Vv(e){const t=[];return Av.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Jt={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Bo(t,"x"),y:(e,{transform:t})=>Bo(t,"y")};Jt.translateX=Jt.x;Jt.translateY=Jt.y;const en=new Set;let Lo=!1,Fo=!1,_o=!1;function pu(){if(Fo){const e=Array.from(en).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=Vv(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([i,a])=>{var s;(s=r.getValue(i))==null||s.set(a)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Fo=!1,Lo=!1,en.forEach(e=>e.complete(_o)),en.clear()}function vu(){en.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Fo=!0)})}function kv(){_o=!0,vu(),pu(),_o=!1}class Pi{constructor(t,n,r,o,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(en.add(this),Lo||(Lo=!0,Te.read(vu),Te.resolveKeyframes(pu))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;if(t[0]===null){const i=o==null?void 0:o.get(),a=t[t.length-1];if(i!==void 0)t[0]=i;else if(r&&n){const s=r.readValue(n,a);s!=null&&(t[0]=s)}t[0]===void 0&&(t[0]=a),o&&i===void 0&&o.set(t[0])}Rv(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),en.delete(this)}cancel(){this.state==="scheduled"&&(en.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Bv=e=>e.startsWith("--");function Lv(e,t,n){Bv(t)?e.style.setProperty(t,n):e.style[t]=n}const Fv=mi(()=>window.ScrollTimeline!==void 0),_v={};function zv(e,t){const n=mi(e);return()=>_v[t]??n()}const bu=zv(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),$n=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Xa={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:$n([0,.65,.55,1]),circOut:$n([.55,0,1,.45]),backIn:$n([.31,.01,.66,-.59]),backOut:$n([.33,1.53,.69,.99])};function yu(e,t){if(e)return typeof e=="function"?bu()?hu(e,t):"ease-out":ru(e)?$n(e):Array.isArray(e)?e.map(n=>yu(n,t)||Xa.easeOut):Xa[e]}function Hv(e,t,n,{delay:r=0,duration:o=300,repeat:i=0,repeatType:a="loop",ease:s="easeOut",times:l}={},u=void 0){const c={[t]:n};l&&(c.offset=l);const f=yu(s,o);Array.isArray(f)&&(c.easing=f);const m={delay:r,duration:o,easing:Array.isArray(f)?"linear":f,fill:"both",iterations:i+1,direction:a==="reverse"?"alternate":"normal"};return u&&(m.pseudoElement=u),e.animate(c,m)}function Cu(e){return typeof e=="function"&&"applyToOptions"in e}function Wv({type:e,...t}){return Cu(e)&&bu()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class qv extends Oi{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:r,keyframes:o,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:s,onComplete:l}=t;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=t,hi(typeof t.type!="string");const u=Wv(t);this.animation=Hv(n,r,o,u,i),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const c=Ei(o,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(c):Lv(n,r,c),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,n;(n=(t=this.animation).finish)==null||n.call(t)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,n;this.isPseudoElement||(n=(t=this.animation).commitStyles)==null||n.call(t)}get duration(){var n,r;const t=((r=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:r.call(n).duration)||0;return ht(Number(t))}get time(){return ht(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=ft(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){var r;return this.allowFlatten&&((r=this.animation.effect)==null||r.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&Fv()?(this.animation.timeline=t,nt):n(this)}}const xu={anticipate:Jc,backInOut:Qc,circInOut:tu};function Gv(e){return e in xu}function Uv(e){typeof e.ease=="string"&&Gv(e.ease)&&(e.ease=xu[e.ease])}const Ka=10;class Xv extends qv{constructor(t){Uv(t),gu(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:r,onComplete:o,element:i,...a}=this.options;if(!n)return;if(t!==void 0){n.set(t);return}const s=new $i({...a,autoplay:!1}),l=ft(this.finishedTime??this.time);n.setWithVelocity(s.sample(l-Ka).value,s.sample(l).value,Ka),s.stop()}}const Ya=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Vt.test(e)||e==="0")&&!e.startsWith("url("));function Kv(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Yv(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],a=Ya(o,t),s=Ya(i,t);return!a||!s?!1:Kv(e)||(n==="spring"||Cu(n))&&r}function Su(e){return qc(e)&&"offsetHeight"in e}const Zv=new Set(["opacity","clipPath","filter","transform"]),Qv=mi(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Jv(e){var u;const{motionValue:t,name:n,repeatDelay:r,repeatType:o,damping:i,type:a}=e;if(!Su((u=t==null?void 0:t.owner)==null?void 0:u.current))return!1;const{onUpdate:s,transformTemplate:l}=t.owner.getProps();return Qv()&&n&&Zv.has(n)&&(n!=="transform"||!l)&&!s&&!r&&o!=="mirror"&&i!==0&&a!=="inertia"}const eb=40;class tb extends Oi{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:i=0,repeatType:a="loop",keyframes:s,name:l,motionValue:u,element:c,...f}){var b;super(),this.stop=()=>{var g,v;this._animation&&(this._animation.stop(),(g=this.stopTimeline)==null||g.call(this)),(v=this.keyframeResolver)==null||v.cancel()},this.createdAt=Ze.now();const m={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:i,repeatType:a,name:l,motionValue:u,element:c,...f},h=(c==null?void 0:c.KeyframeResolver)||Pi;this.keyframeResolver=new h(s,(g,v,y)=>this.onKeyframesResolved(g,v,m,!y),l,u,c),(b=this.keyframeResolver)==null||b.scheduleResolve()}onKeyframesResolved(t,n,r,o){this.keyframeResolver=void 0;const{name:i,type:a,velocity:s,delay:l,isHandoff:u,onUpdate:c}=r;this.resolvedAt=Ze.now(),Yv(t,i,a,s)||(($t.instantAnimations||!l)&&(c==null||c(Ei(t,r,n))),t[0]=t[t.length-1],r.duration=0,r.repeat=0);const m={startTime:o?this.resolvedAt?this.resolvedAt-this.createdAt>eb?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...r,keyframes:t},h=!u&&Jv(m)?new Xv({...m,element:m.motionValue.owner.current}):new $i(m);h.finished.then(()=>this.notifyFinished()).catch(nt),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(t,n){return this.finished.finally(t).then(()=>{})}get animation(){var t;return this._animation||((t=this.keyframeResolver)==null||t.resume(),kv()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),(t=this.keyframeResolver)==null||t.cancel()}}const nb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function rb(e){const t=nb.exec(e);if(!t)return[,];const[,n,r,o]=t;return[`--${n??r}`,o]}function wu(e,t,n=1){const[r,o]=rb(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const a=i.trim();return Wc(a)?parseFloat(a):a}return yi(o)?wu(o,t,n+1):o}function Ti(e,t){return(e==null?void 0:e[t])??(e==null?void 0:e.default)??e}const Eu=new Set(["width","height","top","left","right","bottom",...xn]),ob={test:e=>e==="auto",parse:e=>e},Ou=e=>t=>t.test(e),$u=[Cn,te,mt,jt,Kp,Xp,ob],Za=e=>$u.find(Ou(e));function ib(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Gc(e):!0}const ab=new Set(["brightness","contrast","saturate","opacity"]);function sb(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Ci)||[];if(!r)return e;const o=n.replace(r,"");let i=ab.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const lb=/\b([a-z-]*)\(.*?\)/gu,zo={...Vt,getAnimatableNone:e=>{const t=e.match(lb);return t?t.map(sb).join(" "):e}},Qa={...Cn,transform:Math.round},cb={rotate:jt,rotateX:jt,rotateY:jt,rotateZ:jt,scale:er,scaleX:er,scaleY:er,scaleZ:er,skew:jt,skewX:jt,skewY:jt,distance:te,translateX:te,translateY:te,translateZ:te,x:te,y:te,z:te,perspective:te,transformPerspective:te,opacity:An,originX:Ba,originY:Ba,originZ:te},Ri={borderWidth:te,borderTopWidth:te,borderRightWidth:te,borderBottomWidth:te,borderLeftWidth:te,borderRadius:te,radius:te,borderTopLeftRadius:te,borderTopRightRadius:te,borderBottomRightRadius:te,borderBottomLeftRadius:te,width:te,maxWidth:te,height:te,maxHeight:te,top:te,right:te,bottom:te,left:te,padding:te,paddingTop:te,paddingRight:te,paddingBottom:te,paddingLeft:te,margin:te,marginTop:te,marginRight:te,marginBottom:te,marginLeft:te,backgroundPositionX:te,backgroundPositionY:te,...cb,zIndex:Qa,fillOpacity:An,strokeOpacity:An,numOctaves:Qa},ub={...Ri,color:He,backgroundColor:He,outlineColor:He,fill:He,stroke:He,borderColor:He,borderTopColor:He,borderRightColor:He,borderBottomColor:He,borderLeftColor:He,filter:zo,WebkitFilter:zo},Pu=e=>ub[e];function Tu(e,t){let n=Pu(e);return n!==zo&&(n=Vt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const db=new Set(["auto","none","0"]);function fb(e,t,n){let r=0,o;for(;r<e.length&&!o;){const i=e[r];typeof i=="string"&&!db.has(i)&&Vn(i).values.length&&(o=e[r]),r++}if(o&&n)for(const i of t)e[i]=Tu(n,o)}class hb extends Pi{constructor(t,n,r,o,i){super(t,n,r,o,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),yi(u))){const c=wu(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Eu.has(r)||t.length!==2)return;const[o,i]=t,a=Za(o),s=Za(i);if(a!==s)if(Ua(a)&&Ua(s))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else Jt[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)(t[o]===null||ib(t[o]))&&r.push(o);r.length&&fb(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Jt[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){var s;const{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;const o=t.getValue(n);o&&o.jump(this.measuredOrigin,!1);const i=r.length-1,a=r[i];r[i]=Jt[n](t.measureViewportBox(),window.getComputedStyle(t.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),(s=this.removedTransforms)!=null&&s.length&&this.removedTransforms.forEach(([l,u])=>{t.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function mb(e,t,n){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let r=document;const o=(n==null?void 0:n[e])??r.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}const Ja=30,gb=e=>!isNaN(parseFloat(e));class pb{constructor(t,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{var a,s;const i=Ze.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&((a=this.events.change)==null||a.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();o&&((s=this.events.renderRequest)==null||s.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Ze.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=gb(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new gi);const r=this.events[t].add(n);return t==="change"?()=>{r(),Te.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;(t=this.events.change)==null||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Ze.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Ja)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Ja);return Uc(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,n;(t=this.dependents)==null||t.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function pn(e,t){return new pb(e,t)}const Ru=(e,t)=>t&&typeof e=="number"?t.transform(e):e,{schedule:Mi}=ou(queueMicrotask,!1),it={x:!1,y:!1};function Mu(){return it.x||it.y}function vb(e){return e==="x"||e==="y"?it[e]?null:(it[e]=!0,()=>{it[e]=!1}):it.x||it.y?null:(it.x=it.y=!0,()=>{it.x=it.y=!1})}function Nu(e,t){const n=mb(e),r=new AbortController,o={passive:!0,...t,signal:r.signal};return[n,o,()=>r.abort()]}function es(e){return!(e.pointerType==="touch"||Mu())}function bb(e,t,n={}){const[r,o,i]=Nu(e,n),a=s=>{if(!es(s))return;const{target:l}=s,u=t(l,s);if(typeof u!="function"||!l)return;const c=f=>{es(f)&&(u(f),l.removeEventListener("pointerleave",c))};l.addEventListener("pointerleave",c,o)};return r.forEach(s=>{s.addEventListener("pointerenter",a,o)}),i}const ju=(e,t)=>t?e===t?!0:ju(e,t.parentElement):!1,Ni=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,yb=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Cb(e){return yb.has(e.tagName)||e.tabIndex!==-1}const ir=new WeakSet;function ts(e){return t=>{t.key==="Enter"&&e(t)}}function Fr(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const xb=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=ts(()=>{if(ir.has(n))return;Fr(n,"down");const o=ts(()=>{Fr(n,"up")}),i=()=>Fr(n,"cancel");n.addEventListener("keyup",o,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function ns(e){return Ni(e)&&!Mu()}function Sb(e,t,n={}){const[r,o,i]=Nu(e,n),a=s=>{const l=s.currentTarget;if(!ns(s))return;ir.add(l);const u=t(l,s),c=(h,b)=>{window.removeEventListener("pointerup",f),window.removeEventListener("pointercancel",m),ir.has(l)&&ir.delete(l),ns(h)&&typeof u=="function"&&u(h,{success:b})},f=h=>{c(h,l===window||l===document||n.useGlobalTarget||ju(l,h.target))},m=h=>{c(h,!1)};window.addEventListener("pointerup",f,o),window.addEventListener("pointercancel",m,o)};return r.forEach(s=>{(n.useGlobalTarget?window:s).addEventListener("pointerdown",a,o),Su(s)&&(s.addEventListener("focus",u=>xb(u,o)),!Cb(s)&&!s.hasAttribute("tabindex")&&(s.tabIndex=0))}),i}function Du(e){return qc(e)&&"ownerSVGElement"in e}function wb(e){return Du(e)&&e.tagName==="svg"}const We=e=>!!(e&&e.getVelocity),Eb=[...$u,He,Vt],Ob=e=>Eb.find(Ou(e)),Iu=d.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});function $b(e=!0){const t=d.useContext(ui);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:o}=t,i=d.useId();d.useEffect(()=>{if(e)return o(i)},[e]);const a=d.useCallback(()=>e&&r&&r(i),[i,r,e]);return!n&&r?[!1,a]:[!0]}const Au=d.createContext({strict:!1}),rs={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},vn={};for(const e in rs)vn[e]={isEnabled:t=>rs[e].some(n=>!!t[n])};function Pb(e){for(const t in e)vn[t]={...vn[t],...e[t]}}const Tb=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function br(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Tb.has(e)}let Vu=e=>!br(e);function Rb(e){e&&(Vu=t=>t.startsWith("on")?!br(t):e(t))}try{Rb(require("@emotion/is-prop-valid").default)}catch{}function Mb(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Vu(o)||n===!0&&br(o)||!t&&!br(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function Nb(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}const Or=d.createContext({});function $r(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function kn(e){return typeof e=="string"||Array.isArray(e)}const ji=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Di=["initial",...ji];function Pr(e){return $r(e.animate)||Di.some(t=>kn(e[t]))}function ku(e){return!!(Pr(e)||e.variants)}function jb(e,t){if(Pr(e)){const{initial:n,animate:r}=e;return{initial:n===!1||kn(n)?n:void 0,animate:kn(r)?r:void 0}}return e.inherit!==!1?t:{}}function Db(e){const{initial:t,animate:n}=jb(e,d.useContext(Or));return d.useMemo(()=>({initial:t,animate:n}),[os(t),os(n)])}function os(e){return Array.isArray(e)?e.join(" "):e}const Ib=Symbol.for("motionComponentSymbol");function un(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Ab(e,t,n){return d.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):un(n)&&(n.current=r))},[t])}const Ii=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Vb="framerAppearId",Bu="data-"+Ii(Vb),Lu=d.createContext({});function kb(e,t,n,r,o){var g,v;const{visualElement:i}=d.useContext(Or),a=d.useContext(Au),s=d.useContext(ui),l=d.useContext(Iu).reducedMotion,u=d.useRef(null);r=r||a.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const c=u.current,f=d.useContext(Lu);c&&!c.projection&&o&&(c.type==="html"||c.type==="svg")&&Bb(u.current,n,o,f);const m=d.useRef(!1);d.useInsertionEffect(()=>{c&&m.current&&c.update(n,s)});const h=n[Bu],b=d.useRef(!!h&&!((g=window.MotionHandoffIsComplete)!=null&&g.call(window,h))&&((v=window.MotionHasOptimisedAnimation)==null?void 0:v.call(window,h)));return Rp(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),Mi.render(c.render),b.current&&c.animationState&&c.animationState.animateChanges())}),d.useEffect(()=>{c&&(!b.current&&c.animationState&&c.animationState.animateChanges(),b.current&&(queueMicrotask(()=>{var y;(y=window.MotionHandoffMarkAsComplete)==null||y.call(window,h)}),b.current=!1))}),c}function Bb(e,t,n,r){const{layoutId:o,layout:i,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Fu(e.parent)),e.projection.setOptions({layoutId:o,layout:i,alwaysMeasureLayout:!!a||s&&un(s),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}function Fu(e){if(e)return e.options.allowProjection!==!1?e.projection:Fu(e.parent)}function Lb({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){e&&Pb(e);function i(s,l){let u;const c={...d.useContext(Iu),...s,layoutId:Fb(s)},{isStatic:f}=c,m=Db(s),h=r(s,f);if(!f&&ci){_b();const b=zb(c);u=b.MeasureLayout,m.visualElement=kb(o,h,c,t,b.ProjectionNode)}return V.jsxs(Or.Provider,{value:m,children:[u&&m.visualElement?V.jsx(u,{visualElement:m.visualElement,...c}):null,n(o,s,Ab(h,m.visualElement,l),h,f,m.visualElement)]})}i.displayName=`motion.${typeof o=="string"?o:`create(${o.displayName??o.name??""})`}`;const a=d.forwardRef(i);return a[Ib]=o,a}function Fb({layoutId:e}){const t=d.useContext(Hc).id;return t&&e!==void 0?t+"-"+e:e}function _b(e,t){d.useContext(Au).strict}function zb(e){const{drag:t,layout:n}=vn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const Bn={};function Hb(e){for(const t in e)Bn[t]=e[t],bi(t)&&(Bn[t].isCSSVariable=!0)}function _u(e,{layout:t,layoutId:n}){return Sn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Bn[e]||e==="opacity")}const Wb={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},qb=xn.length;function Gb(e,t,n){let r="",o=!0;for(let i=0;i<qb;i++){const a=xn[i],s=e[a];if(s===void 0)continue;let l=!0;if(typeof s=="number"?l=s===(a.startsWith("scale")?1:0):l=parseFloat(s)===0,!l||n){const u=Ru(s,Ri[a]);if(!l){o=!1;const c=Wb[a]||a;r+=`${c}(${u}) `}n&&(t[a]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function Ai(e,t,n){const{style:r,vars:o,transformOrigin:i}=e;let a=!1,s=!1;for(const l in t){const u=t[l];if(Sn.has(l)){a=!0;continue}else if(bi(l)){o[l]=u;continue}else{const c=Ru(u,Ri[l]);l.startsWith("origin")?(s=!0,i[l]=c):r[l]=c}}if(t.transform||(a||n?r.transform=Gb(t,e.transform,n):r.transform&&(r.transform="none")),s){const{originX:l="50%",originY:u="50%",originZ:c=0}=i;r.transformOrigin=`${l} ${u} ${c}`}}const Vi=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function zu(e,t,n){for(const r in t)!We(t[r])&&!_u(r,n)&&(e[r]=t[r])}function Ub({transformTemplate:e},t){return d.useMemo(()=>{const n=Vi();return Ai(n,t,e),Object.assign({},n.vars,n.style)},[t])}function Xb(e,t){const n=e.style||{},r={};return zu(r,n,e),Object.assign(r,Ub(e,t)),r}function Kb(e,t){const n={},r=Xb(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Yb={offset:"stroke-dashoffset",array:"stroke-dasharray"},Zb={offset:"strokeDashoffset",array:"strokeDasharray"};function Qb(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?Yb:Zb;e[i.offset]=te.transform(-r);const a=te.transform(t),s=te.transform(n);e[i.array]=`${a} ${s}`}function Hu(e,{attrX:t,attrY:n,attrScale:r,pathLength:o,pathSpacing:i=1,pathOffset:a=0,...s},l,u,c){if(Ai(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:m}=e;f.transform&&(m.transform=f.transform,delete f.transform),(m.transform||f.transformOrigin)&&(m.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),m.transform&&(m.transformBox=(c==null?void 0:c.transformBox)??"fill-box",delete f.transformBox),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&Qb(f,o,i,a,!1)}const Wu=()=>({...Vi(),attrs:{}}),qu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Jb(e,t,n,r){const o=d.useMemo(()=>{const i=Wu();return Hu(i,t,qu(r),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};zu(i,e.style,e),o.style={...i,...o.style}}return o}const e0=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ki(e){return typeof e!="string"||e.includes("-")?!1:!!(e0.indexOf(e)>-1||/[A-Z]/u.test(e))}function t0(e=!1){return(n,r,o,{latestValues:i},a)=>{const l=(ki(n)?Jb:Kb)(r,i,a,n),u=Mb(r,typeof n=="string",e),c=n!==d.Fragment?{...u,...l,ref:o}:{},{children:f}=r,m=d.useMemo(()=>We(f)?f.get():f,[f]);return d.createElement(n,{...c,children:m})}}function is(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Bi(e,t,n,r){if(typeof t=="function"){const[o,i]=is(r);t=t(n!==void 0?n:e.custom,o,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,i]=is(r);t=t(n!==void 0?n:e.custom,o,i)}return t}function ar(e){return We(e)?e.get():e}function n0({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:r0(n,r,o,e),renderState:t()}}const Gu=e=>(t,n)=>{const r=d.useContext(Or),o=d.useContext(ui),i=()=>n0(e,t,r,o);return n?i():Tp(i)};function r0(e,t,n,r){const o={},i=r(e,{});for(const m in i)o[m]=ar(i[m]);let{initial:a,animate:s}=e;const l=Pr(e),u=ku(e);t&&u&&!l&&e.inherit!==!1&&(a===void 0&&(a=t.initial),s===void 0&&(s=t.animate));let c=n?n.initial===!1:!1;c=c||a===!1;const f=c?s:a;if(f&&typeof f!="boolean"&&!$r(f)){const m=Array.isArray(f)?f:[f];for(let h=0;h<m.length;h++){const b=Bi(e,m[h]);if(b){const{transitionEnd:g,transition:v,...y}=b;for(const p in y){let C=y[p];if(Array.isArray(C)){const x=c?C.length-1:0;C=C[x]}C!==null&&(o[p]=C)}for(const p in g)o[p]=g[p]}}}return o}function Li(e,t,n){var i;const{style:r}=e,o={};for(const a in r)(We(r[a])||t.style&&We(t.style[a])||_u(a,e)||((i=n==null?void 0:n.getValue(a))==null?void 0:i.liveStyle)!==void 0)&&(o[a]=r[a]);return o}const o0={useVisualState:Gu({scrapeMotionValuesFromProps:Li,createRenderState:Vi})};function Uu(e,t,n){const r=Li(e,t,n);for(const o in e)if(We(e[o])||We(t[o])){const i=xn.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[i]=e[o]}return r}const i0={useVisualState:Gu({scrapeMotionValuesFromProps:Uu,createRenderState:Wu})};function a0(e,t){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const a={...ki(r)?i0:o0,preloadedFeatures:e,useRender:t0(o),createVisualElement:t,Component:r};return Lb(a)}}function Ln(e,t,n){const r=e.getProps();return Bi(r,t,n!==void 0?n:r.custom,e)}const Ho=e=>Array.isArray(e);function s0(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,pn(n))}function l0(e){return Ho(e)?e[e.length-1]||0:e}function c0(e,t){const n=Ln(e,t);let{transitionEnd:r={},transition:o={},...i}=n||{};i={...i,...r};for(const a in i){const s=l0(i[a]);s0(e,a,s)}}function u0(e){return!!(We(e)&&e.add)}function Wo(e,t){const n=e.getValue("willChange");if(u0(n))return n.add(t);if(!n&&$t.WillChange){const r=new $t.WillChange("auto");e.addValue("willChange",r),r.add(t)}}function Xu(e){return e.props[Bu]}const d0=e=>e!==null;function f0(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(d0),i=t&&n!=="loop"&&t%2===1?0:o.length-1;return o[i]}const h0={type:"spring",stiffness:500,damping:25,restSpeed:10},m0=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),g0={type:"keyframes",duration:.8},p0={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},v0=(e,{keyframes:t})=>t.length>2?g0:Sn.has(e)?e.startsWith("scale")?m0(t[1]):h0:p0;function b0({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Fi=(e,t,n,r={},o,i)=>a=>{const s=Ti(r,e)||{},l=s.delay||r.delay||0;let{elapsed:u=0}=r;u=u-ft(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-u,onUpdate:m=>{t.set(m),s.onUpdate&&s.onUpdate(m)},onComplete:()=>{a(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:i?void 0:o};b0(s)||Object.assign(c,v0(e,c)),c.duration&&(c.duration=ft(c.duration)),c.repeatDelay&&(c.repeatDelay=ft(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(f=!0)),($t.instantAnimations||$t.skipAnimations)&&(f=!0,c.duration=0,c.delay=0),c.allowFlatten=!s.type&&!s.ease,f&&!i&&t.get()!==void 0){const m=f0(c.keyframes,s);if(m!==void 0){Te.update(()=>{c.onUpdate(m),c.onComplete()});return}}return s.isSync?new $i(c):new tb(c)};function y0({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Ku(e,t,{delay:n=0,transitionOverride:r,type:o}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:a,...s}=t;r&&(i=r);const l=[],u=o&&e.animationState&&e.animationState.getState()[o];for(const c in s){const f=e.getValue(c,e.latestValues[c]??null),m=s[c];if(m===void 0||u&&y0(u,c))continue;const h={delay:n,...Ti(i||{},c)},b=f.get();if(b!==void 0&&!f.isAnimating&&!Array.isArray(m)&&m===b&&!h.velocity)continue;let g=!1;if(window.MotionHandoffAnimation){const y=Xu(e);if(y){const p=window.MotionHandoffAnimation(y,c,Te);p!==null&&(h.startTime=p,g=!0)}}Wo(e,c),f.start(Fi(c,f,m,e.shouldReduceMotion&&Eu.has(c)?{type:!1}:h,e,g));const v=f.animation;v&&l.push(v)}return a&&Promise.all(l).then(()=>{Te.update(()=>{a&&c0(e,a)})}),l}function qo(e,t,n={}){var l;const r=Ln(e,t,n.type==="exit"?(l=e.presenceContext)==null?void 0:l.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const i=r?()=>Promise.all(Ku(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:m}=o;return C0(e,t,c+u,f,m,n)}:()=>Promise.resolve(),{when:s}=o;if(s){const[u,c]=s==="beforeChildren"?[i,a]:[a,i];return u().then(()=>c())}else return Promise.all([i(),a(n.delay)])}function C0(e,t,n=0,r=0,o=1,i){const a=[],s=(e.variantChildren.size-1)*r,l=o===1?(u=0)=>u*r:(u=0)=>s-u*r;return Array.from(e.variantChildren).sort(x0).forEach((u,c)=>{u.notify("AnimationStart",t),a.push(qo(u,t,{...i,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(a)}function x0(e,t){return e.sortNodePosition(t)}function S0(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>qo(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=qo(e,t,n);else{const o=typeof t=="function"?Ln(e,t,n.custom):t;r=Promise.all(Ku(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}function Yu(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const w0=Di.length;function Zu(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Zu(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<w0;n++){const r=Di[n],o=e.props[r];(kn(o)||o===!1)&&(t[r]=o)}return t}const E0=[...ji].reverse(),O0=ji.length;function $0(e){return t=>Promise.all(t.map(({animation:n,options:r})=>S0(e,n,r)))}function P0(e){let t=$0(e),n=as(),r=!0;const o=l=>(u,c)=>{var m;const f=Ln(e,c,l==="exit"?(m=e.presenceContext)==null?void 0:m.custom:void 0);if(f){const{transition:h,transitionEnd:b,...g}=f;u={...u,...g,...b}}return u};function i(l){t=l(e)}function a(l){const{props:u}=e,c=Zu(e.parent)||{},f=[],m=new Set;let h={},b=1/0;for(let v=0;v<O0;v++){const y=E0[v],p=n[y],C=u[y]!==void 0?u[y]:c[y],x=kn(C),S=y===l?p.isActive:null;S===!1&&(b=v);let w=C===c[y]&&C!==u[y]&&x;if(w&&r&&e.manuallyAnimateOnMount&&(w=!1),p.protectedKeys={...h},!p.isActive&&S===null||!C&&!p.prevProp||$r(C)||typeof C=="boolean")continue;const E=T0(p.prevProp,C);let O=E||y===l&&p.isActive&&!w&&x||v>b&&x,T=!1;const P=Array.isArray(C)?C:[C];let N=P.reduce(o(y),{});S===!1&&(N={});const{prevResolvedValues:D={}}=p,j={...D,...N},M=R=>{O=!0,m.has(R)&&(T=!0,m.delete(R)),p.needsAnimating[R]=!0;const F=e.getValue(R);F&&(F.liveStyle=!1)};for(const R in j){const F=N[R],_=D[R];if(h.hasOwnProperty(R))continue;let Q=!1;Ho(F)&&Ho(_)?Q=!Yu(F,_):Q=F!==_,Q?F!=null?M(R):m.add(R):F!==void 0&&m.has(R)?M(R):p.protectedKeys[R]=!0}p.prevProp=C,p.prevResolvedValues=N,p.isActive&&(h={...h,...N}),r&&e.blockInitialAnimation&&(O=!1),O&&(!(w&&E)||T)&&f.push(...P.map(R=>({animation:R,options:{type:y}})))}if(m.size){const v={};if(typeof u.initial!="boolean"){const y=Ln(e,Array.isArray(u.initial)?u.initial[0]:u.initial);y&&y.transition&&(v.transition=y.transition)}m.forEach(y=>{const p=e.getBaseTarget(y),C=e.getValue(y);C&&(C.liveStyle=!0),v[y]=p??null}),f.push({animation:v})}let g=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(f):Promise.resolve()}function s(l,u){var f;if(n[l].isActive===u)return Promise.resolve();(f=e.variantChildren)==null||f.forEach(m=>{var h;return(h=m.animationState)==null?void 0:h.setActive(l,u)}),n[l].isActive=u;const c=a(l);for(const m in n)n[m].protectedKeys={};return c}return{animateChanges:a,setActive:s,setAnimateFunction:i,getState:()=>n,reset:()=>{n=as(),r=!0}}}function T0(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Yu(t,e):!1}function Ht(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function as(){return{animate:Ht(!0),whileInView:Ht(),whileHover:Ht(),whileTap:Ht(),whileDrag:Ht(),whileFocus:Ht(),exit:Ht()}}class Bt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class R0 extends Bt{constructor(t){super(t),t.animationState||(t.animationState=P0(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();$r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)==null||t.call(this)}}let M0=0;class N0 extends Bt{constructor(){super(...arguments),this.id=M0++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>{n(this.id)})}mount(){const{register:t,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const j0={animation:{Feature:R0},exit:{Feature:N0}};function Fn(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Xn(e){return{point:{x:e.pageX,y:e.pageY}}}const D0=e=>t=>Ni(t)&&e(t,Xn(t));function Rn(e,t,n,r){return Fn(e,t,D0(n),r)}function Qu({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function I0({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function A0(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}const Ju=1e-4,V0=1-Ju,k0=1+Ju,ed=.01,B0=0-ed,L0=0+ed;function Ge(e){return e.max-e.min}function F0(e,t,n){return Math.abs(e-t)<=n}function ss(e,t,n,r=.5){e.origin=r,e.originPoint=Pe(t.min,t.max,e.origin),e.scale=Ge(n)/Ge(t),e.translate=Pe(n.min,n.max,e.origin)-e.originPoint,(e.scale>=V0&&e.scale<=k0||isNaN(e.scale))&&(e.scale=1),(e.translate>=B0&&e.translate<=L0||isNaN(e.translate))&&(e.translate=0)}function Mn(e,t,n,r){ss(e.x,t.x,n.x,r?r.originX:void 0),ss(e.y,t.y,n.y,r?r.originY:void 0)}function ls(e,t,n){e.min=n.min+t.min,e.max=e.min+Ge(t)}function _0(e,t,n){ls(e.x,t.x,n.x),ls(e.y,t.y,n.y)}function cs(e,t,n){e.min=t.min-n.min,e.max=e.min+Ge(t)}function Nn(e,t,n){cs(e.x,t.x,n.x),cs(e.y,t.y,n.y)}const us=()=>({translate:0,scale:1,origin:0,originPoint:0}),dn=()=>({x:us(),y:us()}),ds=()=>({min:0,max:0}),Ne=()=>({x:ds(),y:ds()});function tt(e){return[e("x"),e("y")]}function _r(e){return e===void 0||e===1}function Go({scale:e,scaleX:t,scaleY:n}){return!_r(e)||!_r(t)||!_r(n)}function Wt(e){return Go(e)||td(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function td(e){return fs(e.x)||fs(e.y)}function fs(e){return e&&e!=="0%"}function yr(e,t,n){const r=e-n,o=t*r;return n+o}function hs(e,t,n,r,o){return o!==void 0&&(e=yr(e,o,r)),yr(e,n,r)+t}function Uo(e,t=0,n=1,r,o){e.min=hs(e.min,t,n,r,o),e.max=hs(e.max,t,n,r,o)}function nd(e,{x:t,y:n}){Uo(e.x,t.translate,t.scale,t.originPoint),Uo(e.y,n.translate,n.scale,n.originPoint)}const ms=.999999999999,gs=1.0000000000001;function z0(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,a;for(let s=0;s<o;s++){i=n[s],a=i.projectionDelta;const{visualElement:l}=i.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&hn(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,nd(e,a)),r&&Wt(i.latestValues)&&hn(e,i.latestValues))}t.x<gs&&t.x>ms&&(t.x=1),t.y<gs&&t.y>ms&&(t.y=1)}function fn(e,t){e.min=e.min+t,e.max=e.max+t}function ps(e,t,n,r,o=.5){const i=Pe(e.min,e.max,o);Uo(e,t,n,i,r)}function hn(e,t){ps(e.x,t.x,t.scaleX,t.scale,t.originX),ps(e.y,t.y,t.scaleY,t.scale,t.originY)}function rd(e,t){return Qu(A0(e.getBoundingClientRect(),t))}function H0(e,t,n){const r=rd(e,n),{scroll:o}=t;return o&&(fn(r.x,o.offset.x),fn(r.y,o.offset.y)),r}const od=({current:e})=>e?e.ownerDocument.defaultView:null,vs=(e,t)=>Math.abs(e-t);function W0(e,t){const n=vs(e.x,t.x),r=vs(e.y,t.y);return Math.sqrt(n**2+r**2)}class id{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=Hr(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,h=W0(f.offset,{x:0,y:0})>=3;if(!m&&!h)return;const{point:b}=f,{timestamp:g}=Fe;this.history.push({...b,timestamp:g});const{onStart:v,onMove:y}=this.handlers;m||(v&&v(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,f)},this.handlePointerMove=(f,m)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=zr(m,this.transformPagePoint),Te.update(this.updatePoint,!0)},this.handlePointerUp=(f,m)=>{this.end();const{onEnd:h,onSessionEnd:b,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=Hr(f.type==="pointercancel"?this.lastMoveEventInfo:zr(m,this.transformPagePoint),this.history);this.startEvent&&h&&h(f,v),b&&b(f,v)},!Ni(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const a=Xn(t),s=zr(a,this.transformPagePoint),{point:l}=s,{timestamp:u}=Fe;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Hr(s,this.history)),this.removeListeners=qn(Rn(this.contextWindow,"pointermove",this.handlePointerMove),Rn(this.contextWindow,"pointerup",this.handlePointerUp),Rn(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),At(this.updatePoint)}}function zr(e,t){return t?{point:t(e.point)}:e}function bs(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Hr({point:e},t){return{point:e,delta:bs(e,ad(t)),offset:bs(e,q0(t)),velocity:G0(t,.1)}}function q0(e){return e[0]}function ad(e){return e[e.length-1]}function G0(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=ad(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>ft(t)));)n--;if(!r)return{x:0,y:0};const i=ht(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const a={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function U0(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Pe(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Pe(n,e,r.max):Math.min(e,n)),e}function ys(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function X0(e,{top:t,left:n,bottom:r,right:o}){return{x:ys(e.x,n,o),y:ys(e.y,t,r)}}function Cs(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function K0(e,t){return{x:Cs(e.x,t.x),y:Cs(e.y,t.y)}}function Y0(e,t){let n=.5;const r=Ge(e),o=Ge(t);return o>r?n=In(t.min,t.max-r,e.min):r>o&&(n=In(e.min,e.max-o,t.min)),Ot(0,1,n)}function Z0(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Xo=.35;function Q0(e=Xo){return e===!1?e=0:e===!0&&(e=Xo),{x:xs(e,"left","right"),y:xs(e,"top","bottom")}}function xs(e,t,n){return{min:Ss(e,t),max:Ss(e,n)}}function Ss(e,t){return typeof e=="number"?e:e[t]||0}const J0=new WeakMap;class ey{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Ne(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Xn(c).point)},i=(c,f)=>{const{drag:m,dragPropagation:h,onDragStart:b}=this.getProps();if(m&&!h&&(this.openDragLock&&this.openDragLock(),this.openDragLock=vb(m),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),tt(v=>{let y=this.getAxisMotionValue(v).get()||0;if(mt.test(y)){const{projection:p}=this.visualElement;if(p&&p.layout){const C=p.layout.layoutBox[v];C&&(y=Ge(C)*(parseFloat(y)/100))}}this.originPoint[v]=y}),b&&Te.postRender(()=>b(c,f)),Wo(this.visualElement,"transform");const{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},a=(c,f)=>{const{dragPropagation:m,dragDirectionLock:h,onDirectionLock:b,onDrag:g}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:v}=f;if(h&&this.currentDirection===null){this.currentDirection=ty(v),this.currentDirection!==null&&b&&b(this.currentDirection);return}this.updateAxis("x",f.point,v),this.updateAxis("y",f.point,v),this.visualElement.render(),g&&g(c,f)},s=(c,f)=>this.stop(c,f),l=()=>tt(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)==null?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new id(t,{onSessionStart:o,onStart:i,onMove:a,onSessionEnd:s,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:od(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:i}=this.getProps();i&&Te.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!tr(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let a=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(a=U0(a,this.constraints[t],this.elastic[t])),i.set(a)}resolveConstraints(){var i;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(i=this.visualElement.projection)==null?void 0:i.layout,o=this.constraints;t&&un(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=X0(r.layoutBox,t):this.constraints=!1,this.elastic=Q0(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&tt(a=>{this.constraints!==!1&&this.getAxisMotionValue(a)&&(this.constraints[a]=Z0(r.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!un(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=H0(r,o.root,this.visualElement.getTransformPagePoint());let a=K0(o.layout.layoutBox,i);if(n){const s=n(I0(a));this.hasMutatedConstraints=!!s,s&&(a=Qu(s))}return a}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:s}=this.getProps(),l=this.constraints||{},u=tt(c=>{if(!tr(c,n,this.currentDirection))return;let f=l&&l[c]||{};a&&(f={min:0,max:0});const m=o?200:1e6,h=o?40:1e7,b={type:"inertia",velocity:r?t[c]:0,bounceStiffness:m,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...i,...f};return this.startAxisValueAnimation(c,b)});return Promise.all(u).then(s)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Wo(this.visualElement,t),r.start(Fi(t,r,0,n,this.visualElement,!1))}stopAnimation(){tt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){tt(t=>{var n;return(n=this.getAxisMotionValue(t).animation)==null?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)==null?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){tt(n=>{const{drag:r}=this.getProps();if(!tr(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:a,max:s}=o.layout.layoutBox[n];i.set(t[n]-Pe(a,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!un(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};tt(a=>{const s=this.getAxisMotionValue(a);if(s&&this.constraints!==!1){const l=s.get();o[a]=Y0({min:l,max:l},this.constraints[a])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),tt(a=>{if(!tr(a,t,null))return;const s=this.getAxisMotionValue(a),{min:l,max:u}=this.constraints[a];s.set(Pe(l,u,o[a]))})}addListeners(){if(!this.visualElement.current)return;J0.set(this.visualElement,this);const t=this.visualElement.current,n=Rn(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();un(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),Te.read(r);const a=Fn(window,"resize",()=>this.scalePositionWithinConstraints()),s=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(tt(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=l[c].translate,f.set(f.get()+l[c].translate))}),this.visualElement.render())});return()=>{a(),n(),i(),s&&s()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:a=Xo,dragMomentum:s=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:a,dragMomentum:s}}}function tr(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function ty(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class ny extends Bt{constructor(t){super(t),this.removeGroupControls=nt,this.removeListeners=nt,this.controls=new ey(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||nt}unmount(){this.removeGroupControls(),this.removeListeners()}}const ws=e=>(t,n)=>{e&&Te.postRender(()=>e(t,n))};class ry extends Bt{constructor(){super(...arguments),this.removePointerDownListener=nt}onPointerDown(t){this.session=new id(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:od(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:ws(t),onStart:ws(n),onMove:r,onEnd:(i,a)=>{delete this.session,o&&Te.postRender(()=>o(i,a))}}}mount(){this.removePointerDownListener=Rn(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const sr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Es(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const En={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(te.test(e))e=parseFloat(e);else return e;const n=Es(e,t.target.x),r=Es(e,t.target.y);return`${n}% ${r}%`}},oy={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=Vt.parse(e);if(o.length>5)return r;const i=Vt.createTransformer(e),a=typeof o[0]!="number"?1:0,s=n.x.scale*t.x,l=n.y.scale*t.y;o[0+a]/=s,o[1+a]/=l;const u=Pe(s,l,.5);return typeof o[2+a]=="number"&&(o[2+a]/=u),typeof o[3+a]=="number"&&(o[3+a]/=u),i(o)}};class iy extends d.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;Hb(ay),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),sr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,{projection:a}=r;return a&&(a.isPresent=i,o||t.layoutDependency!==n||n===void 0||t.isPresent!==i?a.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?a.promote():a.relegate()||Te.postRender(()=>{const s=a.getStack();(!s||!s.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Mi.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sd(e){const[t,n]=$b(),r=d.useContext(Hc);return V.jsx(iy,{...e,layoutGroup:r,switchLayoutGroup:d.useContext(Lu),isPresent:t,safeToRemove:n})}const ay={borderRadius:{...En,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:En,borderTopRightRadius:En,borderBottomLeftRadius:En,borderBottomRightRadius:En,boxShadow:oy};function sy(e,t,n){const r=We(e)?e:pn(e);return r.start(Fi("",r,t,n)),r.animation}const ly=(e,t)=>e.depth-t.depth;class cy{constructor(){this.children=[],this.isDirty=!1}add(t){di(this.children,t),this.isDirty=!0}remove(t){fi(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ly),this.isDirty=!1,this.children.forEach(t)}}function uy(e,t){const n=Ze.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(At(r),e(i-t))};return Te.setup(r,!0),()=>At(r)}const ld=["TopLeft","TopRight","BottomLeft","BottomRight"],dy=ld.length,Os=e=>typeof e=="string"?parseFloat(e):e,$s=e=>typeof e=="number"||te.test(e);function fy(e,t,n,r,o,i){o?(e.opacity=Pe(0,n.opacity??1,hy(r)),e.opacityExit=Pe(t.opacity??1,0,my(r))):i&&(e.opacity=Pe(t.opacity??1,n.opacity??1,r));for(let a=0;a<dy;a++){const s=`border${ld[a]}Radius`;let l=Ps(t,s),u=Ps(n,s);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||$s(l)===$s(u)?(e[s]=Math.max(Pe(Os(l),Os(u),r),0),(mt.test(u)||mt.test(l))&&(e[s]+="%")):e[s]=u}(t.rotate||n.rotate)&&(e.rotate=Pe(t.rotate||0,n.rotate||0,r))}function Ps(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const hy=cd(0,.5,eu),my=cd(.5,.95,nt);function cd(e,t,n){return r=>r<e?0:r>t?1:n(In(e,t,r))}function Ts(e,t){e.min=t.min,e.max=t.max}function et(e,t){Ts(e.x,t.x),Ts(e.y,t.y)}function Rs(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Ms(e,t,n,r,o){return e-=t,e=yr(e,1/n,r),o!==void 0&&(e=yr(e,1/o,r)),e}function gy(e,t=0,n=1,r=.5,o,i=e,a=e){if(mt.test(t)&&(t=parseFloat(t),t=Pe(a.min,a.max,t/100)-a.min),typeof t!="number")return;let s=Pe(i.min,i.max,r);e===i&&(s-=t),e.min=Ms(e.min,t,n,s,o),e.max=Ms(e.max,t,n,s,o)}function Ns(e,t,[n,r,o],i,a){gy(e,t[n],t[r],t[o],t.scale,i,a)}const py=["x","scaleX","originX"],vy=["y","scaleY","originY"];function js(e,t,n,r){Ns(e.x,t,py,n?n.x:void 0,r?r.x:void 0),Ns(e.y,t,vy,n?n.y:void 0,r?r.y:void 0)}function Ds(e){return e.translate===0&&e.scale===1}function ud(e){return Ds(e.x)&&Ds(e.y)}function Is(e,t){return e.min===t.min&&e.max===t.max}function by(e,t){return Is(e.x,t.x)&&Is(e.y,t.y)}function As(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function dd(e,t){return As(e.x,t.x)&&As(e.y,t.y)}function Vs(e){return Ge(e.x)/Ge(e.y)}function ks(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class yy{constructor(){this.members=[]}add(t){di(this.members,t),t.scheduleRender()}remove(t){if(fi(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Cy(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y,a=(n==null?void 0:n.z)||0;if((o||i||a)&&(r=`translate3d(${o}px, ${i}px, ${a}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:f,rotateY:m,skewX:h,skewY:b}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),f&&(r+=`rotateX(${f}deg) `),m&&(r+=`rotateY(${m}deg) `),h&&(r+=`skewX(${h}deg) `),b&&(r+=`skewY(${b}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const Wr=["","X","Y","Z"],xy={visibility:"hidden"},Sy=1e3;let wy=0;function qr(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function fd(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Xu(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",Te,!(o||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&fd(r)}function hd({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(a={},s=t==null?void 0:t()){this.id=wy++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach($y),this.nodes.forEach(Ny),this.nodes.forEach(jy),this.nodes.forEach(Py)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=s?s.root||s:this,this.path=s?[...s.path,s]:[],this.parent=s,this.depth=s?s.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new cy)}addEventListener(a,s){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new gi),this.eventHandlers.get(a).add(s)}notifyListeners(a,...s){const l=this.eventHandlers.get(a);l&&l.notify(...s)}hasListeners(a){return this.eventHandlers.has(a)}mount(a){if(this.instance)return;this.isSVG=Du(a)&&!wb(a),this.instance=a;const{layoutId:s,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(a),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||s)&&(this.isLayoutDirty=!0),e){let c;const f=()=>this.root.updateBlockedByResize=!1;e(a,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=uy(f,250),sr.hasAnimatedSinceResize&&(sr.hasAnimatedSinceResize=!1,this.nodes.forEach(Ls))})}s&&this.root.registerSharedNode(s,this),this.options.animate!==!1&&u&&(s||l)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:f,hasRelativeLayoutChanged:m,layout:h})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const b=this.options.transition||u.getDefaultTransition()||ky,{onLayoutAnimationStart:g,onLayoutAnimationComplete:v}=u.getProps(),y=!this.targetLayout||!dd(this.targetLayout,h),p=!f&&m;if(this.options.layoutRoot||this.resumeFrom||p||f&&(y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,p);const C={...Ti(b,"layout"),onPlay:g,onComplete:v};(u.shouldReduceMotion||this.options.layoutRoot)&&(C.delay=0,C.type=!1),this.startAnimation(C)}else f||Ls(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=h})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),At(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Dy),this.animationId++)}getTransformTemplate(){const{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&fd(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:s,layout:l}=this.options;if(s===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Bs);return}this.isUpdating||this.nodes.forEach(Ry),this.isUpdating=!1,this.nodes.forEach(My),this.nodes.forEach(Ey),this.nodes.forEach(Oy),this.clearAllSnapshots();const s=Ze.now();Fe.delta=Ot(0,1e3/60,s-Fe.timestamp),Fe.timestamp=s,Fe.isProcessing=!0,Vr.update.process(Fe),Vr.preRender.process(Fe),Vr.render.process(Fe),Fe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Mi.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Ty),this.sharedNodes.forEach(Iy)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Te.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Te.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Ge(this.snapshot.measuredBox.x)&&!Ge(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:s}=this.options;s&&s.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let s=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(s=!1),s&&this.instance){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!o)return;const a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,s=this.projectionDelta&&!ud(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;a&&this.instance&&(s||Wt(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){const s=this.measurePageBox();let l=this.removeElementScroll(s);return a&&(l=this.removeTransform(l)),By(l),{animationId:this.root.animationId,measuredBox:s,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var u;const{visualElement:a}=this.options;if(!a)return Ne();const s=a.measureViewportBox();if(!(((u=this.scroll)==null?void 0:u.wasRoot)||this.path.some(Ly))){const{scroll:c}=this.root;c&&(fn(s.x,c.offset.x),fn(s.y,c.offset.y))}return s}removeElementScroll(a){var l;const s=Ne();if(et(s,a),(l=this.scroll)!=null&&l.wasRoot)return s;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:f,options:m}=c;c!==this.root&&f&&m.layoutScroll&&(f.wasRoot&&et(s,a),fn(s.x,f.offset.x),fn(s.y,f.offset.y))}return s}applyTransform(a,s=!1){const l=Ne();et(l,a);for(let u=0;u<this.path.length;u++){const c=this.path[u];!s&&c.options.layoutScroll&&c.scroll&&c!==c.root&&hn(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Wt(c.latestValues)&&hn(l,c.latestValues)}return Wt(this.latestValues)&&hn(l,this.latestValues),l}removeTransform(a){const s=Ne();et(s,a);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Wt(u.latestValues))continue;Go(u.latestValues)&&u.updateSnapshot();const c=Ne(),f=u.measurePageBox();et(c,f),js(s,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Wt(this.latestValues)&&js(s,this.latestValues),s}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:a.crossfade!==void 0?a.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Fe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){var m;const s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==s;if(!(a||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(m=this.parent)!=null&&m.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:f}=this.options;if(!(!this.layout||!(c||f))){if(this.resolvedRelativeTargetAt=Fe.timestamp,!this.targetDelta&&!this.relativeTarget){const h=this.getClosestProjectingParent();h&&h.layout&&this.animationProgress!==1?(this.relativeParent=h,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ne(),this.relativeTargetOrigin=Ne(),Nn(this.relativeTargetOrigin,this.layout.layoutBox,h.layout.layoutBox),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Ne(),this.targetWithTransforms=Ne()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),_0(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):et(this.target,this.layout.layoutBox),nd(this.target,this.targetDelta)):et(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const h=this.getClosestProjectingParent();h&&!!h.resumingFrom==!!this.resumingFrom&&!h.options.layoutScroll&&h.target&&this.animationProgress!==1?(this.relativeParent=h,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ne(),this.relativeTargetOrigin=Ne(),Nn(this.relativeTargetOrigin,this.target,h.target),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Go(this.parent.latestValues)||td(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var b;const a=this.getLead(),s=!!this.resumingFrom||this!==a;let l=!0;if((this.isProjectionDirty||(b=this.parent)!=null&&b.isProjectionDirty)&&(l=!1),s&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===Fe.timestamp&&(l=!1),l)return;const{layout:u,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||c))return;et(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;z0(this.layoutCorrected,this.treeScale,this.path,s),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=Ne());const{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Rs(this.prevProjectionDelta.x,this.projectionDelta.x),Rs(this.prevProjectionDelta.y,this.projectionDelta.y)),Mn(this.projectionDelta,this.layoutCorrected,h,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==m||!ks(this.projectionDelta.x,this.prevProjectionDelta.x)||!ks(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){var s;if((s=this.options.visualElement)==null||s.scheduleRender(),a){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=dn(),this.projectionDelta=dn(),this.projectionDeltaWithTransform=dn()}setAnimationOrigin(a,s=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},f=dn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!s;const m=Ne(),h=l?l.source:void 0,b=this.layout?this.layout.source:void 0,g=h!==b,v=this.getStack(),y=!v||v.members.length<=1,p=!!(g&&!y&&this.options.crossfade===!0&&!this.path.some(Vy));this.animationProgress=0;let C;this.mixTargetDelta=x=>{const S=x/1e3;Fs(f.x,a.x,S),Fs(f.y,a.y,S),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Nn(m,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Ay(this.relativeTarget,this.relativeTargetOrigin,m,S),C&&by(this.relativeTarget,C)&&(this.isProjectionDirty=!1),C||(C=Ne()),et(C,this.relativeTarget)),g&&(this.animationValues=c,fy(c,u,this.latestValues,S,p,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(a){var s,l,u;this.notifyListeners("animationStart"),(s=this.currentAnimation)==null||s.stop(!1),(u=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||u.stop(!1),this.pendingAnimation&&(At(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Te.update(()=>{sr.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=pn(0)),this.currentAnimation=sy(this.motionValue,[0,1e3],{...a,isSync:!0,onUpdate:c=>{this.mixTargetDelta(c),a.onUpdate&&a.onUpdate(c)},onStop:()=>{},onComplete:()=>{a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Sy),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const a=this.getLead();let{targetWithTransforms:s,target:l,layout:u,latestValues:c}=a;if(!(!s||!l||!u)){if(this!==a&&this.layout&&u&&md(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||Ne();const f=Ge(this.layout.layoutBox.x);l.x.min=a.target.x.min,l.x.max=l.x.min+f;const m=Ge(this.layout.layoutBox.y);l.y.min=a.target.y.min,l.y.max=l.y.min+m}et(s,l),hn(s,c),Mn(this.projectionDeltaWithTransform,this.layoutCorrected,s,c)}}registerSharedNode(a,s){this.sharedNodes.has(a)||this.sharedNodes.set(a,new yy),this.sharedNodes.get(a).add(s);const u=s.options.initialPromotionConfig;s.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(s):void 0})}isLead(){const a=this.getStack();return a?a.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())==null?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())==null?void 0:s.prevLead:void 0}getStack(){const{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:s,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),a&&(this.projectionDelta=void 0,this.needsReset=!0),s&&this.setOptions({transition:s})}relegate(){const a=this.getStack();return a?a.relegate(this):!1}resetSkewAndRotation(){const{visualElement:a}=this.options;if(!a)return;let s=!1;const{latestValues:l}=a;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(s=!0),!s)return;const u={};l.z&&qr("z",a,u,this.animationValues);for(let c=0;c<Wr.length;c++)qr(`rotate${Wr[c]}`,a,u,this.animationValues),qr(`skew${Wr[c]}`,a,u,this.animationValues);a.render();for(const c in u)a.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);a.scheduleRender()}getProjectionStyles(a){if(!this.instance||this.isSVG)return;if(!this.isVisible)return xy;const s={visibility:""},l=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=ar(a==null?void 0:a.pointerEvents)||"",s.transform=l?l(this.latestValues,""):"none",s;const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){const h={};return this.options.layoutId&&(h.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,h.pointerEvents=ar(a==null?void 0:a.pointerEvents)||""),this.hasProjected&&!Wt(this.latestValues)&&(h.transform=l?l({},""):"none",this.hasProjected=!1),h}const c=u.animationValues||u.latestValues;this.applyTransformsToTarget(),s.transform=Cy(this.projectionDeltaWithTransform,this.treeScale,c),l&&(s.transform=l(c,s.transform));const{x:f,y:m}=this.projectionDelta;s.transformOrigin=`${f.origin*100}% ${m.origin*100}% 0`,u.animationValues?s.opacity=u===this?c.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:c.opacityExit:s.opacity=u===this?c.opacity!==void 0?c.opacity:"":c.opacityExit!==void 0?c.opacityExit:0;for(const h in Bn){if(c[h]===void 0)continue;const{correct:b,applyTo:g,isCSSVariable:v}=Bn[h],y=s.transform==="none"?c[h]:b(c[h],u);if(g){const p=g.length;for(let C=0;C<p;C++)s[g[C]]=y}else v?this.options.visualElement.renderState.vars[h]=y:s[h]=y}return this.options.layoutId&&(s.pointerEvents=u===this?ar(a==null?void 0:a.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>{var s;return(s=a.currentAnimation)==null?void 0:s.stop(!1)}),this.root.nodes.forEach(Bs),this.root.sharedNodes.clear()}}}function Ey(e){e.updateLayout()}function Oy(e){var n;const t=((n=e.resumeFrom)==null?void 0:n.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,a=t.source!==e.layout.source;i==="size"?tt(f=>{const m=a?t.measuredBox[f]:t.layoutBox[f],h=Ge(m);m.min=r[f].min,m.max=m.min+h}):md(i,t.layoutBox,r)&&tt(f=>{const m=a?t.measuredBox[f]:t.layoutBox[f],h=Ge(r[f]);m.max=m.min+h,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+h)});const s=dn();Mn(s,r,t.layoutBox);const l=dn();a?Mn(l,e.applyTransform(o,!0),t.measuredBox):Mn(l,r,t.layoutBox);const u=!ud(s);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:m,layout:h}=f;if(m&&h){const b=Ne();Nn(b,t.layoutBox,m.layoutBox);const g=Ne();Nn(g,r,h.layoutBox),dd(b,g)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=g,e.relativeTargetOrigin=b,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:l,layoutDelta:s,hasLayoutChanged:u,hasRelativeLayoutChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function $y(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Py(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Ty(e){e.clearSnapshot()}function Bs(e){e.clearMeasurements()}function Ry(e){e.isLayoutDirty=!1}function My(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ls(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Ny(e){e.resolveTargetDelta()}function jy(e){e.calcProjection()}function Dy(e){e.resetSkewAndRotation()}function Iy(e){e.removeLeadSnapshot()}function Fs(e,t,n){e.translate=Pe(t.translate,0,n),e.scale=Pe(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function _s(e,t,n,r){e.min=Pe(t.min,n.min,r),e.max=Pe(t.max,n.max,r)}function Ay(e,t,n,r){_s(e.x,t.x,n.x,r),_s(e.y,t.y,n.y,r)}function Vy(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const ky={duration:.45,ease:[.4,0,.1,1]},zs=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Hs=zs("applewebkit/")&&!zs("chrome/")?Math.round:nt;function Ws(e){e.min=Hs(e.min),e.max=Hs(e.max)}function By(e){Ws(e.x),Ws(e.y)}function md(e,t,n){return e==="position"||e==="preserve-aspect"&&!F0(Vs(t),Vs(n),.2)}function Ly(e){var t;return e!==e.root&&((t=e.scroll)==null?void 0:t.wasRoot)}const Fy=hd({attachResizeListener:(e,t)=>Fn(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Gr={current:void 0},gd=hd({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Gr.current){const e=new Fy({});e.mount(window),e.setOptions({layoutScroll:!0}),Gr.current=e}return Gr.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),_y={pan:{Feature:ry},drag:{Feature:ny,ProjectionNode:gd,MeasureLayout:sd}};function qs(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const o="onHover"+n,i=r[o];i&&Te.postRender(()=>i(t,Xn(t)))}class zy extends Bt{mount(){const{current:t}=this.node;t&&(this.unmount=bb(t,(n,r)=>(qs(this.node,r,"Start"),o=>qs(this.node,o,"End"))))}unmount(){}}class Hy extends Bt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=qn(Fn(this.node.current,"focus",()=>this.onFocus()),Fn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Gs(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const o="onTap"+(n==="End"?"":n),i=r[o];i&&Te.postRender(()=>i(t,Xn(t)))}class Wy extends Bt{mount(){const{current:t}=this.node;t&&(this.unmount=Sb(t,(n,r)=>(Gs(this.node,r,"Start"),(o,{success:i})=>Gs(this.node,o,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ko=new WeakMap,Ur=new WeakMap,qy=e=>{const t=Ko.get(e.target);t&&t(e)},Gy=e=>{e.forEach(qy)};function Uy({root:e,...t}){const n=e||document;Ur.has(n)||Ur.set(n,{});const r=Ur.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(Gy,{root:e,...t})),r[o]}function Xy(e,t,n){const r=Uy(t);return Ko.set(e,n),r.observe(e),()=>{Ko.delete(e),r.unobserve(e)}}const Ky={some:0,all:1};class Yy extends Bt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,a={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:Ky[o]},s=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),m=u?c:f;m&&m(l)};return Xy(this.node.current,a,s)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Zy(t,n))&&this.startObserver()}unmount(){}}function Zy({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Qy={inView:{Feature:Yy},tap:{Feature:Wy},focus:{Feature:Hy},hover:{Feature:zy}},Jy={layout:{ProjectionNode:gd,MeasureLayout:sd}},Yo={current:null},pd={current:!1};function eC(){if(pd.current=!0,!!ci)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Yo.current=e.matches;e.addListener(t),t()}else Yo.current=!1}const tC=new WeakMap;function nC(e,t,n){for(const r in t){const o=t[r],i=n[r];if(We(o))e.addValue(r,o);else if(We(i))e.addValue(r,pn(o,{owner:e}));else if(i!==o)if(e.hasValue(r)){const a=e.getValue(r);a.liveStyle===!0?a.jump(o):a.hasAnimated||a.set(o)}else{const a=e.getStaticValue(r);e.addValue(r,pn(a!==void 0?a:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Us=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rC{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:i,visualState:a},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Pi,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const m=Ze.now();this.renderScheduledAt<m&&(this.renderScheduledAt=m,Te.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=a;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=Pr(n),this.isVariantNode=ku(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const m in f){const h=f[m];l[m]!==void 0&&We(h)&&h.set(l[m],!1)}}mount(t){this.current=t,tC.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),pd.current||eC(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Yo.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),At(this.notifyUpdate),At(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=Sn.has(t);r&&this.onBindTransform&&this.onBindTransform();const o=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&Te.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let a;window.MotionCheckAppearSync&&(a=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),i(),a&&a(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in vn){const n=vn[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Ne()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Us.length;r++){const o=Us[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i="on"+o,a=t[i];a&&(this.propEventSubscriptions[o]=this.on(o,a))}this.prevMotionValues=nC(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=pn(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){let r=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return r!=null&&(typeof r=="string"&&(Wc(r)||Gc(r))?r=parseFloat(r):!Ob(r)&&Vt.test(n)&&(r=Tu(t,n)),this.setBaseTarget(t,We(r)?r.get():r)),We(r)?r.get():r}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var i;const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const a=Bi(this.props,n,(i=this.presenceContext)==null?void 0:i.custom);a&&(r=a[t])}if(n&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!We(o)?o:this.initialValues[t]!==void 0&&r===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new gi),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class vd extends rC{constructor(){super(...arguments),this.KeyframeResolver=hb}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;We(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function bd(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}function oC(e){return window.getComputedStyle(e)}class iC extends vd{constructor(){super(...arguments),this.type="html",this.renderInstance=bd}readValueFromInstance(t,n){var r;if(Sn.has(n))return(r=this.projection)!=null&&r.isProjecting?ko(n):jv(t,n);{const o=oC(t),i=(bi(n)?o.getPropertyValue(n):o[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return rd(t,n)}build(t,n,r){Ai(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Li(t,n,r)}}const yd=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function aC(e,t,n,r){bd(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(yd.has(o)?o:Ii(o),t.attrs[o])}class sC extends vd{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ne}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Sn.has(n)){const r=Pu(n);return r&&r.default||0}return n=yd.has(n)?n:Ii(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Uu(t,n,r)}build(t,n,r){Hu(t,n,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,n,r,o){aC(t,n,r,o)}mount(t){this.isSVGTag=qu(t.tagName),super.mount(t)}}const lC=(e,t)=>ki(e)?new sC(t):new iC(t,{allowProjection:e!==d.Fragment}),cC=a0({...j0,...Qy,..._y,...Jy},lC),uC=Nb(cC);var Xr,Xs;function _i(){if(Xs)return Xr;Xs=1;function e(t){var n=typeof t;return t!=null&&(n=="object"||n=="function")}return Xr=e,Xr}var Kr,Ks;function dC(){if(Ks)return Kr;Ks=1;var e=typeof Zn=="object"&&Zn&&Zn.Object===Object&&Zn;return Kr=e,Kr}var Yr,Ys;function Cd(){if(Ys)return Yr;Ys=1;var e=dC(),t=typeof self=="object"&&self&&self.Object===Object&&self,n=e||t||Function("return this")();return Yr=n,Yr}var Zr,Zs;function fC(){if(Zs)return Zr;Zs=1;var e=Cd(),t=function(){return e.Date.now()};return Zr=t,Zr}var Qr,Qs;function hC(){if(Qs)return Qr;Qs=1;var e=/\s/;function t(n){for(var r=n.length;r--&&e.test(n.charAt(r)););return r}return Qr=t,Qr}var Jr,Js;function mC(){if(Js)return Jr;Js=1;var e=hC(),t=/^\s+/;function n(r){return r&&r.slice(0,e(r)+1).replace(t,"")}return Jr=n,Jr}var eo,el;function xd(){if(el)return eo;el=1;var e=Cd(),t=e.Symbol;return eo=t,eo}var to,tl;function gC(){if(tl)return to;tl=1;var e=xd(),t=Object.prototype,n=t.hasOwnProperty,r=t.toString,o=e?e.toStringTag:void 0;function i(a){var s=n.call(a,o),l=a[o];try{a[o]=void 0;var u=!0}catch{}var c=r.call(a);return u&&(s?a[o]=l:delete a[o]),c}return to=i,to}var no,nl;function pC(){if(nl)return no;nl=1;var e=Object.prototype,t=e.toString;function n(r){return t.call(r)}return no=n,no}var ro,rl;function vC(){if(rl)return ro;rl=1;var e=xd(),t=gC(),n=pC(),r="[object Null]",o="[object Undefined]",i=e?e.toStringTag:void 0;function a(s){return s==null?s===void 0?o:r:i&&i in Object(s)?t(s):n(s)}return ro=a,ro}var oo,ol;function bC(){if(ol)return oo;ol=1;function e(t){return t!=null&&typeof t=="object"}return oo=e,oo}var io,il;function yC(){if(il)return io;il=1;var e=vC(),t=bC(),n="[object Symbol]";function r(o){return typeof o=="symbol"||t(o)&&e(o)==n}return io=r,io}var ao,al;function CC(){if(al)return ao;al=1;var e=mC(),t=_i(),n=yC(),r=NaN,o=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,a=/^0o[0-7]+$/i,s=parseInt;function l(u){if(typeof u=="number")return u;if(n(u))return r;if(t(u)){var c=typeof u.valueOf=="function"?u.valueOf():u;u=t(c)?c+"":c}if(typeof u!="string")return u===0?u:+u;u=e(u);var f=i.test(u);return f||a.test(u)?s(u.slice(2),f?2:8):o.test(u)?r:+u}return ao=l,ao}var so,sl;function Sd(){if(sl)return so;sl=1;var e=_i(),t=fC(),n=CC(),r="Expected a function",o=Math.max,i=Math.min;function a(s,l,u){var c,f,m,h,b,g,v=0,y=!1,p=!1,C=!0;if(typeof s!="function")throw new TypeError(r);l=n(l)||0,e(u)&&(y=!!u.leading,p="maxWait"in u,m=p?o(n(u.maxWait)||0,l):m,C="trailing"in u?!!u.trailing:C);function x(j){var M=c,I=f;return c=f=void 0,v=j,h=s.apply(I,M),h}function S(j){return v=j,b=setTimeout(O,l),y?x(j):h}function w(j){var M=j-g,I=j-v,L=l-M;return p?i(L,m-I):L}function E(j){var M=j-g,I=j-v;return g===void 0||M>=l||M<0||p&&I>=m}function O(){var j=t();if(E(j))return T(j);b=setTimeout(O,w(j))}function T(j){return b=void 0,C&&c?x(j):(c=f=void 0,h)}function P(){b!==void 0&&clearTimeout(b),v=0,c=g=f=b=void 0}function N(){return b===void 0?h:T(t())}function D(){var j=t(),M=E(j);if(c=arguments,f=this,g=j,M){if(b===void 0)return S(g);if(p)return clearTimeout(b),b=setTimeout(O,l),x(g)}return b===void 0&&(b=setTimeout(O,l)),h}return D.cancel=P,D.flush=N,D}return so=a,so}var xC=Sd();const lo=vl(xC);var co,ll;function SC(){if(ll)return co;ll=1;var e=Sd(),t=_i(),n="Expected a function";function r(o,i,a){var s=!0,l=!0;if(typeof o!="function")throw new TypeError(n);return t(a)&&(s="leading"in a?!!a.leading:s,l="trailing"in a?!!a.trailing:l),e(o,i,{leading:s,maxWait:i,trailing:l})}return co=r,co}var wC=SC();const EC=vl(wC);var mn=function(){return mn=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},mn.apply(this,arguments)};function wd(e){return!e||!e.ownerDocument||!e.ownerDocument.defaultView?window:e.ownerDocument.defaultView}function Ed(e){return!e||!e.ownerDocument?document:e.ownerDocument}var Od=function(e){var t={},n=Array.prototype.reduce.call(e,function(r,o){var i=o.name.match(/data-simplebar-(.+)/);if(i){var a=i[1].replace(/\W+(.)/g,function(s,l){return l.toUpperCase()});switch(o.value){case"true":r[a]=!0;break;case"false":r[a]=!1;break;case void 0:r[a]=!0;break;default:r[a]=o.value}}return r},t);return n};function $d(e,t){var n;e&&(n=e.classList).add.apply(n,t.split(" "))}function Pd(e,t){e&&t.split(" ").forEach(function(n){e.classList.remove(n)})}function Td(e){return".".concat(e.split(" ").join("."))}var zi=!!(typeof window<"u"&&window.document&&window.document.createElement),OC=Object.freeze({__proto__:null,addClasses:$d,canUseDOM:zi,classNamesToQuery:Td,getElementDocument:Ed,getElementWindow:wd,getOptions:Od,removeClasses:Pd}),ln=null,cl=null;zi&&window.addEventListener("resize",function(){cl!==window.devicePixelRatio&&(cl=window.devicePixelRatio,ln=null)});function ul(){if(ln===null){if(typeof document>"u")return ln=0,ln;var e=document.body,t=document.createElement("div");t.classList.add("simplebar-hide-scrollbar"),e.appendChild(t);var n=t.getBoundingClientRect().right;e.removeChild(t),ln=n}return ln}var Rt=wd,uo=Ed,$C=Od,Mt=$d,Nt=Pd,Ye=Td,On=function(){function e(t,n){n===void 0&&(n={});var r=this;if(this.removePreventClickId=null,this.minScrollbarWidth=20,this.stopScrollDelay=175,this.isScrolling=!1,this.isMouseEntering=!1,this.isDragging=!1,this.scrollXTicking=!1,this.scrollYTicking=!1,this.wrapperEl=null,this.contentWrapperEl=null,this.contentEl=null,this.offsetEl=null,this.maskEl=null,this.placeholderEl=null,this.heightAutoObserverWrapperEl=null,this.heightAutoObserverEl=null,this.rtlHelpers=null,this.scrollbarWidth=0,this.resizeObserver=null,this.mutationObserver=null,this.elStyles=null,this.isRtl=null,this.mouseX=0,this.mouseY=0,this.onMouseMove=function(){},this.onWindowResize=function(){},this.onStopScrolling=function(){},this.onMouseEntered=function(){},this.onScroll=function(){var o=Rt(r.el);r.scrollXTicking||(o.requestAnimationFrame(r.scrollX),r.scrollXTicking=!0),r.scrollYTicking||(o.requestAnimationFrame(r.scrollY),r.scrollYTicking=!0),r.isScrolling||(r.isScrolling=!0,Mt(r.el,r.classNames.scrolling)),r.showScrollbar("x"),r.showScrollbar("y"),r.onStopScrolling()},this.scrollX=function(){r.axis.x.isOverflowing&&r.positionScrollbar("x"),r.scrollXTicking=!1},this.scrollY=function(){r.axis.y.isOverflowing&&r.positionScrollbar("y"),r.scrollYTicking=!1},this._onStopScrolling=function(){Nt(r.el,r.classNames.scrolling),r.options.autoHide&&(r.hideScrollbar("x"),r.hideScrollbar("y")),r.isScrolling=!1},this.onMouseEnter=function(){r.isMouseEntering||(Mt(r.el,r.classNames.mouseEntered),r.showScrollbar("x"),r.showScrollbar("y"),r.isMouseEntering=!0),r.onMouseEntered()},this._onMouseEntered=function(){Nt(r.el,r.classNames.mouseEntered),r.options.autoHide&&(r.hideScrollbar("x"),r.hideScrollbar("y")),r.isMouseEntering=!1},this._onMouseMove=function(o){r.mouseX=o.clientX,r.mouseY=o.clientY,(r.axis.x.isOverflowing||r.axis.x.forceVisible)&&r.onMouseMoveForAxis("x"),(r.axis.y.isOverflowing||r.axis.y.forceVisible)&&r.onMouseMoveForAxis("y")},this.onMouseLeave=function(){r.onMouseMove.cancel(),(r.axis.x.isOverflowing||r.axis.x.forceVisible)&&r.onMouseLeaveForAxis("x"),(r.axis.y.isOverflowing||r.axis.y.forceVisible)&&r.onMouseLeaveForAxis("y"),r.mouseX=-1,r.mouseY=-1},this._onWindowResize=function(){r.scrollbarWidth=r.getScrollbarWidth(),r.hideNativeScrollbar()},this.onPointerEvent=function(o){if(!(!r.axis.x.track.el||!r.axis.y.track.el||!r.axis.x.scrollbar.el||!r.axis.y.scrollbar.el)){var i,a;r.axis.x.track.rect=r.axis.x.track.el.getBoundingClientRect(),r.axis.y.track.rect=r.axis.y.track.el.getBoundingClientRect(),(r.axis.x.isOverflowing||r.axis.x.forceVisible)&&(i=r.isWithinBounds(r.axis.x.track.rect)),(r.axis.y.isOverflowing||r.axis.y.forceVisible)&&(a=r.isWithinBounds(r.axis.y.track.rect)),(i||a)&&(o.stopPropagation(),o.type==="pointerdown"&&o.pointerType!=="touch"&&(i&&(r.axis.x.scrollbar.rect=r.axis.x.scrollbar.el.getBoundingClientRect(),r.isWithinBounds(r.axis.x.scrollbar.rect)?r.onDragStart(o,"x"):r.onTrackClick(o,"x")),a&&(r.axis.y.scrollbar.rect=r.axis.y.scrollbar.el.getBoundingClientRect(),r.isWithinBounds(r.axis.y.scrollbar.rect)?r.onDragStart(o,"y"):r.onTrackClick(o,"y"))))}},this.drag=function(o){var i,a,s,l,u,c,f,m,h,b,g;if(!(!r.draggedAxis||!r.contentWrapperEl)){var v,y=r.axis[r.draggedAxis].track,p=(a=(i=y.rect)===null||i===void 0?void 0:i[r.axis[r.draggedAxis].sizeAttr])!==null&&a!==void 0?a:0,C=r.axis[r.draggedAxis].scrollbar,x=(l=(s=r.contentWrapperEl)===null||s===void 0?void 0:s[r.axis[r.draggedAxis].scrollSizeAttr])!==null&&l!==void 0?l:0,S=parseInt((c=(u=r.elStyles)===null||u===void 0?void 0:u[r.axis[r.draggedAxis].sizeAttr])!==null&&c!==void 0?c:"0px",10);o.preventDefault(),o.stopPropagation(),r.draggedAxis==="y"?v=o.pageY:v=o.pageX;var w=v-((m=(f=y.rect)===null||f===void 0?void 0:f[r.axis[r.draggedAxis].offsetAttr])!==null&&m!==void 0?m:0)-r.axis[r.draggedAxis].dragOffset;w=r.draggedAxis==="x"&&r.isRtl?((b=(h=y.rect)===null||h===void 0?void 0:h[r.axis[r.draggedAxis].sizeAttr])!==null&&b!==void 0?b:0)-C.size-w:w;var E=w/(p-C.size),O=E*(x-S);r.draggedAxis==="x"&&r.isRtl&&(O=!((g=e.getRtlHelpers())===null||g===void 0)&&g.isScrollingToNegative?-O:O),r.contentWrapperEl[r.axis[r.draggedAxis].scrollOffsetAttr]=O}},this.onEndDrag=function(o){r.isDragging=!1;var i=uo(r.el),a=Rt(r.el);o.preventDefault(),o.stopPropagation(),Nt(r.el,r.classNames.dragging),r.onStopScrolling(),i.removeEventListener("mousemove",r.drag,!0),i.removeEventListener("mouseup",r.onEndDrag,!0),r.removePreventClickId=a.setTimeout(function(){i.removeEventListener("click",r.preventClick,!0),i.removeEventListener("dblclick",r.preventClick,!0),r.removePreventClickId=null})},this.preventClick=function(o){o.preventDefault(),o.stopPropagation()},this.el=t,this.options=mn(mn({},e.defaultOptions),n),this.classNames=mn(mn({},e.defaultOptions.classNames),n.classNames),this.axis={x:{scrollOffsetAttr:"scrollLeft",sizeAttr:"width",scrollSizeAttr:"scrollWidth",offsetSizeAttr:"offsetWidth",offsetAttr:"left",overflowAttr:"overflowX",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}},y:{scrollOffsetAttr:"scrollTop",sizeAttr:"height",scrollSizeAttr:"scrollHeight",offsetSizeAttr:"offsetHeight",offsetAttr:"top",overflowAttr:"overflowY",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}}},typeof this.el!="object"||!this.el.nodeName)throw new Error("Argument passed to SimpleBar must be an HTML element instead of ".concat(this.el));this.onMouseMove=EC(this._onMouseMove,64),this.onWindowResize=lo(this._onWindowResize,64,{leading:!0}),this.onStopScrolling=lo(this._onStopScrolling,this.stopScrollDelay),this.onMouseEntered=lo(this._onMouseEntered,this.stopScrollDelay),this.init()}return e.getRtlHelpers=function(){if(e.rtlHelpers)return e.rtlHelpers;var t=document.createElement("div");t.innerHTML='<div class="simplebar-dummy-scrollbar-size"><div></div></div>';var n=t.firstElementChild,r=n==null?void 0:n.firstElementChild;if(!r)return null;document.body.appendChild(n),n.scrollLeft=0;var o=e.getOffset(n),i=e.getOffset(r);n.scrollLeft=-999;var a=e.getOffset(r);return document.body.removeChild(n),e.rtlHelpers={isScrollOriginAtZero:o.left!==i.left,isScrollingToNegative:i.left!==a.left},e.rtlHelpers},e.prototype.getScrollbarWidth=function(){try{return this.contentWrapperEl&&getComputedStyle(this.contentWrapperEl,"::-webkit-scrollbar").display==="none"||"scrollbarWidth"in document.documentElement.style||"-ms-overflow-style"in document.documentElement.style?0:ul()}catch{return ul()}},e.getOffset=function(t){var n=t.getBoundingClientRect(),r=uo(t),o=Rt(t);return{top:n.top+(o.pageYOffset||r.documentElement.scrollTop),left:n.left+(o.pageXOffset||r.documentElement.scrollLeft)}},e.prototype.init=function(){zi&&(this.initDOM(),this.rtlHelpers=e.getRtlHelpers(),this.scrollbarWidth=this.getScrollbarWidth(),this.recalculate(),this.initListeners())},e.prototype.initDOM=function(){var t,n;this.wrapperEl=this.el.querySelector(Ye(this.classNames.wrapper)),this.contentWrapperEl=this.options.scrollableNode||this.el.querySelector(Ye(this.classNames.contentWrapper)),this.contentEl=this.options.contentNode||this.el.querySelector(Ye(this.classNames.contentEl)),this.offsetEl=this.el.querySelector(Ye(this.classNames.offset)),this.maskEl=this.el.querySelector(Ye(this.classNames.mask)),this.placeholderEl=this.findChild(this.wrapperEl,Ye(this.classNames.placeholder)),this.heightAutoObserverWrapperEl=this.el.querySelector(Ye(this.classNames.heightAutoObserverWrapperEl)),this.heightAutoObserverEl=this.el.querySelector(Ye(this.classNames.heightAutoObserverEl)),this.axis.x.track.el=this.findChild(this.el,"".concat(Ye(this.classNames.track)).concat(Ye(this.classNames.horizontal))),this.axis.y.track.el=this.findChild(this.el,"".concat(Ye(this.classNames.track)).concat(Ye(this.classNames.vertical))),this.axis.x.scrollbar.el=((t=this.axis.x.track.el)===null||t===void 0?void 0:t.querySelector(Ye(this.classNames.scrollbar)))||null,this.axis.y.scrollbar.el=((n=this.axis.y.track.el)===null||n===void 0?void 0:n.querySelector(Ye(this.classNames.scrollbar)))||null,this.options.autoHide||(Mt(this.axis.x.scrollbar.el,this.classNames.visible),Mt(this.axis.y.scrollbar.el,this.classNames.visible))},e.prototype.initListeners=function(){var t=this,n,r=Rt(this.el);if(this.el.addEventListener("mouseenter",this.onMouseEnter),this.el.addEventListener("pointerdown",this.onPointerEvent,!0),this.el.addEventListener("mousemove",this.onMouseMove),this.el.addEventListener("mouseleave",this.onMouseLeave),(n=this.contentWrapperEl)===null||n===void 0||n.addEventListener("scroll",this.onScroll),r.addEventListener("resize",this.onWindowResize),!!this.contentEl){if(window.ResizeObserver){var o=!1,i=r.ResizeObserver||ResizeObserver;this.resizeObserver=new i(function(){o&&r.requestAnimationFrame(function(){t.recalculate()})}),this.resizeObserver.observe(this.el),this.resizeObserver.observe(this.contentEl),r.requestAnimationFrame(function(){o=!0})}this.mutationObserver=new r.MutationObserver(function(){r.requestAnimationFrame(function(){t.recalculate()})}),this.mutationObserver.observe(this.contentEl,{childList:!0,subtree:!0,characterData:!0})}},e.prototype.recalculate=function(){if(!(!this.heightAutoObserverEl||!this.contentEl||!this.contentWrapperEl||!this.wrapperEl||!this.placeholderEl)){var t=Rt(this.el);this.elStyles=t.getComputedStyle(this.el),this.isRtl=this.elStyles.direction==="rtl";var n=this.contentEl.offsetWidth,r=this.heightAutoObserverEl.offsetHeight<=1,o=this.heightAutoObserverEl.offsetWidth<=1||n>0,i=this.contentWrapperEl.offsetWidth,a=this.elStyles.overflowX,s=this.elStyles.overflowY;this.contentEl.style.padding="".concat(this.elStyles.paddingTop," ").concat(this.elStyles.paddingRight," ").concat(this.elStyles.paddingBottom," ").concat(this.elStyles.paddingLeft),this.wrapperEl.style.margin="-".concat(this.elStyles.paddingTop," -").concat(this.elStyles.paddingRight," -").concat(this.elStyles.paddingBottom," -").concat(this.elStyles.paddingLeft);var l=this.contentEl.scrollHeight,u=this.contentEl.scrollWidth;this.contentWrapperEl.style.height=r?"auto":"100%",this.placeholderEl.style.width=o?"".concat(n||u,"px"):"auto",this.placeholderEl.style.height="".concat(l,"px");var c=this.contentWrapperEl.offsetHeight;this.axis.x.isOverflowing=n!==0&&u>n,this.axis.y.isOverflowing=l>c,this.axis.x.isOverflowing=a==="hidden"?!1:this.axis.x.isOverflowing,this.axis.y.isOverflowing=s==="hidden"?!1:this.axis.y.isOverflowing,this.axis.x.forceVisible=this.options.forceVisible==="x"||this.options.forceVisible===!0,this.axis.y.forceVisible=this.options.forceVisible==="y"||this.options.forceVisible===!0,this.hideNativeScrollbar();var f=this.axis.x.isOverflowing?this.scrollbarWidth:0,m=this.axis.y.isOverflowing?this.scrollbarWidth:0;this.axis.x.isOverflowing=this.axis.x.isOverflowing&&u>i-m,this.axis.y.isOverflowing=this.axis.y.isOverflowing&&l>c-f,this.axis.x.scrollbar.size=this.getScrollbarSize("x"),this.axis.y.scrollbar.size=this.getScrollbarSize("y"),this.axis.x.scrollbar.el&&(this.axis.x.scrollbar.el.style.width="".concat(this.axis.x.scrollbar.size,"px")),this.axis.y.scrollbar.el&&(this.axis.y.scrollbar.el.style.height="".concat(this.axis.y.scrollbar.size,"px")),this.positionScrollbar("x"),this.positionScrollbar("y"),this.toggleTrackVisibility("x"),this.toggleTrackVisibility("y")}},e.prototype.getScrollbarSize=function(t){var n,r;if(t===void 0&&(t="y"),!this.axis[t].isOverflowing||!this.contentEl)return 0;var o=this.contentEl[this.axis[t].scrollSizeAttr],i=(r=(n=this.axis[t].track.el)===null||n===void 0?void 0:n[this.axis[t].offsetSizeAttr])!==null&&r!==void 0?r:0,a=i/o,s;return s=Math.max(~~(a*i),this.options.scrollbarMinSize),this.options.scrollbarMaxSize&&(s=Math.min(s,this.options.scrollbarMaxSize)),s},e.prototype.positionScrollbar=function(t){var n,r,o;t===void 0&&(t="y");var i=this.axis[t].scrollbar;if(!(!this.axis[t].isOverflowing||!this.contentWrapperEl||!i.el||!this.elStyles)){var a=this.contentWrapperEl[this.axis[t].scrollSizeAttr],s=((n=this.axis[t].track.el)===null||n===void 0?void 0:n[this.axis[t].offsetSizeAttr])||0,l=parseInt(this.elStyles[this.axis[t].sizeAttr],10),u=this.contentWrapperEl[this.axis[t].scrollOffsetAttr];u=t==="x"&&this.isRtl&&(!((r=e.getRtlHelpers())===null||r===void 0)&&r.isScrollOriginAtZero)?-u:u,t==="x"&&this.isRtl&&(u=!((o=e.getRtlHelpers())===null||o===void 0)&&o.isScrollingToNegative?u:-u);var c=u/(a-l),f=~~((s-i.size)*c);f=t==="x"&&this.isRtl?-f+(s-i.size):f,i.el.style.transform=t==="x"?"translate3d(".concat(f,"px, 0, 0)"):"translate3d(0, ".concat(f,"px, 0)")}},e.prototype.toggleTrackVisibility=function(t){t===void 0&&(t="y");var n=this.axis[t].track.el,r=this.axis[t].scrollbar.el;!n||!r||!this.contentWrapperEl||(this.axis[t].isOverflowing||this.axis[t].forceVisible?(n.style.visibility="visible",this.contentWrapperEl.style[this.axis[t].overflowAttr]="scroll",this.el.classList.add("".concat(this.classNames.scrollable,"-").concat(t))):(n.style.visibility="hidden",this.contentWrapperEl.style[this.axis[t].overflowAttr]="hidden",this.el.classList.remove("".concat(this.classNames.scrollable,"-").concat(t))),this.axis[t].isOverflowing?r.style.display="block":r.style.display="none")},e.prototype.showScrollbar=function(t){t===void 0&&(t="y"),this.axis[t].isOverflowing&&!this.axis[t].scrollbar.isVisible&&(Mt(this.axis[t].scrollbar.el,this.classNames.visible),this.axis[t].scrollbar.isVisible=!0)},e.prototype.hideScrollbar=function(t){t===void 0&&(t="y"),!this.isDragging&&this.axis[t].isOverflowing&&this.axis[t].scrollbar.isVisible&&(Nt(this.axis[t].scrollbar.el,this.classNames.visible),this.axis[t].scrollbar.isVisible=!1)},e.prototype.hideNativeScrollbar=function(){this.offsetEl&&(this.offsetEl.style[this.isRtl?"left":"right"]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px",this.offsetEl.style.bottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px")},e.prototype.onMouseMoveForAxis=function(t){t===void 0&&(t="y");var n=this.axis[t];!n.track.el||!n.scrollbar.el||(n.track.rect=n.track.el.getBoundingClientRect(),n.scrollbar.rect=n.scrollbar.el.getBoundingClientRect(),this.isWithinBounds(n.track.rect)?(this.showScrollbar(t),Mt(n.track.el,this.classNames.hover),this.isWithinBounds(n.scrollbar.rect)?Mt(n.scrollbar.el,this.classNames.hover):Nt(n.scrollbar.el,this.classNames.hover)):(Nt(n.track.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(t)))},e.prototype.onMouseLeaveForAxis=function(t){t===void 0&&(t="y"),Nt(this.axis[t].track.el,this.classNames.hover),Nt(this.axis[t].scrollbar.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(t)},e.prototype.onDragStart=function(t,n){var r;n===void 0&&(n="y"),this.isDragging=!0;var o=uo(this.el),i=Rt(this.el),a=this.axis[n].scrollbar,s=n==="y"?t.pageY:t.pageX;this.axis[n].dragOffset=s-(((r=a.rect)===null||r===void 0?void 0:r[this.axis[n].offsetAttr])||0),this.draggedAxis=n,Mt(this.el,this.classNames.dragging),o.addEventListener("mousemove",this.drag,!0),o.addEventListener("mouseup",this.onEndDrag,!0),this.removePreventClickId===null?(o.addEventListener("click",this.preventClick,!0),o.addEventListener("dblclick",this.preventClick,!0)):(i.clearTimeout(this.removePreventClickId),this.removePreventClickId=null)},e.prototype.onTrackClick=function(t,n){var r=this,o,i,a,s;n===void 0&&(n="y");var l=this.axis[n];if(!(!this.options.clickOnTrack||!l.scrollbar.el||!this.contentWrapperEl)){t.preventDefault();var u=Rt(this.el);this.axis[n].scrollbar.rect=l.scrollbar.el.getBoundingClientRect();var c=this.axis[n].scrollbar,f=(i=(o=c.rect)===null||o===void 0?void 0:o[this.axis[n].offsetAttr])!==null&&i!==void 0?i:0,m=parseInt((s=(a=this.elStyles)===null||a===void 0?void 0:a[this.axis[n].sizeAttr])!==null&&s!==void 0?s:"0px",10),h=this.contentWrapperEl[this.axis[n].scrollOffsetAttr],b=n==="y"?this.mouseY-f:this.mouseX-f,g=b<0?-1:1,v=g===-1?h-m:h+m,y=40,p=function(){r.contentWrapperEl&&(g===-1?h>v&&(h-=y,r.contentWrapperEl[r.axis[n].scrollOffsetAttr]=h,u.requestAnimationFrame(p)):h<v&&(h+=y,r.contentWrapperEl[r.axis[n].scrollOffsetAttr]=h,u.requestAnimationFrame(p)))};p()}},e.prototype.getContentElement=function(){return this.contentEl},e.prototype.getScrollElement=function(){return this.contentWrapperEl},e.prototype.removeListeners=function(){var t=Rt(this.el);this.el.removeEventListener("mouseenter",this.onMouseEnter),this.el.removeEventListener("pointerdown",this.onPointerEvent,!0),this.el.removeEventListener("mousemove",this.onMouseMove),this.el.removeEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl&&this.contentWrapperEl.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onWindowResize),this.mutationObserver&&this.mutationObserver.disconnect(),this.resizeObserver&&this.resizeObserver.disconnect(),this.onMouseMove.cancel(),this.onWindowResize.cancel(),this.onStopScrolling.cancel(),this.onMouseEntered.cancel()},e.prototype.unMount=function(){this.removeListeners()},e.prototype.isWithinBounds=function(t){return this.mouseX>=t.left&&this.mouseX<=t.left+t.width&&this.mouseY>=t.top&&this.mouseY<=t.top+t.height},e.prototype.findChild=function(t,n){var r=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector;return Array.prototype.filter.call(t.children,function(o){return r.call(o,n)})[0]},e.rtlHelpers=null,e.defaultOptions={forceVisible:!1,clickOnTrack:!0,scrollbarMinSize:25,scrollbarMaxSize:0,ariaLabel:"scrollable content",tabIndex:0,classNames:{contentEl:"simplebar-content",contentWrapper:"simplebar-content-wrapper",offset:"simplebar-offset",mask:"simplebar-mask",wrapper:"simplebar-wrapper",placeholder:"simplebar-placeholder",scrollbar:"simplebar-scrollbar",track:"simplebar-track",heightAutoObserverWrapperEl:"simplebar-height-auto-observer-wrapper",heightAutoObserverEl:"simplebar-height-auto-observer",visible:"simplebar-visible",horizontal:"simplebar-horizontal",vertical:"simplebar-vertical",hover:"simplebar-hover",dragging:"simplebar-dragging",scrolling:"simplebar-scrolling",scrollable:"simplebar-scrollable",mouseEntered:"simplebar-mouse-entered"},scrollableNode:null,contentNode:null,autoHide:!0},e.getOptions=$C,e.helpers=OC,e}(),Qe=function(){return Qe=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Qe.apply(this,arguments)};function PC(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}var Zo=d.forwardRef(function(e,t){var n=e.children,r=e.scrollableNodeProps,o=r===void 0?{}:r,i=PC(e,["children","scrollableNodeProps"]),a=d.useRef(),s=d.useRef(),l=d.useRef(),u={},c={};Object.keys(i).forEach(function(h){Object.prototype.hasOwnProperty.call(On.defaultOptions,h)?u[h]=i[h]:c[h]=i[h]});var f=Qe(Qe({},On.defaultOptions.classNames),u.classNames),m=Qe(Qe({},o),{className:"".concat(f.contentWrapper).concat(o.className?" ".concat(o.className):""),tabIndex:u.tabIndex||On.defaultOptions.tabIndex,role:"region","aria-label":u.ariaLabel||On.defaultOptions.ariaLabel});return d.useEffect(function(){var h;return s.current=m.ref?m.ref.current:s.current,a.current&&(h=new On(a.current,Qe(Qe(Qe({},u),s.current&&{scrollableNode:s.current}),l.current&&{contentNode:l.current})),typeof t=="function"?t(h):t&&(t.current=h)),function(){h==null||h.unMount(),h=null,typeof t=="function"&&t(null)}},[]),d.createElement("div",Qe({"data-simplebar":"init",ref:a},c),d.createElement("div",{className:f.wrapper},d.createElement("div",{className:f.heightAutoObserverWrapperEl},d.createElement("div",{className:f.heightAutoObserverEl})),d.createElement("div",{className:f.mask},d.createElement("div",{className:f.offset},typeof n=="function"?n({scrollableNodeRef:s,scrollableNodeProps:Qe(Qe({},m),{ref:s}),contentNodeRef:l,contentNodeProps:{className:f.contentEl,ref:l}}):d.createElement("div",Qe({},m),d.createElement("div",{className:f.contentEl},n)))),d.createElement("div",{className:f.placeholder})),d.createElement("div",{className:"".concat(f.track," simplebar-horizontal")},d.createElement("div",{className:f.scrollbar})),d.createElement("div",{className:"".concat(f.track," simplebar-vertical")},d.createElement("div",{className:f.scrollbar})))});Zo.displayName="SimpleBar";const fo=[{key:"/AppointmentRecord",label:"预约记录",icon:$.createElement(Ar)},{key:"/timeManagement",label:"预约时间管理",icon:$.createElement(Ar)},{key:"/medicalNotice",label:"就诊须知",icon:$.createElement(Ar)}];/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TC=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),RC=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),dl=e=>{const t=RC(e);return t.charAt(0).toUpperCase()+t.slice(1)},Rd=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var MC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NC=d.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:a,...s},l)=>d.createElement("svg",{ref:l,...MC,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Rd("lucide",o),...s},[...a.map(([u,c])=>d.createElement(u,c)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Md=(e,t)=>{const n=d.forwardRef(({className:r,...o},i)=>d.createElement(NC,{ref:i,iconNode:t,className:Rd(`lucide-${TC(dl(e))}`,`lucide-${e}`,r),...o}));return n.displayName=dl(e),n};/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jC=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],DC=Md("moon",jC);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IC=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],AC=Md("sun",IC),VC="_layout_4neml_1",kC="_sider_4neml_9",BC="_logo_4neml_21",LC="_menuWrapper_4neml_48",FC="_menu_4neml_48",_C="_header_4neml_92",zC="_actions_4neml_119",HC="_breadcrumb_4neml_125",WC="_icon_4neml_137",qC="_avatar_4neml_147",GC="_content_4neml_187",UC="_contentScroll_4neml_209",XC="_menuScroll_4neml_212",KC="_footer_4neml_215",ze={layout:VC,sider:kC,logo:BC,menuWrapper:LC,menu:FC,header:_C,actions:zC,breadcrumb:HC,icon:WC,avatar:qC,content:GC,contentScroll:UC,menuScroll:XC,footer:KC},{Header:YC,Sider:ZC,Content:QC,Footer:JC}=It,cx=()=>{var w;const{theme:e,setTheme:t}=d.useContext(pl),n=e==="dark",[r,o]=d.useState(!1),[i,a]=d.useState([]),{menu:s,setMenu:l}=qf(),[u,c]=d.useState(!1),[f,m]=d.useState(!1);d.useEffect(()=>{const E=()=>m(!!document.fullscreenElement);return document.addEventListener("fullscreenchange",E),()=>document.removeEventListener("fullscreenchange",E)},[]);const h=()=>{document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen()},b=()=>c(!1),g=Zd(),[v,y]=st.useModal(),p=()=>{St.localRemove("accountId"),St.localRemove("sid"),St.localRemove("cid"),St.localRemove("permissions"),St.localSet("loginStatus",!1),g("/login",{replace:!0}),l("/login"),Il.success("退出成功!")},C=()=>{v.confirm({width:400,content:"是否确定退出系统?",okText:"确认",cancelText:"取消",onOk:()=>p(),onCancel:()=>b()})},x=[{key:"changePW",label:V.jsx(nn,{onClick:()=>g("/changePW"),icon:V.jsx(xp,{}),...Ia,children:"更改密码"})},{key:"logout",label:V.jsx(nn,{icon:V.jsx(wp,{}),onClick:C,...Ia,children:"退出登录"})}],S=({item:E,key:O})=>{l(O),g(O);const T=Aa(fo,O);a(T)};return d.useEffect(()=>{if(s){sessionStorage.setItem("menu",s);const E=Aa(fo,s);a(E)}},[s]),V.jsxs(V.Fragment,{children:[y,V.jsxs(It,{className:ze.layout,children:[V.jsxs(ZC,{className:ze.sider,collapsible:!0,collapsed:r,onCollapse:o,trigger:null,children:[V.jsxs("div",{className:ze.logo,children:[r&&V.jsx(mp,{}),!r&&V.jsx("span",{children:"智影在线服务"})]}),V.jsx("div",{className:ze.menuWrapper,children:V.jsx(Zo,{className:ze.menuScroll,children:V.jsx(uC.div,{initial:{x:-100,opacity:0},animate:{x:0,opacity:1},transition:{type:"spring",stiffness:80,damping:12},children:V.jsx(Af,{mode:"inline",selectedKeys:[s],items:fo,multiple:!0,onSelect:S,className:ze.menu})})})})]}),V.jsxs(It,{className:ze.main,children:[V.jsxs(YC,{className:ze.header,children:[r?V.jsx(Pp,{onClick:()=>o(!r),style:{fontSize:"26px",marginRight:"10px"}}):V.jsx(Op,{onClick:()=>o(!r),style:{fontSize:"26px",marginRight:"10px"}}),V.jsx(oi,{className:ze.breadcrumb,items:i}),V.jsxs("div",{className:ze.actions,children:[V.jsx(dr,{title:f?"退出全屏":"进入全屏",className:ze.icon,onClick:h,children:f?V.jsx(vp,{}):V.jsx(yp,{})}),V.jsx(dr,{title:`切换为${n?"浅色":"深色"}主题`,className:ze.icon,onClick:()=>{setTimeout(()=>{t(n?"light":"dark")},100)},children:n?V.jsx(AC,{}):V.jsx(DC,{})}),V.jsx(Vf,{menu:{items:x},trigger:["click"],children:V.jsxs("div",{className:ze.avatar,children:[V.jsx(vc,{icon:V.jsx(Gf,{})}),V.jsxs("span",{children:["欢迎登录,",((w=St.localGet("account"))==null?void 0:w.name)||"您好"]})]})})]})]}),V.jsx(QC,{className:ze.content,children:V.jsxs(Zo,{className:ze.contentScroll,children:[V.jsx(Qd,{}),V.jsx(fp,{open:u,onCancel:b})]})}),V.jsxs(JC,{className:ze.footer,children:["©",new Date().getFullYear()," 丰选科技支持"]})]})]})]})};export{cx as default};
