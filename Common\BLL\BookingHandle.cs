﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Model;

namespace zhiying_online.Common.BLL
{
    /// <summary>
    /// 预约检查
    /// </summary>
    public class BookingHandle
    {
        /// <summary>
        /// 获取预约列表
        /// </summary>
        /// <param name="name"></param>
        /// <param name="phone"></param>
        /// <param name="idcard"></param>
        /// <param name="type"></param>
        /// <param name="star"></param>
        /// <param name="end"></param>
        /// <param name="status"></param>
        /// <param name="PageSize"></param>
        /// <param name="PageNo"></param>
        /// <returns></returns>
        public static RetMsg GetBookingList(string name, string phone, string idcard,int? type, DateTime? star, DateTime? end,string status, int PageSize = 10, int PageNo = 1)
        {
            using (var db = new Model.zhiying_online())
            {
                IEnumerable<booking> list = db.booking.Where(m => m.b_id > 0);
                if (!string.IsNullOrEmpty(name))
                {
                    var plist = db.patient.Where(m => m.p_name == name).Select(m => m.p_id).ToList();
                    list = list.Where(m => plist.Contains(m.b_pid.Value));
                }
                if (!string.IsNullOrEmpty(phone))
                {
                    var plist = db.patient.Where(m => m.p_phone == phone).Select(m => m.p_id).ToList();
                    list = list.Where(m => plist.Contains(m.b_pid.Value));
                }
                if (!string.IsNullOrEmpty(idcard))
                {
                    var plist = db.patient.Where(m => m.p_idcard == idcard).Select(m => m.p_id).ToList();
                    list = list.Where(m => plist.Contains(m.b_pid.Value));
                }
                if (star.HasValue && end.HasValue)
                {
                    list = list.Where(m => m.b_bookingtime >= star && m.b_bookingtime <= end);
                }
                if (type.HasValue)
                {
                    list = list.Where(m => m.b_type == type);
                }
                if (!string.IsNullOrEmpty(status))
                {
                    if (status == "已取消")
                    {
                        list = list.Where(m => m.b_enabletime.HasValue);
                    }
                    if (status == "待检查")
                    {
                        list = list.Where(m => !m.b_enabletime.HasValue && m.b_bookingtime > DateTime.Now.AddDays(1).Date);
                    }
                    if (status == "已完成")
                    {
                        list = list.Where(m => !m.b_enabletime.HasValue && m.b_bookingtime < DateTime.Now.AddDays(1).Date);
                    }
                }

                var count = list.Count();

                list = list.OrderByDescending(m => m.b_time).Skip((PageNo - 1) * PageSize).Take(PageSize).ToList();

                var puids = list.Select(m => m.b_pid).ToList();
                var users = db.patient.Where(m => puids.Contains(m.p_id)).ToList();

                List<object> ulist = new List<object> { };
                foreach (var item in list)
                {
                    var _status = "待检查";
                    if (item.b_enabletime.HasValue)
                    {
                        _status = "已取消";
                    }
                    if (!item.b_enabletime.HasValue && item.b_bookingtime > DateTime.Now.AddDays(1).Date)
                    {
                        _status = "待检查";
                    }
                    if (!item.b_enabletime.HasValue && item.b_bookingtime < DateTime.Now.AddDays(1).Date)
                    {
                        _status = "已完成";
                    }
                    var _type = "";
                    switch (item.b_type)
                    {
                        case 1:
                            _type = "PET";
                            break;
                        case 2:
                            _type = "DR";
                            break;
                        case 3:
                            _type = "CT";
                            break;
                    }
                    var pu = users.Where(m => m.p_id == item.b_pid).FirstOrDefault();
                    ulist.Add(new
                    {
                        name = pu.p_name,
                        phone = pu.p_phone,//Tools.MaskPhoneNumber(pu.p_phone),
                        idcard = pu.p_idcard,//Tools.MaskIdNumber(pu.p_idcard),
                        id = item.b_id,
                        uid = item.b_uid,
                        pid = item.b_pid,
                        type = _type,
                        time = item.b_time,
                        bookingtime = item.b_bookingtime,
                        enabletime = item.b_enabletime,
                        status = _status
                    });
                }

                return new RetMsg
                {
                    code = 0,
                    msg = "请求成功",
                    count = count,
                    result = ulist
                };
            }
        }

        /// <summary>
        /// 根据就诊人获取预约列表
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        public static RetMsg GetBookingListByPid(long? pid)
        {
            using (var db = new Model.zhiying_online())
            {
                var list = db.booking.Where(m => m.b_pid == pid).OrderByDescending(m => m.b_time).ToList();
                var puids = list.Select(m => m.b_pid).ToList();
                var users = db.patient.Where(m => puids.Contains(m.p_id)).ToList();

                List<object> ulist = new List<object> { };
                foreach (var item in list)
                {
                    var _status = "待检查";
                    if (item.b_enabletime.HasValue)
                    {
                        _status = "已取消";
                    }
                    if (!item.b_enabletime.HasValue && item.b_bookingtime > DateTime.Now.AddDays(1).Date)
                    {
                        _status = "待检查";
                    }
                    if (!item.b_enabletime.HasValue && item.b_bookingtime < DateTime.Now.AddDays(1).Date)
                    {
                        _status = "已完成";
                    }
                    var pu = users.Where(m => m.p_id == item.b_pid).FirstOrDefault();
                    ulist.Add(new
                    {
                        name = pu.p_name,
                        phone = pu.p_phone,//Tools.MaskPhoneNumber(pu.p_phone),
                        idcard = pu.p_idcard,// Tools.MaskIdNumber(pu.p_idcard),
                        id = item.b_id,
                        uid = item.b_uid,
                        pid = item.b_pid,
                        type = item.b_type,
                        time = item.b_time,
                        bookingtime = item.b_bookingtime,
                        enabletime = item.b_enabletime,
                        status = _status
                    });
                }

                return new RetMsg
                {
                    code = 0,
                    msg = "请求成功",
                    count = list.Count,
                    result = ulist
                };
            }
        }

        /// <summary>
        /// 保存预约
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="pid"></param>
        /// <param name="type"></param>
        /// <param name="bookingtime"></param>
        /// <returns></returns>
        public static RetMsg SaveBooking(long? uid, long? pid, int type, DateTime? bookingtime)
        {
            using (var db = new Model.zhiying_online())
            {
                var ubr = db.booking.Where(m => m.b_pid == pid && m.b_bookingtime == bookingtime).FirstOrDefault();
                if (ubr != null)
                {
                    return new RetMsg { code = 1, msg = "您已经预约过了" };
                }
                ubr = new booking
                {
                    b_bookingtime = bookingtime,
                    b_id = Tools.Get_Id(),
                    b_pid = pid,
                    b_time = DateTime.Now,
                    b_type = type,
                    b_uid = uid
                };
                db.booking.Add(ubr);
                db.SaveChanges();

                return new RetMsg { code = 0, msg = "预约成功" };
            }
        }

        /// <summary>
        /// 取消预约
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static RetMsg EnableBooking(long? id)
        {
            using (var db = new Model.zhiying_online())
            {
                var ubr = db.booking.Find(id);
                ubr.b_enabletime = DateTime.Now;
                db.SaveChanges();
                return new RetMsg { code = 0, msg = "取消成功" };
            }
        }
    }
}
