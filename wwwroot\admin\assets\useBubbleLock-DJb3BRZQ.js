import{s as bt,R as ht,r as c,y as Ht,c as j,z as vt,d as S,e as yt,l as Y,m as Xt,_ as ft,o as Wt,x as Ut,a2 as Yt,a as Zt,b as ut,H as mt}from"./index-DU7CKWYf.js";import{F as J,K as C,i as qt,h as Kt,av as Ft,l as Gt,m as wt,u as Ct,q as Qt,aH as Jt,o as te,r as ee,G as oe,z as re}from"./index-NJKMylyL.js";function ne(t,o){return J.reduce((r,e)=>{const a=t[`${e}1`],n=t[`${e}3`],s=t[`${e}6`],i=t[`${e}7`];return Object.assign(Object.assign({},r),o(e,{lightColor:a,lightBorderColor:n,darkColor:s,textColor:i}))},{})}const ae=new C("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),se=new C("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),pt=new C("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),dt=new C("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),ie=new C("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),le=new C("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),ce=new C("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),fe=new C("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),ue=new C("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),me=new C("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),pe=new C("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),de=new C("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),ge={zoom:{inKeyframes:ae,outKeyframes:se},"zoom-big":{inKeyframes:pt,outKeyframes:dt},"zoom-big-fast":{inKeyframes:pt,outKeyframes:dt},"zoom-left":{inKeyframes:ce,outKeyframes:fe},"zoom-right":{inKeyframes:ue,outKeyframes:me},"zoom-up":{inKeyframes:ie,outKeyframes:le},"zoom-down":{inKeyframes:pe,outKeyframes:de}},be=(t,o)=>{const{antCls:r}=t,e=`${r}-${o}`,{inKeyframes:a,outKeyframes:n}=ge[o];return[qt(e,a,n,o==="zoom-big-fast"?t.motionDurationFast:t.motionDurationMid),{[`
        ${e}-enter,
        ${e}-appear
      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:t.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${e}-leave`]:{animationTimingFunction:t.motionEaseInOutCirc}}]},he=["xxl","xl","lg","md","sm","xs"],ve=t=>({xs:`(max-width: ${t.screenXSMax}px)`,sm:`(min-width: ${t.screenSM}px)`,md:`(min-width: ${t.screenMD}px)`,lg:`(min-width: ${t.screenLG}px)`,xl:`(min-width: ${t.screenXL}px)`,xxl:`(min-width: ${t.screenXXL}px)`}),ye=t=>{const o=t,r=[].concat(he).reverse();return r.forEach((e,a)=>{const n=e.toUpperCase(),s=`screen${n}Min`,i=`screen${n}`;if(!(o[s]<=o[i]))throw new Error(`${s}<=${i} fails : !(${o[s]}<=${o[i]})`);if(a<r.length-1){const l=`screen${n}Max`;if(!(o[i]<=o[l]))throw new Error(`${i}<=${l} fails : !(${o[i]}<=${o[l]})`);const m=`screen${r[a+1].toUpperCase()}Min`;if(!(o[l]<=o[m]))throw new Error(`${l}<=${m} fails : !(${o[l]}<=${o[m]})`)}}),t};function we(){const[,t]=bt(),o=ve(ye(t));return ht.useMemo(()=>{const r=new Map;let e=-1,a={};return{matchHandlers:{},dispatch(n){return a=n,r.forEach(s=>s(a)),r.size>=1},subscribe(n){return r.size||this.register(),e+=1,r.set(e,n),n(a),e},unsubscribe(n){r.delete(n),r.size||this.unregister()},unregister(){Object.keys(o).forEach(n=>{const s=o[n],i=this.matchHandlers[s];i==null||i.mql.removeListener(i==null?void 0:i.listener)}),r.clear()},register(){Object.keys(o).forEach(n=>{const s=o[n],i=f=>{let{matches:m}=f;this.dispatch(Object.assign(Object.assign({},a),{[n]:m}))},l=window.matchMedia(s);l.addListener(i),this.matchHandlers[s]={mql:l,listener:i},i(l)})},responsiveMap:o}},[t])}function Ce(){const[,t]=c.useReducer(o=>o+1,0);return t}function We(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=c.useRef(o),e=Ce(),a=we();return Ht(()=>{const n=a.subscribe(s=>{r.current=s,t&&e()});return()=>a.unsubscribe(n)},[]),r.current}function Ot(t){var o=t.children,r=t.prefixCls,e=t.id,a=t.overlayInnerStyle,n=t.bodyClassName,s=t.className,i=t.style;return c.createElement("div",{className:j("".concat(r,"-content"),s),style:i},c.createElement("div",{className:j("".concat(r,"-inner"),n),id:e,role:"tooltip",style:a},typeof o=="function"?o():o))}var D={shiftX:64,adjustY:1},H={adjustX:1,shiftY:!0},w=[0,0],Oe={left:{points:["cr","cl"],overflow:H,offset:[-4,0],targetOffset:w},right:{points:["cl","cr"],overflow:H,offset:[4,0],targetOffset:w},top:{points:["bc","tc"],overflow:D,offset:[0,-4],targetOffset:w},bottom:{points:["tc","bc"],overflow:D,offset:[0,4],targetOffset:w},topLeft:{points:["bl","tl"],overflow:D,offset:[0,-4],targetOffset:w},leftTop:{points:["tr","tl"],overflow:H,offset:[-4,0],targetOffset:w},topRight:{points:["br","tr"],overflow:D,offset:[0,-4],targetOffset:w},rightTop:{points:["tl","tr"],overflow:H,offset:[4,0],targetOffset:w},bottomRight:{points:["tr","br"],overflow:D,offset:[0,4],targetOffset:w},rightBottom:{points:["bl","br"],overflow:H,offset:[4,0],targetOffset:w},bottomLeft:{points:["tl","bl"],overflow:D,offset:[0,4],targetOffset:w},leftBottom:{points:["br","bl"],overflow:H,offset:[-4,0],targetOffset:w}},$e=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],xe=function(o,r){var e=o.overlayClassName,a=o.trigger,n=a===void 0?["hover"]:a,s=o.mouseEnterDelay,i=s===void 0?0:s,l=o.mouseLeaveDelay,f=l===void 0?.1:l,m=o.overlayStyle,u=o.prefixCls,p=u===void 0?"rc-tooltip":u,d=o.children,g=o.onVisibleChange,b=o.afterVisibleChange,O=o.transitionName,$=o.animation,y=o.motion,z=o.placement,N=z===void 0?"right":z,A=o.align,X=A===void 0?{}:A,I=o.destroyTooltipOnHide,h=I===void 0?!1:I,_=o.defaultVisible,tt=o.getTooltipContainer,T=o.overlayInnerStyle;o.arrowContent;var P=o.overlay,Z=o.id,W=o.showArrow,E=W===void 0?!0:W,k=o.classNames,R=o.styles,et=vt(o,$e),q=Kt(Z),K=c.useRef(null);c.useImperativeHandle(r,function(){return K.current});var U=S({},et);"visible"in o&&(U.popupVisible=o.visible);var F=function(){return c.createElement(Ot,{key:"content",prefixCls:p,id:q,bodyClassName:k==null?void 0:k.body,overlayInnerStyle:S(S({},T),R==null?void 0:R.body)},P)},ot=function(){var L=c.Children.only(d),rt=(L==null?void 0:L.props)||{},nt=S(S({},rt),{},{"aria-describedby":P?q:null});return c.cloneElement(d,nt)};return c.createElement(Ft,yt({popupClassName:j(e,k==null?void 0:k.root),prefixCls:p,popup:F,action:n,builtinPlacements:Oe,popupPlacement:N,ref:K,popupAlign:X,getPopupContainer:tt,onPopupVisibleChange:g,afterPopupVisibleChange:b,popupTransitionName:O,popupAnimation:$,popupMotion:y,defaultPopupVisible:_,autoDestroy:h,mouseLeaveDelay:f,popupStyle:S(S({},m),R==null?void 0:R.root),mouseEnterDelay:i,arrow:E},U),ot())};const _e=c.forwardRef(xe);function Pe(t){const{sizePopupArrow:o,borderRadiusXS:r,borderRadiusOuter:e}=t,a=o/2,n=0,s=a,i=e*1/Math.sqrt(2),l=a-e*(1-1/Math.sqrt(2)),f=a-r*(1/Math.sqrt(2)),m=e*(Math.sqrt(2)-1)+r*(1/Math.sqrt(2)),u=2*a-f,p=m,d=2*a-i,g=l,b=2*a-n,O=s,$=a*Math.sqrt(2)+e*(Math.sqrt(2)-2),y=e*(Math.sqrt(2)-1),z=`polygon(${y}px 100%, 50% ${y}px, ${2*a-y}px 100%, ${y}px 100%)`,N=`path('M ${n} ${s} A ${e} ${e} 0 0 0 ${i} ${l} L ${f} ${m} A ${r} ${r} 0 0 1 ${u} ${p} L ${d} ${g} A ${e} ${e} 0 0 0 ${b} ${O} Z')`;return{arrowShadowWidth:$,arrowPath:N,arrowPolygon:z}}const Re=(t,o,r)=>{const{sizePopupArrow:e,arrowPolygon:a,arrowPath:n,arrowShadowWidth:s,borderRadiusXS:i,calc:l}=t;return{pointerEvents:"none",width:e,height:e,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:e,height:l(e).div(2).equal(),background:o,clipPath:{_multi_value_:!0,value:[a,n]},content:'""'},"&::after":{content:'""',position:"absolute",width:s,height:s,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${Y(i)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:r,zIndex:0,background:"transparent"}}},$t=8;function xt(t){const{contentRadius:o,limitVerticalRadius:r}=t,e=o>12?o+2:12;return{arrowOffsetHorizontal:e,arrowOffsetVertical:r?$t:e}}function Q(t,o){return t?o:{}}function Se(t,o,r){const{componentCls:e,boxShadowPopoverArrow:a,arrowOffsetVertical:n,arrowOffsetHorizontal:s}=t,{arrowDistance:i=0,arrowPlacement:l={left:!0,right:!0,top:!0,bottom:!0}}=r||{};return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({[`${e}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},Re(t,o,a)),{"&:before":{background:o}})]},Q(!!l.top,{[[`&-placement-top > ${e}-arrow`,`&-placement-topLeft > ${e}-arrow`,`&-placement-topRight > ${e}-arrow`].join(",")]:{bottom:i,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${e}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":s,[`> ${e}-arrow`]:{left:{_skip_check_:!0,value:s}}},"&-placement-topRight":{"--arrow-offset-horizontal":`calc(100% - ${Y(s)})`,[`> ${e}-arrow`]:{right:{_skip_check_:!0,value:s}}}})),Q(!!l.bottom,{[[`&-placement-bottom > ${e}-arrow`,`&-placement-bottomLeft > ${e}-arrow`,`&-placement-bottomRight > ${e}-arrow`].join(",")]:{top:i,transform:"translateY(-100%)"},[`&-placement-bottom > ${e}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":s,[`> ${e}-arrow`]:{left:{_skip_check_:!0,value:s}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":`calc(100% - ${Y(s)})`,[`> ${e}-arrow`]:{right:{_skip_check_:!0,value:s}}}})),Q(!!l.left,{[[`&-placement-left > ${e}-arrow`,`&-placement-leftTop > ${e}-arrow`,`&-placement-leftBottom > ${e}-arrow`].join(",")]:{right:{_skip_check_:!0,value:i},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${e}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${e}-arrow`]:{top:n},[`&-placement-leftBottom > ${e}-arrow`]:{bottom:n}})),Q(!!l.right,{[[`&-placement-right > ${e}-arrow`,`&-placement-rightTop > ${e}-arrow`,`&-placement-rightBottom > ${e}-arrow`].join(",")]:{left:{_skip_check_:!0,value:i},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${e}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${e}-arrow`]:{top:n},[`&-placement-rightBottom > ${e}-arrow`]:{bottom:n}}))}}function je(t,o,r,e){if(e===!1)return{adjustX:!1,adjustY:!1};const a=e&&typeof e=="object"?e:{},n={};switch(t){case"top":case"bottom":n.shiftX=o.arrowOffsetHorizontal*2+r,n.shiftY=!0,n.adjustY=!0;break;case"left":case"right":n.shiftY=o.arrowOffsetVertical*2+r,n.shiftX=!0,n.adjustX=!0;break}const s=Object.assign(Object.assign({},n),a);return s.shiftX||(s.adjustX=!0),s.shiftY||(s.adjustY=!0),s}const gt={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},ze={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},Te=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function ke(t){const{arrowWidth:o,autoAdjustOverflow:r,arrowPointAtCenter:e,offset:a,borderRadius:n,visibleFirst:s}=t,i=o/2,l={};return Object.keys(gt).forEach(f=>{const m=e&&ze[f]||gt[f],u=Object.assign(Object.assign({},m),{offset:[0,0],dynamicInset:!0});switch(l[f]=u,Te.has(f)&&(u.autoArrow=!1),f){case"top":case"topLeft":case"topRight":u.offset[1]=-i-a;break;case"bottom":case"bottomLeft":case"bottomRight":u.offset[1]=i+a;break;case"left":case"leftTop":case"leftBottom":u.offset[0]=-i-a;break;case"right":case"rightTop":case"rightBottom":u.offset[0]=i+a;break}const p=xt({contentRadius:n,limitVerticalRadius:!0});if(e)switch(f){case"topLeft":case"bottomLeft":u.offset[0]=-p.arrowOffsetHorizontal-i;break;case"topRight":case"bottomRight":u.offset[0]=p.arrowOffsetHorizontal+i;break;case"leftTop":case"rightTop":u.offset[1]=-p.arrowOffsetHorizontal*2+i;break;case"leftBottom":case"rightBottom":u.offset[1]=p.arrowOffsetHorizontal*2-i;break}u.overflow=je(f,p,o,r),s&&(u.htmlRegion="visibleFirst")}),l}const Ne=t=>{const{calc:o,componentCls:r,tooltipMaxWidth:e,tooltipColor:a,tooltipBg:n,tooltipBorderRadius:s,zIndexPopup:i,controlHeight:l,boxShadowSecondary:f,paddingSM:m,paddingXS:u,arrowOffsetHorizontal:p,sizePopupArrow:d}=t,g=o(s).add(d).add(p).equal(),b=o(s).mul(2).add(d).equal();return[{[r]:Object.assign(Object.assign(Object.assign(Object.assign({},Xt(t)),{position:"absolute",zIndex:i,display:"block",width:"max-content",maxWidth:e,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"&-hidden":{display:"none"},"--antd-arrow-background-color":n,[`${r}-inner`]:{minWidth:b,minHeight:l,padding:`${Y(t.calc(m).div(2).equal())} ${Y(u)}`,color:a,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:n,borderRadius:s,boxShadow:f,boxSizing:"border-box"},[["&-placement-topLeft","&-placement-topRight","&-placement-bottomLeft","&-placement-bottomRight"].join(",")]:{minWidth:g},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${r}-inner`]:{borderRadius:t.min(s,$t)}},[`${r}-content`]:{position:"relative"}}),ne(t,(O,$)=>{let{darkColor:y}=$;return{[`&${r}-${O}`]:{[`${r}-inner`]:{backgroundColor:y},[`${r}-arrow`]:{"--antd-arrow-background-color":y}}}})),{"&-rtl":{direction:"rtl"}})},Se(t,"var(--antd-arrow-background-color)"),{[`${r}-pure`]:{position:"relative",maxWidth:"none",margin:t.sizePopupArrow}}]},Ie=t=>Object.assign(Object.assign({zIndexPopup:t.zIndexPopupBase+70},xt({contentRadius:t.borderRadius,limitVerticalRadius:!0})),Pe(wt(t,{borderRadiusOuter:Math.min(t.borderRadiusOuter,4)}))),_t=function(t){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Gt("Tooltip",e=>{const{borderRadius:a,colorTextLightSolid:n,colorBgSpotlight:s}=e,i=wt(e,{tooltipMaxWidth:250,tooltipColor:n,tooltipBorderRadius:a,tooltipBg:s});return[Ne(i),be(e,"zoom-big-fast")]},Ie,{resetStyle:!1,injectStyle:o})(t)},Ae=J.map(t=>`${t}-inverse`);function Ee(t){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0)?[].concat(ft(Ae),ft(J)).includes(t):J.includes(t)}function Pt(t,o){const r=Ee(o),e=j({[`${t}-${o}`]:o&&r}),a={},n={};return o&&!r&&(a.background=o,n["--antd-arrow-background-color"]=o),{className:e,overlayStyle:a,arrowStyle:n}}const Me=t=>{const{prefixCls:o,className:r,placement:e="top",title:a,color:n,overlayInnerStyle:s}=t,{getPrefixCls:i}=c.useContext(Wt),l=i("tooltip",o),[f,m,u]=_t(l),p=Pt(l,n),d=p.arrowStyle,g=Object.assign(Object.assign({},s),p.overlayStyle),b=j(m,u,l,`${l}-pure`,`${l}-placement-${e}`,r,p.className);return f(c.createElement("div",{className:b,style:d},c.createElement("div",{className:`${l}-arrow`}),c.createElement(Ot,Object.assign({},t,{className:m,prefixCls:l,overlayInnerStyle:g}),a)))};var Le=function(t,o){var r={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.indexOf(e)<0&&(r[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)o.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(r[e[a]]=t[e[a]]);return r};const Be=c.forwardRef((t,o)=>{var r,e;const{prefixCls:a,openClassName:n,getTooltipContainer:s,color:i,overlayInnerStyle:l,children:f,afterOpenChange:m,afterVisibleChange:u,destroyTooltipOnHide:p,arrow:d=!0,title:g,overlay:b,builtinPlacements:O,arrowPointAtCenter:$=!1,autoAdjustOverflow:y=!0,motion:z,getPopupContainer:N,placement:A="top",mouseEnterDelay:X=.1,mouseLeaveDelay:I=.1,overlayStyle:h,rootClassName:_,overlayClassName:tt,styles:T,classNames:P}=t,Z=Le(t,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),W=!!d,[,E]=bt(),{getPopupContainer:k,getPrefixCls:R,direction:et,className:q,style:K,classNames:U,styles:F}=Ut("tooltip"),ot=Yt(),M=c.useRef(null),L=()=>{var v;(v=M.current)===null||v===void 0||v.forceAlign()};c.useImperativeHandle(o,()=>{var v,x;return{forceAlign:L,forcePopupAlign:()=>{ot.deprecated(!1,"forcePopupAlign","forceAlign"),L()},nativeElement:(v=M.current)===null||v===void 0?void 0:v.nativeElement,popupElement:(x=M.current)===null||x===void 0?void 0:x.popupElement}});const[rt,nt]=Ct(!1,{value:(r=t.open)!==null&&r!==void 0?r:t.visible,defaultValue:(e=t.defaultOpen)!==null&&e!==void 0?e:t.defaultVisible}),at=!g&&!b&&g!==0,Rt=v=>{var x,V;nt(at?!1:v),at||((x=t.onOpenChange)===null||x===void 0||x.call(t,v),(V=t.onVisibleChange)===null||V===void 0||V.call(t,v))},St=c.useMemo(()=>{var v,x;let V=$;return typeof d=="object"&&(V=(x=(v=d.pointAtCenter)!==null&&v!==void 0?v:d.arrowPointAtCenter)!==null&&x!==void 0?x:$),O||ke({arrowPointAtCenter:V,autoAdjustOverflow:y,arrowWidth:W?E.sizePopupArrow:0,borderRadius:E.borderRadius,offset:E.marginXXS,visibleFirst:!0})},[$,d,O,E]),st=c.useMemo(()=>g===0?g:b||g||"",[b,g]),jt=c.createElement(Qt,{space:!0},typeof st=="function"?st():st),B=R("tooltip",a),zt=R(),Tt=t["data-popover-inject"];let it=rt;!("open"in t)&&!("visible"in t)&&at&&(it=!1);const lt=c.isValidElement(f)&&!Jt(f)?f:c.createElement("span",null,f),G=lt.props,kt=!G.className||typeof G.className=="string"?j(G.className,n||`${B}-open`):G.className,[Nt,It,At]=_t(B,!Tt),ct=Pt(B,i),Et=ct.arrowStyle,Mt=j(tt,{[`${B}-rtl`]:et==="rtl"},ct.className,_,It,At,q,U.root,P==null?void 0:P.root),Lt=j(U.body,P==null?void 0:P.body),[Bt,Vt]=te("Tooltip",Z.zIndex),Dt=c.createElement(_e,Object.assign({},Z,{zIndex:Bt,showArrow:W,placement:A,mouseEnterDelay:X,mouseLeaveDelay:I,prefixCls:B,classNames:{root:Mt,body:Lt},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Et),F.root),K),h),T==null?void 0:T.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},F.body),l),T==null?void 0:T.body),ct.overlayStyle)},getTooltipContainer:N||s||k,ref:M,builtinPlacements:St,overlay:jt,visible:it,onVisibleChange:Rt,afterVisibleChange:m??u,arrowContent:c.createElement("span",{className:`${B}-arrow-content`}),motion:{motionName:ee(zt,"zoom-big-fast",t.transitionName),motionDeadline:1e3},destroyTooltipOnHide:!!p}),it?oe(lt,{className:kt}):lt);return Nt(c.createElement(re.Provider,{value:Vt},Dt))}),Ve=Be;Ve._InternalPanelDoNotUseOrYouWillBeFired=Me;var De=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],Ue=c.forwardRef(function(t,o){var r=t.prefixCls,e=r===void 0?"rc-checkbox":r,a=t.className,n=t.style,s=t.checked,i=t.disabled,l=t.defaultChecked,f=l===void 0?!1:l,m=t.type,u=m===void 0?"checkbox":m,p=t.title,d=t.onChange,g=vt(t,De),b=c.useRef(null),O=c.useRef(null),$=Ct(f,{value:s}),y=Zt($,2),z=y[0],N=y[1];c.useImperativeHandle(o,function(){return{focus:function(h){var _;(_=b.current)===null||_===void 0||_.focus(h)},blur:function(){var h;(h=b.current)===null||h===void 0||h.blur()},input:b.current,nativeElement:O.current}});var A=j(e,a,ut(ut({},"".concat(e,"-checked"),z),"".concat(e,"-disabled"),i)),X=function(h){i||("checked"in t||N(h.target.checked),d==null||d({target:S(S({},t),{},{type:u,checked:h.target.checked}),stopPropagation:function(){h.stopPropagation()},preventDefault:function(){h.preventDefault()},nativeEvent:h.nativeEvent}))};return c.createElement("span",{className:A,title:p,style:n,ref:O},c.createElement("input",yt({},g,{className:"".concat(e,"-input"),ref:b,onChange:X,disabled:i,checked:!!z,type:u})),c.createElement("span",{className:"".concat(e,"-inner")}))});function Ye(t){const o=ht.useRef(null),r=()=>{mt.cancel(o.current),o.current=null};return[()=>{r(),o.current=mt(()=>{o.current=null})},n=>{o.current&&(n.stopPropagation(),r()),t==null||t(n)}]}export{Ue as C,Ot as P,Ve as T,Pe as a,xt as b,Re as c,Ce as d,ke as e,Ye as f,Se as g,be as i,he as r,We as u,ae as z};
