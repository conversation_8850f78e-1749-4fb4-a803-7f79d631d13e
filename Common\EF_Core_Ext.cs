﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

namespace zhiying_online.Common
{

    /// <summary>
    /// EF Core 拓展SqlQuery方法
    /// </summary>
    public static class EF_Core_Ext
    {
        /// <summary>
        /// 查询int类型
        /// </summary>
        public class ObjSum
        {
            public int count { get; set; }

            public long? id { get; set; }
        }

        /// <summary>
        /// 查询int类型
        /// </summary>
        public class ObjInt { 
            public int count { get; set; }
        }

        /// <summary>
        /// 查询decimal类型
        /// </summary>
        public class Objdecimal
        {
            public int count { get; set; }
        }

        /// <summary>
        /// 查询string
        /// </summary>
        public class ObjString { 
            public string str { get; set; }
        }

        /// <summary>
        /// 查询string
        /// </summary>
        public class Objlong
        {
            public long? longstr { get; set; }
        }

        /// <summary>
        /// 查询DateTime
        /// </summary>
        public class Objtime
        {
            public DateTime? longtime { get; set; }
        }

        /// <summary>
        /// 返回对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="facade"></param>
        /// <param name="sql"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public static IEnumerable<T> SqlQuery<T>(this DatabaseFacade facade, string sql, params object[] parameters) where T : class, new()
        {
            DataTable dt = SqlQuery(facade, sql, parameters);
            return dt.ToEnumerable<T>();
        }

        /// <summary>
        /// datat转对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static IEnumerable<T> ToEnumerable<T>(this DataTable dt) where T : class, new()
        {
            PropertyInfo[] propertyInfos = typeof(T).GetProperties();
            T[] ts = new T[dt.Rows.Count];
            int i = 0;
            foreach (DataRow row in dt.Rows)
            {
                T t = new T();
                foreach (PropertyInfo p in propertyInfos)
                {
                    if (dt.Columns.IndexOf(p.Name) != -1 && row[p.Name] != DBNull.Value)
                    {
                        if (p.PropertyType.FullName.Contains("System.Int32"))//此处判断下Int32类型，如果是则强转
                        {
                            p.SetValue(t, Convert.ToInt32(row[p.Name]), null);
                        }
                        else if (p.PropertyType.FullName.Contains("System.Int64"))
                        {
                            p.SetValue(t, Convert.ToInt64(row[p.Name]), null);
                        }
                        else if (p.PropertyType.FullName.Contains("System.Boolean"))
                        {
                            p.SetValue(t, Convert.ToBoolean(row[p.Name]), null);
                        }
                        else
                        {
                            p.SetValue(t, row[p.Name], null);
                        }
                    }
                }
                ts[i] = (T)t;
                i++;
            }
            return ts;
        }

        /// <summary>
        /// 返回DataTable
        /// </summary>
        /// <param name="facade"></param>
        /// <param name="sql"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public static DataTable SqlQuery(this DatabaseFacade facade, string sql, params object[] parameters)
        {
            //sharding路由

            //DbCommand cmd = CreateCommand(facade, sql, out DbConnection conn, parameters);
            ////超时时间设置5分钟
            ////cmd.CommandTimeout = 1000 * 60 * 5;
            //DbDataReader reader = cmd.ExecuteReader();
            //DataTable dt = new DataTable();
            //dt.Load(reader);
            //reader.Close();
            //conn.Close();
            //return dt;

            DbConnection conn = facade.GetDbConnection();
            conn.Open();
            DbCommand cmd = conn.CreateCommand();
            if (facade.IsMySql())
            {
                cmd.CommandText = sql;
                CombineParams(ref cmd, parameters);
            };
            //超时时间设置5分钟
            cmd.CommandTimeout = 1000 * 60 * 5;
            DbDataReader reader = cmd.ExecuteReader();
            DataTable dt = new DataTable();
            dt.Load(reader);
            reader.Close();
            conn.Close();
            return dt;
        }



        private static DbCommand CreateCommand(DatabaseFacade facade, string sql, out DbConnection dbConn, params object[] parameters)
        {
            DbConnection conn = facade.GetDbConnection();
            dbConn = conn;
            conn.Open();
            DbCommand cmd = conn.CreateCommand();
            if (facade.IsMySql())
            {
                cmd.CommandText = sql;
                CombineParams(ref cmd, parameters);
            }
            return cmd;
        }

        private static void CombineParams(ref DbCommand command, params object[] parameters)
        {
            if (parameters != null)
            {
                foreach (var parameter in parameters)
                {
                    //if (!parameter.ParameterName.Contains("@"))
                    //    parameter.ParameterName = $"@{parameter.ParameterName}";
                    command.Parameters.Add(parameter);
                }
            }
        }


        /// <summary>
        /// list转sql
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <param name="tableName"></param>
        /// <returns></returns>
        public static string InsertSql<T>(List<T> t, string tableName) where T : class
        {
            if (t.Count == 0 || string.IsNullOrEmpty(tableName))
            {
                return string.Empty;
            }

            //获取栏目名称
            string columns = GetColmons(t.FirstOrDefault());
            if (string.IsNullOrEmpty(columns))
            {
                return string.Empty;
            }

            //批量解析成字符串
            List<string> values = new List<string> { };
            foreach (var item in t)
            {
                var vs = GetValues(item);
                if (!string.IsNullOrEmpty(vs))
                {
                    values.Add(vs);
                }
            }
            if (values.Count==0)
            {
                return string.Empty;
            }

            //构造字符串对象
            StringBuilder sql = new StringBuilder();
            sql.Append("Insert IGNORE into " + tableName);
            sql.Append("(" + columns + ")values");
            foreach (var item in values)
            {
                sql.Append("(" + item + "),");
            }
            //去掉最后一个字符
            sql.Remove(sql.Length - 1,1);

            //返回数据
            return sql.ToString();
        }

        /// <summary>
        /// 获得类型的列名
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        private static string GetColmons<T>(T obj)
        {
            if (obj == null)
            {
                return string.Empty;
            }
            return string.Join(",", obj.GetType().GetProperties().Select(p => p.Name).ToList());
        }

        /// <summary>
        /// 获得值
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        private static string GetValues<T>(T obj)
        {
            if (obj == null)
            {
                return string.Empty;
            }
            List<string> oList = new List<string> { };
            foreach (var item in obj.GetType().GetProperties())
            {
                if (item.GetValue(obj) != null)
                {
                    //时间取到毫秒级
                    if (item.PropertyType.FullName.Contains("System.DateTime"))
                    {
                        oList.Add(string.Format("'{0:yyyy-MM-dd HH:mm:ss.fff}'", item.GetValue(obj)));
                    }
                    else
                    {
                        var str = string.Format("{0}", item.GetValue(obj));
                        oList.Add("'" + GetStrByRegex(str) + "'");
                    }
                }
                else
                {
                    oList.Add("null");
                }
            }
            return string.Join(",", oList);
            //return string.Join(",", obj.GetType().GetProperties().Select(p => p.GetValue(obj) == null ? "null" : string.Format("'{0}'", p.GetValue(obj))).ToArray());
        }


        /// <summary>
        /// 过滤特殊字符
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static string GetStrByRegex(string str)
        {
            string wunickname = "";
            if (!string.IsNullOrEmpty(str))
            {
                wunickname = str.Replace("?", "").Replace("\r", "").Replace("\n", "").Replace("，", "").Replace("'", "");//收货人 .Replace("\"", "").Replace(",", "")
                ////判断纯数字防止科学技术
                //Regex regex = new Regex("^[0-9]*$");
                ////数字位数长度大于10为转字符串 手机11位
                //if (regex.IsMatch(wunickname) && wunickname.Length > 10)
                //{
                //    wunickname += "\t";
                //}
            }
            return wunickname;
        }

        //public static bool ExecuteSqlCommand(this DatabaseFacade facade, string sql, params object[] parameters) { DbCommand cmd = CreateCommand(facade, sql, out DbConnection conn, parameters); try { cmd.ExecuteNonQuery(); return true; } catch (Exception) { return false; } finally { conn.Close(); } }
    }
}
