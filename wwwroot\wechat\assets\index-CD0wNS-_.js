import{a as re,s as O,r as c,u as C,j as e,C as M,L as I,M as D,R as A,B as y,b as ae,c as ce,d as de,e as ue,f as J,T as $,g as me,h as P,S as G,F as S,I as R,i as L,k as ee,D as j,l as pe,E as te,A as xe,m as he,n as ne,o as ge,p as w,q as fe,t as je}from"./vendor-CNKWSgxo.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const d of i)if(d.type==="childList")for(const u of d.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&s(u)}).observe(document,{childList:!0,subtree:!0});function o(i){const d={};return i.integrity&&(d.integrity=i.integrity),i.referrerPolicy&&(d.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?d.credentials="include":i.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function s(i){if(i.ep)return;i.ep=!0;const d=o(i);fetch(i.href,d)}})();const ye=()=>{let t="";for(let n=1;n<=32;n++){let o=Math.floor(Math.random()*16).toString(16);t+=o,(n==8||n==12||n==16||n==20)&&(t+="-")}return t};function be(t){let n,o=[],s,i;for(n=0;n<t.length;n++)(s=t.charCodeAt(n))<128?o.push(s):s<2048?o.push(192+(s>>6&31),128+(s&63)):((i=s^55296)>>10==0?(s=(i<<10)+(t.charCodeAt(++n)^56320)+65536,o.push(240+(s>>18&7),128+(s>>12&63))):o.push(224+(s>>12&15)),o.push(128+(s>>6&63),128+(s&63)));return o}const Q=t=>{let n=new Uint8Array(be(t)),o,s,i,d=(n.length+8>>>6<<4)+16,u=new Uint8Array(d<<2);for(u.set(new Uint8Array(n.buffer)),t=new Uint32Array(u.buffer),i=new DataView(t.buffer),o=0;o<d;o++)t[o]=i.getUint32(o<<2);t[n.length>>2]|=128<<24-(n.length&3)*8,t[d-1]=n.length<<3;let a=[],r=[function(){return l[1]&l[2]|~l[1]&l[3]},function(){return l[1]^l[2]^l[3]},function(){return l[1]&l[2]|l[1]&l[3]|l[2]&l[3]},function(){return l[1]^l[2]^l[3]}],g=function(f,h){return f<<h|f>>>32-h},m=[1518500249,1859775393,-1894007588,-899497514],l=[1732584193,-271733879,null,null,-1009589776];for(l[2]=~l[0],l[3]=~l[1],o=0;o<t.length;o+=16){let f=l.slice(0);for(s=0;s<80;s++)a[s]=s<16?t[o+s]:g(a[s-3]^a[s-8]^a[s-14]^a[s-16],1),i=g(l[0],5)+r[s/20|0]()+l[4]+a[s]+m[s/20|0]|0,l[1]=g(l[1],30),l.pop(),l.unshift(i);for(s=0;s<5;s++)l[s]=l[s]+f[s]|0}i=new DataView(new Uint32Array(l).buffer);for(let f=0;f<5;f++)l[f]=i.getUint32(f<<2);return Array.prototype.map.call(new Uint8Array(new Uint32Array(l).buffer),function(f){return(f<16?"0":"")+f.toString(16)}).join("")},Ie="https://test1.fengxuan.cn",Se=ye(),ke=new Date().getTime();Q(Q("1D3CB8DECCD51d1e04b7d64fff82e40C0BD6251C44E036C8C56a3f5f5aa9e9b1&"+Se+"&"+ke));const B=re.create({baseURL:Ie,timeout:1e4,headers:{"Content-Type":"application/x-www-form-urlencoded","X-Requested-With":"XMLHttpRequest"}});B.interceptors.request.use(t=>{const n=localStorage.getItem("access_token");return n&&(t.headers.Authorization="Bearer "+n),t},t=>Promise.reject(t));B.interceptors.response.use(t=>t.data,t=>(O.error("服务端无响应，请刷新重试！"),Promise.reject(t)));const T={get(t,n){return B.get(t,n)},put(t,n){return B.put(t,n)},post(t,n){return B.post(t,n)},delete(t,n){return B.delete(t,n)}},Z=t=>T.post("/Booking/GetBookingListByPid",t),ve=t=>T.post("/Booking/SaveBooking",t),Pe=t=>T.post("/Booking/EnableBooking",t),W=t=>T.post("/Patient/GetGetPatientByUid",t),Te=t=>T.post("/Patient/SavePatient",t),we=t=>T.post("/Patient/DelPatient",t),De=t=>T.post("/User/GetUserByCode",t),Ce=t=>T.post("/User/GetUserInfoByOpenid",t),Me=t=>T.post("/Report/GetReportList",t),X=t=>{if(t){const n=t.split(" ")[0],o=new Date,s=new Date(n);let i=o.getFullYear()-s.getFullYear();const d=o.getMonth()-s.getMonth();return(d<0||d===0&&o.getDate()<s.getDate())&&i--,i+"岁"}return"0岁"},Le=[],Oe=()=>{const[t,n]=c.useState(!1),[o,s]=c.useState(null),[i,d]=c.useState(Le),[u,a]=c.useState(""),[r,g]=c.useState(null),m=C(),l=new URLSearchParams(window.location.search),[p,f]=c.useState(l.get("code")),[h,v]=c.useState(l.get("openid")),[K,F]=c.useState(!1);c.useEffect(()=>{sessionStorage.removeItem("patient"),K?W({uid:localStorage.getItem("uid")}).then(x=>{if(console.log("getPatientList",x),x.code==0){let b=x.result.map(U=>({...U,age:X(U.birthday)}));d(b)}}):F(!0)},[r]),c.useEffect(()=>{const x=new Date().getTime();h?Ce({openid:h}).then(b=>{console.log("getUserInfoByOpenid",b),b.code==0&&(localStorage.setItem("openid",b.result.openid),localStorage.setItem("access_token",b.token.access_token),localStorage.setItem("timestamp",x),localStorage.setItem("uid",b.result.id),g(b.result.id))}):localStorage.getItem("access_token")&&localStorage.getItem("uid")&&localStorage.getItem("timestamp")&&x-localStorage.getItem("timestamp")<1e3*60*60?g(localStorage.getItem("uid")):De({code:p}).then(b=>{console.log("getUserByCode",b),b.code==0?(localStorage.setItem("openid",b.result.openid),localStorage.setItem("access_token",b.token.access_token),localStorage.setItem("timestamp",x),localStorage.setItem("uid",b.result.id),g(b.result.id)):(alert(b.code+" "+b.message),window.close())})},[]);const V=()=>Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);c.useEffect(()=>{const x="wx4e8858689fca9c85",b=new Date().getTime(),U=V();wx.config({debug:!1,appId:x,timestamp:b,nonceStr:U,jsApiList:["hideOptionMenu"]}),wx.ready(function(){wx.hideOptionMenu()}),wx.error(function(k){console.error("微信 JSAPI 配置错误:",k)}),document.addEventListener("touchstart",function(k){k.touches.length>1&&k.preventDefault()},{passive:!1});let Y=0;document.addEventListener("touchend",function(k){const N=Date.now();N-Y<=300&&k.preventDefault(),Y=N},{passive:!1});const q=document.createElement("meta");q.name="viewport",q.content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",document.head.appendChild(q);const _=new URLSearchParams(window.location.search).get("openid");if(_)v(_),console.log("OpenID from URL:",_),localStorage.setItem("openid",_);else{const k="mock_openid_"+Math.random().toString(36).substring(7);v(k),console.log("Generated Mock OpenID:",k),localStorage.setItem("openid",k)}return()=>{document.removeEventListener("touchstart",function(k){k.touches.length>1&&k.preventDefault()}),document.removeEventListener("touchend",function(k){const N=Date.now();N-Y<=300&&k.preventDefault(),Y=N}),document.head.removeChild(q)}},[]);const se=[{title:"在线预约",icon:e.jsx(ae,{style:{fontSize:"24px",color:"#1890ff"}}),path:"/online-booking",requiresPatientSelection:!1},{title:"我的预约",icon:e.jsx(ce,{style:{fontSize:"24px",color:"#52c41a"}}),path:"/my-bookings",requiresPatientSelection:!0},{title:"我的报告",icon:e.jsx(de,{style:{fontSize:"24px",color:"#faad14"}}),path:"/my-reports",requiresPatientSelection:!0},{title:"就诊人管理",icon:e.jsx(A,{style:{fontSize:"24px",color:"#722ed1"}}),path:"/patient-list",requiresPatientSelection:!1},{title:"在线问诊",icon:e.jsx(ue,{style:{fontSize:"24px",color:"#52c41a"}}),path:"https://work.weixin.qq.com/kfid/",requiresPatientSelection:!1}],oe=x=>{if(x.path.startsWith("http"))window.open(x.path,"_blank");else if(x.requiresPatientSelection){if((i==null?void 0:i.length)===0){D.info({title:"请先添加就诊人",onOk:()=>m("/new-patient"),okText:"去添加"});return}else if((i==null?void 0:i.length)==1){localStorage.setItem("selectedPid",i[0].id),m(x.path);return}a(x.path),n(!0)}else m(x.path)},ie=x=>{s(x),localStorage.setItem("selectedPid",x.id),localStorage.setItem("selectedPatientName",x.name),console.log("Selected patient:",x.name,"for route:",u),n(!1),m(u)},le=()=>{n(!1),s(null),a("")};return h?e.jsxs("div",{className:"no-zoom",style:{padding:"16px"},children:[e.jsx(M,{title:"智影在线服务",variant:"outlined",children:e.jsx(I,{itemLayout:"horizontal",dataSource:se,renderItem:x=>e.jsxs(I.Item,{onClick:()=>oe(x),style:{cursor:"pointer"},children:[e.jsx(I.Item.Meta,{avatar:x.icon,title:e.jsx("span",{style:{fontSize:"16px"},children:x.title})}),e.jsx("div",{children:">"})]})})}),e.jsxs(D,{title:"选择就诊人",open:t,onCancel:le,footer:null,children:[(i==null?void 0:i.length)>0?e.jsx(I,{dataSource:i,renderItem:x=>e.jsx(I.Item,{onClick:()=>ie(x),style:{cursor:"pointer"},children:e.jsx(I.Item.Meta,{avatar:e.jsx(A,{}),title:x.name,description:`性别: ${x.sex} 年龄: ${x.age}`})})}):e.jsx("p",{children:"没有绑定的就诊人信息，请先添加。"}),e.jsx(y,{type:"primary",block:!0,disabled:(i==null?void 0:i.length)>=6,onClick:()=>{n(!1),m("/new-patient")},style:{marginTop:"10px"},children:"添加新就诊人"})]})]}):e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"正在获取用户信息中..."})},$e=[],Re=()=>{const t=C(),[n,o]=J.useState($e),[s,i]=J.useState(!0);c.useEffect(()=>{sessionStorage.removeItem("patient"),setTimeout(()=>{W({uid:localStorage.getItem("uid")}).then(a=>{if(console.log("getPatientList",a),a.code==0){let r=a.result.map(g=>({...g,age:X(g.birthday)}));o(r)}i(!1)})},500)},[]);const d=a=>{a.birthday=P(a.birthday).format("YYYY-MM-DD"),sessionStorage.setItem(`patient_${a.id}`,JSON.stringify(a)),t(`/patient-detail/${a.id}`)},u=()=>{t("/new-patient")};return s?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载就诊人信息中..."}):e.jsx("div",{style:{padding:"16px"},children:e.jsxs(M,{title:"就诊人列表",children:[n.length>0?e.jsx(I,{itemLayout:"horizontal",dataSource:n,renderItem:a=>e.jsx(I.Item,{onClick:()=>d(a),actions:[e.jsx("a",{children:">"},"list-edit")],children:e.jsx(I.Item.Meta,{avatar:e.jsx(A,{style:{fontSize:"24px"}}),title:e.jsx($.Text,{strong:!0,children:a.name}),description:e.jsxs("div",{children:[e.jsxs("span",{children:["性别: ",a.sex]}),e.jsxs("span",{children:["年龄: ",a.age]}),e.jsxs("div",{children:["证件号: ",a.idcard]})]})})})}):e.jsx($.Text,{children:"暂无就诊人信息"}),e.jsxs("div",{style:{marginTop:"20px",display:"flex",flexDirection:"column",gap:"10px"},children:[e.jsx(y,{type:"primary",icon:e.jsx(me,{}),onClick:u,block:!0,style:{marginTop:"20px"},disabled:(n==null?void 0:n.length)>=6,children:"添加就诊人"}),e.jsx(y,{onClick:()=>t("/"),block:!0,children:"返回首页"})]})]})})},{Title:Ke,Paragraph:E,Text:Qe}=$,{Option:Ze}=G,Be=()=>{const t=C(),[n]=S.useForm(),[o,s]=c.useState(!1),[i,d]=c.useState(null),[u,a]=c.useState(null),[r,g]=c.useState(null);c.useEffect(()=>{const h=sessionStorage.getItem("patient");console.log("patient",h),h&&(n.setFieldsValue(JSON.parse(h)),d(JSON.parse(h).sex),a(JSON.parse(h).birthday),g(JSON.parse(h).id),console.log("sex",JSON.parse(h).sex))},[]);const m=h=>{Te({id:r||"",uid:localStorage.getItem("uid"),name:n.getFieldValue("name"),phone:n.getFieldValue("phone"),idcard:n.getFieldValue("idcard"),sex:n.getFieldValue("sex"),birthday:n.getFieldValue("birthday")}).then(v=>{console.log("savePatient",v),D.success({title:"保存成功",content:`就诊人 ${h.name} ${r?"已编辑":"已成功添加"}。`,onOk:()=>t("/"),okText:"返回首页"})})},l=h=>{const v=h.target.value;if(n.setFieldsValue({idcard:v}),v.length===18){const F=v.charAt(16)%2===0?"女":"男";console.log("sexhandle",F),n.setFieldsValue({sex:F});const V=Ee(v);V?n.setFieldsValue({birthday:V}):n.setFieldsValue({birthday:null})}else n.setFieldsValue({sex:null})},p=()=>{s(!1)},f=()=>{s(!1)};return e.jsxs("div",{style:{padding:"16px"},children:[e.jsxs(M,{title:r?"编辑就诊人":"添加新就诊人",children:[e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#f0f8ff",borderRadius:"4px"},children:[e.jsx(E,{strong:!0,style:{fontSize:"13px"},children:"注：请确保姓名及证件号码与您证件（身份证）上的信息一致否则无法取号或取消预约，请您认真填写。"}),e.jsx(E,{style:{fontSize:"13px"},children:"同一账号最多绑定6位历史就诊人。"}),e.jsx(E,{style:{fontSize:"13px"},children:"我们收集您的身份证号码是为了确保医疗服务的真实性和安全性，以满足政府或法律规定的实名制要求。"})]}),e.jsxs(S,{form:n,layout:"vertical",name:"new_patient_form",onFinish:m,initialValues:{agreedToPolicy:!1,idType:"ID_CARD"},children:[e.jsx(S.Item,{name:"name",label:"就诊⼈姓名",rules:[{required:!0,message:"请输入姓名!"}],children:e.jsx(R,{placeholder:"请输入姓名"})}),e.jsx(S.Item,{name:"phone",label:"手机号码",rules:[{required:!0,message:"请输入手机号码!"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码!"}],children:e.jsx(R,{placeholder:"请输入手机号码",maxLength:11,minLength:11})}),e.jsx(S.Item,{name:"idcard",label:"居民身份证",rules:[{required:!0,message:"请输入证件号码!"},{validator:(h,v)=>!v||Ne(v)?Promise.resolve():Promise.reject(new Error("居民身份证件号码格式不正确!"))}],children:e.jsx(R,{placeholder:"请输入证件号码",onChange:l,maxLength:18,minLength:18})}),e.jsx(S.Item,{name:"sex",label:"性别",rules:[{required:!0,message:"请选择性别!"}],children:e.jsxs(L.Group,{value:i,children:[e.jsx(L,{value:"男",disabled:!0,children:"男"}),e.jsx(L,{value:"女",disabled:!0,children:"女"})]})}),e.jsx(S.Item,{name:"birthday",label:"出生日期",rules:[{required:!0,message:"请输入出生日期!"}],children:e.jsx(R,{disabled:!0})}),e.jsx(S.Item,{children:e.jsx(y,{type:"primary",htmlType:"submit",block:!0,children:"保存"})}),e.jsx(S.Item,{children:e.jsx(y,{block:!0,onClick:()=>t(-1),children:"返回"})})]})]}),e.jsxs(D,{title:"法律声明及隐私权政策",visible:o,onOk:p,onCancel:f,footer:[e.jsx(y,{type:"primary",onClick:p,children:"我知道了"},"confirm")],children:[e.jsx(E,{children:"这里是法律声明和隐私权政策的详细内容..."}),e.jsx(E,{children:"请仔细阅读..."})]})]})};function Ne(t){if(!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(t))return!1;const o=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],s=["1","0","X","9","8","7","6","5","4","3","2"];let i=0;for(let u=0;u<17;u++)i+=parseInt(t[u],10)*o[u];return s[i%11].toUpperCase()===t[17].toUpperCase()}const Ee=t=>{if(!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(t))return null;const o=t.slice(6,10),s=t.slice(10,12),i=t.slice(12,14);return`${o}-${s}-${i}`},{Option:et}=G,Ae=()=>{const t=C(),[n,o]=c.useState(null),[s,i]=c.useState(!1),[d]=S.useForm(),{id:u}=ee();c.useEffect(()=>{const r=JSON.parse(sessionStorage.getItem(`patient_${u}`));console.log("fetchedPatient",r),r?(o(r),d.setFieldsValue(r)):(O.error("数据异常"),t("/"))},[d,t]);const a=()=>{D.confirm({title:"确认删除就诊人吗?",icon:e.jsx(pe,{}),content:`您确定要删除就诊人 ${n==null?void 0:n.name} 吗？此操作无法撤销。`,okText:"确认删除",okType:"danger",cancelText:"取消",onOk(){console.log("Patient deleted:",n==null?void 0:n.id),we({id:n==null?void 0:n.id}).then(r=>{console.log("delPatient",r),r.code==0&&(O.success("就诊人已删除"),t("/patient-list"))})},onCancel(){console.log("Delete cancelled")}})};return n?e.jsx("div",{style:{padding:"16px"},children:e.jsxs(M,{title:"就诊人信息",children:[e.jsxs(j,{variant:"outlined",column:1,size:"small",children:[e.jsx(j.Item,{label:"姓名",children:n.name}),e.jsx(j.Item,{label:"性别",children:n.sex}),e.jsx(j.Item,{label:"年龄",children:n.age}),e.jsx(j.Item,{label:"证件类型",children:"居民身份证"}),e.jsx(j.Item,{label:"证件号",children:n.idcard}),e.jsx(j.Item,{label:"手机号",children:n.phone}),e.jsx(j.Item,{label:"出生日期",children:n.birthday})]}),e.jsxs("div",{style:{marginTop:"20px",display:"flex",flexDirection:"column",gap:"10px"},children:[e.jsx(y,{type:"primary",onClick:()=>t("/new-patient"),block:!0,children:"编辑信息"}),e.jsx(y,{type:"danger",onClick:a,block:!0,children:"删除就诊人"}),e.jsx(y,{onClick:()=>t("/patient-list"),block:!0,children:"返回列表"})]})]})}):e.jsx("div",{children:"加载中..."})},{Option:tt}=G,{Title:nt}=$,Fe=[],Ve=()=>{const t=C(),[n]=S.useForm(),[o,s]=c.useState(!1),[i,d]=c.useState(Fe),[u,a]=c.useState(null),r=p=>{console.log("Received values of booking form: ",p);const f={...p,pid:u?u.id:null,uid:localStorage.getItem("uid")},h={1:"PET",2:"DR",3:"CT"};console.log("Booking Data to send to API:",f),ve(f).then(v=>{console.log("saveBooking",v)}),D.success({title:"预约成功",content:`${(u==null?void 0:u.name)||"-"} 预约 ${h[p.type]}，时间：${f.bookingTime}。`,onOk:()=>t("/"),okText:"返回首页"})},g=()=>{s(!0)},m=p=>{a(p),n.setFieldsValue({patientName:p.name}),localStorage.setItem("selectedPid",p.id),s(!1)},l=()=>{s(!1)};return c.useEffect(()=>{W({uid:localStorage.getItem("uid")}).then(p=>{if(console.log("getPatientList",p),p.code==0){let f=p.result.map(h=>({...h,age:X(h.birthday),sex:h.sex=="MALE"?"男":"女"}));d(f),f.length==1&&(a(f[0]),n.setFieldsValue({patientName:f[0].name}))}})},[]),e.jsxs("div",{style:{padding:"16px"},children:[e.jsx(M,{title:"在线预约",children:e.jsxs(S,{form:n,layout:"vertical",name:"online_booking_form",onFinish:r,children:[e.jsx(S.Item,{name:"patientName",label:"选择就诊人",rules:[{required:!0,message:"请选择就诊人!"}],children:e.jsx(R,{placeholder:"请选择就诊人",onClick:g,readOnly:!0,addonAfter:e.jsx(A,{})})}),e.jsx(S.Item,{name:"type",label:"检查类型",rules:[{required:!0,message:"请选择检查类型!"}],children:e.jsxs(L.Group,{children:[e.jsx(L,{value:"1",style:{fontWeight:"bold",marginRight:50},children:"PET"}),e.jsx(L,{value:"2",style:{fontWeight:"bold",marginRight:50},children:"DR"}),e.jsx(L,{value:"3",style:{fontWeight:"bold"},children:"CT"})]})}),e.jsx(S.Item,{name:"bookingTime",label:"选择就诊时间",rules:[{required:!0,message:"请选择就诊时间!"}],children:e.jsx(R,{placeholder:"请选择日期和时间"})}),e.jsx(S.Item,{children:e.jsxs("div",{style:{marginTop:"20px",display:"flex",flexDirection:"column",gap:"10px"},children:[e.jsx(y,{type:"primary",htmlType:"submit",block:!0,children:"预约"}),e.jsx(y,{onClick:()=>t("/"),block:!0,children:"返回首页"})]})})]})}),e.jsxs(D,{title:"选择就诊人",open:o,onCancel:l,footer:null,children:[(i==null?void 0:i.length)>0?e.jsx(I,{dataSource:i,renderItem:p=>e.jsx(I.Item,{onClick:()=>m(p),style:{cursor:"pointer"},children:e.jsx(I.Item.Meta,{avatar:e.jsx(A,{}),title:p.name,description:`性别: ${p.sex} 年龄: ${p.age}`})})}):e.jsx("p",{children:"没有绑定的就诊人信息，请先添加。"}),e.jsx(y,{type:"link",onClick:()=>{s(!1),t("/new-patient")},block:!0,style:{marginTop:"10px"},children:"+ 添加新就诊人"})]})]})},{Title:st,Text:Ue}=$,Ye=()=>{const t=C(),[n,o]=c.useState([]),[s,i]=c.useState(null),[d,u]=c.useState(!0),[a,r]=c.useState(null);c.useEffect(()=>{const m=localStorage.getItem("selectedPid");r(m);const l=localStorage.getItem("selectedPatientName");m?(i({id:m,name:l||"当前就诊人"}),console.log(`Fetching bookings for patient ID: ${m}`),setTimeout(()=>{Z({pid:m}).then(p=>{console.log("getBookingList",p),p.code==0&&(o(p.result||[]),u(!1))})},500)):(O.warn("请先在首页选择就诊人"),t("/"),u(!1))},[t]);const g=m=>{D.confirm({title:"确认取消预约吗?",content:"此操作无法撤销。",okText:"确认取消",okType:"danger",cancelText:"返回",onOk(){console.log("Booking cancelled:",m),Pe({id:m}).then(l=>{l.code==0&&(O.success("预约已取消"),Z({pid:a}).then(p=>{console.log("getBookingList",p),p.code==0&&(o(p.result||[]),u(!1))}))})}})};return d?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载预约信息中..."}):e.jsx("div",{style:{padding:"16px"},children:e.jsxs(M,{title:s?`${s.name} - 我的预约`:"我的预约",children:[n.length>0?e.jsx(I,{itemLayout:"horizontal",dataSource:n.sort((m,l)=>P(l.bookingtime).valueOf()-P(m.bookingtime).valueOf()),renderItem:m=>{let l;switch(m.type){case 1:l="PET";break;case 2:l="DR";break;case 3:l="CT";break;default:l="其他"}return e.jsx(I.Item,{actions:[m.status==="待检查"?e.jsx(y,{type:"link",danger:!0,onClick:()=>g(m.id),children:"取消预约"}):m.status==="已取消"?e.jsx(y,{type:"link",disabled:!0,children:"已取消"}):e.jsx(y,{type:"link",disabled:!0,children:m.status})],children:e.jsx(I.Item.Meta,{title:e.jsx(Ue,{strong:!0,children:`${l}`}),description:P(m.bookingtime).format("YYYY年MM月DD日 HH:mm:ss")})})}}):e.jsx(te,{description:s?"暂无预约记录":"请先选择就诊人以查看预约"}),e.jsx(y,{type:"default",onClick:()=>t("/"),style:{marginTop:"20px"},block:!0,children:"返回首页"})]})})},{Title:ot,Text:qe}=$,_e=()=>{const t=C(),[n,o]=c.useState([]),[s,i]=c.useState(null),[d,u]=c.useState(!0);c.useEffect(()=>{const r=localStorage.getItem("selectedPid"),g=localStorage.getItem("selectedpatientName");r?(i({id:r,name:g||"当前就诊人"}),console.log(`Fetching reports for patient ID: ${r}`),setTimeout(()=>{Me({pid:r}).then(m=>{console.log("getReport",m),m.code==0?(o(m.result||[]),u(!1)):(O.error(m.msg),u(!1))})},500)):(O.warn("请先在首页选择就诊人"),t("/"),u(!1))},[t]);const a=r=>{sessionStorage.setItem(`report_${r.accessionNo}`,JSON.stringify(r)),t(`/report-detail/${r.accessionNo}`)};return d?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载报告信息中..."}):e.jsx("div",{style:{padding:"16px"},children:e.jsxs(M,{title:s?`${s.name} - 我的报告`:"我的报告",children:[n.length>0?e.jsx(I,{itemLayout:"horizontal",dataSource:n.sort((r,g)=>P(g.studyTime).valueOf()-P(r.studyTime).valueOf()),renderItem:r=>e.jsx(I.Item,{onClick:()=>a(r),actions:[e.jsx("a",{children:">"},`view-${r.id}`)],children:e.jsx(I.Item.Meta,{title:e.jsx(qe,{strong:!0,children:r.procedureName}),description:e.jsxs("div",{children:[e.jsxs("div",{children:["检查类型：",r.modalityType]}),e.jsxs("div",{children:["检查日期：",P(r.studyTime).format("YYYY年MM月DD日 HH:mm")]})]})})})}):e.jsx(te,{description:s?"暂无报告记录":"请先选择就诊人以查看报告"}),e.jsx(y,{type:"default",onClick:()=>t("/"),style:{marginTop:"20px"},block:!0,children:"返回首页"})]})})},{Title:z,Paragraph:H}=$,ze=()=>{const{id:t}=ee(),n=C(),[o,s]=c.useState(null),[i,d]=c.useState(!0),[u,a]=c.useState(null),r=sessionStorage.getItem(`report_${t}`);return c.useEffect(()=>{setTimeout(()=>{const g=JSON.parse(r);console.log("reportData",g),console.log("accessionNo",t),g?s(g):a("未找到该报告的详细信息。"),d(!1)},500)},[t]),i?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载报告详情中..."}):u?e.jsxs("div",{style:{padding:"16px"},children:[e.jsx(xe,{message:"错误",description:u,type:"error",showIcon:!0}),e.jsx(y,{onClick:()=>n(-1),style:{marginTop:"20px"},block:!0,children:"返回上一页"})]}):o?e.jsx("div",{style:{padding:"16px"},children:e.jsxs(M,{title:o.procedureName||"报告详情",children:[e.jsxs(j,{variant:"outlined",column:1,size:"small",children:[e.jsx(j.Item,{label:"影像号",children:o.accessionNo}),e.jsxs(j.Item,{label:"就诊人",children:[o.patientName,o.sex=="M"?" （男":o.sex=="F"?" （女":"（",o.age?`${o.age}岁）`:"）"]}),e.jsx(j.Item,{label:"电话号码",children:o.phoneNumber}),e.jsx(j.Item,{label:"就诊类型",children:o.patientType=="O"?"门诊":"住院"}),e.jsx(j.Item,{label:"检查院所",children:o.locationName}),e.jsx(j.Item,{label:"检查科室",children:o.procedureOffice}),e.jsx(j.Item,{label:"检查方法",children:o.procedureName}),e.jsx(j.Item,{label:"检查日期",children:P(o.studyTime).format("YYYY年MM月DD日 HH:mm")}),e.jsx(j.Item,{label:"报告医生",children:o.reportDoctorName}),e.jsx(j.Item,{label:"报告日期",children:P(o.reportDatetime).format("YYYY年MM月DD日 HH:mm")}),e.jsx(j.Item,{label:"审核医生",children:o.auditDoctorName}),e.jsx(j.Item,{label:"审核日期",children:P(o.auditDatetime).format("YYYY年MM月DD日 HH:mm")})]}),e.jsx(z,{level:5,style:{marginTop:"20px"},children:"检查所见"}),e.jsx(H,{children:o.imageDescription}),e.jsx(z,{level:5,style:{marginTop:"20px"},children:"印象"}),e.jsx(H,{children:o.conclusion}),o.recommendations&&e.jsxs(e.Fragment,{children:[e.jsx(z,{level:5,style:{marginTop:"20px"},children:"建议"}),e.jsx(H,{children:o.recommendations})]}),e.jsx(y,{type:"primary",onClick:()=>window.open(o.report_url,"_blank"),style:{marginTop:"20px"},block:!0,children:"查看报告"}),e.jsx(y,{type:"default",onClick:()=>n(-1),style:{marginTop:"20px"},block:!0,children:"返回列表"})]})}):e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"报告信息不存在。"})},He=()=>e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:e.jsx(he,{size:"large",tip:"加载中..."})}),{Content:Je}=ne;function Ge(){return e.jsx(ne,{style:{minHeight:"100vh"},children:e.jsx(Je,{style:{padding:"0",margin:"0"},children:e.jsx(c.Suspense,{fallback:e.jsx(He,{}),children:e.jsxs(ge,{children:[e.jsx(w,{path:"/",element:e.jsx(Oe,{})}),e.jsx(w,{path:"/patient-list",element:e.jsx(Re,{})}),e.jsx(w,{path:"/new-patient",element:e.jsx(Be,{})}),e.jsx(w,{path:"/patient-detail/:id",element:e.jsx(Ae,{})}),e.jsx(w,{path:"/online-booking",element:e.jsx(Ve,{})}),e.jsx(w,{path:"/my-bookings",element:e.jsx(Ye,{})}),e.jsx(w,{path:"/my-reports",element:e.jsx(_e,{})}),e.jsx(w,{path:"/report-detail/:id",element:e.jsx(ze,{})})]})})})})}fe.createRoot(document.getElementById("root")).render(e.jsx(J.StrictMode,{children:e.jsx(je,{basename:"/wechat",children:e.jsx(Ge,{})})}));
