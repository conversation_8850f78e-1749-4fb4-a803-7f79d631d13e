import{r as e,P as n}from"./index-DU7CKWYf.js";import{i as w,F as y}from"./typeCheck-Bny-qHCS.js";import{r as k}from"./request-BC0RU1dX.js";import{S as b}from"./index-DzBe7p-9.js";import"./index-NJKMylyL.js";import"./index--oV3THjE.js";import"./useBubbleLock-DJb3BRZQ.js";import"./EllipsisOutlined-DIxi2Udi.js";const _=o=>k.post("/Booking/GetBookingList",o);function B(){const[o,l]=e.useState([]),[p,m]=e.useState(!0),[i,c]=e.useState(),[r,d]=e.useState(),[a,g]=e.useState({PageNo:1,pageSize:20,total:i});e.useEffect(()=>{_({...a,status:r}).then(t=>{l(t.result),c(t.count)})},[a,r]),e.useEffect(()=>{w(o)&&m(!1)},[o]);const u=i>a.pageSize||a.current>1,h=t=>t.map(s=>({align:"center",...s})),x=t=>{g(t)},f=t=>{d(t)},S=[{title:"预约项目",dataIndex:"type",width:"15%",key:"type"},{title:"预约时间",dataIndex:"bookingtime",key:"bookingtime",width:"15%"},{title:"就诊人姓名",dataIndex:"name",key:"name",width:"15%"},{title:"就诊人手机号",dataIndex:"phone",key:"phone",width:"15%"},{title:"状态",dataIndex:"status",width:"10%",key:"status"}];return n.jsxs(n.Fragment,{children:[n.jsx(b,{style:{width:200,marginBottom:10},placeholder:"请选择类型",onChange:f,options:[{label:"全部",value:"全部"},{label:"待就诊",value:"待检查"},{label:"已完成",value:"已完成"}]}),n.jsx(y,{scroll:{x:"max-content"},bordered:!0,loading:p,locale:{emptyText:"暂无数据"},pagination:u?{...a,showTotal:t=>`共 ${t} 条`,showSizeChanger:!0,pageSizeOptions,position:["topRight","bottomRight"],locale:{items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页"}}:!1,onChange:x,rowKey:(t,s)=>s,dataSource:o,columns:h(S)})]})}export{B as default};
