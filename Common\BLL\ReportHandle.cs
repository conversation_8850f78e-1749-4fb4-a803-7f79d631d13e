﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace zhiying_online.Common.BLL
{
    /// <summary>
    /// 获取影片
    /// </summary>
    public class ReportHandle
    {
        /// <summary>
        /// 根据用户获取报告列表
        /// </summary>
        /// <returns></returns>
        public static object GetReportList(long? pid)
        {
            using (var db = new Model.zhiying_online())
            {
                var pu = db.patient.Find(pid);
                if (pu == null)
                {
                    return new { code = 1, msg = "就诊人不存在" };
                }

                //生成sql语句（后期部署要改）
                var sql = "select * from report where (PhoneNumber='" + pu.p_phone + "' or IdCardNo='" + pu.p_idcard + "') order by ReportDatetime desc";

                var list = db.Database.SqlQuery(sql);
                return new { code = 0, msg = "请求成功", result = list };
            }
        }

        /// <summary>
        /// 获取api的token（暂时没用）
        /// </summary>
        /// <returns></returns>
        private static string GetToken()
        {
            var Configuration = Tools.GetConfiguration();
            var accesstokne = CacheManager.Default.Get<string>("zhiying_token");
            if (accesstokne == null)
            {
                var timestamp = Tools.GetWeixinDateTimeHm(DateTime.Now);
                var nonce = Tools.GetStr(8);
                var sign = GetSign(Configuration["ZhiYing: AppId"], Configuration["ZhiYing: Secret"], timestamp, nonce);
                var url = $"http://cloud.uihcloud.com/m/common/token?appid={Configuration["ZhiYing: AppId"]}&timestamp={timestamp}&nonce={nonce}&signature={sign}";
                var ct = Tools.HtmlGet(url);
                if (!string.IsNullOrEmpty(ct))
                {
                    accesstokne = ct;
                    CacheManager.Default.Set_AbsoluteExpire<string>("zhiying_token", accesstokne, TimeSpan.FromSeconds(7200 - 200));
                }
                else
                {
                    Logger.debug("智影AccesstokenBySecret：" + Configuration["Wechat:AppId"] + "_" + Configuration["Wechat:Secret"] + "：" + JsonConvert.SerializeObject(ct));
                }
            }
            return accesstokne;
        }

        /// <summary>
        /// 生成签名（暂时没用）
        /// </summary>
        /// <param name="appid"></param>
        /// <param name="secret"></param>
        /// <param name="timestamp"></param>
        /// <param name="nonce"></param>
        /// <returns></returns>
        private static string GetSign(string appid, string secret, long? timestamp, string nonce)
        {
            Dictionary<string, string> dic = new Dictionary<string, string> { };
            dic.Add("appid", appid);
            dic.Add("secret", secret);
            dic.Add("timestamp", timestamp.ToString());
            dic.Add("nonce", nonce);
            var connet = string.Join("", dic.OrderBy(m => m.Key).Select(m => m.Value));

            //执行加密
            return Tools.SHA256Hash(connet);
        }
    }
}
