﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Common.Attr;
using zhiying_online.Common.Jwt;

namespace zhiying_online.Controllers
{
    /// <summary>
    /// 账号信息
    /// </summary>
    [Route("[controller]/[action]")]
    public class AccountController : Controller
    {
        /// <summary>
        /// 获取账号列表
        /// </summary>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":{"id":"表id","name":"名称","time":"时间","accountid":"账号id"}}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtAttribute]
        public IActionResult GetAccountList()
        {
            return Json(Common.BLL.AccountHandle.GetAccountList());
        }

        /// <summary>
        /// 保存账号信息
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="name">名称</param>
        /// <param name="accountid">账号登录id</param>
        /// <param name="password">密码</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtAttribute]
        [RequestLog]
        public IActionResult SaveAccount(long? id, string name, string accountid, string password)
        {
            return Json(Common.BLL.AccountHandle.SaveAccount(id,name, accountid, password));
        }

        /// <summary>
        /// 账号登录
        /// </summary>
        /// <param name="accountid">账号登录id</param>
        /// <param name="password">密码</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","account":{"accountid":"账号id","name":"账号名称"},"token":{"access_token":"接口请求access_token","expires_in":"过期超时时间s","token_type":"token类型"}}</para>
        /// <para>请求失败：{"code":1,"msg":"账号不存在"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        public IActionResult Login(string accountid, string password)
        {
            return Json(Common.BLL.AccountHandle.Login(accountid, password));
        }

        /// <summary>
        /// 账号登出
        /// </summary>
        /// <param name="accountid"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        public IActionResult LogOut(string accountid)
        {
            return Json(Common.BLL.AccountHandle.LogOut(accountid));
        }
    }
}
