import{H as tn,j as yo,h as ft,d as W,G as Et,_ as $e,z as mt,a7 as wr,r as l,e as de,R as X,a as Re,c as Y,b as T,m as sn,l as A,A as zn,n as ba,s as Jn,x as bo,a8 as xa,y as Mt,M as Ca,u as $t,N as on,a3 as Er,a9 as xo,a0 as Qn,aa as $r,X as Zn,a2 as er,C as Sa,ab as wa,ac as Ea,F as $a,ad as be,E as Na,o as hn,B as Ra,ae as Ia,p as ka,J as Zt,v as Oa}from"./index-CgnIOky4.js";import{I as Co,H as So,L as dt,j as et,u as Bn,p as an,l as yn,m as bn,S as Ka,Q as Pa,U as Ta,ax as Da,ay as Ma,az as za,s as Ba,D as wo,k as La,aA as Nr,G as Ln,K as tr,E as nr,aB as Rr,ao as _a,aC as Ir,af as Ha,an as ja,ah as Aa,a8 as Fa,aD as Wa,B as kr,n as Va}from"./index-DFzqX_wY.js";import{k as Or,S as qa,l as Eo,d as $o,R as Xa,c as No,E as Kr,M as Ua,O as Ga,m as Ya}from"./index-CQl9k0HT.js";import{C as vn,g as Ja}from"./index-CLLH-YNP.js";import{u as Ro,d as Qa,T as Pr}from"./useBubbleLock-DcUaCS0h.js";function _n(e){return e!=null&&e===e.window}const Za=e=>{var t,r;if(typeof window>"u")return 0;let n=0;return _n(e)?n=e.pageYOffset:e instanceof Document?n=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(n=e.scrollTop),e&&!_n(e)&&typeof n!="number"&&(n=(r=((t=e.ownerDocument)!==null&&t!==void 0?t:e).documentElement)===null||r===void 0?void 0:r.scrollTop),n};function ei(e,t,r,n){const o=r-t;return e/=n/2,e<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}function ti(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{getContainer:r=()=>window,callback:n,duration:o=450}=t,i=r(),d=Za(i),a=Date.now(),c=()=>{const m=Date.now()-a,u=ei(m>o?o:m,d,e,o);_n(i)?i.scrollTo(window.pageXOffset,u):i instanceof Document||i.constructor.name==="HTMLDocument"?i.documentElement.scrollTop=u:i.scrollTop=u,m<o?tn(c):typeof n=="function"&&n()};tn(c)}var Io=function(t){if(yo()&&window.document.documentElement){var r=Array.isArray(t)?t:[t],n=window.document.documentElement;return r.some(function(o){return o in n.style})}return!1},ni=function(t,r){if(!Io(t))return!1;var n=document.createElement("div"),o=n.style[t];return n.style[t]=r,n.style[t]!==o};function ri(e,t){return!Array.isArray(e)&&t!==void 0?ni(e,t):Io(e)}function oi(e,t,r){var n=r||{},o=n.noTrailing,i=o===void 0?!1:o,d=n.noLeading,a=d===void 0?!1:d,c=n.debounceMode,s=c===void 0?void 0:c,m,u=!1,f=0;function v(){m&&clearTimeout(m)}function g(p){var y=p||{},C=y.upcomingOnly,x=C===void 0?!1:C;v(),u=!x}function b(){for(var p=arguments.length,y=new Array(p),C=0;C<p;C++)y[C]=arguments[C];var x=this,w=Date.now()-f;if(u)return;function S(){f=Date.now(),t.apply(x,y)}function E(){m=void 0}!a&&s&&!m&&S(),v(),s===void 0&&w>e?a?(f=Date.now(),i||(m=setTimeout(s?E:S,e))):S():i!==!0&&(m=setTimeout(s?E:S,s===void 0?e-w:e))}return b.cancel=g,b}function ai(e,t,r){var n={},o=n.atBegin,i=o===void 0?!1:o;return oi(e,t,{debounceMode:i!==!1})}function st(e,t){return e[t]}var ii=["children"];function ko(e,t){return"".concat(e,"-").concat(t)}function li(e){return e&&e.type&&e.type.isTreeNode}function cn(e,t){return e??t}function At(e){var t=e||{},r=t.title,n=t._title,o=t.key,i=t.children,d=r||"title";return{title:d,_title:n||[d],key:o||"key",children:i||"children"}}function Oo(e){function t(r){var n=So(r);return n.map(function(o){if(!li(o))return Et(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var i=o.key,d=o.props,a=d.children,c=mt(d,ii),s=W({key:i},c),m=t(a);return m.length&&(s.children=m),s}).filter(function(o){return o})}return t(e)}function kn(e,t,r){var n=At(r),o=n._title,i=n.key,d=n.children,a=new Set(t===!0?[]:t),c=[];function s(m){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return m.map(function(f,v){for(var g=ko(u?u.pos:"0",v),b=cn(f[i],g),p,y=0;y<o.length;y+=1){var C=o[y];if(f[C]!==void 0){p=f[C];break}}var x=Object.assign(Co(f,[].concat($e(o),[i,d])),{title:p,key:b,parent:u,pos:g,children:null,data:f,isStart:[].concat($e(u?u.isStart:[]),[v===0]),isEnd:[].concat($e(u?u.isEnd:[]),[v===m.length-1])});return c.push(x),t===!0||a.has(b)?x.children=s(f[d]||[],x):x.children=[],x})}return s(e),c}function si(e,t,r){var n={};ft(r)==="object"?n=r:n={externalGetKey:r},n=n||{};var o=n,i=o.childrenPropName,d=o.externalGetKey,a=o.fieldNames,c=At(a),s=c.key,m=c.children,u=i||m,f;d?typeof d=="string"?f=function(b){return b[d]}:typeof d=="function"&&(f=function(b){return d(b)}):f=function(b,p){return cn(b[s],p)};function v(g,b,p,y){var C=g?g[u]:e,x=g?ko(p.pos,b):"0",w=g?[].concat($e(y),[g]):[];if(g){var S=f(g,x),E={node:g,index:b,pos:x,key:S,parentPos:p.node?p.pos:null,level:p.level+1,nodes:w};t(E)}C&&C.forEach(function(I,P){v(I,P,{node:g,pos:x,level:p?p.level+1:-1},w)})}v(null)}function rr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,n=t.processEntity,o=t.onProcessFinished,i=t.externalGetKey,d=t.childrenPropName,a=t.fieldNames,c=arguments.length>2?arguments[2]:void 0,s=i||c,m={},u={},f={posEntities:m,keyEntities:u};return r&&(f=r(f)||f),si(e,function(v){var g=v.node,b=v.index,p=v.pos,y=v.key,C=v.parentPos,x=v.level,w=v.nodes,S={node:g,nodes:w,index:b,key:y,pos:p,level:x},E=cn(y,p);m[p]=S,u[E]=S,S.parent=m[C],S.parent&&(S.parent.children=S.parent.children||[],S.parent.children.push(S)),n&&n(S,f)},{externalGetKey:s,childrenPropName:d,fieldNames:a}),o&&o(f),f}function nn(e,t){var r=t.expandedKeys,n=t.selectedKeys,o=t.loadedKeys,i=t.loadingKeys,d=t.checkedKeys,a=t.halfCheckedKeys,c=t.dragOverNodeKey,s=t.dropPosition,m=t.keyEntities,u=st(m,e),f={eventKey:e,expanded:r.indexOf(e)!==-1,selected:n.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:i.indexOf(e)!==-1,checked:d.indexOf(e)!==-1,halfChecked:a.indexOf(e)!==-1,pos:String(u?u.pos:""),dragOver:c===e&&s===0,dragOverGapTop:c===e&&s===-1,dragOverGapBottom:c===e&&s===1};return f}function Fe(e){var t=e.data,r=e.expanded,n=e.selected,o=e.checked,i=e.loaded,d=e.loading,a=e.halfChecked,c=e.dragOver,s=e.dragOverGapTop,m=e.dragOverGapBottom,u=e.pos,f=e.active,v=e.eventKey,g=W(W({},t),{},{expanded:r,selected:n,checked:o,loaded:i,loading:d,halfChecked:a,dragOver:c,dragOverGapTop:s,dragOverGapBottom:m,pos:u,active:f,key:v});return"props"in g||Object.defineProperty(g,"props",{get:function(){return Et(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),g}function Ko(e,t){var r=new Set;return e.forEach(function(n){t.has(n)||r.add(n)}),r}function ci(e){var t=e||{},r=t.disabled,n=t.disableCheckbox,o=t.checkable;return!!(r||n)||o===!1}function di(e,t,r,n){for(var o=new Set(e),i=new Set,d=0;d<=r;d+=1){var a=t.get(d)||new Set;a.forEach(function(u){var f=u.key,v=u.node,g=u.children,b=g===void 0?[]:g;o.has(f)&&!n(v)&&b.filter(function(p){return!n(p.node)}).forEach(function(p){o.add(p.key)})})}for(var c=new Set,s=r;s>=0;s-=1){var m=t.get(s)||new Set;m.forEach(function(u){var f=u.parent,v=u.node;if(!(n(v)||!u.parent||c.has(u.parent.key))){if(n(u.parent.node)){c.add(f.key);return}var g=!0,b=!1;(f.children||[]).filter(function(p){return!n(p.node)}).forEach(function(p){var y=p.key,C=o.has(y);g&&!C&&(g=!1),!b&&(C||i.has(y))&&(b=!0)}),g&&o.add(f.key),b&&i.add(f.key),c.add(f.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(Ko(i,o))}}function ui(e,t,r,n,o){for(var i=new Set(e),d=new Set(t),a=0;a<=n;a+=1){var c=r.get(a)||new Set;c.forEach(function(f){var v=f.key,g=f.node,b=f.children,p=b===void 0?[]:b;!i.has(v)&&!d.has(v)&&!o(g)&&p.filter(function(y){return!o(y.node)}).forEach(function(y){i.delete(y.key)})})}d=new Set;for(var s=new Set,m=n;m>=0;m-=1){var u=r.get(m)||new Set;u.forEach(function(f){var v=f.parent,g=f.node;if(!(o(g)||!f.parent||s.has(f.parent.key))){if(o(f.parent.node)){s.add(v.key);return}var b=!0,p=!1;(v.children||[]).filter(function(y){return!o(y.node)}).forEach(function(y){var C=y.key,x=i.has(C);b&&!x&&(b=!1),!p&&(x||d.has(C))&&(p=!0)}),b||i.delete(v.key),p&&d.add(v.key),s.add(v.key)}})}return{checkedKeys:Array.from(i),halfCheckedKeys:Array.from(Ko(d,i))}}function Ht(e,t,r,n){var o=[],i;n?i=n:i=ci;var d=new Set(e.filter(function(m){var u=!!st(r,m);return u||o.push(m),u})),a=new Map,c=0;Object.keys(r).forEach(function(m){var u=r[m],f=u.level,v=a.get(f);v||(v=new Set,a.set(f,v)),v.add(u),c=Math.max(c,f)}),Et(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(m){return"'".concat(m,"'")}).join(", ")));var s;return t===!0?s=di(d,a,c,i):s=ui(d,t.halfCheckedKeys,a,c,i),s}function Tr(e,t,r,n){var o=wr.unstable_batchedUpdates?function(d){wr.unstable_batchedUpdates(r,d)}:r;return e!=null&&e.addEventListener&&e.addEventListener(t,o,n),{remove:function(){e!=null&&e.removeEventListener&&e.removeEventListener(t,o,n)}}}const Po=function(){const e=Object.assign({},arguments.length<=0?void 0:arguments[0]);for(let t=1;t<arguments.length;t++){const r=t<0||arguments.length<=t?void 0:arguments[t];r&&Object.keys(r).forEach(n=>{const o=r[n];o!==void 0&&(e[n]=o)})}return e};var fi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M272.9 512l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L186.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H532c6.7 0 10.4-7.7 6.3-12.9L272.9 512zm304 0l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L490.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H836c6.7 0 10.4-7.7 6.3-12.9L576.9 512z"}}]},name:"double-left",theme:"outlined"},mi=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:fi}))},Dr=l.forwardRef(mi),vi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"}}]},name:"double-right",theme:"outlined"},pi=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:vi}))},Mr=l.forwardRef(pi),gi={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"},hi=[10,20,50,100],yi=function(t){var r=t.pageSizeOptions,n=r===void 0?hi:r,o=t.locale,i=t.changeSize,d=t.pageSize,a=t.goButton,c=t.quickGo,s=t.rootPrefixCls,m=t.disabled,u=t.buildOptionText,f=t.showSizeChanger,v=t.sizeChangerRender,g=X.useState(""),b=Re(g,2),p=b[0],y=b[1],C=function(){return!p||Number.isNaN(p)?void 0:Number(p)},x=typeof u=="function"?u:function(R){return"".concat(R," ").concat(o.items_per_page)},w=function(k){y(k.target.value)},S=function(k){a||p===""||(y(""),!(k.relatedTarget&&(k.relatedTarget.className.indexOf("".concat(s,"-item-link"))>=0||k.relatedTarget.className.indexOf("".concat(s,"-item"))>=0))&&(c==null||c(C())))},E=function(k){p!==""&&(k.keyCode===et.ENTER||k.type==="click")&&(y(""),c==null||c(C()))},I=function(){return n.some(function(k){return k.toString()===d.toString()})?n:n.concat([d]).sort(function(k,N){var $=Number.isNaN(Number(k))?0:Number(k),O=Number.isNaN(Number(N))?0:Number(N);return $-O})},P="".concat(s,"-options");if(!f&&!c)return null;var h=null,K=null,z=null;return f&&v&&(h=v({disabled:m,size:d,onSizeChange:function(k){i==null||i(Number(k))},"aria-label":o.page_size,className:"".concat(P,"-size-changer"),options:I().map(function(R){return{label:x(R),value:R}})})),c&&(a&&(z=typeof a=="boolean"?X.createElement("button",{type:"button",onClick:E,onKeyUp:E,disabled:m,className:"".concat(P,"-quick-jumper-button")},o.jump_to_confirm):X.createElement("span",{onClick:E,onKeyUp:E},a)),K=X.createElement("div",{className:"".concat(P,"-quick-jumper")},o.jump_to,X.createElement("input",{disabled:m,type:"text",value:p,onChange:w,onKeyUp:E,onBlur:S,"aria-label":o.page}),o.page,z)),X.createElement("li",{className:P},h,K)},en=function(t){var r=t.rootPrefixCls,n=t.page,o=t.active,i=t.className,d=t.showTitle,a=t.onClick,c=t.onKeyPress,s=t.itemRender,m="".concat(r,"-item"),u=Y(m,"".concat(m,"-").concat(n),T(T({},"".concat(m,"-active"),o),"".concat(m,"-disabled"),!n),i),f=function(){a(n)},v=function(p){c(p,a,n)},g=s(n,"page",X.createElement("a",{rel:"nofollow"},n));return g?X.createElement("li",{title:d?String(n):null,className:u,onClick:f,onKeyDown:v,tabIndex:0},g):null},bi=function(t,r,n){return n};function zr(){}function Br(e){var t=Number(e);return typeof t=="number"&&!Number.isNaN(t)&&isFinite(t)&&Math.floor(t)===t}function Dt(e,t,r){var n=typeof e>"u"?t:e;return Math.floor((r-1)/n)+1}var xi=function(t){var r=t.prefixCls,n=r===void 0?"rc-pagination":r,o=t.selectPrefixCls,i=o===void 0?"rc-select":o,d=t.className,a=t.current,c=t.defaultCurrent,s=c===void 0?1:c,m=t.total,u=m===void 0?0:m,f=t.pageSize,v=t.defaultPageSize,g=v===void 0?10:v,b=t.onChange,p=b===void 0?zr:b,y=t.hideOnSinglePage,C=t.align,x=t.showPrevNextJumpers,w=x===void 0?!0:x,S=t.showQuickJumper,E=t.showLessItems,I=t.showTitle,P=I===void 0?!0:I,h=t.onShowSizeChange,K=h===void 0?zr:h,z=t.locale,R=z===void 0?gi:z,k=t.style,N=t.totalBoundaryShowSizeChanger,$=N===void 0?50:N,O=t.disabled,D=t.simple,_=t.showTotal,M=t.showSizeChanger,G=M===void 0?u>$:M,ee=t.sizeChangerRender,ye=t.pageSizeOptions,oe=t.itemRender,pe=oe===void 0?bi:oe,xe=t.jumpPrevIcon,ue=t.jumpNextIcon,J=t.prevIcon,te=t.nextIcon,Ce=X.useRef(null),le=Bn(10,{value:f,defaultValue:g}),U=Re(le,2),j=U[0],H=U[1],q=Bn(1,{value:a,defaultValue:s,postState:function(we){return Math.max(1,Math.min(we,Dt(void 0,j,u)))}}),Z=Re(q,2),F=Z[0],ne=Z[1],Ie=X.useState(F),We=Re(Ie,2),ke=We[0],B=We[1];l.useEffect(function(){B(F)},[F]);var V=Math.max(1,F-(E?3:5)),ae=Math.min(Dt(void 0,j,u),F+(E?3:5));function fe(re,we){var He=re||X.createElement("button",{type:"button","aria-label":we,className:"".concat(n,"-item-link")});return typeof re=="function"&&(He=X.createElement(re,W({},t))),He}function me(re){var we=re.target.value,He=Dt(void 0,j,u),pt;return we===""?pt=we:Number.isNaN(Number(we))?pt=ke:we>=He?pt=He:pt=Number(we),pt}function Oe(re){return Br(re)&&re!==F&&Br(u)&&u>0}var ze=u>j?S:!1;function je(re){(re.keyCode===et.UP||re.keyCode===et.DOWN)&&re.preventDefault()}function ge(re){var we=me(re);switch(we!==ke&&B(we),re.keyCode){case et.ENTER:L(we);break;case et.UP:L(we-1);break;case et.DOWN:L(we+1);break}}function ve(re){L(me(re))}function Q(re){var we=Dt(re,j,u),He=F>we&&we!==0?we:F;H(re),B(He),K==null||K(F,re),ne(He),p==null||p(He,re)}function L(re){if(Oe(re)&&!O){var we=Dt(void 0,j,u),He=re;return re>we?He=we:re<1&&(He=1),He!==ke&&B(He),ne(He),p==null||p(He,j),He}return F}var Ne=F>1,Te=F<Dt(void 0,j,u);function se(){Ne&&L(F-1)}function Pe(){Te&&L(F+1)}function Se(){L(V)}function Le(){L(ae)}function Ae(re,we){if(re.key==="Enter"||re.charCode===et.ENTER||re.keyCode===et.ENTER){for(var He=arguments.length,pt=new Array(He>2?He-2:0),Kt=2;Kt<He;Kt++)pt[Kt-2]=arguments[Kt];we.apply(void 0,pt)}}function Ue(re){Ae(re,se)}function vt(re){Ae(re,Pe)}function De(re){Ae(re,Se)}function xt(re){Ae(re,Le)}function Ct(re){var we=pe(re,"prev",fe(J,"prev page"));return X.isValidElement(we)?X.cloneElement(we,{disabled:!Ne}):we}function Ge(re){var we=pe(re,"next",fe(te,"next page"));return X.isValidElement(we)?X.cloneElement(we,{disabled:!Te}):we}function nt(re){(re.type==="click"||re.keyCode===et.ENTER)&&L(ke)}var rt=null,Ye=an(t,{aria:!0,data:!0}),qe=_&&X.createElement("li",{className:"".concat(n,"-total-text")},_(u,[u===0?0:(F-1)*j+1,F*j>u?u:F*j])),Je=null,Me=Dt(void 0,j,u);if(y&&u<=j)return null;var _e=[],ot={rootPrefixCls:n,onClick:L,onKeyPress:Ae,showTitle:P,itemRender:pe,page:-1},Xt=F-1>0?F-1:0,Ut=F+1<Me?F+1:Me,ht=S&&S.goButton,Gt=ft(D)==="object"?D.readOnly:!D,ie=ht,ce=null;D&&(ht&&(typeof ht=="boolean"?ie=X.createElement("button",{type:"button",onClick:nt,onKeyUp:nt},R.jump_to_confirm):ie=X.createElement("span",{onClick:nt,onKeyUp:nt},ht),ie=X.createElement("li",{title:P?"".concat(R.jump_to).concat(F,"/").concat(Me):null,className:"".concat(n,"-simple-pager")},ie)),ce=X.createElement("li",{title:P?"".concat(F,"/").concat(Me):null,className:"".concat(n,"-simple-pager")},Gt?ke:X.createElement("input",{type:"text","aria-label":R.jump_to,value:ke,disabled:O,onKeyDown:je,onKeyUp:ge,onChange:ge,onBlur:ve,size:3}),X.createElement("span",{className:"".concat(n,"-slash")},"/"),Me));var Ke=E?1:2;if(Me<=3+Ke*2){Me||_e.push(X.createElement(en,de({},ot,{key:"noPager",page:1,className:"".concat(n,"-item-disabled")})));for(var Be=1;Be<=Me;Be+=1)_e.push(X.createElement(en,de({},ot,{key:Be,page:Be,active:F===Be})))}else{var Xe=E?R.prev_3:R.prev_5,at=E?R.next_3:R.next_5,it=pe(V,"jump-prev",fe(xe,"prev page")),Ve=pe(ae,"jump-next",fe(ue,"next page"));w&&(rt=it?X.createElement("li",{title:P?Xe:null,key:"prev",onClick:Se,tabIndex:0,onKeyDown:De,className:Y("".concat(n,"-jump-prev"),T({},"".concat(n,"-jump-prev-custom-icon"),!!xe))},it):null,Je=Ve?X.createElement("li",{title:P?at:null,key:"next",onClick:Le,tabIndex:0,onKeyDown:xt,className:Y("".concat(n,"-jump-next"),T({},"".concat(n,"-jump-next-custom-icon"),!!ue))},Ve):null);var Bt=Math.max(1,F-Ke),Yt=Math.min(F+Ke,Me);F-1<=Ke&&(Yt=1+Ke*2),Me-F<=Ke&&(Bt=Me-Ke*2);for(var St=Bt;St<=Yt;St+=1)_e.push(X.createElement(en,de({},ot,{key:St,page:St,active:F===St})));if(F-1>=Ke*2&&F!==3&&(_e[0]=X.cloneElement(_e[0],{className:Y("".concat(n,"-item-after-jump-prev"),_e[0].props.className)}),_e.unshift(rt)),Me-F>=Ke*2&&F!==Me-2){var It=_e[_e.length-1];_e[_e.length-1]=X.cloneElement(It,{className:Y("".concat(n,"-item-before-jump-next"),It.props.className)}),_e.push(Je)}Bt!==1&&_e.unshift(X.createElement(en,de({},ot,{key:1,page:1}))),Yt!==Me&&_e.push(X.createElement(en,de({},ot,{key:Me,page:Me})))}var ut=Ct(Xt);if(ut){var Jt=!Ne||!Me;ut=X.createElement("li",{title:P?R.prev_page:null,onClick:se,tabIndex:Jt?null:0,onKeyDown:Ue,className:Y("".concat(n,"-prev"),T({},"".concat(n,"-disabled"),Jt)),"aria-disabled":Jt},ut)}var kt=Ge(Ut);if(kt){var Ot,Lt;D?(Ot=!Te,Lt=Ne?0:null):(Ot=!Te||!Me,Lt=Ot?null:0),kt=X.createElement("li",{title:P?R.next_page:null,onClick:Pe,tabIndex:Lt,onKeyDown:vt,className:Y("".concat(n,"-next"),T({},"".concat(n,"-disabled"),Ot)),"aria-disabled":Ot},kt)}var wn=Y(n,d,T(T(T(T(T({},"".concat(n,"-start"),C==="start"),"".concat(n,"-center"),C==="center"),"".concat(n,"-end"),C==="end"),"".concat(n,"-simple"),D),"".concat(n,"-disabled"),O));return X.createElement("ul",de({className:wn,style:k,ref:Ce},Ye),qe,ut,D?ce:_e,kt,X.createElement(yi,{locale:R,rootPrefixCls:n,disabled:O,selectPrefixCls:i,changeSize:Q,pageSize:j,pageSizeOptions:ye,quickGo:ze?L:null,goButton:ie,showSizeChanger:G,sizeChangerRender:ee}))};const Ci=e=>{const{componentCls:t}=e;return{[`${t}-disabled`]:{"&, &:hover":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}},"&:focus-visible":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-item`]:{cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},a:{color:e.colorTextDisabled,backgroundColor:"transparent",border:"none",cursor:"not-allowed"},"&-active":{borderColor:e.colorBorder,backgroundColor:e.itemActiveBgDisabled,"&:hover, &:active":{backgroundColor:e.itemActiveBgDisabled},a:{color:e.itemActiveColorDisabled}}},[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},[`${t}-simple&`]:{backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"}}},[`${t}-simple-pager`]:{color:e.colorTextDisabled},[`${t}-jump-prev, ${t}-jump-next`]:{[`${t}-item-link-icon`]:{opacity:0},[`${t}-item-ellipsis`]:{opacity:1}}},[`&${t}-simple`]:{[`${t}-prev, ${t}-next`]:{[`&${t}-disabled ${t}-item-link`]:{"&:hover, &:active":{backgroundColor:"transparent"}}}}}},Si=e=>{const{componentCls:t}=e;return{[`&${t}-mini ${t}-total-text, &${t}-mini ${t}-simple-pager`]:{height:e.itemSizeSM,lineHeight:A(e.itemSizeSM)},[`&${t}-mini ${t}-item`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:A(e.calc(e.itemSizeSM).sub(2).equal())},[`&${t}-mini:not(${t}-disabled) ${t}-item:not(${t}-item-active)`]:{backgroundColor:"transparent",borderColor:"transparent","&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},[`&${t}-mini ${t}-prev, &${t}-mini ${t}-next`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:A(e.itemSizeSM)},[`&${t}-mini:not(${t}-disabled)`]:{[`${t}-prev, ${t}-next`]:{[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover ${t}-item-link`]:{backgroundColor:"transparent"}}},[`
    &${t}-mini ${t}-prev ${t}-item-link,
    &${t}-mini ${t}-next ${t}-item-link
    `]:{backgroundColor:"transparent",borderColor:"transparent","&::after":{height:e.itemSizeSM,lineHeight:A(e.itemSizeSM)}},[`&${t}-mini ${t}-jump-prev, &${t}-mini ${t}-jump-next`]:{height:e.itemSizeSM,marginInlineEnd:0,lineHeight:A(e.itemSizeSM)},[`&${t}-mini ${t}-options`]:{marginInlineStart:e.paginationMiniOptionsMarginInlineStart,"&-size-changer":{top:e.miniOptionsSizeChangerTop},"&-quick-jumper":{height:e.itemSizeSM,lineHeight:A(e.itemSizeSM),input:Object.assign(Object.assign({},za(e)),{width:e.paginationMiniQuickJumperInputWidth,height:e.controlHeightSM})}}}},wi=e=>{const{componentCls:t}=e;return{[`
    &${t}-simple ${t}-prev,
    &${t}-simple ${t}-next
    `]:{height:e.itemSizeSM,lineHeight:A(e.itemSizeSM),verticalAlign:"top",[`${t}-item-link`]:{height:e.itemSizeSM,backgroundColor:"transparent",border:0,"&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive},"&::after":{height:e.itemSizeSM,lineHeight:A(e.itemSizeSM)}}},[`&${t}-simple ${t}-simple-pager`]:{display:"inline-block",height:e.itemSizeSM,marginInlineEnd:e.marginXS,input:{boxSizing:"border-box",height:"100%",padding:`0 ${A(e.paginationItemPaddingInline)}`,textAlign:"center",backgroundColor:e.itemInputBg,border:`${A(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadius,outline:"none",transition:`border-color ${e.motionDurationMid}`,color:"inherit","&:hover":{borderColor:e.colorPrimary},"&:focus":{borderColor:e.colorPrimaryHover,boxShadow:`${A(e.inputOutlineOffset)} 0 ${A(e.controlOutlineWidth)} ${e.controlOutline}`},"&[disabled]":{color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"}}}}},Ei=e=>{const{componentCls:t}=e;return{[`${t}-jump-prev, ${t}-jump-next`]:{outline:0,[`${t}-item-container`]:{position:"relative",[`${t}-item-link-icon`]:{color:e.colorPrimary,fontSize:e.fontSizeSM,opacity:0,transition:`all ${e.motionDurationMid}`,"&-svg":{top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,margin:"auto"}},[`${t}-item-ellipsis`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,display:"block",margin:"auto",color:e.colorTextDisabled,letterSpacing:e.paginationEllipsisLetterSpacing,textAlign:"center",textIndent:e.paginationEllipsisTextIndent,opacity:1,transition:`all ${e.motionDurationMid}`}},"&:hover":{[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}}},[`
    ${t}-prev,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{marginInlineEnd:e.marginXS},[`
    ${t}-prev,
    ${t}-next,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,color:e.colorText,fontFamily:e.fontFamily,lineHeight:A(e.itemSize),textAlign:"center",verticalAlign:"middle",listStyle:"none",borderRadius:e.borderRadius,cursor:"pointer",transition:`all ${e.motionDurationMid}`},[`${t}-prev, ${t}-next`]:{outline:0,button:{color:e.colorText,cursor:"pointer",userSelect:"none"},[`${t}-item-link`]:{display:"block",width:"100%",height:"100%",padding:0,fontSize:e.fontSizeSM,textAlign:"center",backgroundColor:"transparent",border:`${A(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:"none",transition:`all ${e.motionDurationMid}`},[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover`]:{[`${t}-item-link`]:{backgroundColor:"transparent"}}},[`${t}-slash`]:{marginInlineEnd:e.paginationSlashMarginInlineEnd,marginInlineStart:e.paginationSlashMarginInlineStart},[`${t}-options`]:{display:"inline-block",marginInlineStart:e.margin,verticalAlign:"middle","&-size-changer":{display:"inline-block",width:"auto"},"&-quick-jumper":{display:"inline-block",height:e.controlHeight,marginInlineStart:e.marginXS,lineHeight:A(e.controlHeight),verticalAlign:"top",input:Object.assign(Object.assign(Object.assign({},Ta(e)),Da(e,{borderColor:e.colorBorder,hoverBorderColor:e.colorPrimaryHover,activeBorderColor:e.colorPrimary,activeShadow:e.activeShadow})),{"&[disabled]":Object.assign({},Ma(e)),width:e.calc(e.controlHeightLG).mul(1.25).equal(),height:e.controlHeight,boxSizing:"border-box",margin:0,marginInlineStart:e.marginXS,marginInlineEnd:e.marginXS})}}}},$i=e=>{const{componentCls:t}=e;return{[`${t}-item`]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,marginInlineEnd:e.marginXS,fontFamily:e.fontFamily,lineHeight:A(e.calc(e.itemSize).sub(2).equal()),textAlign:"center",verticalAlign:"middle",listStyle:"none",backgroundColor:e.itemBg,border:`${A(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:0,cursor:"pointer",userSelect:"none",a:{display:"block",padding:`0 ${A(e.paginationItemPaddingInline)}`,color:e.colorText,"&:hover":{textDecoration:"none"}},[`&:not(${t}-item-active)`]:{"&:hover":{transition:`all ${e.motionDurationMid}`,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},"&-active":{fontWeight:e.fontWeightStrong,backgroundColor:e.itemActiveBg,borderColor:e.colorPrimary,a:{color:e.colorPrimary},"&:hover":{borderColor:e.colorPrimaryHover},"&:hover a":{color:e.colorPrimaryHover}}}}},Ni=e=>{const{componentCls:t}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},sn(e)),{display:"flex","&-start":{justifyContent:"start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"end"},"ul, ol":{margin:0,padding:0,listStyle:"none"},"&::after":{display:"block",clear:"both",height:0,overflow:"hidden",visibility:"hidden",content:'""'},[`${t}-total-text`]:{display:"inline-block",height:e.itemSize,marginInlineEnd:e.marginXS,lineHeight:A(e.calc(e.itemSize).sub(2).equal()),verticalAlign:"middle"}}),$i(e)),Ei(e)),wi(e)),Si(e)),Ci(e)),{[`@media only screen and (max-width: ${e.screenLG}px)`]:{[`${t}-item`]:{"&-after-jump-prev, &-before-jump-next":{display:"none"}}},[`@media only screen and (max-width: ${e.screenSM}px)`]:{[`${t}-options`]:{display:"none"}}}),[`&${e.componentCls}-rtl`]:{direction:"rtl"}}},Ri=e=>{const{componentCls:t}=e;return{[`${t}:not(${t}-disabled)`]:{[`${t}-item`]:Object.assign({},ba(e)),[`${t}-jump-prev, ${t}-jump-next`]:{"&:focus-visible":Object.assign({[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}},zn(e))},[`${t}-prev, ${t}-next`]:{[`&:focus-visible ${t}-item-link`]:Object.assign({},zn(e))}}}},To=e=>Object.assign({itemBg:e.colorBgContainer,itemSize:e.controlHeight,itemSizeSM:e.controlHeightSM,itemActiveBg:e.colorBgContainer,itemLinkBg:e.colorBgContainer,itemActiveColorDisabled:e.colorTextDisabled,itemActiveBgDisabled:e.controlItemBgActiveDisabled,itemInputBg:e.colorBgContainer,miniOptionsSizeChangerTop:0},Pa(e)),Do=e=>bn(e,{inputOutlineOffset:0,paginationMiniOptionsMarginInlineStart:e.calc(e.marginXXS).div(2).equal(),paginationMiniQuickJumperInputWidth:e.calc(e.controlHeightLG).mul(1.1).equal(),paginationItemPaddingInline:e.calc(e.marginXXS).mul(1.5).equal(),paginationEllipsisLetterSpacing:e.calc(e.marginXXS).div(2).equal(),paginationSlashMarginInlineStart:e.marginSM,paginationSlashMarginInlineEnd:e.marginSM,paginationEllipsisTextIndent:"0.13em"},Ka(e)),Ii=yn("Pagination",e=>{const t=Do(e);return[Ni(t),Ri(t)]},To),ki=e=>{const{componentCls:t}=e;return{[`${t}${t}-bordered${t}-disabled:not(${t}-mini)`]:{"&, &:hover":{[`${t}-item-link`]:{borderColor:e.colorBorder}},"&:focus-visible":{[`${t}-item-link`]:{borderColor:e.colorBorder}},[`${t}-item, ${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,[`&:hover:not(${t}-item-active)`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,a:{color:e.colorTextDisabled}},[`&${t}-item-active`]:{backgroundColor:e.itemActiveBgDisabled}},[`${t}-prev, ${t}-next`]:{"&:hover button":{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,color:e.colorTextDisabled},[`${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder}}},[`${t}${t}-bordered:not(${t}-mini)`]:{[`${t}-prev, ${t}-next`]:{"&:hover button":{borderColor:e.colorPrimaryHover,backgroundColor:e.itemBg},[`${t}-item-link`]:{backgroundColor:e.itemLinkBg,borderColor:e.colorBorder},[`&:hover ${t}-item-link`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,color:e.colorPrimary},[`&${t}-disabled`]:{[`${t}-item-link`]:{borderColor:e.colorBorder,color:e.colorTextDisabled}}},[`${t}-item`]:{backgroundColor:e.itemBg,border:`${A(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,[`&:hover:not(${t}-item-active)`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,a:{color:e.colorPrimary}},"&-active":{borderColor:e.colorPrimary}}}}},Oi=Ba(["Pagination","bordered"],e=>{const t=Do(e);return[ki(t)]},To);function Lr(e){return l.useMemo(()=>typeof e=="boolean"?[e,{}]:e&&typeof e=="object"?[!0,e]:[void 0,void 0],[e])}var Ki=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Pi=e=>{const{align:t,prefixCls:r,selectPrefixCls:n,className:o,rootClassName:i,style:d,size:a,locale:c,responsive:s,showSizeChanger:m,selectComponentClass:u,pageSizeOptions:f}=e,v=Ki(e,["align","prefixCls","selectPrefixCls","className","rootClassName","style","size","locale","responsive","showSizeChanger","selectComponentClass","pageSizeOptions"]),{xs:g}=Ro(s),[,b]=Jn(),{getPrefixCls:p,direction:y,showSizeChanger:C,className:x,style:w}=bo("pagination"),S=p("pagination",r),[E,I,P]=Ii(S),h=wo(a),K=h==="small"||!!(g&&!h&&s),[z]=La("Pagination",xa),R=Object.assign(Object.assign({},z),c),[k,N]=Lr(m),[$,O]=Lr(C),D=k??$,_=N??O,M=u||qa,G=l.useMemo(()=>f?f.map(ue=>Number(ue)):void 0,[f]),ee=ue=>{var J;const{disabled:te,size:Ce,onSizeChange:le,"aria-label":U,className:j,options:H}=ue,{className:q,onChange:Z}=_||{},F=(J=H.find(ne=>String(ne.value)===String(Ce)))===null||J===void 0?void 0:J.value;return l.createElement(M,Object.assign({disabled:te,showSearch:!0,popupMatchSelectWidth:!1,getPopupContainer:ne=>ne.parentNode,"aria-label":U,options:H},_,{value:F,onChange:(ne,Ie)=>{le==null||le(ne),Z==null||Z(ne,Ie)},size:K?"small":"middle",className:Y(j,q)}))},ye=l.useMemo(()=>{const ue=l.createElement("span",{className:`${S}-item-ellipsis`},"•••"),J=l.createElement("button",{className:`${S}-item-link`,type:"button",tabIndex:-1},y==="rtl"?l.createElement(Nr,null):l.createElement(Or,null)),te=l.createElement("button",{className:`${S}-item-link`,type:"button",tabIndex:-1},y==="rtl"?l.createElement(Or,null):l.createElement(Nr,null)),Ce=l.createElement("a",{className:`${S}-item-link`},l.createElement("div",{className:`${S}-item-container`},y==="rtl"?l.createElement(Mr,{className:`${S}-item-link-icon`}):l.createElement(Dr,{className:`${S}-item-link-icon`}),ue)),le=l.createElement("a",{className:`${S}-item-link`},l.createElement("div",{className:`${S}-item-container`},y==="rtl"?l.createElement(Dr,{className:`${S}-item-link-icon`}):l.createElement(Mr,{className:`${S}-item-link-icon`}),ue));return{prevIcon:J,nextIcon:te,jumpPrevIcon:Ce,jumpNextIcon:le}},[y,S]),oe=p("select",n),pe=Y({[`${S}-${t}`]:!!t,[`${S}-mini`]:K,[`${S}-rtl`]:y==="rtl",[`${S}-bordered`]:b.wireframe},x,o,i,I,P),xe=Object.assign(Object.assign({},w),d);return E(l.createElement(l.Fragment,null,b.wireframe&&l.createElement(Oi,{prefixCls:S}),l.createElement(xi,Object.assign({},ye,v,{style:xe,prefixCls:S,selectPrefixCls:oe,className:pe,locale:R,pageSizeOptions:G,showSizeChanger:D,sizeChangerRender:ee}))))},pn=100,Mo=pn/5,zo=pn/2-Mo/2,On=zo*2*Math.PI,_r=50,Hr=e=>{const{dotClassName:t,style:r,hasCircleCls:n}=e;return l.createElement("circle",{className:Y(`${t}-circle`,{[`${t}-circle-bg`]:n}),r:zo,cx:_r,cy:_r,strokeWidth:Mo,style:r})},Ti=e=>{let{percent:t,prefixCls:r}=e;const n=`${r}-dot`,o=`${n}-holder`,i=`${o}-hidden`,[d,a]=l.useState(!1);Mt(()=>{t!==0&&a(!0)},[t!==0]);const c=Math.max(Math.min(t,100),0);if(!d)return null;const s={strokeDashoffset:`${On/4}`,strokeDasharray:`${On*c/100} ${On*(100-c)/100}`};return l.createElement("span",{className:Y(o,`${n}-progress`,c<=0&&i)},l.createElement("svg",{viewBox:`0 0 ${pn} ${pn}`,role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":c},l.createElement(Hr,{dotClassName:n,hasCircleCls:!0}),l.createElement(Hr,{dotClassName:n,style:s})))};function Di(e){const{prefixCls:t,percent:r=0}=e,n=`${t}-dot`,o=`${n}-holder`,i=`${o}-hidden`;return l.createElement(l.Fragment,null,l.createElement("span",{className:Y(o,r>0&&i)},l.createElement("span",{className:Y(n,`${t}-dot-spin`)},[1,2,3,4].map(d=>l.createElement("i",{className:`${t}-dot-item`,key:d})))),l.createElement(Ti,{prefixCls:t,percent:r}))}function Mi(e){const{prefixCls:t,indicator:r,percent:n}=e,o=`${t}-dot`;return r&&l.isValidElement(r)?Ln(r,{className:Y(r.props.className,o),percent:n}):l.createElement(Di,{prefixCls:t,percent:n})}const zi=new tr("antSpinMove",{to:{opacity:1}}),Bi=new tr("antRotate",{to:{transform:"rotate(405deg)"}}),Li=e=>{const{componentCls:t,calc:r}=e;return{[t]:Object.assign(Object.assign({},sn(e)),{position:"absolute",display:"none",color:e.colorPrimary,fontSize:0,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"relative",display:"inline-block",opacity:1},[`${t}-text`]:{fontSize:e.fontSize,paddingTop:r(r(e.dotSize).sub(e.fontSize)).div(2).add(2).equal()},"&-fullscreen":{position:"fixed",width:"100vw",height:"100vh",backgroundColor:e.colorBgMask,zIndex:e.zIndexPopupBase,inset:0,display:"flex",alignItems:"center",flexDirection:"column",justifyContent:"center",opacity:0,visibility:"hidden",transition:`all ${e.motionDurationMid}`,"&-show":{opacity:1,visibility:"visible"},[t]:{[`${t}-dot-holder`]:{color:e.colorWhite},[`${t}-text`]:{color:e.colorTextLightSolid}}},"&-nested-loading":{position:"relative",[`> div > ${t}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${t}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:r(e.dotSize).mul(-1).div(2).equal()},[`${t}-text`]:{position:"absolute",top:"50%",width:"100%",textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${t}-show-text ${t}-dot`]:{marginTop:r(e.dotSize).div(2).mul(-1).sub(10).equal()},"&-sm":{[`${t}-dot`]:{margin:r(e.dotSizeSM).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:r(r(e.dotSizeSM).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:r(e.dotSizeSM).div(2).mul(-1).sub(10).equal()}},"&-lg":{[`${t}-dot`]:{margin:r(e.dotSizeLG).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:r(r(e.dotSizeLG).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:r(e.dotSizeLG).div(2).mul(-1).sub(10).equal()}}},[`${t}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${t}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${t}-dot-holder`]:{width:"1em",height:"1em",fontSize:e.dotSize,display:"inline-block",transition:`transform ${e.motionDurationSlow} ease, opacity ${e.motionDurationSlow} ease`,transformOrigin:"50% 50%",lineHeight:1,color:e.colorPrimary,"&-hidden":{transform:"scale(0.3)",opacity:0}},[`${t}-dot-progress`]:{position:"absolute",inset:0},[`${t}-dot`]:{position:"relative",display:"inline-block",fontSize:e.dotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:r(e.dotSize).sub(r(e.marginXXS).div(2)).div(2).equal(),height:r(e.dotSize).sub(r(e.marginXXS).div(2)).div(2).equal(),background:"currentColor",borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:zi,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0,animationDelay:"0s"},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:Bi,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"},"&-circle":{strokeLinecap:"round",transition:["stroke-dashoffset","stroke-dasharray","stroke","stroke-width","opacity"].map(n=>`${n} ${e.motionDurationSlow} ease`).join(","),fillOpacity:0,stroke:"currentcolor"},"&-circle-bg":{stroke:e.colorFillSecondary}},[`&-sm ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeSM}},[`&-sm ${t}-dot-holder`]:{i:{width:r(r(e.dotSizeSM).sub(r(e.marginXXS).div(2))).div(2).equal(),height:r(r(e.dotSizeSM).sub(r(e.marginXXS).div(2))).div(2).equal()}},[`&-lg ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeLG}},[`&-lg ${t}-dot-holder`]:{i:{width:r(r(e.dotSizeLG).sub(e.marginXXS)).div(2).equal(),height:r(r(e.dotSizeLG).sub(e.marginXXS)).div(2).equal()}},[`&${t}-show-text ${t}-text`]:{display:"block"}})}},_i=e=>{const{controlHeightLG:t,controlHeight:r}=e;return{contentHeight:400,dotSize:t/2,dotSizeSM:t*.35,dotSizeLG:r}},Hi=yn("Spin",e=>{const t=bn(e,{spinDotDefault:e.colorTextDescription});return[Li(t)]},_i),ji=200,jr=[[30,.05],[70,.03],[96,.01]];function Ai(e,t){const[r,n]=l.useState(0),o=l.useRef(null),i=t==="auto";return l.useEffect(()=>(i&&e&&(n(0),o.current=setInterval(()=>{n(d=>{const a=100-d;for(let c=0;c<jr.length;c+=1){const[s,m]=jr[c];if(d<=s)return d+a*m}return d})},ji)),()=>{clearInterval(o.current)}),[i,e]),i?r:t}var Fi=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let Bo;function Wi(e,t){return!!e&&!!t&&!Number.isNaN(Number(t))}const Lo=e=>{var t;const{prefixCls:r,spinning:n=!0,delay:o=0,className:i,rootClassName:d,size:a="default",tip:c,wrapperClassName:s,style:m,children:u,fullscreen:f=!1,indicator:v,percent:g}=e,b=Fi(e,["prefixCls","spinning","delay","className","rootClassName","size","tip","wrapperClassName","style","children","fullscreen","indicator","percent"]),{getPrefixCls:p,direction:y,className:C,style:x,indicator:w}=bo("spin"),S=p("spin",r),[E,I,P]=Hi(S),[h,K]=l.useState(()=>n&&!Wi(n,o)),z=Ai(h,g);l.useEffect(()=>{if(n){const _=ai(o,()=>{K(!0)});return _(),()=>{var M;(M=_==null?void 0:_.cancel)===null||M===void 0||M.call(_)}}K(!1)},[o,n]);const R=l.useMemo(()=>typeof u<"u"&&!f,[u,f]),k=Y(S,C,{[`${S}-sm`]:a==="small",[`${S}-lg`]:a==="large",[`${S}-spinning`]:h,[`${S}-show-text`]:!!c,[`${S}-rtl`]:y==="rtl"},i,!f&&d,I,P),N=Y(`${S}-container`,{[`${S}-blur`]:h}),$=(t=v??w)!==null&&t!==void 0?t:Bo,O=Object.assign(Object.assign({},x),m),D=l.createElement("div",Object.assign({},b,{style:O,className:k,"aria-live":"polite","aria-busy":h}),l.createElement(Mi,{prefixCls:S,indicator:$,percent:z}),c&&(R||f)?l.createElement("div",{className:`${S}-text`},c):null);return E(R?l.createElement("div",Object.assign({},b,{className:Y(`${S}-nested-loading`,s,I,P)}),h&&l.createElement("div",{key:"loading"},D),l.createElement("div",{className:N,key:"container"},u)):f?l.createElement("div",{className:Y(`${S}-fullscreen`,{[`${S}-fullscreen-show`]:h},d,I,P)},D):D)};Lo.setDefaultIndicator=e=>{Bo=e};var Vi={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},qi=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:Vi}))},Xi=l.forwardRef(qi),Ui={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},Gi=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:Ui}))},Yi=l.forwardRef(Gi),Ji={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},Qi=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:Ji}))},Zi=l.forwardRef(Qi),el={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},tl=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:el}))},_o=l.forwardRef(tl),nl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},rl=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:nl}))},ol=l.forwardRef(rl),al={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},il=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:al}))},ll=l.forwardRef(il),sl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},cl=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:sl}))},dl=l.forwardRef(cl),ul={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},fl=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:ul}))},ml=l.forwardRef(fl),vl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},pl=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:vl}))},gl=l.forwardRef(pl),hl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},yl=function(t,r){return l.createElement(dt,de({},t,{ref:r,icon:hl}))},bl=l.forwardRef(yl),bt={},dn="rc-table-internal-hook";function or(e){var t=l.createContext(void 0),r=function(o){var i=o.value,d=o.children,a=l.useRef(i);a.current=i;var c=l.useState(function(){return{getValue:function(){return a.current},listeners:new Set}}),s=Re(c,1),m=s[0];return Mt(function(){Ca.unstable_batchedUpdates(function(){m.listeners.forEach(function(u){u(i)})})},[i]),l.createElement(t.Provider,{value:m},d)};return{Context:t,Provider:r,defaultValue:e}}function tt(e,t){var r=$t(typeof t=="function"?t:function(u){if(t===void 0)return u;if(!Array.isArray(t))return u[t];var f={};return t.forEach(function(v){f[v]=u[v]}),f}),n=l.useContext(e==null?void 0:e.Context),o=n||{},i=o.listeners,d=o.getValue,a=l.useRef();a.current=r(n?d():e==null?void 0:e.defaultValue);var c=l.useState({}),s=Re(c,2),m=s[1];return Mt(function(){if(!n)return;function u(f){var v=r(f);on(a.current,v,!0)||m({})}return i.add(u),function(){i.delete(u)}},[n]),a.current}function xl(){var e=l.createContext(null);function t(){return l.useContext(e)}function r(o,i){var d=Er(o),a=function(s,m){var u=d?{ref:m}:{},f=l.useRef(0),v=l.useRef(s),g=t();return g!==null?l.createElement(o,de({},s,u)):((!i||i(v.current,s))&&(f.current+=1),v.current=s,l.createElement(e.Provider,{value:f.current},l.createElement(o,de({},s,u))))};return d?l.forwardRef(a):a}function n(o,i){var d=Er(o),a=function(s,m){var u=d?{ref:m}:{};return t(),l.createElement(o,de({},s,u))};return d?l.memo(l.forwardRef(a),i):l.memo(a,i)}return{makeImmutable:r,responseImmutable:n,useImmutableMark:t}}var ar=xl(),Ho=ar.makeImmutable,Ft=ar.responseImmutable,Cl=ar.useImmutableMark,lt=or(),jo=l.createContext({renderWithProps:!1}),Sl="RC_TABLE_KEY";function wl(e){return e==null?[]:Array.isArray(e)?e:[e]}function xn(e){var t=[],r={};return e.forEach(function(n){for(var o=n||{},i=o.key,d=o.dataIndex,a=i||wl(d).join("-")||Sl;r[a];)a="".concat(a,"_next");r[a]=!0,t.push(a)}),t}function Hn(e){return e!=null}function El(e){return typeof e=="number"&&!Number.isNaN(e)}function $l(e){return e&&ft(e)==="object"&&!Array.isArray(e)&&!l.isValidElement(e)}function Nl(e,t,r,n,o,i){var d=l.useContext(jo),a=Cl(),c=xo(function(){if(Hn(n))return[n];var s=t==null||t===""?[]:Array.isArray(t)?t:[t],m=Qn(e,s),u=m,f=void 0;if(o){var v=o(m,e,r);$l(v)?(u=v.children,f=v.props,d.renderWithProps=!0):u=v}return[u,f]},[a,e,n,t,o,r],function(s,m){if(i){var u=Re(s,2),f=u[1],v=Re(m,2),g=v[1];return i(g,f)}return d.renderWithProps?!0:!on(s,m,!0)});return c}function Rl(e,t,r,n){var o=e+t-1;return e<=n&&o>=r}function Il(e,t){return tt(lt,function(r){var n=Rl(e,t||1,r.hoverStartRow,r.hoverEndRow);return[n,r.onHover]})}var kl=function(t){var r=t.ellipsis,n=t.rowType,o=t.children,i,d=r===!0?{showTitle:!0}:r;return d&&(d.showTitle||n==="header")&&(typeof o=="string"||typeof o=="number"?i=o.toString():l.isValidElement(o)&&typeof o.props.children=="string"&&(i=o.props.children)),i};function Ol(e){var t,r,n,o,i,d,a,c,s=e.component,m=e.children,u=e.ellipsis,f=e.scope,v=e.prefixCls,g=e.className,b=e.align,p=e.record,y=e.render,C=e.dataIndex,x=e.renderIndex,w=e.shouldCellUpdate,S=e.index,E=e.rowType,I=e.colSpan,P=e.rowSpan,h=e.fixLeft,K=e.fixRight,z=e.firstFixLeft,R=e.lastFixLeft,k=e.firstFixRight,N=e.lastFixRight,$=e.appendNode,O=e.additionalProps,D=O===void 0?{}:O,_=e.isSticky,M="".concat(v,"-cell"),G=tt(lt,["supportSticky","allColumnsFixedLeft","rowHoverable"]),ee=G.supportSticky,ye=G.allColumnsFixedLeft,oe=G.rowHoverable,pe=Nl(p,C,x,m,y,w),xe=Re(pe,2),ue=xe[0],J=xe[1],te={},Ce=typeof h=="number"&&ee,le=typeof K=="number"&&ee;Ce&&(te.position="sticky",te.left=h),le&&(te.position="sticky",te.right=K);var U=(t=(r=(n=J==null?void 0:J.colSpan)!==null&&n!==void 0?n:D.colSpan)!==null&&r!==void 0?r:I)!==null&&t!==void 0?t:1,j=(o=(i=(d=J==null?void 0:J.rowSpan)!==null&&d!==void 0?d:D.rowSpan)!==null&&i!==void 0?i:P)!==null&&o!==void 0?o:1,H=Il(S,j),q=Re(H,2),Z=q[0],F=q[1],ne=$t(function(fe){var me;p&&F(S,S+j-1),D==null||(me=D.onMouseEnter)===null||me===void 0||me.call(D,fe)}),Ie=$t(function(fe){var me;p&&F(-1,-1),D==null||(me=D.onMouseLeave)===null||me===void 0||me.call(D,fe)});if(U===0||j===0)return null;var We=(a=D.title)!==null&&a!==void 0?a:kl({rowType:E,ellipsis:u,children:ue}),ke=Y(M,g,(c={},T(T(T(T(T(T(T(T(T(T(c,"".concat(M,"-fix-left"),Ce&&ee),"".concat(M,"-fix-left-first"),z&&ee),"".concat(M,"-fix-left-last"),R&&ee),"".concat(M,"-fix-left-all"),R&&ye&&ee),"".concat(M,"-fix-right"),le&&ee),"".concat(M,"-fix-right-first"),k&&ee),"".concat(M,"-fix-right-last"),N&&ee),"".concat(M,"-ellipsis"),u),"".concat(M,"-with-append"),$),"".concat(M,"-fix-sticky"),(Ce||le)&&_&&ee),T(c,"".concat(M,"-row-hover"),!J&&Z)),D.className,J==null?void 0:J.className),B={};b&&(B.textAlign=b);var V=W(W(W(W({},J==null?void 0:J.style),te),B),D.style),ae=ue;return ft(ae)==="object"&&!Array.isArray(ae)&&!l.isValidElement(ae)&&(ae=null),u&&(R||k)&&(ae=l.createElement("span",{className:"".concat(M,"-content")},ae)),l.createElement(s,de({},J,D,{className:ke,style:V,title:We,scope:f,onMouseEnter:oe?ne:void 0,onMouseLeave:oe?Ie:void 0,colSpan:U!==1?U:null,rowSpan:j!==1?j:null}),$,ae)}const Wt=l.memo(Ol);function ir(e,t,r,n,o){var i=r[e]||{},d=r[t]||{},a,c;i.fixed==="left"?a=n.left[o==="rtl"?t:e]:d.fixed==="right"&&(c=n.right[o==="rtl"?e:t]);var s=!1,m=!1,u=!1,f=!1,v=r[t+1],g=r[e-1],b=v&&!v.fixed||g&&!g.fixed||r.every(function(w){return w.fixed==="left"});if(o==="rtl"){if(a!==void 0){var p=g&&g.fixed==="left";f=!p&&b}else if(c!==void 0){var y=v&&v.fixed==="right";u=!y&&b}}else if(a!==void 0){var C=v&&v.fixed==="left";s=!C&&b}else if(c!==void 0){var x=g&&g.fixed==="right";m=!x&&b}return{fixLeft:a,fixRight:c,lastFixLeft:s,firstFixRight:m,lastFixRight:u,firstFixLeft:f,isSticky:n.isSticky}}var Ao=l.createContext({});function Kl(e){var t=e.className,r=e.index,n=e.children,o=e.colSpan,i=o===void 0?1:o,d=e.rowSpan,a=e.align,c=tt(lt,["prefixCls","direction"]),s=c.prefixCls,m=c.direction,u=l.useContext(Ao),f=u.scrollColumnIndex,v=u.stickyOffsets,g=u.flattenColumns,b=r+i-1,p=b+1===f?i+1:i,y=ir(r,r+p-1,g,v,m);return l.createElement(Wt,de({className:t,index:r,component:"td",prefixCls:s,record:null,dataIndex:null,align:a,colSpan:p,rowSpan:d,render:function(){return n}},y))}var Pl=["children"];function Tl(e){var t=e.children,r=mt(e,Pl);return l.createElement("tr",r,t)}function Cn(e){var t=e.children;return t}Cn.Row=Tl;Cn.Cell=Kl;function Dl(e){var t=e.children,r=e.stickyOffsets,n=e.flattenColumns,o=tt(lt,"prefixCls"),i=n.length-1,d=n[i],a=l.useMemo(function(){return{stickyOffsets:r,flattenColumns:n,scrollColumnIndex:d!=null&&d.scrollbar?i:null}},[d,n,i,r]);return l.createElement(Ao.Provider,{value:a},l.createElement("tfoot",{className:"".concat(o,"-summary")},t))}const fn=Ft(Dl);var Fo=Cn;function Ml(e){return null}function zl(e){return null}function Wo(e,t,r,n,o,i,d){e.push({record:t,indent:r,index:d});var a=i(t),c=o==null?void 0:o.has(a);if(t&&Array.isArray(t[n])&&c)for(var s=0;s<t[n].length;s+=1)Wo(e,t[n][s],r+1,n,o,i,s)}function Vo(e,t,r,n){var o=l.useMemo(function(){if(r!=null&&r.size){for(var i=[],d=0;d<(e==null?void 0:e.length);d+=1){var a=e[d];Wo(i,a,0,t,r,n,d)}return i}return e==null?void 0:e.map(function(c,s){return{record:c,indent:0,index:s}})},[e,t,r,n]);return o}function qo(e,t,r,n){var o=tt(lt,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),i=o.flattenColumns,d=o.expandableType,a=o.expandedKeys,c=o.childrenColumnName,s=o.onTriggerExpand,m=o.rowExpandable,u=o.onRow,f=o.expandRowByClick,v=o.rowClassName,g=d==="nest",b=d==="row"&&(!m||m(e)),p=b||g,y=a&&a.has(t),C=c&&e&&e[c],x=$t(s),w=u==null?void 0:u(e,r),S=w==null?void 0:w.onClick,E=function(K){f&&p&&s(e,K);for(var z=arguments.length,R=new Array(z>1?z-1:0),k=1;k<z;k++)R[k-1]=arguments[k];S==null||S.apply(void 0,[K].concat(R))},I;typeof v=="string"?I=v:typeof v=="function"&&(I=v(e,r,n));var P=xn(i);return W(W({},o),{},{columnsKey:P,nestExpandable:g,expanded:y,hasNestChildren:C,record:e,onTriggerExpand:x,rowSupportExpand:b,expandable:p,rowProps:W(W({},w),{},{className:Y(I,w==null?void 0:w.className),onClick:E})})}function Xo(e){var t=e.prefixCls,r=e.children,n=e.component,o=e.cellComponent,i=e.className,d=e.expanded,a=e.colSpan,c=e.isEmpty,s=tt(lt,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),m=s.scrollbarSize,u=s.fixHeader,f=s.fixColumn,v=s.componentWidth,g=s.horizonScroll,b=r;return(c?g&&v:f)&&(b=l.createElement("div",{style:{width:v-(u&&!c?m:0),position:"sticky",left:0,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},b)),l.createElement(n,{className:i,style:{display:d?null:"none"}},l.createElement(Wt,{component:o,prefixCls:t,colSpan:a},b))}function Bl(e){var t=e.prefixCls,r=e.record,n=e.onExpand,o=e.expanded,i=e.expandable,d="".concat(t,"-row-expand-icon");if(!i)return l.createElement("span",{className:Y(d,"".concat(t,"-row-spaced"))});var a=function(s){n(r,s),s.stopPropagation()};return l.createElement("span",{className:Y(d,T(T({},"".concat(t,"-row-expanded"),o),"".concat(t,"-row-collapsed"),!o)),onClick:a})}function Ll(e,t,r){var n=[];function o(i){(i||[]).forEach(function(d,a){n.push(t(d,a)),o(d[r])})}return o(e),n}function Uo(e,t,r,n){return typeof e=="string"?e:typeof e=="function"?e(t,r,n):""}function Go(e,t,r,n,o){var i=e.record,d=e.prefixCls,a=e.columnsKey,c=e.fixedInfoList,s=e.expandIconColumnIndex,m=e.nestExpandable,u=e.indentSize,f=e.expandIcon,v=e.expanded,g=e.hasNestChildren,b=e.onTriggerExpand,p=a[r],y=c[r],C;r===(s||0)&&m&&(C=l.createElement(l.Fragment,null,l.createElement("span",{style:{paddingLeft:"".concat(u*n,"px")},className:"".concat(d,"-row-indent indent-level-").concat(n)}),f({prefixCls:d,expanded:v,expandable:g,record:i,onExpand:b})));var x;return t.onCell&&(x=t.onCell(i,o)),{key:p,fixedInfo:y,appendCellNode:C,additionalCellProps:x||{}}}function _l(e){var t=e.className,r=e.style,n=e.record,o=e.index,i=e.renderIndex,d=e.rowKey,a=e.indent,c=a===void 0?0:a,s=e.rowComponent,m=e.cellComponent,u=e.scopeCellComponent,f=qo(n,d,o,c),v=f.prefixCls,g=f.flattenColumns,b=f.expandedRowClassName,p=f.expandedRowRender,y=f.rowProps,C=f.expanded,x=f.rowSupportExpand,w=l.useRef(!1);w.current||(w.current=C);var S=Uo(b,n,o,c),E=l.createElement(s,de({},y,{"data-row-key":d,className:Y(t,"".concat(v,"-row"),"".concat(v,"-row-level-").concat(c),y==null?void 0:y.className,T({},S,c>=1)),style:W(W({},r),y==null?void 0:y.style)}),g.map(function(h,K){var z=h.render,R=h.dataIndex,k=h.className,N=Go(f,h,K,c,o),$=N.key,O=N.fixedInfo,D=N.appendCellNode,_=N.additionalCellProps;return l.createElement(Wt,de({className:k,ellipsis:h.ellipsis,align:h.align,scope:h.rowScope,component:h.rowScope?u:m,prefixCls:v,key:$,record:n,index:o,renderIndex:i,dataIndex:R,render:z,shouldCellUpdate:h.shouldCellUpdate},O,{appendNode:D,additionalProps:_}))})),I;if(x&&(w.current||C)){var P=p(n,o,c+1,C);I=l.createElement(Xo,{expanded:C,className:Y("".concat(v,"-expanded-row"),"".concat(v,"-expanded-row-level-").concat(c+1),S),prefixCls:v,component:s,cellComponent:m,colSpan:g.length,isEmpty:!1},P)}return l.createElement(l.Fragment,null,E,I)}const Hl=Ft(_l);function jl(e){var t=e.columnKey,r=e.onColumnResize,n=l.useRef();return l.useEffect(function(){n.current&&r(t,n.current.offsetWidth)},[]),l.createElement(nr,{data:t},l.createElement("td",{ref:n,style:{padding:0,border:0,height:0}},l.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function Al(e){var t=e.prefixCls,r=e.columnsKey,n=e.onColumnResize;return l.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0}},l.createElement(nr.Collection,{onBatchResize:function(i){i.forEach(function(d){var a=d.data,c=d.size;n(a,c.offsetWidth)})}},r.map(function(o){return l.createElement(jl,{key:o,columnKey:o,onColumnResize:n})})))}function Fl(e){var t=e.data,r=e.measureColumnWidth,n=tt(lt,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode"]),o=n.prefixCls,i=n.getComponent,d=n.onColumnResize,a=n.flattenColumns,c=n.getRowKey,s=n.expandedKeys,m=n.childrenColumnName,u=n.emptyNode,f=Vo(t,m,s,c),v=l.useRef({renderWithProps:!1}),g=i(["body","wrapper"],"tbody"),b=i(["body","row"],"tr"),p=i(["body","cell"],"td"),y=i(["body","cell"],"th"),C;t.length?C=f.map(function(w,S){var E=w.record,I=w.indent,P=w.index,h=c(E,S);return l.createElement(Hl,{key:h,rowKey:h,record:E,index:S,renderIndex:P,rowComponent:b,cellComponent:p,scopeCellComponent:y,indent:I})}):C=l.createElement(Xo,{expanded:!0,className:"".concat(o,"-placeholder"),prefixCls:o,component:b,cellComponent:p,colSpan:a.length,isEmpty:!0},u);var x=xn(a);return l.createElement(jo.Provider,{value:v.current},l.createElement(g,{className:"".concat(o,"-tbody")},r&&l.createElement(Al,{prefixCls:o,columnsKey:x,onColumnResize:d}),C))}const Wl=Ft(Fl);var Vl=["expandable"],rn="RC_TABLE_INTERNAL_COL_DEFINE";function ql(e){var t=e.expandable,r=mt(e,Vl),n;return"expandable"in e?n=W(W({},r),t):n=r,n.showExpandColumn===!1&&(n.expandIconColumnIndex=-1),n}var Xl=["columnType"];function Yo(e){for(var t=e.colWidths,r=e.columns,n=e.columCount,o=tt(lt,["tableLayout"]),i=o.tableLayout,d=[],a=n||r.length,c=!1,s=a-1;s>=0;s-=1){var m=t[s],u=r&&r[s],f=void 0,v=void 0;if(u&&(f=u[rn],i==="auto"&&(v=u.minWidth)),m||v||f||c){var g=f||{};g.columnType;var b=mt(g,Xl);d.unshift(l.createElement("col",de({key:s,style:{width:m,minWidth:v}},b))),c=!0}}return l.createElement("colgroup",null,d)}var Ul=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];function Gl(e,t){return l.useMemo(function(){for(var r=[],n=0;n<t;n+=1){var o=e[n];if(o!==void 0)r[n]=o;else return null}return r},[e.join("_"),t])}var Yl=l.forwardRef(function(e,t){var r=e.className,n=e.noData,o=e.columns,i=e.flattenColumns,d=e.colWidths,a=e.columCount,c=e.stickyOffsets,s=e.direction,m=e.fixHeader,u=e.stickyTopOffset,f=e.stickyBottomOffset,v=e.stickyClassName,g=e.onScroll,b=e.maxContentScroll,p=e.children,y=mt(e,Ul),C=tt(lt,["prefixCls","scrollbarSize","isSticky","getComponent"]),x=C.prefixCls,w=C.scrollbarSize,S=C.isSticky,E=C.getComponent,I=E(["header","table"],"table"),P=S&&!m?0:w,h=l.useRef(null),K=l.useCallback(function(_){$r(t,_),$r(h,_)},[]);l.useEffect(function(){var _;function M(G){var ee=G,ye=ee.currentTarget,oe=ee.deltaX;oe&&(g({currentTarget:ye,scrollLeft:ye.scrollLeft+oe}),G.preventDefault())}return(_=h.current)===null||_===void 0||_.addEventListener("wheel",M,{passive:!1}),function(){var G;(G=h.current)===null||G===void 0||G.removeEventListener("wheel",M)}},[]);var z=l.useMemo(function(){return i.every(function(_){return _.width})},[i]),R=i[i.length-1],k={fixed:R?R.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(x,"-cell-scrollbar")}}},N=l.useMemo(function(){return P?[].concat($e(o),[k]):o},[P,o]),$=l.useMemo(function(){return P?[].concat($e(i),[k]):i},[P,i]),O=l.useMemo(function(){var _=c.right,M=c.left;return W(W({},c),{},{left:s==="rtl"?[].concat($e(M.map(function(G){return G+P})),[0]):M,right:s==="rtl"?_:[].concat($e(_.map(function(G){return G+P})),[0]),isSticky:S})},[P,c,S]),D=Gl(d,a);return l.createElement("div",{style:W({overflow:"hidden"},S?{top:u,bottom:f}:{}),ref:K,className:Y(r,T({},v,!!v))},l.createElement(I,{style:{tableLayout:"fixed",visibility:n||D?null:"hidden"}},(!n||!b||z)&&l.createElement(Yo,{colWidths:D?[].concat($e(D),[P]):[],columCount:a+1,columns:$}),p(W(W({},y),{},{stickyOffsets:O,columns:N,flattenColumns:$}))))});const Ar=l.memo(Yl);var Jl=function(t){var r=t.cells,n=t.stickyOffsets,o=t.flattenColumns,i=t.rowComponent,d=t.cellComponent,a=t.onHeaderRow,c=t.index,s=tt(lt,["prefixCls","direction"]),m=s.prefixCls,u=s.direction,f;a&&(f=a(r.map(function(g){return g.column}),c));var v=xn(r.map(function(g){return g.column}));return l.createElement(i,f,r.map(function(g,b){var p=g.column,y=ir(g.colStart,g.colEnd,o,n,u),C;return p&&p.onHeaderCell&&(C=g.column.onHeaderCell(p)),l.createElement(Wt,de({},g,{scope:p.title?g.colSpan>1?"colgroup":"col":null,ellipsis:p.ellipsis,align:p.align,component:d,prefixCls:m,key:v[b]},y,{additionalProps:C,rowType:"header"}))}))};function Ql(e){var t=[];function r(d,a){var c=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;t[c]=t[c]||[];var s=a,m=d.filter(Boolean).map(function(u){var f={key:u.key,className:u.className||"",children:u.title,column:u,colStart:s},v=1,g=u.children;return g&&g.length>0&&(v=r(g,s,c+1).reduce(function(b,p){return b+p},0),f.hasSubColumns=!0),"colSpan"in u&&(v=u.colSpan),"rowSpan"in u&&(f.rowSpan=u.rowSpan),f.colSpan=v,f.colEnd=f.colStart+v-1,t[c].push(f),s+=v,v});return m}r(e,0);for(var n=t.length,o=function(a){t[a].forEach(function(c){!("rowSpan"in c)&&!c.hasSubColumns&&(c.rowSpan=n-a)})},i=0;i<n;i+=1)o(i);return t}var Zl=function(t){var r=t.stickyOffsets,n=t.columns,o=t.flattenColumns,i=t.onHeaderRow,d=tt(lt,["prefixCls","getComponent"]),a=d.prefixCls,c=d.getComponent,s=l.useMemo(function(){return Ql(n)},[n]),m=c(["header","wrapper"],"thead"),u=c(["header","row"],"tr"),f=c(["header","cell"],"th");return l.createElement(m,{className:"".concat(a,"-thead")},s.map(function(v,g){var b=l.createElement(Jl,{key:g,flattenColumns:o,cells:v,stickyOffsets:r,rowComponent:u,cellComponent:f,onHeaderRow:i,index:g});return b}))};const Fr=Ft(Zl);function Wr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return typeof t=="number"?t:t.endsWith("%")?e*parseFloat(t)/100:null}function es(e,t,r){return l.useMemo(function(){if(t&&t>0){var n=0,o=0;e.forEach(function(f){var v=Wr(t,f.width);v?n+=v:o+=1});var i=Math.max(t,r),d=Math.max(i-n,o),a=o,c=d/o,s=0,m=e.map(function(f){var v=W({},f),g=Wr(t,v.width);if(g)v.width=g;else{var b=Math.floor(c);v.width=a===1?d:b,d-=b,a-=1}return s+=v.width,v});if(s<i){var u=i/s;d=i,m.forEach(function(f,v){var g=Math.floor(f.width*u);f.width=v===m.length-1?d:g,d-=g})}return[m,Math.max(s,i)]}return[e,t]},[e,t,r])}var ts=["children"],ns=["fixed"];function lr(e){return So(e).filter(function(t){return l.isValidElement(t)}).map(function(t){var r=t.key,n=t.props,o=n.children,i=mt(n,ts),d=W({key:r},i);return o&&(d.children=lr(o)),d})}function Jo(e){return e.filter(function(t){return t&&ft(t)==="object"&&!t.hidden}).map(function(t){var r=t.children;return r&&r.length>0?W(W({},t),{},{children:Jo(r)}):t})}function jn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key";return e.filter(function(r){return r&&ft(r)==="object"}).reduce(function(r,n,o){var i=n.fixed,d=i===!0?"left":i,a="".concat(t,"-").concat(o),c=n.children;return c&&c.length>0?[].concat($e(r),$e(jn(c,a).map(function(s){return W({fixed:d},s)}))):[].concat($e(r),[W(W({key:a},n),{},{fixed:d})])},[])}function rs(e){return e.map(function(t){var r=t.fixed,n=mt(t,ns),o=r;return r==="left"?o="right":r==="right"&&(o="left"),W({fixed:o},n)})}function os(e,t){var r=e.prefixCls,n=e.columns,o=e.children,i=e.expandable,d=e.expandedKeys,a=e.columnTitle,c=e.getRowKey,s=e.onTriggerExpand,m=e.expandIcon,u=e.rowExpandable,f=e.expandIconColumnIndex,v=e.direction,g=e.expandRowByClick,b=e.columnWidth,p=e.fixed,y=e.scrollWidth,C=e.clientWidth,x=l.useMemo(function(){var R=n||lr(o)||[];return Jo(R.slice())},[n,o]),w=l.useMemo(function(){if(i){var R=x.slice();if(!R.includes(bt)){var k=f||0;k>=0&&(k||p==="left"||!p)&&R.splice(k,0,bt),p==="right"&&R.splice(x.length,0,bt)}var N=R.indexOf(bt);R=R.filter(function(_,M){return _!==bt||M===N});var $=x[N],O;p?O=p:O=$?$.fixed:null;var D=T(T(T(T(T(T({},rn,{className:"".concat(r,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",a),"fixed",O),"className","".concat(r,"-row-expand-icon-cell")),"width",b),"render",function(M,G,ee){var ye=c(G,ee),oe=d.has(ye),pe=u?u(G):!0,xe=m({prefixCls:r,expanded:oe,expandable:pe,record:G,onExpand:s});return g?l.createElement("span",{onClick:function(J){return J.stopPropagation()}},xe):xe});return R.map(function(_){return _===bt?D:_})}return x.filter(function(_){return _!==bt})},[i,x,c,d,m,v]),S=l.useMemo(function(){var R=w;return t&&(R=t(R)),R.length||(R=[{render:function(){return null}}]),R},[t,w,v]),E=l.useMemo(function(){return v==="rtl"?rs(jn(S)):jn(S)},[S,v,y]),I=l.useMemo(function(){for(var R=-1,k=E.length-1;k>=0;k-=1){var N=E[k].fixed;if(N==="left"||N===!0){R=k;break}}if(R>=0)for(var $=0;$<=R;$+=1){var O=E[$].fixed;if(O!=="left"&&O!==!0)return!0}var D=E.findIndex(function(G){var ee=G.fixed;return ee==="right"});if(D>=0)for(var _=D;_<E.length;_+=1){var M=E[_].fixed;if(M!=="right")return!0}return!1},[E]),P=es(E,y,C),h=Re(P,2),K=h[0],z=h[1];return[S,K,z,I]}function as(e,t,r){var n=ql(e),o=n.expandIcon,i=n.expandedRowKeys,d=n.defaultExpandedRowKeys,a=n.defaultExpandAllRows,c=n.expandedRowRender,s=n.onExpand,m=n.onExpandedRowsChange,u=n.childrenColumnName,f=o||Bl,v=u||"children",g=l.useMemo(function(){return c?"row":e.expandable&&e.internalHooks===dn&&e.expandable.__PARENT_RENDER_ICON__||t.some(function(S){return S&&ft(S)==="object"&&S[v]})?"nest":!1},[!!c,t]),b=l.useState(function(){return d||(a?Ll(t,r,v):[])}),p=Re(b,2),y=p[0],C=p[1],x=l.useMemo(function(){return new Set(i||y||[])},[i,y]),w=l.useCallback(function(S){var E=r(S,t.indexOf(S)),I,P=x.has(E);P?(x.delete(E),I=$e(x)):I=[].concat($e(x),[E]),C(I),s&&s(!P,S),m&&m(I)},[r,x,t,s,m]);return[n,g,x,f,v,w]}function is(e,t,r){var n=e.map(function(o,i){return ir(i,i,e,t,r)});return xo(function(){return n},[n],function(o,i){return!on(o,i)})}function Qo(e){var t=l.useRef(e),r=l.useState({}),n=Re(r,2),o=n[1],i=l.useRef(null),d=l.useRef([]);function a(c){d.current.push(c);var s=Promise.resolve();i.current=s,s.then(function(){if(i.current===s){var m=d.current,u=t.current;d.current=[],m.forEach(function(f){t.current=f(t.current)}),i.current=null,u!==t.current&&o({})}})}return l.useEffect(function(){return function(){i.current=null}},[]),[t.current,a]}function ls(e){var t=l.useRef(null),r=l.useRef();function n(){window.clearTimeout(r.current)}function o(d){t.current=d,n(),r.current=window.setTimeout(function(){t.current=null,r.current=void 0},100)}function i(){return t.current}return l.useEffect(function(){return n},[]),[o,i]}function ss(){var e=l.useState(-1),t=Re(e,2),r=t[0],n=t[1],o=l.useState(-1),i=Re(o,2),d=i[0],a=i[1],c=l.useCallback(function(s,m){n(s),a(m)},[]);return[r,d,c]}var Vr=yo()?window:null;function cs(e,t){var r=ft(e)==="object"?e:{},n=r.offsetHeader,o=n===void 0?0:n,i=r.offsetSummary,d=i===void 0?0:i,a=r.offsetScroll,c=a===void 0?0:a,s=r.getContainer,m=s===void 0?function(){return Vr}:s,u=m()||Vr,f=!!e;return l.useMemo(function(){return{isSticky:f,stickyClassName:f?"".concat(t,"-sticky-holder"):"",offsetHeader:o,offsetSummary:d,offsetScroll:c,container:u}},[f,c,o,d,t,u])}function ds(e,t,r){var n=l.useMemo(function(){var o=t.length,i=function(s,m,u){for(var f=[],v=0,g=s;g!==m;g+=u)f.push(v),t[g].fixed&&(v+=e[g]||0);return f},d=i(0,o,1),a=i(o-1,-1,-1).reverse();return r==="rtl"?{left:a,right:d}:{left:d,right:a}},[e,t,r]);return n}function qr(e){var t=e.className,r=e.children;return l.createElement("div",{className:t},r)}function Xr(e){var t=Zn(e),r=t.getBoundingClientRect(),n=document.documentElement;return{left:r.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:r.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var us=function(t,r){var n,o,i=t.scrollBodyRef,d=t.onScroll,a=t.offsetScroll,c=t.container,s=t.direction,m=tt(lt,"prefixCls"),u=((n=i.current)===null||n===void 0?void 0:n.scrollWidth)||0,f=((o=i.current)===null||o===void 0?void 0:o.clientWidth)||0,v=u&&f*(f/u),g=l.useRef(),b=Qo({scrollLeft:0,isHiddenScrollBar:!0}),p=Re(b,2),y=p[0],C=p[1],x=l.useRef({delta:0,x:0}),w=l.useState(!1),S=Re(w,2),E=S[0],I=S[1],P=l.useRef(null);l.useEffect(function(){return function(){tn.cancel(P.current)}},[]);var h=function(){I(!1)},K=function($){$.persist(),x.current.delta=$.pageX-y.scrollLeft,x.current.x=0,I(!0),$.preventDefault()},z=function($){var O,D=$||((O=window)===null||O===void 0?void 0:O.event),_=D.buttons;if(!E||_===0){E&&I(!1);return}var M=x.current.x+$.pageX-x.current.x-x.current.delta,G=s==="rtl";M=Math.max(G?v-f:0,Math.min(G?0:f-v,M));var ee=!G||Math.abs(M)+Math.abs(v)<f;ee&&(d({scrollLeft:M/f*(u+2)}),x.current.x=$.pageX)},R=function(){tn.cancel(P.current),P.current=tn(function(){if(i.current){var $=Xr(i.current).top,O=$+i.current.offsetHeight,D=c===window?document.documentElement.scrollTop+window.innerHeight:Xr(c).top+c.clientHeight;O-Rr()<=D||$>=D-a?C(function(_){return W(W({},_),{},{isHiddenScrollBar:!0})}):C(function(_){return W(W({},_),{},{isHiddenScrollBar:!1})})}})},k=function($){C(function(O){return W(W({},O),{},{scrollLeft:$/u*f||0})})};return l.useImperativeHandle(r,function(){return{setScrollLeft:k,checkScrollBarVisible:R}}),l.useEffect(function(){var N=Tr(document.body,"mouseup",h,!1),$=Tr(document.body,"mousemove",z,!1);return R(),function(){N.remove(),$.remove()}},[v,E]),l.useEffect(function(){if(i.current){for(var N=[],$=Zn(i.current);$;)N.push($),$=$.parentElement;return N.forEach(function(O){return O.addEventListener("scroll",R,!1)}),window.addEventListener("resize",R,!1),window.addEventListener("scroll",R,!1),c.addEventListener("scroll",R,!1),function(){N.forEach(function(O){return O.removeEventListener("scroll",R)}),window.removeEventListener("resize",R),window.removeEventListener("scroll",R),c.removeEventListener("scroll",R)}}},[c]),l.useEffect(function(){y.isHiddenScrollBar||C(function(N){var $=i.current;return $?W(W({},N),{},{scrollLeft:$.scrollLeft/$.scrollWidth*$.clientWidth}):N})},[y.isHiddenScrollBar]),u<=f||!v||y.isHiddenScrollBar?null:l.createElement("div",{style:{height:Rr(),width:f,bottom:a},className:"".concat(m,"-sticky-scroll")},l.createElement("div",{onMouseDown:K,ref:g,className:Y("".concat(m,"-sticky-scroll-bar"),T({},"".concat(m,"-sticky-scroll-bar-active"),E)),style:{width:"".concat(v,"px"),transform:"translate3d(".concat(y.scrollLeft,"px, 0, 0)")}}))};const fs=l.forwardRef(us);var Zo="rc-table",ms=[],vs={};function ps(){return"No Data"}function gs(e,t){var r=W({rowKey:"key",prefixCls:Zo,emptyText:ps},e),n=r.prefixCls,o=r.className,i=r.rowClassName,d=r.style,a=r.data,c=r.rowKey,s=r.scroll,m=r.tableLayout,u=r.direction,f=r.title,v=r.footer,g=r.summary,b=r.caption,p=r.id,y=r.showHeader,C=r.components,x=r.emptyText,w=r.onRow,S=r.onHeaderRow,E=r.onScroll,I=r.internalHooks,P=r.transformColumns,h=r.internalRefs,K=r.tailor,z=r.getContainerWidth,R=r.sticky,k=r.rowHoverable,N=k===void 0?!0:k,$=a||ms,O=!!$.length,D=I===dn,_=l.useCallback(function(he,Ee){return Qn(C,he)||Ee},[C]),M=l.useMemo(function(){return typeof c=="function"?c:function(he){var Ee=he&&he[c];return Ee}},[c]),G=_(["body"]),ee=ss(),ye=Re(ee,3),oe=ye[0],pe=ye[1],xe=ye[2],ue=as(r,$,M),J=Re(ue,6),te=J[0],Ce=J[1],le=J[2],U=J[3],j=J[4],H=J[5],q=s==null?void 0:s.x,Z=l.useState(0),F=Re(Z,2),ne=F[0],Ie=F[1],We=os(W(W(W({},r),te),{},{expandable:!!te.expandedRowRender,columnTitle:te.columnTitle,expandedKeys:le,getRowKey:M,onTriggerExpand:H,expandIcon:U,expandIconColumnIndex:te.expandIconColumnIndex,direction:u,scrollWidth:D&&K&&typeof q=="number"?q:null,clientWidth:ne}),D?P:null),ke=Re(We,4),B=ke[0],V=ke[1],ae=ke[2],fe=ke[3],me=ae??q,Oe=l.useMemo(function(){return{columns:B,flattenColumns:V}},[B,V]),ze=l.useRef(),je=l.useRef(),ge=l.useRef(),ve=l.useRef();l.useImperativeHandle(t,function(){return{nativeElement:ze.current,scrollTo:function(Ee){var Qe;if(ge.current instanceof HTMLElement){var ct=Ee.index,Ze=Ee.top,_t=Ee.key;if(El(Ze)){var Pt;(Pt=ge.current)===null||Pt===void 0||Pt.scrollTo({top:Ze})}else{var Tt,Qt=_t??M($[ct]);(Tt=ge.current.querySelector('[data-row-key="'.concat(Qt,'"]')))===null||Tt===void 0||Tt.scrollIntoView()}}else(Qe=ge.current)!==null&&Qe!==void 0&&Qe.scrollTo&&ge.current.scrollTo(Ee)}}});var Q=l.useRef(),L=l.useState(!1),Ne=Re(L,2),Te=Ne[0],se=Ne[1],Pe=l.useState(!1),Se=Re(Pe,2),Le=Se[0],Ae=Se[1],Ue=Qo(new Map),vt=Re(Ue,2),De=vt[0],xt=vt[1],Ct=xn(V),Ge=Ct.map(function(he){return De.get(he)}),nt=l.useMemo(function(){return Ge},[Ge.join("_")]),rt=ds(nt,V,u),Ye=s&&Hn(s.y),qe=s&&Hn(me)||!!te.fixed,Je=qe&&V.some(function(he){var Ee=he.fixed;return Ee}),Me=l.useRef(),_e=cs(R,n),ot=_e.isSticky,Xt=_e.offsetHeader,Ut=_e.offsetSummary,ht=_e.offsetScroll,Gt=_e.stickyClassName,ie=_e.container,ce=l.useMemo(function(){return g==null?void 0:g($)},[g,$]),Ke=(Ye||ot)&&l.isValidElement(ce)&&ce.type===Cn&&ce.props.fixed,Be,Xe,at;Ye&&(Xe={overflowY:O?"scroll":"auto",maxHeight:s.y}),qe&&(Be={overflowX:"auto"},Ye||(Xe={overflowY:"hidden"}),at={width:me===!0?"auto":me,minWidth:"100%"});var it=l.useCallback(function(he,Ee){_a(ze.current)&&xt(function(Qe){if(Qe.get(he)!==Ee){var ct=new Map(Qe);return ct.set(he,Ee),ct}return Qe})},[]),Ve=ls(),Bt=Re(Ve,2),Yt=Bt[0],St=Bt[1];function It(he,Ee){Ee&&(typeof Ee=="function"?Ee(he):Ee.scrollLeft!==he&&(Ee.scrollLeft=he,Ee.scrollLeft!==he&&setTimeout(function(){Ee.scrollLeft=he},0)))}var ut=$t(function(he){var Ee=he.currentTarget,Qe=he.scrollLeft,ct=u==="rtl",Ze=typeof Qe=="number"?Qe:Ee.scrollLeft,_t=Ee||vs;if(!St()||St()===_t){var Pt;Yt(_t),It(Ze,je.current),It(Ze,ge.current),It(Ze,Q.current),It(Ze,(Pt=Me.current)===null||Pt===void 0?void 0:Pt.setScrollLeft)}var Tt=Ee||je.current;if(Tt){var Qt=D&&K&&typeof me=="number"?me:Tt.scrollWidth,In=Tt.clientWidth;if(Qt===In){se(!1),Ae(!1);return}ct?(se(-Ze<Qt-In),Ae(-Ze>0)):(se(Ze>0),Ae(Ze<Qt-In))}}),Jt=$t(function(he){ut(he),E==null||E(he)}),kt=function(){if(qe&&ge.current){var Ee;ut({currentTarget:Zn(ge.current),scrollLeft:(Ee=ge.current)===null||Ee===void 0?void 0:Ee.scrollLeft})}else se(!1),Ae(!1)},Ot=function(Ee){var Qe,ct=Ee.width;(Qe=Me.current)===null||Qe===void 0||Qe.checkScrollBarVisible();var Ze=ze.current?ze.current.offsetWidth:ct;D&&z&&ze.current&&(Ze=z(ze.current,Ze)||Ze),Ze!==ne&&(kt(),Ie(Ze))},Lt=l.useRef(!1);l.useEffect(function(){Lt.current&&kt()},[qe,a,B.length]),l.useEffect(function(){Lt.current=!0},[]);var wn=l.useState(0),re=Re(wn,2),we=re[0],He=re[1],pt=l.useState(!0),Kt=Re(pt,2),vr=Kt[0],va=Kt[1];l.useEffect(function(){(!K||!D)&&(ge.current instanceof Element?He(Ir(ge.current).width):He(Ir(ve.current).width)),va(ri("position","sticky"))},[]),l.useEffect(function(){D&&h&&(h.body.current=ge.current)});var pa=l.useCallback(function(he){return l.createElement(l.Fragment,null,l.createElement(Fr,he),Ke==="top"&&l.createElement(fn,he,ce))},[Ke,ce]),ga=l.useCallback(function(he){return l.createElement(fn,he,ce)},[ce]),pr=_(["table"],"table"),un=l.useMemo(function(){return m||(Je?me==="max-content"?"auto":"fixed":Ye||ot||V.some(function(he){var Ee=he.ellipsis;return Ee})?"fixed":"auto")},[Ye,Je,V,m,ot]),En,$n={colWidths:nt,columCount:V.length,stickyOffsets:rt,onHeaderRow:S,fixHeader:Ye,scroll:s},gr=l.useMemo(function(){return O?null:typeof x=="function"?x():x},[O,x]),hr=l.createElement(Wl,{data:$,measureColumnWidth:Ye||qe||ot}),yr=l.createElement(Yo,{colWidths:V.map(function(he){var Ee=he.width;return Ee}),columns:V}),br=b!=null?l.createElement("caption",{className:"".concat(n,"-caption")},b):void 0,ha=an(r,{data:!0}),xr=an(r,{aria:!0});if(Ye||ot){var Nn;typeof G=="function"?(Nn=G($,{scrollbarSize:we,ref:ge,onScroll:ut}),$n.colWidths=V.map(function(he,Ee){var Qe=he.width,ct=Ee===V.length-1?Qe-we:Qe;return typeof ct=="number"&&!Number.isNaN(ct)?ct:0})):Nn=l.createElement("div",{style:W(W({},Be),Xe),onScroll:Jt,ref:ge,className:Y("".concat(n,"-body"))},l.createElement(pr,de({style:W(W({},at),{},{tableLayout:un})},xr),br,yr,hr,!Ke&&ce&&l.createElement(fn,{stickyOffsets:rt,flattenColumns:V},ce)));var Cr=W(W(W({noData:!$.length,maxContentScroll:qe&&me==="max-content"},$n),Oe),{},{direction:u,stickyClassName:Gt,onScroll:ut});En=l.createElement(l.Fragment,null,y!==!1&&l.createElement(Ar,de({},Cr,{stickyTopOffset:Xt,className:"".concat(n,"-header"),ref:je}),pa),Nn,Ke&&Ke!=="top"&&l.createElement(Ar,de({},Cr,{stickyBottomOffset:Ut,className:"".concat(n,"-summary"),ref:Q}),ga),ot&&ge.current&&ge.current instanceof Element&&l.createElement(fs,{ref:Me,offsetScroll:ht,scrollBodyRef:ge,onScroll:ut,container:ie,direction:u}))}else En=l.createElement("div",{style:W(W({},Be),Xe),className:Y("".concat(n,"-content")),onScroll:ut,ref:ge},l.createElement(pr,de({style:W(W({},at),{},{tableLayout:un})},xr),br,yr,y!==!1&&l.createElement(Fr,de({},$n,Oe)),hr,ce&&l.createElement(fn,{stickyOffsets:rt,flattenColumns:V},ce)));var Rn=l.createElement("div",de({className:Y(n,o,T(T(T(T(T(T(T(T(T(T({},"".concat(n,"-rtl"),u==="rtl"),"".concat(n,"-ping-left"),Te),"".concat(n,"-ping-right"),Le),"".concat(n,"-layout-fixed"),m==="fixed"),"".concat(n,"-fixed-header"),Ye),"".concat(n,"-fixed-column"),Je),"".concat(n,"-fixed-column-gapped"),Je&&fe),"".concat(n,"-scroll-horizontal"),qe),"".concat(n,"-has-fix-left"),V[0]&&V[0].fixed),"".concat(n,"-has-fix-right"),V[V.length-1]&&V[V.length-1].fixed==="right")),style:d,id:p,ref:ze},ha),f&&l.createElement(qr,{className:"".concat(n,"-title")},f($)),l.createElement("div",{ref:ve,className:"".concat(n,"-container")},En),v&&l.createElement(qr,{className:"".concat(n,"-footer")},v($)));qe&&(Rn=l.createElement(nr,{onResize:Ot},Rn));var Sr=is(V,rt,u),ya=l.useMemo(function(){return{scrollX:me,prefixCls:n,getComponent:_,scrollbarSize:we,direction:u,fixedInfoList:Sr,isSticky:ot,supportSticky:vr,componentWidth:ne,fixHeader:Ye,fixColumn:Je,horizonScroll:qe,tableLayout:un,rowClassName:i,expandedRowClassName:te.expandedRowClassName,expandIcon:U,expandableType:Ce,expandRowByClick:te.expandRowByClick,expandedRowRender:te.expandedRowRender,onTriggerExpand:H,expandIconColumnIndex:te.expandIconColumnIndex,indentSize:te.indentSize,allColumnsFixedLeft:V.every(function(he){return he.fixed==="left"}),emptyNode:gr,columns:B,flattenColumns:V,onColumnResize:it,hoverStartRow:oe,hoverEndRow:pe,onHover:xe,rowExpandable:te.rowExpandable,onRow:w,getRowKey:M,expandedKeys:le,childrenColumnName:j,rowHoverable:N}},[me,n,_,we,u,Sr,ot,vr,ne,Ye,Je,qe,un,i,te.expandedRowClassName,U,Ce,te.expandRowByClick,te.expandedRowRender,H,te.expandIconColumnIndex,te.indentSize,gr,B,V,it,oe,pe,xe,te.rowExpandable,w,M,le,j,N]);return l.createElement(lt.Provider,{value:ya},Rn)}var hs=l.forwardRef(gs);function ea(e){return Ho(hs,e)}var Vt=ea();Vt.EXPAND_COLUMN=bt;Vt.INTERNAL_HOOKS=dn;Vt.Column=Ml;Vt.ColumnGroup=zl;Vt.Summary=Fo;var sr=or(null),ta=or(null);function ys(e,t,r){var n=t||1;return r[e+n]-(r[e]||0)}function bs(e){var t=e.rowInfo,r=e.column,n=e.colIndex,o=e.indent,i=e.index,d=e.component,a=e.renderIndex,c=e.record,s=e.style,m=e.className,u=e.inverse,f=e.getHeight,v=r.render,g=r.dataIndex,b=r.className,p=r.width,y=tt(ta,["columnsOffset"]),C=y.columnsOffset,x=Go(t,r,n,o,i),w=x.key,S=x.fixedInfo,E=x.appendCellNode,I=x.additionalCellProps,P=I.style,h=I.colSpan,K=h===void 0?1:h,z=I.rowSpan,R=z===void 0?1:z,k=n-1,N=ys(k,K,C),$=K>1?p-N:0,O=W(W(W({},P),s),{},{flex:"0 0 ".concat(N,"px"),width:"".concat(N,"px"),marginRight:$,pointerEvents:"auto"}),D=l.useMemo(function(){return u?R<=1:K===0||R===0||R>1},[R,K,u]);D?O.visibility="hidden":u&&(O.height=f==null?void 0:f(R));var _=D?function(){return null}:v,M={};return(R===0||K===0)&&(M.rowSpan=1,M.colSpan=1),l.createElement(Wt,de({className:Y(b,m),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:d,prefixCls:t.prefixCls,key:w,record:c,index:i,renderIndex:a,dataIndex:g,render:_,shouldCellUpdate:r.shouldCellUpdate},S,{appendNode:E,additionalProps:W(W({},I),{},{style:O},M)}))}var xs=["data","index","className","rowKey","style","extra","getHeight"],Cs=l.forwardRef(function(e,t){var r=e.data,n=e.index,o=e.className,i=e.rowKey,d=e.style,a=e.extra,c=e.getHeight,s=mt(e,xs),m=r.record,u=r.indent,f=r.index,v=tt(lt,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),g=v.scrollX,b=v.flattenColumns,p=v.prefixCls,y=v.fixColumn,C=v.componentWidth,x=tt(sr,["getComponent"]),w=x.getComponent,S=qo(m,i,n,u),E=w(["body","row"],"div"),I=w(["body","cell"],"div"),P=S.rowSupportExpand,h=S.expanded,K=S.rowProps,z=S.expandedRowRender,R=S.expandedRowClassName,k;if(P&&h){var N=z(m,n,u+1,h),$=Uo(R,m,n,u),O={};y&&(O={style:T({},"--virtual-width","".concat(C,"px"))});var D="".concat(p,"-expanded-row-cell");k=l.createElement(E,{className:Y("".concat(p,"-expanded-row"),"".concat(p,"-expanded-row-level-").concat(u+1),$)},l.createElement(Wt,{component:I,prefixCls:p,className:Y(D,T({},"".concat(D,"-fixed"),y)),additionalProps:O},N))}var _=W(W({},d),{},{width:g});a&&(_.position="absolute",_.pointerEvents="none");var M=l.createElement(E,de({},K,s,{"data-row-key":i,ref:P?null:t,className:Y(o,"".concat(p,"-row"),K==null?void 0:K.className,T({},"".concat(p,"-row-extra"),a)),style:W(W({},_),K==null?void 0:K.style)}),b.map(function(G,ee){return l.createElement(bs,{key:ee,component:I,rowInfo:S,column:G,colIndex:ee,indent:u,index:n,renderIndex:f,record:m,inverse:a,getHeight:c})}));return P?l.createElement("div",{ref:t},M,k):M}),Ur=Ft(Cs),Ss=l.forwardRef(function(e,t){var r=e.data,n=e.onScroll,o=tt(lt,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),i=o.flattenColumns,d=o.onColumnResize,a=o.getRowKey,c=o.expandedKeys,s=o.prefixCls,m=o.childrenColumnName,u=o.scrollX,f=o.direction,v=tt(sr),g=v.sticky,b=v.scrollY,p=v.listItemHeight,y=v.getComponent,C=v.onScroll,x=l.useRef(),w=Vo(r,m,c,a),S=l.useMemo(function(){var k=0;return i.map(function(N){var $=N.width,O=N.key;return k+=$,[O,$,k]})},[i]),E=l.useMemo(function(){return S.map(function(k){return k[2]})},[S]);l.useEffect(function(){S.forEach(function(k){var N=Re(k,2),$=N[0],O=N[1];d($,O)})},[S]),l.useImperativeHandle(t,function(){var k,N={scrollTo:function(O){var D;(D=x.current)===null||D===void 0||D.scrollTo(O)},nativeElement:(k=x.current)===null||k===void 0?void 0:k.nativeElement};return Object.defineProperty(N,"scrollLeft",{get:function(){var O;return((O=x.current)===null||O===void 0?void 0:O.getScrollInfo().x)||0},set:function(O){var D;(D=x.current)===null||D===void 0||D.scrollTo({left:O})}}),N});var I=function(N,$){var O,D=(O=w[$])===null||O===void 0?void 0:O.record,_=N.onCell;if(_){var M,G=_(D,$);return(M=G==null?void 0:G.rowSpan)!==null&&M!==void 0?M:1}return 1},P=function(N){var $=N.start,O=N.end,D=N.getSize,_=N.offsetY;if(O<0)return null;for(var M=i.filter(function(U){return I(U,$)===0}),G=$,ee=function(j){if(M=M.filter(function(H){return I(H,j)===0}),!M.length)return G=j,1},ye=$;ye>=0&&!ee(ye);ye-=1);for(var oe=i.filter(function(U){return I(U,O)!==1}),pe=O,xe=function(j){if(oe=oe.filter(function(H){return I(H,j)!==1}),!oe.length)return pe=Math.max(j-1,O),1},ue=O;ue<w.length&&!xe(ue);ue+=1);for(var J=[],te=function(j){var H=w[j];if(!H)return 1;i.some(function(q){return I(q,j)>1})&&J.push(j)},Ce=G;Ce<=pe;Ce+=1)te(Ce);var le=J.map(function(U){var j=w[U],H=a(j.record,U),q=function(ne){var Ie=U+ne-1,We=a(w[Ie].record,Ie),ke=D(H,We);return ke.bottom-ke.top},Z=D(H);return l.createElement(Ur,{key:U,data:j,rowKey:H,index:U,style:{top:-_+Z.top},extra:!0,getHeight:q})});return le},h=l.useMemo(function(){return{columnsOffset:E}},[E]),K="".concat(s,"-tbody"),z=y(["body","wrapper"]),R={};return g&&(R.position="sticky",R.bottom=0,ft(g)==="object"&&g.offsetScroll&&(R.bottom=g.offsetScroll)),l.createElement(ta.Provider,{value:h},l.createElement(Eo,{fullHeight:!1,ref:x,prefixCls:"".concat(K,"-virtual"),styles:{horizontalScrollBar:R},className:K,height:b,itemHeight:p||24,data:w,itemKey:function(N){return a(N.record)},component:z,scrollWidth:u,direction:f,onVirtualScroll:function(N){var $,O=N.x;n({currentTarget:($=x.current)===null||$===void 0?void 0:$.nativeElement,scrollLeft:O})},onScroll:C,extraRender:P},function(k,N,$){var O=a(k.record,N);return l.createElement(Ur,{data:k,rowKey:O,index:N,style:$.style})}))}),ws=Ft(Ss),Es=function(t,r){var n=r.ref,o=r.onScroll;return l.createElement(ws,{ref:n,data:t,onScroll:o})};function $s(e,t){var r=e.data,n=e.columns,o=e.scroll,i=e.sticky,d=e.prefixCls,a=d===void 0?Zo:d,c=e.className,s=e.listItemHeight,m=e.components,u=e.onScroll,f=o||{},v=f.x,g=f.y;typeof v!="number"&&(v=1),typeof g!="number"&&(g=500);var b=$t(function(C,x){return Qn(m,C)||x}),p=$t(u),y=l.useMemo(function(){return{sticky:i,scrollY:g,listItemHeight:s,getComponent:b,onScroll:p}},[i,g,s,b,p]);return l.createElement(sr.Provider,{value:y},l.createElement(Vt,de({},e,{className:Y(c,"".concat(a,"-virtual")),scroll:W(W({},o),{},{x:v}),components:W(W({},m),{},{body:r!=null&&r.length?Es:void 0}),columns:n,internalHooks:dn,tailor:!0,ref:t})))}var Ns=l.forwardRef($s);function na(e){return Ho(Ns,e)}na();const Rs=e=>null,Is=e=>null;var cr=l.createContext(null),ks=l.createContext({}),Os=function(t){for(var r=t.prefixCls,n=t.level,o=t.isStart,i=t.isEnd,d="".concat(r,"-indent-unit"),a=[],c=0;c<n;c+=1)a.push(l.createElement("span",{key:c,className:Y(d,T(T({},"".concat(d,"-start"),o[c]),"".concat(d,"-end"),i[c]))}));return l.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},a)};const Ks=l.memo(Os);var Ps=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],Gr="open",Yr="close",Ts="---",ln=function(t){var r,n,o,i=t.eventKey,d=t.className,a=t.style,c=t.dragOver,s=t.dragOverGapTop,m=t.dragOverGapBottom,u=t.isLeaf,f=t.isStart,v=t.isEnd,g=t.expanded,b=t.selected,p=t.checked,y=t.halfChecked,C=t.loading,x=t.domRef,w=t.active,S=t.data,E=t.onMouseMove,I=t.selectable,P=mt(t,Ps),h=X.useContext(cr),K=X.useContext(ks),z=X.useRef(null),R=X.useState(!1),k=Re(R,2),N=k[0],$=k[1],O=!!(h.disabled||t.disabled||(r=K.nodeDisabled)!==null&&r!==void 0&&r.call(K,S)),D=X.useMemo(function(){return!h.checkable||t.checkable===!1?!1:h.checkable},[h.checkable,t.checkable]),_=function(L){O||h.onNodeSelect(L,Fe(t))},M=function(L){O||!D||t.disableCheckbox||h.onNodeCheck(L,Fe(t),!p)},G=X.useMemo(function(){return typeof I=="boolean"?I:h.selectable},[I,h.selectable]),ee=function(L){h.onNodeClick(L,Fe(t)),G?_(L):M(L)},ye=function(L){h.onNodeDoubleClick(L,Fe(t))},oe=function(L){h.onNodeMouseEnter(L,Fe(t))},pe=function(L){h.onNodeMouseLeave(L,Fe(t))},xe=function(L){h.onNodeContextMenu(L,Fe(t))},ue=X.useMemo(function(){return!!(h.draggable&&(!h.draggable.nodeDraggable||h.draggable.nodeDraggable(S)))},[h.draggable,S]),J=function(L){L.stopPropagation(),$(!0),h.onNodeDragStart(L,t);try{L.dataTransfer.setData("text/plain","")}catch{}},te=function(L){L.preventDefault(),L.stopPropagation(),h.onNodeDragEnter(L,t)},Ce=function(L){L.preventDefault(),L.stopPropagation(),h.onNodeDragOver(L,t)},le=function(L){L.stopPropagation(),h.onNodeDragLeave(L,t)},U=function(L){L.stopPropagation(),$(!1),h.onNodeDragEnd(L,t)},j=function(L){L.preventDefault(),L.stopPropagation(),$(!1),h.onNodeDrop(L,t)},H=function(L){C||h.onNodeExpand(L,Fe(t))},q=X.useMemo(function(){var Q=st(h.keyEntities,i)||{},L=Q.children;return!!(L||[]).length},[h.keyEntities,i]),Z=X.useMemo(function(){return u===!1?!1:u||!h.loadData&&!q||h.loadData&&t.loaded&&!q},[u,h.loadData,q,t.loaded]);X.useEffect(function(){C||typeof h.loadData=="function"&&g&&!Z&&!t.loaded&&h.onNodeLoad(Fe(t))},[C,h.loadData,h.onNodeLoad,g,Z,t]);var F=X.useMemo(function(){var Q;return(Q=h.draggable)!==null&&Q!==void 0&&Q.icon?X.createElement("span",{className:"".concat(h.prefixCls,"-draggable-icon")},h.draggable.icon):null},[h.draggable]),ne=function(L){var Ne=t.switcherIcon||h.switcherIcon;return typeof Ne=="function"?Ne(W(W({},t),{},{isLeaf:L})):Ne},Ie=function(){if(Z){var L=ne(!0);return L!==!1?X.createElement("span",{className:Y("".concat(h.prefixCls,"-switcher"),"".concat(h.prefixCls,"-switcher-noop"))},L):null}var Ne=ne(!1);return Ne!==!1?X.createElement("span",{onClick:H,className:Y("".concat(h.prefixCls,"-switcher"),"".concat(h.prefixCls,"-switcher_").concat(g?Gr:Yr))},Ne):null},We=X.useMemo(function(){if(!D)return null;var Q=typeof D!="boolean"?D:null;return X.createElement("span",{className:Y("".concat(h.prefixCls,"-checkbox"),T(T(T({},"".concat(h.prefixCls,"-checkbox-checked"),p),"".concat(h.prefixCls,"-checkbox-indeterminate"),!p&&y),"".concat(h.prefixCls,"-checkbox-disabled"),O||t.disableCheckbox)),onClick:M,role:"checkbox","aria-checked":y?"mixed":p,"aria-disabled":O||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},Q)},[D,p,y,O,t.disableCheckbox,t.title]),ke=X.useMemo(function(){return Z?null:g?Gr:Yr},[Z,g]),B=X.useMemo(function(){return X.createElement("span",{className:Y("".concat(h.prefixCls,"-iconEle"),"".concat(h.prefixCls,"-icon__").concat(ke||"docu"),T({},"".concat(h.prefixCls,"-icon_loading"),C))})},[h.prefixCls,ke,C]),V=X.useMemo(function(){var Q=!!h.draggable,L=!t.disabled&&Q&&h.dragOverNodeKey===i;return L?h.dropIndicatorRender({dropPosition:h.dropPosition,dropLevelOffset:h.dropLevelOffset,indent:h.indent,prefixCls:h.prefixCls,direction:h.direction}):null},[h.dropPosition,h.dropLevelOffset,h.indent,h.prefixCls,h.direction,h.draggable,h.dragOverNodeKey,h.dropIndicatorRender]),ae=X.useMemo(function(){var Q=t.title,L=Q===void 0?Ts:Q,Ne="".concat(h.prefixCls,"-node-content-wrapper"),Te;if(h.showIcon){var se=t.icon||h.icon;Te=se?X.createElement("span",{className:Y("".concat(h.prefixCls,"-iconEle"),"".concat(h.prefixCls,"-icon__customize"))},typeof se=="function"?se(t):se):B}else h.loadData&&C&&(Te=B);var Pe;return typeof L=="function"?Pe=L(S):h.titleRender?Pe=h.titleRender(S):Pe=L,X.createElement("span",{ref:z,title:typeof L=="string"?L:"",className:Y(Ne,"".concat(Ne,"-").concat(ke||"normal"),T({},"".concat(h.prefixCls,"-node-selected"),!O&&(b||N))),onMouseEnter:oe,onMouseLeave:pe,onContextMenu:xe,onClick:ee,onDoubleClick:ye},Te,X.createElement("span",{className:"".concat(h.prefixCls,"-title")},Pe),V)},[h.prefixCls,h.showIcon,t,h.icon,B,h.titleRender,S,ke,oe,pe,xe,ee,ye]),fe=an(P,{aria:!0,data:!0}),me=st(h.keyEntities,i)||{},Oe=me.level,ze=v[v.length-1],je=!O&&ue,ge=h.draggingNodeKey===i,ve=I!==void 0?{"aria-selected":!!I}:void 0;return X.createElement("div",de({ref:x,role:"treeitem","aria-expanded":u?void 0:g,className:Y(d,"".concat(h.prefixCls,"-treenode"),(o={},T(T(T(T(T(T(T(T(T(T(o,"".concat(h.prefixCls,"-treenode-disabled"),O),"".concat(h.prefixCls,"-treenode-switcher-").concat(g?"open":"close"),!u),"".concat(h.prefixCls,"-treenode-checkbox-checked"),p),"".concat(h.prefixCls,"-treenode-checkbox-indeterminate"),y),"".concat(h.prefixCls,"-treenode-selected"),b),"".concat(h.prefixCls,"-treenode-loading"),C),"".concat(h.prefixCls,"-treenode-active"),w),"".concat(h.prefixCls,"-treenode-leaf-last"),ze),"".concat(h.prefixCls,"-treenode-draggable"),ue),"dragging",ge),T(T(T(T(T(T(T(o,"drop-target",h.dropTargetKey===i),"drop-container",h.dropContainerKey===i),"drag-over",!O&&c),"drag-over-gap-top",!O&&s),"drag-over-gap-bottom",!O&&m),"filter-node",(n=h.filterTreeNode)===null||n===void 0?void 0:n.call(h,Fe(t))),"".concat(h.prefixCls,"-treenode-leaf"),Z))),style:a,draggable:je,onDragStart:je?J:void 0,onDragEnter:ue?te:void 0,onDragOver:ue?Ce:void 0,onDragLeave:ue?le:void 0,onDrop:ue?j:void 0,onDragEnd:ue?U:void 0,onMouseMove:E},ve,fe),X.createElement(Ks,{prefixCls:h.prefixCls,level:Oe,isStart:f,isEnd:v}),F,Ie(),We,ae)};ln.isTreeNode=1;function gt(e,t){if(!e)return[];var r=e.slice(),n=r.indexOf(t);return n>=0&&r.splice(n,1),r}function yt(e,t){var r=(e||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function dr(e){return e.split("-")}function Ds(e,t){var r=[],n=st(t,e);function o(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];i.forEach(function(d){var a=d.key,c=d.children;r.push(a),o(c)})}return o(n.children),r}function Ms(e){if(e.parent){var t=dr(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function zs(e){var t=dr(e.pos);return Number(t[t.length-1])===0}function Jr(e,t,r,n,o,i,d,a,c,s){var m,u=e.clientX,f=e.clientY,v=e.target.getBoundingClientRect(),g=v.top,b=v.height,p=(s==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-u),y=(p-12)/n,C=c.filter(function(O){var D;return(D=a[O])===null||D===void 0||(D=D.children)===null||D===void 0?void 0:D.length}),x=st(a,r.eventKey);if(f<g+b/2){var w=d.findIndex(function(O){return O.key===x.key}),S=w<=0?0:w-1,E=d[S].key;x=st(a,E)}var I=x.key,P=x,h=x.key,K=0,z=0;if(!C.includes(I))for(var R=0;R<y&&Ms(x);R+=1)x=x.parent,z+=1;var k=t.data,N=x.node,$=!0;return zs(x)&&x.level===0&&f<g+b/2&&i({dragNode:k,dropNode:N,dropPosition:-1})&&x.key===r.eventKey?K=-1:(P.children||[]).length&&C.includes(h)?i({dragNode:k,dropNode:N,dropPosition:0})?K=0:$=!1:z===0?y>-1.5?i({dragNode:k,dropNode:N,dropPosition:1})?K=1:$=!1:i({dragNode:k,dropNode:N,dropPosition:0})?K=0:i({dragNode:k,dropNode:N,dropPosition:1})?K=1:$=!1:i({dragNode:k,dropNode:N,dropPosition:1})?K=1:$=!1,{dropPosition:K,dropLevelOffset:z,dropTargetKey:x.key,dropTargetPos:x.pos,dragOverNodeKey:h,dropContainerKey:K===0?null:((m=x.parent)===null||m===void 0?void 0:m.key)||null,dropAllowed:$}}function Qr(e,t){if(e){var r=t.multiple;return r?e.slice():e.length?[e[0]]:e}}function Kn(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(ft(e)==="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return Et(!1,"`checkedKeys` is not an array or an object"),null;return t}function An(e,t){var r=new Set;function n(o){if(!r.has(o)){var i=st(t,o);if(i){r.add(o);var d=i.parent,a=i.node;a.disabled||d&&n(d.key)}}}return(e||[]).forEach(function(o){n(o)}),$e(r)}function Bs(e){const[t,r]=l.useState(null);return[l.useCallback((i,d,a)=>{const c=t??i,s=Math.min(c||0,i),m=Math.max(c||0,i),u=d.slice(s,m+1).map(g=>e(g)),f=u.some(g=>!a.has(g)),v=[];return u.forEach(g=>{f?(a.has(g)||v.push(g),a.add(g)):(a.delete(g),v.push(g))}),r(f?m:null),v},[t]),i=>{r(i)}]}const wt={},Fn="SELECT_ALL",Wn="SELECT_INVERT",Vn="SELECT_NONE",Zr=[],ra=(e,t)=>{let r=[];return(t||[]).forEach(n=>{r.push(n),n&&typeof n=="object"&&e in n&&(r=[].concat($e(r),$e(ra(e,n[e]))))}),r},Ls=(e,t)=>{const{preserveSelectedRowKeys:r,selectedRowKeys:n,defaultSelectedRowKeys:o,getCheckboxProps:i,onChange:d,onSelect:a,onSelectAll:c,onSelectInvert:s,onSelectNone:m,onSelectMultiple:u,columnWidth:f,type:v,selections:g,fixed:b,renderCell:p,hideSelectAll:y,checkStrictly:C=!0}=t||{},{prefixCls:x,data:w,pageData:S,getRecordByKey:E,getRowKey:I,expandType:P,childrenColumnName:h,locale:K,getPopupContainer:z}=e,R=er(),[k,N]=Bs(U=>U),[$,O]=Bn(n||o||Zr,{value:n}),D=l.useRef(new Map),_=l.useCallback(U=>{if(r){const j=new Map;U.forEach(H=>{let q=E(H);!q&&D.current.has(H)&&(q=D.current.get(H)),j.set(H,q)}),D.current=j}},[E,r]);l.useEffect(()=>{_($)},[$]);const M=l.useMemo(()=>ra(h,S),[h,S]),{keyEntities:G}=l.useMemo(()=>{if(C)return{keyEntities:null};let U=w;if(r){const j=new Set(M.map((q,Z)=>I(q,Z))),H=Array.from(D.current).reduce((q,Z)=>{let[F,ne]=Z;return j.has(F)?q:q.concat(ne)},[]);U=[].concat($e(U),$e(H))}return rr(U,{externalGetKey:I,childrenPropName:h})},[w,I,C,h,r,M]),ee=l.useMemo(()=>{const U=new Map;return M.forEach((j,H)=>{const q=I(j,H),Z=(i?i(j):null)||{};U.set(q,Z)}),U},[M,I,i]),ye=l.useCallback(U=>{const j=I(U);let H;return ee.has(j)?H=ee.get(I(U)):H=i?i(U):void 0,!!(H!=null&&H.disabled)},[ee,I]),[oe,pe]=l.useMemo(()=>{if(C)return[$||[],[]];const{checkedKeys:U,halfCheckedKeys:j}=Ht($,!0,G,ye);return[U||[],j]},[$,C,G,ye]),xe=l.useMemo(()=>{const U=v==="radio"?oe.slice(0,1):oe;return new Set(U)},[oe,v]),ue=l.useMemo(()=>v==="radio"?new Set:new Set(pe),[pe,v]);l.useEffect(()=>{t||O(Zr)},[!!t]);const J=l.useCallback((U,j)=>{let H,q;_(U),r?(H=U,q=U.map(Z=>D.current.get(Z))):(H=[],q=[],U.forEach(Z=>{const F=E(Z);F!==void 0&&(H.push(Z),q.push(F))})),O(H),d==null||d(H,q,{type:j})},[O,E,d,r]),te=l.useCallback((U,j,H,q)=>{if(a){const Z=H.map(F=>E(F));a(E(U),j,Z,q)}J(H,"single")},[a,E,J]),Ce=l.useMemo(()=>!g||y?null:(g===!0?[Fn,Wn,Vn]:g).map(j=>j===Fn?{key:"all",text:K.selectionAll,onSelect(){J(w.map((H,q)=>I(H,q)).filter(H=>{const q=ee.get(H);return!(q!=null&&q.disabled)||xe.has(H)}),"all")}}:j===Wn?{key:"invert",text:K.selectInvert,onSelect(){const H=new Set(xe);S.forEach((Z,F)=>{const ne=I(Z,F),Ie=ee.get(ne);Ie!=null&&Ie.disabled||(H.has(ne)?H.delete(ne):H.add(ne))});const q=Array.from(H);s&&(R.deprecated(!1,"onSelectInvert","onChange"),s(q)),J(q,"invert")}}:j===Vn?{key:"none",text:K.selectNone,onSelect(){m==null||m(),J(Array.from(xe).filter(H=>{const q=ee.get(H);return q==null?void 0:q.disabled}),"none")}}:j).map(j=>Object.assign(Object.assign({},j),{onSelect:function(){for(var H,q,Z=arguments.length,F=new Array(Z),ne=0;ne<Z;ne++)F[ne]=arguments[ne];(q=j.onSelect)===null||q===void 0||(H=q).call.apply(H,[j].concat(F)),N(null)}})),[g,xe,S,I,s,J]);return[l.useCallback(U=>{var j;if(!t)return U.filter(ve=>ve!==wt);let H=$e(U);const q=new Set(xe),Z=M.map(I).filter(ve=>!ee.get(ve).disabled),F=Z.every(ve=>q.has(ve)),ne=Z.some(ve=>q.has(ve)),Ie=()=>{const ve=[];F?Z.forEach(L=>{q.delete(L),ve.push(L)}):Z.forEach(L=>{q.has(L)||(q.add(L),ve.push(L))});const Q=Array.from(q);c==null||c(!F,Q.map(L=>E(L)),ve.map(L=>E(L))),J(Q,"all"),N(null)};let We,ke;if(v!=="radio"){let ve;if(Ce){const se={getPopupContainer:z,items:Ce.map((Pe,Se)=>{const{key:Le,text:Ae,onSelect:Ue}=Pe;return{key:Le??Se,onClick:()=>{Ue==null||Ue(Z)},label:Ae}})};ve=l.createElement("div",{className:`${x}-selection-extra`},l.createElement($o,{menu:se,getPopupContainer:z},l.createElement("span",null,l.createElement(Xa,null))))}const Q=M.map((se,Pe)=>{const Se=I(se,Pe),Le=ee.get(Se)||{};return Object.assign({checked:q.has(Se)},Le)}).filter(se=>{let{disabled:Pe}=se;return Pe}),L=!!Q.length&&Q.length===M.length,Ne=L&&Q.every(se=>{let{checked:Pe}=se;return Pe}),Te=L&&Q.some(se=>{let{checked:Pe}=se;return Pe});ke=l.createElement(vn,{checked:L?Ne:!!M.length&&F,indeterminate:L?!Ne&&Te:!F&&ne,onChange:Ie,disabled:M.length===0||L,"aria-label":ve?"Custom selection":"Select all",skipGroup:!0}),We=!y&&l.createElement("div",{className:`${x}-selection`},ke,ve)}let B;v==="radio"?B=(ve,Q,L)=>{const Ne=I(Q,L),Te=q.has(Ne),se=ee.get(Ne);return{node:l.createElement(No,Object.assign({},se,{checked:Te,onClick:Pe=>{var Se;Pe.stopPropagation(),(Se=se==null?void 0:se.onClick)===null||Se===void 0||Se.call(se,Pe)},onChange:Pe=>{var Se;q.has(Ne)||te(Ne,!0,[Ne],Pe.nativeEvent),(Se=se==null?void 0:se.onChange)===null||Se===void 0||Se.call(se,Pe)}})),checked:Te}}:B=(ve,Q,L)=>{var Ne;const Te=I(Q,L),se=q.has(Te),Pe=ue.has(Te),Se=ee.get(Te);let Le;return P==="nest"?Le=Pe:Le=(Ne=Se==null?void 0:Se.indeterminate)!==null&&Ne!==void 0?Ne:Pe,{node:l.createElement(vn,Object.assign({},Se,{indeterminate:Le,checked:se,skipGroup:!0,onClick:Ae=>{var Ue;Ae.stopPropagation(),(Ue=Se==null?void 0:Se.onClick)===null||Ue===void 0||Ue.call(Se,Ae)},onChange:Ae=>{var Ue;const{nativeEvent:vt}=Ae,{shiftKey:De}=vt,xt=Z.findIndex(Ge=>Ge===Te),Ct=oe.some(Ge=>Z.includes(Ge));if(De&&C&&Ct){const Ge=k(xt,Z,q),nt=Array.from(q);u==null||u(!se,nt.map(rt=>E(rt)),Ge.map(rt=>E(rt))),J(nt,"multiple")}else{const Ge=oe;if(C){const nt=se?gt(Ge,Te):yt(Ge,Te);te(Te,!se,nt,vt)}else{const nt=Ht([].concat($e(Ge),[Te]),!0,G,ye),{checkedKeys:rt,halfCheckedKeys:Ye}=nt;let qe=rt;if(se){const Je=new Set(rt);Je.delete(Te),qe=Ht(Array.from(Je),{halfCheckedKeys:Ye},G,ye).checkedKeys}te(Te,!se,qe,vt)}}N(se?null:xt),(Ue=Se==null?void 0:Se.onChange)===null||Ue===void 0||Ue.call(Se,Ae)}})),checked:se}};const V=(ve,Q,L)=>{const{node:Ne,checked:Te}=B(ve,Q,L);return p?p(Te,Q,L,Ne):Ne};if(!H.includes(wt))if(H.findIndex(ve=>{var Q;return((Q=ve[rn])===null||Q===void 0?void 0:Q.columnType)==="EXPAND_COLUMN"})===0){const[ve,...Q]=H;H=[ve,wt].concat($e(Q))}else H=[wt].concat($e(H));const ae=H.indexOf(wt);H=H.filter((ve,Q)=>ve!==wt||Q===ae);const fe=H[ae-1],me=H[ae+1];let Oe=b;Oe===void 0&&((me==null?void 0:me.fixed)!==void 0?Oe=me.fixed:(fe==null?void 0:fe.fixed)!==void 0&&(Oe=fe.fixed)),Oe&&fe&&((j=fe[rn])===null||j===void 0?void 0:j.columnType)==="EXPAND_COLUMN"&&fe.fixed===void 0&&(fe.fixed=Oe);const ze=Y(`${x}-selection-col`,{[`${x}-selection-col-with-dropdown`]:g&&v==="checkbox"}),je=()=>t!=null&&t.columnTitle?typeof t.columnTitle=="function"?t.columnTitle(ke):t.columnTitle:We,ge={fixed:Oe,width:f,className:`${x}-selection-column`,title:je(),render:V,onCell:t.onCell,[rn]:{className:ze}};return H.map(ve=>ve===wt?ge:ve)},[I,M,t,oe,xe,ue,f,Ce,P,ee,u,te,ye]),xe]};function _s(e,t){return e._antProxy=e._antProxy||{},Object.keys(t).forEach(r=>{if(!(r in e._antProxy)){const n=e[r];e._antProxy[r]=n,e[r]=t[r]}}),e}function Hs(e,t){return l.useImperativeHandle(e,()=>{const r=t(),{nativeElement:n}=r;return typeof Proxy<"u"?new Proxy(n,{get(o,i){return r[i]?r[i]:Reflect.get(o,i)}}):_s(n,r)})}function js(e){return t=>{const{prefixCls:r,onExpand:n,record:o,expanded:i,expandable:d}=t,a=`${r}-row-expand-icon`;return l.createElement("button",{type:"button",onClick:c=>{n(o,c),c.stopPropagation()},className:Y(a,{[`${a}-spaced`]:!d,[`${a}-expanded`]:d&&i,[`${a}-collapsed`]:d&&!i}),"aria-label":i?e.collapse:e.expand,"aria-expanded":i})}}function As(e){return(r,n)=>{const o=r.querySelector(`.${e}-container`);let i=n;if(o){const d=getComputedStyle(o),a=parseInt(d.borderLeftWidth,10),c=parseInt(d.borderRightWidth,10);i=n-a-c}return i}}const Nt=(e,t)=>"key"in e&&e.key!==void 0&&e.key!==null?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function qt(e,t){return t?`${t}-${e}`:`${e}`}const Sn=(e,t)=>typeof e=="function"?e(t):e,Fs=(e,t)=>{const r=Sn(e,t);return Object.prototype.toString.call(r)==="[object Object]"?"":r};function Ws(e){const t=l.useRef(e),r=Qa();return[()=>t.current,n=>{t.current=n,r()}]}var Vs=function(t){var r=t.dropPosition,n=t.dropLevelOffset,o=t.indent,i={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:i.top=0,i.left=-n*o;break;case 1:i.bottom=0,i.left=-n*o;break;case 0:i.bottom=0,i.left=o;break}return X.createElement("div",{style:i})};function oa(e){if(e==null)throw new TypeError("Cannot destructure "+e)}function qs(e,t){var r=l.useState(!1),n=Re(r,2),o=n[0],i=n[1];Mt(function(){if(o)return e(),function(){t()}},[o]),Mt(function(){return i(!0),function(){i(!1)}},[])}var Xs=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],Us=l.forwardRef(function(e,t){var r=e.className,n=e.style,o=e.motion,i=e.motionNodes,d=e.motionType,a=e.onMotionStart,c=e.onMotionEnd,s=e.active,m=e.treeNodeRequiredProps,u=mt(e,Xs),f=l.useState(!0),v=Re(f,2),g=v[0],b=v[1],p=l.useContext(cr),y=p.prefixCls,C=i&&d!=="hide";Mt(function(){i&&C!==g&&b(C)},[i]);var x=function(){i&&a()},w=l.useRef(!1),S=function(){i&&!w.current&&(w.current=!0,c())};qs(x,S);var E=function(P){C===P&&S()};return i?l.createElement(Sa,de({ref:t,visible:g},o,{motionAppear:d==="show",onVisibleChanged:E}),function(I,P){var h=I.className,K=I.style;return l.createElement("div",{ref:P,className:Y("".concat(y,"-treenode-motion"),h),style:K},i.map(function(z){var R=Object.assign({},(oa(z.data),z.data)),k=z.title,N=z.key,$=z.isStart,O=z.isEnd;delete R.children;var D=nn(N,m);return l.createElement(ln,de({},R,D,{title:k,active:s,data:z.data,key:N,isStart:$,isEnd:O}))}))}):l.createElement(ln,de({domRef:t,className:r,style:n},u,{active:s}))});function Gs(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,n=t.length;if(Math.abs(r-n)!==1)return{add:!1,key:null};function o(i,d){var a=new Map;i.forEach(function(s){a.set(s,!0)});var c=d.filter(function(s){return!a.has(s)});return c.length===1?c[0]:null}return r<n?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}function eo(e,t,r){var n=e.findIndex(function(a){return a.key===r}),o=e[n+1],i=t.findIndex(function(a){return a.key===r});if(o){var d=t.findIndex(function(a){return a.key===o.key});return t.slice(i+1,d)}return t.slice(i+1)}var Ys=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],to={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Js=function(){},zt="RC_TREE_MOTION_".concat(Math.random()),qn={key:zt},aa={key:zt,level:0,index:0,pos:"0",node:qn,nodes:[qn]},no={parent:null,children:[],pos:aa.pos,data:qn,title:null,key:zt,isStart:[],isEnd:[]};function ro(e,t,r,n){return t===!1||!r?e:e.slice(0,Math.ceil(r/n)+1)}function oo(e){var t=e.key,r=e.pos;return cn(t,r)}function Qs(e){for(var t=String(e.data.key),r=e;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var Zs=l.forwardRef(function(e,t){var r=e.prefixCls,n=e.data;e.selectable,e.checkable;var o=e.expandedKeys,i=e.selectedKeys,d=e.checkedKeys,a=e.loadedKeys,c=e.loadingKeys,s=e.halfCheckedKeys,m=e.keyEntities,u=e.disabled,f=e.dragging,v=e.dragOverNodeKey,g=e.dropPosition,b=e.motion,p=e.height,y=e.itemHeight,C=e.virtual,x=e.scrollWidth,w=e.focusable,S=e.activeItem,E=e.focused,I=e.tabIndex,P=e.onKeyDown,h=e.onFocus,K=e.onBlur,z=e.onActiveChange,R=e.onListChangeStart,k=e.onListChangeEnd,N=mt(e,Ys),$=l.useRef(null),O=l.useRef(null);l.useImperativeHandle(t,function(){return{scrollTo:function(V){$.current.scrollTo(V)},getIndentWidth:function(){return O.current.offsetWidth}}});var D=l.useState(o),_=Re(D,2),M=_[0],G=_[1],ee=l.useState(n),ye=Re(ee,2),oe=ye[0],pe=ye[1],xe=l.useState(n),ue=Re(xe,2),J=ue[0],te=ue[1],Ce=l.useState([]),le=Re(Ce,2),U=le[0],j=le[1],H=l.useState(null),q=Re(H,2),Z=q[0],F=q[1],ne=l.useRef(n);ne.current=n;function Ie(){var B=ne.current;pe(B),te(B),j([]),F(null),k()}Mt(function(){G(o);var B=Gs(M,o);if(B.key!==null)if(B.add){var V=oe.findIndex(function(je){var ge=je.key;return ge===B.key}),ae=ro(eo(oe,n,B.key),C,p,y),fe=oe.slice();fe.splice(V+1,0,no),te(fe),j(ae),F("show")}else{var me=n.findIndex(function(je){var ge=je.key;return ge===B.key}),Oe=ro(eo(n,oe,B.key),C,p,y),ze=n.slice();ze.splice(me+1,0,no),te(ze),j(Oe),F("hide")}else oe!==n&&(pe(n),te(n))},[o,n]),l.useEffect(function(){f||Ie()},[f]);var We=b?J:n,ke={expandedKeys:o,selectedKeys:i,loadedKeys:a,loadingKeys:c,checkedKeys:d,halfCheckedKeys:s,dragOverNodeKey:v,dropPosition:g,keyEntities:m};return l.createElement(l.Fragment,null,E&&S&&l.createElement("span",{style:to,"aria-live":"assertive"},Qs(S)),l.createElement("div",null,l.createElement("input",{style:to,disabled:w===!1||u,tabIndex:w!==!1?I:null,onKeyDown:P,onFocus:h,onBlur:K,value:"",onChange:Js,"aria-label":"for screen reader"})),l.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},l.createElement("div",{className:"".concat(r,"-indent")},l.createElement("div",{ref:O,className:"".concat(r,"-indent-unit")}))),l.createElement(Eo,de({},N,{data:We,itemKey:oo,height:p,fullHeight:!1,virtual:C,itemHeight:y,scrollWidth:x,prefixCls:"".concat(r,"-list"),ref:$,role:"tree",onVisibleChange:function(V){V.every(function(ae){return oo(ae)!==zt})&&Ie()}}),function(B){var V=B.pos,ae=Object.assign({},(oa(B.data),B.data)),fe=B.title,me=B.key,Oe=B.isStart,ze=B.isEnd,je=cn(me,V);delete ae.key,delete ae.children;var ge=nn(je,ke);return l.createElement(Us,de({},ae,ge,{title:fe,active:!!S&&me===S.key,pos:V,data:B.data,isStart:Oe,isEnd:ze,motion:b,motionNodes:me===zt?U:null,motionType:Z,onMotionStart:R,onMotionEnd:Ie,treeNodeRequiredProps:ke,onMouseMove:function(){z(null)}}))}))}),ec=10,ur=function(e){wa(r,e);var t=Ea(r);function r(){var n;$a(this,r);for(var o=arguments.length,i=new Array(o),d=0;d<o;d++)i[d]=arguments[d];return n=t.call.apply(t,[this].concat(i)),T(be(n),"destroyed",!1),T(be(n),"delayedDragEnterLogic",void 0),T(be(n),"loadingRetryTimes",{}),T(be(n),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:At()}),T(be(n),"dragStartMousePosition",null),T(be(n),"dragNodeProps",null),T(be(n),"currentMouseOverDroppableNodeKey",null),T(be(n),"listRef",l.createRef()),T(be(n),"onNodeDragStart",function(a,c){var s=n.state,m=s.expandedKeys,u=s.keyEntities,f=n.props.onDragStart,v=c.eventKey;n.dragNodeProps=c,n.dragStartMousePosition={x:a.clientX,y:a.clientY};var g=gt(m,v);n.setState({draggingNodeKey:v,dragChildrenKeys:Ds(v,u),indent:n.listRef.current.getIndentWidth()}),n.setExpandedKeys(g),window.addEventListener("dragend",n.onWindowDragEnd),f==null||f({event:a,node:Fe(c)})}),T(be(n),"onNodeDragEnter",function(a,c){var s=n.state,m=s.expandedKeys,u=s.keyEntities,f=s.dragChildrenKeys,v=s.flattenNodes,g=s.indent,b=n.props,p=b.onDragEnter,y=b.onExpand,C=b.allowDrop,x=b.direction,w=c.pos,S=c.eventKey;if(n.currentMouseOverDroppableNodeKey!==S&&(n.currentMouseOverDroppableNodeKey=S),!n.dragNodeProps){n.resetDragState();return}var E=Jr(a,n.dragNodeProps,c,g,n.dragStartMousePosition,C,v,u,m,x),I=E.dropPosition,P=E.dropLevelOffset,h=E.dropTargetKey,K=E.dropContainerKey,z=E.dropTargetPos,R=E.dropAllowed,k=E.dragOverNodeKey;if(f.includes(h)||!R){n.resetDragState();return}if(n.delayedDragEnterLogic||(n.delayedDragEnterLogic={}),Object.keys(n.delayedDragEnterLogic).forEach(function(N){clearTimeout(n.delayedDragEnterLogic[N])}),n.dragNodeProps.eventKey!==c.eventKey&&(a.persist(),n.delayedDragEnterLogic[w]=window.setTimeout(function(){if(n.state.draggingNodeKey!==null){var N=$e(m),$=st(u,c.eventKey);$&&($.children||[]).length&&(N=yt(m,c.eventKey)),n.props.hasOwnProperty("expandedKeys")||n.setExpandedKeys(N),y==null||y(N,{node:Fe(c),expanded:!0,nativeEvent:a.nativeEvent})}},800)),n.dragNodeProps.eventKey===h&&P===0){n.resetDragState();return}n.setState({dragOverNodeKey:k,dropPosition:I,dropLevelOffset:P,dropTargetKey:h,dropContainerKey:K,dropTargetPos:z,dropAllowed:R}),p==null||p({event:a,node:Fe(c),expandedKeys:m})}),T(be(n),"onNodeDragOver",function(a,c){var s=n.state,m=s.dragChildrenKeys,u=s.flattenNodes,f=s.keyEntities,v=s.expandedKeys,g=s.indent,b=n.props,p=b.onDragOver,y=b.allowDrop,C=b.direction;if(n.dragNodeProps){var x=Jr(a,n.dragNodeProps,c,g,n.dragStartMousePosition,y,u,f,v,C),w=x.dropPosition,S=x.dropLevelOffset,E=x.dropTargetKey,I=x.dropContainerKey,P=x.dropTargetPos,h=x.dropAllowed,K=x.dragOverNodeKey;m.includes(E)||!h||(n.dragNodeProps.eventKey===E&&S===0?n.state.dropPosition===null&&n.state.dropLevelOffset===null&&n.state.dropTargetKey===null&&n.state.dropContainerKey===null&&n.state.dropTargetPos===null&&n.state.dropAllowed===!1&&n.state.dragOverNodeKey===null||n.resetDragState():w===n.state.dropPosition&&S===n.state.dropLevelOffset&&E===n.state.dropTargetKey&&I===n.state.dropContainerKey&&P===n.state.dropTargetPos&&h===n.state.dropAllowed&&K===n.state.dragOverNodeKey||n.setState({dropPosition:w,dropLevelOffset:S,dropTargetKey:E,dropContainerKey:I,dropTargetPos:P,dropAllowed:h,dragOverNodeKey:K}),p==null||p({event:a,node:Fe(c)}))}}),T(be(n),"onNodeDragLeave",function(a,c){n.currentMouseOverDroppableNodeKey===c.eventKey&&!a.currentTarget.contains(a.relatedTarget)&&(n.resetDragState(),n.currentMouseOverDroppableNodeKey=null);var s=n.props.onDragLeave;s==null||s({event:a,node:Fe(c)})}),T(be(n),"onWindowDragEnd",function(a){n.onNodeDragEnd(a,null,!0),window.removeEventListener("dragend",n.onWindowDragEnd)}),T(be(n),"onNodeDragEnd",function(a,c){var s=n.props.onDragEnd;n.setState({dragOverNodeKey:null}),n.cleanDragState(),s==null||s({event:a,node:Fe(c)}),n.dragNodeProps=null,window.removeEventListener("dragend",n.onWindowDragEnd)}),T(be(n),"onNodeDrop",function(a,c){var s,m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=n.state,f=u.dragChildrenKeys,v=u.dropPosition,g=u.dropTargetKey,b=u.dropTargetPos,p=u.dropAllowed;if(p){var y=n.props.onDrop;if(n.setState({dragOverNodeKey:null}),n.cleanDragState(),g!==null){var C=W(W({},nn(g,n.getTreeNodeRequiredProps())),{},{active:((s=n.getActiveItem())===null||s===void 0?void 0:s.key)===g,data:st(n.state.keyEntities,g).node}),x=f.includes(g);Et(!x,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var w=dr(b),S={event:a,node:Fe(C),dragNode:n.dragNodeProps?Fe(n.dragNodeProps):null,dragNodesKeys:[n.dragNodeProps.eventKey].concat(f),dropToGap:v!==0,dropPosition:v+Number(w[w.length-1])};m||y==null||y(S),n.dragNodeProps=null}}}),T(be(n),"cleanDragState",function(){var a=n.state.draggingNodeKey;a!==null&&n.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),n.dragStartMousePosition=null,n.currentMouseOverDroppableNodeKey=null}),T(be(n),"triggerExpandActionExpand",function(a,c){var s=n.state,m=s.expandedKeys,u=s.flattenNodes,f=c.expanded,v=c.key,g=c.isLeaf;if(!(g||a.shiftKey||a.metaKey||a.ctrlKey)){var b=u.filter(function(y){return y.key===v})[0],p=Fe(W(W({},nn(v,n.getTreeNodeRequiredProps())),{},{data:b.data}));n.setExpandedKeys(f?gt(m,v):yt(m,v)),n.onNodeExpand(a,p)}}),T(be(n),"onNodeClick",function(a,c){var s=n.props,m=s.onClick,u=s.expandAction;u==="click"&&n.triggerExpandActionExpand(a,c),m==null||m(a,c)}),T(be(n),"onNodeDoubleClick",function(a,c){var s=n.props,m=s.onDoubleClick,u=s.expandAction;u==="doubleClick"&&n.triggerExpandActionExpand(a,c),m==null||m(a,c)}),T(be(n),"onNodeSelect",function(a,c){var s=n.state.selectedKeys,m=n.state,u=m.keyEntities,f=m.fieldNames,v=n.props,g=v.onSelect,b=v.multiple,p=c.selected,y=c[f.key],C=!p;C?b?s=yt(s,y):s=[y]:s=gt(s,y);var x=s.map(function(w){var S=st(u,w);return S?S.node:null}).filter(Boolean);n.setUncontrolledState({selectedKeys:s}),g==null||g(s,{event:"select",selected:C,node:c,selectedNodes:x,nativeEvent:a.nativeEvent})}),T(be(n),"onNodeCheck",function(a,c,s){var m=n.state,u=m.keyEntities,f=m.checkedKeys,v=m.halfCheckedKeys,g=n.props,b=g.checkStrictly,p=g.onCheck,y=c.key,C,x={event:"check",node:c,checked:s,nativeEvent:a.nativeEvent};if(b){var w=s?yt(f,y):gt(f,y),S=gt(v,y);C={checked:w,halfChecked:S},x.checkedNodes=w.map(function(z){return st(u,z)}).filter(Boolean).map(function(z){return z.node}),n.setUncontrolledState({checkedKeys:w})}else{var E=Ht([].concat($e(f),[y]),!0,u),I=E.checkedKeys,P=E.halfCheckedKeys;if(!s){var h=new Set(I);h.delete(y);var K=Ht(Array.from(h),{halfCheckedKeys:P},u);I=K.checkedKeys,P=K.halfCheckedKeys}C=I,x.checkedNodes=[],x.checkedNodesPositions=[],x.halfCheckedKeys=P,I.forEach(function(z){var R=st(u,z);if(R){var k=R.node,N=R.pos;x.checkedNodes.push(k),x.checkedNodesPositions.push({node:k,pos:N})}}),n.setUncontrolledState({checkedKeys:I},!1,{halfCheckedKeys:P})}p==null||p(C,x)}),T(be(n),"onNodeLoad",function(a){var c,s=a.key,m=n.state.keyEntities,u=st(m,s);if(!(u!=null&&(c=u.children)!==null&&c!==void 0&&c.length)){var f=new Promise(function(v,g){n.setState(function(b){var p=b.loadedKeys,y=p===void 0?[]:p,C=b.loadingKeys,x=C===void 0?[]:C,w=n.props,S=w.loadData,E=w.onLoad;if(!S||y.includes(s)||x.includes(s))return null;var I=S(a);return I.then(function(){var P=n.state.loadedKeys,h=yt(P,s);E==null||E(h,{event:"load",node:a}),n.setUncontrolledState({loadedKeys:h}),n.setState(function(K){return{loadingKeys:gt(K.loadingKeys,s)}}),v()}).catch(function(P){if(n.setState(function(K){return{loadingKeys:gt(K.loadingKeys,s)}}),n.loadingRetryTimes[s]=(n.loadingRetryTimes[s]||0)+1,n.loadingRetryTimes[s]>=ec){var h=n.state.loadedKeys;Et(!1,"Retry for `loadData` many times but still failed. No more retry."),n.setUncontrolledState({loadedKeys:yt(h,s)}),v()}g(P)}),{loadingKeys:yt(x,s)}})});return f.catch(function(){}),f}}),T(be(n),"onNodeMouseEnter",function(a,c){var s=n.props.onMouseEnter;s==null||s({event:a,node:c})}),T(be(n),"onNodeMouseLeave",function(a,c){var s=n.props.onMouseLeave;s==null||s({event:a,node:c})}),T(be(n),"onNodeContextMenu",function(a,c){var s=n.props.onRightClick;s&&(a.preventDefault(),s({event:a,node:c}))}),T(be(n),"onFocus",function(){var a=n.props.onFocus;n.setState({focused:!0});for(var c=arguments.length,s=new Array(c),m=0;m<c;m++)s[m]=arguments[m];a==null||a.apply(void 0,s)}),T(be(n),"onBlur",function(){var a=n.props.onBlur;n.setState({focused:!1}),n.onActiveChange(null);for(var c=arguments.length,s=new Array(c),m=0;m<c;m++)s[m]=arguments[m];a==null||a.apply(void 0,s)}),T(be(n),"getTreeNodeRequiredProps",function(){var a=n.state,c=a.expandedKeys,s=a.selectedKeys,m=a.loadedKeys,u=a.loadingKeys,f=a.checkedKeys,v=a.halfCheckedKeys,g=a.dragOverNodeKey,b=a.dropPosition,p=a.keyEntities;return{expandedKeys:c||[],selectedKeys:s||[],loadedKeys:m||[],loadingKeys:u||[],checkedKeys:f||[],halfCheckedKeys:v||[],dragOverNodeKey:g,dropPosition:b,keyEntities:p}}),T(be(n),"setExpandedKeys",function(a){var c=n.state,s=c.treeData,m=c.fieldNames,u=kn(s,a,m);n.setUncontrolledState({expandedKeys:a,flattenNodes:u},!0)}),T(be(n),"onNodeExpand",function(a,c){var s=n.state.expandedKeys,m=n.state,u=m.listChanging,f=m.fieldNames,v=n.props,g=v.onExpand,b=v.loadData,p=c.expanded,y=c[f.key];if(!u){var C=s.includes(y),x=!p;if(Et(p&&C||!p&&!C,"Expand state not sync with index check"),s=x?yt(s,y):gt(s,y),n.setExpandedKeys(s),g==null||g(s,{node:c,expanded:x,nativeEvent:a.nativeEvent}),x&&b){var w=n.onNodeLoad(c);w&&w.then(function(){var S=kn(n.state.treeData,s,f);n.setUncontrolledState({flattenNodes:S})}).catch(function(){var S=n.state.expandedKeys,E=gt(S,y);n.setExpandedKeys(E)})}}}),T(be(n),"onListChangeStart",function(){n.setUncontrolledState({listChanging:!0})}),T(be(n),"onListChangeEnd",function(){setTimeout(function(){n.setUncontrolledState({listChanging:!1})})}),T(be(n),"onActiveChange",function(a){var c=n.state.activeKey,s=n.props,m=s.onActiveChange,u=s.itemScrollOffset,f=u===void 0?0:u;c!==a&&(n.setState({activeKey:a}),a!==null&&n.scrollTo({key:a,offset:f}),m==null||m(a))}),T(be(n),"getActiveItem",function(){var a=n.state,c=a.activeKey,s=a.flattenNodes;return c===null?null:s.find(function(m){var u=m.key;return u===c})||null}),T(be(n),"offsetActiveKey",function(a){var c=n.state,s=c.flattenNodes,m=c.activeKey,u=s.findIndex(function(g){var b=g.key;return b===m});u===-1&&a<0&&(u=s.length),u=(u+a+s.length)%s.length;var f=s[u];if(f){var v=f.key;n.onActiveChange(v)}else n.onActiveChange(null)}),T(be(n),"onKeyDown",function(a){var c=n.state,s=c.activeKey,m=c.expandedKeys,u=c.checkedKeys,f=c.fieldNames,v=n.props,g=v.onKeyDown,b=v.checkable,p=v.selectable;switch(a.which){case et.UP:{n.offsetActiveKey(-1),a.preventDefault();break}case et.DOWN:{n.offsetActiveKey(1),a.preventDefault();break}}var y=n.getActiveItem();if(y&&y.data){var C=n.getTreeNodeRequiredProps(),x=y.data.isLeaf===!1||!!(y.data[f.children]||[]).length,w=Fe(W(W({},nn(s,C)),{},{data:y.data,active:!0}));switch(a.which){case et.LEFT:{x&&m.includes(s)?n.onNodeExpand({},w):y.parent&&n.onActiveChange(y.parent.key),a.preventDefault();break}case et.RIGHT:{x&&!m.includes(s)?n.onNodeExpand({},w):y.children&&y.children.length&&n.onActiveChange(y.children[0].key),a.preventDefault();break}case et.ENTER:case et.SPACE:{b&&!w.disabled&&w.checkable!==!1&&!w.disableCheckbox?n.onNodeCheck({},w,!u.includes(s)):!b&&p&&!w.disabled&&w.selectable!==!1&&n.onNodeSelect({},w);break}}}g==null||g(a)}),T(be(n),"setUncontrolledState",function(a){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!n.destroyed){var m=!1,u=!0,f={};Object.keys(a).forEach(function(v){if(n.props.hasOwnProperty(v)){u=!1;return}m=!0,f[v]=a[v]}),m&&(!c||u)&&n.setState(W(W({},f),s))}}),T(be(n),"scrollTo",function(a){n.listRef.current.scrollTo(a)}),n}return Na(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,i=o.activeKey,d=o.itemScrollOffset,a=d===void 0?0:d;i!==void 0&&i!==this.state.activeKey&&(this.setState({activeKey:i}),i!==null&&this.scrollTo({key:i,offset:a}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,i=o.focused,d=o.flattenNodes,a=o.keyEntities,c=o.draggingNodeKey,s=o.activeKey,m=o.dropLevelOffset,u=o.dropContainerKey,f=o.dropTargetKey,v=o.dropPosition,g=o.dragOverNodeKey,b=o.indent,p=this.props,y=p.prefixCls,C=p.className,x=p.style,w=p.showLine,S=p.focusable,E=p.tabIndex,I=E===void 0?0:E,P=p.selectable,h=p.showIcon,K=p.icon,z=p.switcherIcon,R=p.draggable,k=p.checkable,N=p.checkStrictly,$=p.disabled,O=p.motion,D=p.loadData,_=p.filterTreeNode,M=p.height,G=p.itemHeight,ee=p.scrollWidth,ye=p.virtual,oe=p.titleRender,pe=p.dropIndicatorRender,xe=p.onContextMenu,ue=p.onScroll,J=p.direction,te=p.rootClassName,Ce=p.rootStyle,le=an(this.props,{aria:!0,data:!0}),U;R&&(ft(R)==="object"?U=R:typeof R=="function"?U={nodeDraggable:R}:U={});var j={prefixCls:y,selectable:P,showIcon:h,icon:K,switcherIcon:z,draggable:U,draggingNodeKey:c,checkable:k,checkStrictly:N,disabled:$,keyEntities:a,dropLevelOffset:m,dropContainerKey:u,dropTargetKey:f,dropPosition:v,dragOverNodeKey:g,indent:b,direction:J,dropIndicatorRender:pe,loadData:D,filterTreeNode:_,titleRender:oe,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return l.createElement(cr.Provider,{value:j},l.createElement("div",{className:Y(y,C,te,T(T(T({},"".concat(y,"-show-line"),w),"".concat(y,"-focused"),i),"".concat(y,"-active-focused"),s!==null)),style:Ce},l.createElement(Zs,de({ref:this.listRef,prefixCls:y,style:x,data:d,disabled:$,selectable:P,checkable:!!k,motion:O,dragging:c!==null,height:M,itemHeight:G,virtual:ye,focusable:S,focused:i,tabIndex:I,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:xe,onScroll:ue,scrollWidth:ee},this.getTreeNodeRequiredProps(),le))))}}],[{key:"getDerivedStateFromProps",value:function(o,i){var d=i.prevProps,a={prevProps:o};function c(I){return!d&&o.hasOwnProperty(I)||d&&d[I]!==o[I]}var s,m=i.fieldNames;if(c("fieldNames")&&(m=At(o.fieldNames),a.fieldNames=m),c("treeData")?s=o.treeData:c("children")&&(Et(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),s=Oo(o.children)),s){a.treeData=s;var u=rr(s,{fieldNames:m});a.keyEntities=W(T({},zt,aa),u.keyEntities)}var f=a.keyEntities||i.keyEntities;if(c("expandedKeys")||d&&c("autoExpandParent"))a.expandedKeys=o.autoExpandParent||!d&&o.defaultExpandParent?An(o.expandedKeys,f):o.expandedKeys;else if(!d&&o.defaultExpandAll){var v=W({},f);delete v[zt];var g=[];Object.keys(v).forEach(function(I){var P=v[I];P.children&&P.children.length&&g.push(P.key)}),a.expandedKeys=g}else!d&&o.defaultExpandedKeys&&(a.expandedKeys=o.autoExpandParent||o.defaultExpandParent?An(o.defaultExpandedKeys,f):o.defaultExpandedKeys);if(a.expandedKeys||delete a.expandedKeys,s||a.expandedKeys){var b=kn(s||i.treeData,a.expandedKeys||i.expandedKeys,m);a.flattenNodes=b}if(o.selectable&&(c("selectedKeys")?a.selectedKeys=Qr(o.selectedKeys,o):!d&&o.defaultSelectedKeys&&(a.selectedKeys=Qr(o.defaultSelectedKeys,o))),o.checkable){var p;if(c("checkedKeys")?p=Kn(o.checkedKeys)||{}:!d&&o.defaultCheckedKeys?p=Kn(o.defaultCheckedKeys)||{}:s&&(p=Kn(o.checkedKeys)||{checkedKeys:i.checkedKeys,halfCheckedKeys:i.halfCheckedKeys}),p){var y=p,C=y.checkedKeys,x=C===void 0?[]:C,w=y.halfCheckedKeys,S=w===void 0?[]:w;if(!o.checkStrictly){var E=Ht(x,!0,f);x=E.checkedKeys,S=E.halfCheckedKeys}a.checkedKeys=x,a.halfCheckedKeys=S}}return c("loadedKeys")&&(a.loadedKeys=o.loadedKeys),a}}]),r}(l.Component);T(ur,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:Vs,allowDrop:function(){return!0},expandAction:!1});T(ur,"TreeNode",ln);const tc=e=>{let{treeCls:t,treeNodeCls:r,directoryNodeSelectedBg:n,directoryNodeSelectedColor:o,motionDurationMid:i,borderRadius:d,controlItemBgHover:a}=e;return{[`${t}${t}-directory ${r}`]:{[`${t}-node-content-wrapper`]:{position:"static",[`> *:not(${t}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${i}`,content:'""',borderRadius:d},"&:hover:before":{background:a}},[`${t}-switcher, ${t}-checkbox, ${t}-draggable-icon`]:{zIndex:1},"&-selected":{[`${t}-switcher, ${t}-draggable-icon`]:{color:o},[`${t}-node-content-wrapper`]:{color:o,background:"transparent","&:before, &:hover:before":{background:n}}}}}},nc=new tr("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),rc=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),oc=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${A(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),ac=(e,t)=>{const{treeCls:r,treeNodeCls:n,treeNodePadding:o,titleHeight:i,indentSize:d,nodeSelectedBg:a,nodeHoverBg:c,colorTextQuaternary:s,controlItemBgActiveDisabled:m}=t;return{[r]:Object.assign(Object.assign({},sn(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:Object.assign({},zn(t)),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${n}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:nc,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[n]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:A(i),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${n}-disabled${n}-selected ${r}-node-content-wrapper`]:{backgroundColor:m},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${n}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${n}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:i,textAlign:"center",visibility:"visible",color:s},[`&${n}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:d}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:t.calc(t.calc(i).sub(t.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},rc(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:i,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:i,height:i,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(i).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(i).div(2).equal()).mul(.8).equal(),height:t.calc(i).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:i,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},oc(e,t)),{"&:hover":{backgroundColor:c},[`&${r}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:a},[`${r}-iconEle`]:{display:"inline-block",width:i,height:i,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(i).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${n}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${A(t.calc(i).div(2).equal())} !important`}})}},ic=(e,t)=>{const r=`.${e}`,n=`${r}-treenode`,o=t.calc(t.paddingXS).div(2).equal(),i=bn(t,{treeCls:r,treeNodeCls:n,treeNodePadding:o});return[ac(e,i),tc(i)]},lc=e=>{const{controlHeightSM:t,controlItemBgHover:r,controlItemBgActive:n}=e,o=t;return{titleHeight:o,indentSize:o,nodeHoverBg:r,nodeHoverColor:e.colorText,nodeSelectedBg:n,nodeSelectedColor:e.colorText}},sc=e=>{const{colorTextLightSolid:t,colorPrimary:r}=e;return Object.assign(Object.assign({},lc(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},cc=yn("Tree",(e,t)=>{let{prefixCls:r}=t;return[{[e.componentCls]:Ja(`${r}-checkbox`,e)},ic(r,e),Ha(e)]},sc),ao=4;function dc(e){const{dropPosition:t,dropLevelOffset:r,prefixCls:n,indent:o,direction:i="ltr"}=e,d=i==="ltr"?"left":"right",a=i==="ltr"?"right":"left",c={[d]:-r*o+ao,[a]:0};switch(t){case-1:c.top=-3;break;case 1:c.bottom=-3;break;default:c.bottom=-3,c[d]=o+ao;break}return X.createElement("div",{style:c,className:`${n}-drop-indicator`})}const uc=e=>{const{prefixCls:t,switcherIcon:r,treeNodeProps:n,showLine:o,switcherLoadingIcon:i}=e,{isLeaf:d,expanded:a,loading:c}=n;if(c)return l.isValidElement(i)?i:l.createElement(ja,{className:`${t}-switcher-loading-icon`});let s;if(o&&typeof o=="object"&&(s=o.showLeafIcon),d){if(!o)return null;if(typeof s!="boolean"&&s){const f=typeof s=="function"?s(n):s,v=`${t}-switcher-line-custom-icon`;return l.isValidElement(f)?Ln(f,{className:Y(f.props.className||"",v)}):f}return s?l.createElement(_o,{className:`${t}-switcher-line-icon`}):l.createElement("span",{className:`${t}-switcher-leaf-line`})}const m=`${t}-switcher-icon`,u=typeof r=="function"?r(n):r;return l.isValidElement(u)?Ln(u,{className:Y(u.props.className||"",m)}):u!==void 0?u:o?a?l.createElement(gl,{className:`${t}-switcher-line-icon`}):l.createElement(bl,{className:`${t}-switcher-line-icon`}):l.createElement(Xi,{className:m})},ia=X.forwardRef((e,t)=>{var r;const{getPrefixCls:n,direction:o,virtual:i,tree:d}=X.useContext(hn),{prefixCls:a,className:c,showIcon:s=!1,showLine:m,switcherIcon:u,switcherLoadingIcon:f,blockNode:v=!1,children:g,checkable:b=!1,selectable:p=!0,draggable:y,motion:C,style:x}=e,w=n("tree",a),S=n(),E=C??Object.assign(Object.assign({},Aa(S)),{motionAppear:!1}),I=Object.assign(Object.assign({},e),{checkable:b,selectable:p,showIcon:s,motion:E,blockNode:v,showLine:!!m,dropIndicatorRender:dc}),[P,h,K]=cc(w),[,z]=Jn(),R=z.paddingXS/2+(((r=z.Tree)===null||r===void 0?void 0:r.titleHeight)||z.controlHeightSM),k=X.useMemo(()=>{if(!y)return!1;let $={};switch(typeof y){case"function":$.nodeDraggable=y;break;case"object":$=Object.assign({},y);break}return $.icon!==!1&&($.icon=$.icon||X.createElement(ml,null)),$},[y]),N=$=>X.createElement(uc,{prefixCls:w,switcherIcon:u,switcherLoadingIcon:f,treeNodeProps:$,showLine:m});return P(X.createElement(ur,Object.assign({itemHeight:R,ref:t,virtual:i},I,{style:Object.assign(Object.assign({},d==null?void 0:d.style),x),prefixCls:w,className:Y({[`${w}-icon-hide`]:!s,[`${w}-block-node`]:v,[`${w}-unselectable`]:!p,[`${w}-rtl`]:o==="rtl"},d==null?void 0:d.className,c,h,K),direction:o,checkable:b&&X.createElement("span",{className:`${w}-checkbox-inner`}),selectable:p,switcherIcon:N,draggable:k}),g))}),io=0,Pn=1,lo=2;function fr(e,t,r){const{key:n,children:o}=r;function i(d){const a=d[n],c=d[o];t(a,d)!==!1&&fr(c||[],t,r)}e.forEach(i)}function fc(e){let{treeData:t,expandedKeys:r,startKey:n,endKey:o,fieldNames:i}=e;const d=[];let a=io;if(n&&n===o)return[n];if(!n||!o)return[];function c(s){return s===n||s===o}return fr(t,s=>{if(a===lo)return!1;if(c(s)){if(d.push(s),a===io)a=Pn;else if(a===Pn)return a=lo,!1}else a===Pn&&d.push(s);return r.includes(s)},At(i)),d}function Tn(e,t,r){const n=$e(t),o=[];return fr(e,(i,d)=>{const a=n.indexOf(i);return a!==-1&&(o.push(d),n.splice(a,1)),!!n.length},At(r)),o}var so=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function mc(e){const{isLeaf:t,expanded:r}=e;return t?l.createElement(_o,null):r?l.createElement(ll,null):l.createElement(dl,null)}function co(e){let{treeData:t,children:r}=e;return t||Oo(r)}const vc=(e,t)=>{var{defaultExpandAll:r,defaultExpandParent:n,defaultExpandedKeys:o}=e,i=so(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const d=l.useRef(null),a=l.useRef(null),c=()=>{const{keyEntities:P}=rr(co(i));let h;return r?h=Object.keys(P):n?h=An(i.expandedKeys||o||[],P):h=i.expandedKeys||o||[],h},[s,m]=l.useState(i.selectedKeys||i.defaultSelectedKeys||[]),[u,f]=l.useState(()=>c());l.useEffect(()=>{"selectedKeys"in i&&m(i.selectedKeys)},[i.selectedKeys]),l.useEffect(()=>{"expandedKeys"in i&&f(i.expandedKeys)},[i.expandedKeys]);const v=(P,h)=>{var K;return"expandedKeys"in i||f(P),(K=i.onExpand)===null||K===void 0?void 0:K.call(i,P,h)},g=(P,h)=>{var K;const{multiple:z,fieldNames:R}=i,{node:k,nativeEvent:N}=h,{key:$=""}=k,O=co(i),D=Object.assign(Object.assign({},h),{selected:!0}),_=(N==null?void 0:N.ctrlKey)||(N==null?void 0:N.metaKey),M=N==null?void 0:N.shiftKey;let G;z&&_?(G=P,d.current=$,a.current=G,D.selectedNodes=Tn(O,G,R)):z&&M?(G=Array.from(new Set([].concat($e(a.current||[]),$e(fc({treeData:O,expandedKeys:u,startKey:$,endKey:d.current,fieldNames:R}))))),D.selectedNodes=Tn(O,G,R)):(G=[$],d.current=$,a.current=G,D.selectedNodes=Tn(O,G,R)),(K=i.onSelect)===null||K===void 0||K.call(i,G,D),"selectedKeys"in i||m(G)},{getPrefixCls:b,direction:p}=l.useContext(hn),{prefixCls:y,className:C,showIcon:x=!0,expandAction:w="click"}=i,S=so(i,["prefixCls","className","showIcon","expandAction"]),E=b("tree",y),I=Y(`${E}-directory`,{[`${E}-directory-rtl`]:p==="rtl"},C);return l.createElement(ia,Object.assign({icon:mc,ref:t,blockNode:!0},S,{showIcon:x,expandAction:w,prefixCls:E,className:I,expandedKeys:u,selectedKeys:s,onSelect:g,onExpand:v}))},pc=l.forwardRef(vc),mr=ia;mr.DirectoryTree=pc;mr.TreeNode=ln;const uo=e=>{const{value:t,filterSearch:r,tablePrefixCls:n,locale:o,onChange:i}=e;return r?l.createElement("div",{className:`${n}-filter-dropdown-search`},l.createElement(Fa,{prefix:l.createElement(Wa,null),placeholder:o.filterSearchPlaceholder,onChange:i,value:t,htmlSize:1,className:`${n}-filter-dropdown-search-input`})):null},gc=e=>{const{keyCode:t}=e;t===et.ENTER&&e.stopPropagation()},hc=l.forwardRef((e,t)=>l.createElement("div",{className:e.className,onClick:r=>r.stopPropagation(),onKeyDown:gc,ref:t},e.children));function jt(e){let t=[];return(e||[]).forEach(r=>{let{value:n,children:o}=r;t.push(n),o&&(t=[].concat($e(t),$e(jt(o))))}),t}function yc(e){return e.some(t=>{let{children:r}=t;return r})}function la(e,t){return typeof t=="string"||typeof t=="number"?t==null?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()):!1}function sa(e){let{filters:t,prefixCls:r,filteredKeys:n,filterMultiple:o,searchValue:i,filterSearch:d}=e;return t.map((a,c)=>{const s=String(a.value);if(a.children)return{key:s||c,label:a.text,popupClassName:`${r}-dropdown-submenu`,children:sa({filters:a.children,prefixCls:r,filteredKeys:n,filterMultiple:o,searchValue:i,filterSearch:d})};const m=o?vn:No,u={key:a.value!==void 0?s:c,label:l.createElement(l.Fragment,null,l.createElement(m,{checked:n.includes(s)}),l.createElement("span",null,a.text))};return i.trim()?typeof d=="function"?d(i,a)?u:null:la(i,a.text)?u:null:u})}function Dn(e){return e||[]}const bc=e=>{var t,r,n,o;const{tablePrefixCls:i,prefixCls:d,column:a,dropdownPrefixCls:c,columnKey:s,filterOnClose:m,filterMultiple:u,filterMode:f="menu",filterSearch:v=!1,filterState:g,triggerFilter:b,locale:p,children:y,getPopupContainer:C,rootClassName:x}=e,{filterResetToDefaultFilteredValue:w,defaultFilteredValue:S,filterDropdownProps:E={},filterDropdownOpen:I,filterDropdownVisible:P,onFilterDropdownVisibleChange:h,onFilterDropdownOpenChange:K}=a,[z,R]=l.useState(!1),k=!!(g&&(!((t=g.filteredKeys)===null||t===void 0)&&t.length||g.forceFiltered)),N=B=>{var V;R(B),(V=E.onOpenChange)===null||V===void 0||V.call(E,B),K==null||K(B),h==null||h(B)},$=(o=(n=(r=E.open)!==null&&r!==void 0?r:I)!==null&&n!==void 0?n:P)!==null&&o!==void 0?o:z,O=g==null?void 0:g.filteredKeys,[D,_]=Ws(Dn(O)),M=B=>{let{selectedKeys:V}=B;_(V)},G=(B,V)=>{let{node:ae,checked:fe}=V;M(u?{selectedKeys:B}:{selectedKeys:fe&&ae.key?[ae.key]:[]})};l.useEffect(()=>{z&&M({selectedKeys:Dn(O)})},[O]);const[ee,ye]=l.useState([]),oe=B=>{ye(B)},[pe,xe]=l.useState(""),ue=B=>{const{value:V}=B.target;xe(V)};l.useEffect(()=>{z||xe("")},[z]);const J=B=>{const V=B!=null&&B.length?B:null;if(V===null&&(!g||!g.filteredKeys)||on(V,g==null?void 0:g.filteredKeys,!0))return null;b({column:a,key:s,filteredKeys:V})},te=()=>{N(!1),J(D())},Ce=function(){let{confirm:B,closeDropdown:V}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{confirm:!1,closeDropdown:!1};B&&J([]),V&&N(!1),xe(""),_(w?(S||[]).map(ae=>String(ae)):[])},le=function(){let{closeDropdown:B}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{closeDropdown:!0};B&&N(!1),J(D())},U=(B,V)=>{V.source==="trigger"&&(B&&O!==void 0&&_(Dn(O)),N(B),!B&&!a.filterDropdown&&m&&te())},j=Y({[`${c}-menu-without-submenu`]:!yc(a.filters||[])}),H=B=>{if(B.target.checked){const V=jt(a==null?void 0:a.filters).map(ae=>String(ae));_(V)}else _([])},q=B=>{let{filters:V}=B;return(V||[]).map((ae,fe)=>{const me=String(ae.value),Oe={title:ae.text,key:ae.value!==void 0?me:String(fe)};return ae.children&&(Oe.children=q({filters:ae.children})),Oe})},Z=B=>{var V;return Object.assign(Object.assign({},B),{text:B.title,value:B.key,children:((V=B.children)===null||V===void 0?void 0:V.map(ae=>Z(ae)))||[]})};let F;const{direction:ne,renderEmpty:Ie}=l.useContext(hn);if(typeof a.filterDropdown=="function")F=a.filterDropdown({prefixCls:`${c}-custom`,setSelectedKeys:B=>M({selectedKeys:B}),selectedKeys:D(),confirm:le,clearFilters:Ce,filters:a.filters,visible:$,close:()=>{N(!1)}});else if(a.filterDropdown)F=a.filterDropdown;else{const B=D()||[],V=()=>{var fe,me;const Oe=(fe=Ie==null?void 0:Ie("Table.filter"))!==null&&fe!==void 0?fe:l.createElement(Kr,{image:Kr.PRESENTED_IMAGE_SIMPLE,description:p.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if((a.filters||[]).length===0)return Oe;if(f==="tree")return l.createElement(l.Fragment,null,l.createElement(uo,{filterSearch:v,value:pe,onChange:ue,tablePrefixCls:i,locale:p}),l.createElement("div",{className:`${i}-filter-dropdown-tree`},u?l.createElement(vn,{checked:B.length===jt(a.filters).length,indeterminate:B.length>0&&B.length<jt(a.filters).length,className:`${i}-filter-dropdown-checkall`,onChange:H},(me=p==null?void 0:p.filterCheckall)!==null&&me!==void 0?me:p==null?void 0:p.filterCheckAll):null,l.createElement(mr,{checkable:!0,selectable:!1,blockNode:!0,multiple:u,checkStrictly:!u,className:`${c}-menu`,onCheck:G,checkedKeys:B,selectedKeys:B,showIcon:!1,treeData:q({filters:a.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:pe.trim()?ge=>typeof v=="function"?v(pe,Z(ge)):la(pe,ge.title):void 0})));const ze=sa({filters:a.filters||[],filterSearch:v,prefixCls:d,filteredKeys:D(),filterMultiple:u,searchValue:pe}),je=ze.every(ge=>ge===null);return l.createElement(l.Fragment,null,l.createElement(uo,{filterSearch:v,value:pe,onChange:ue,tablePrefixCls:i,locale:p}),je?Oe:l.createElement(Ua,{selectable:!0,multiple:u,prefixCls:`${c}-menu`,className:j,onSelect:M,onDeselect:M,selectedKeys:B,getPopupContainer:C,openKeys:ee,onOpenChange:oe,items:ze}))},ae=()=>w?on((S||[]).map(fe=>String(fe)),B,!0):B.length===0;F=l.createElement(l.Fragment,null,V(),l.createElement("div",{className:`${d}-dropdown-btns`},l.createElement(kr,{type:"link",size:"small",disabled:ae(),onClick:()=>Ce()},p.filterReset),l.createElement(kr,{type:"primary",size:"small",onClick:te},p.filterConfirm)))}a.filterDropdown&&(F=l.createElement(Ga,{selectable:void 0},F)),F=l.createElement(hc,{className:`${d}-dropdown`},F);const ke=Po({trigger:["click"],placement:ne==="rtl"?"bottomLeft":"bottomRight",children:(()=>{let B;return typeof a.filterIcon=="function"?B=a.filterIcon(k):a.filterIcon?B=a.filterIcon:B=l.createElement(ol,null),l.createElement("span",{role:"button",tabIndex:-1,className:Y(`${d}-trigger`,{active:k}),onClick:V=>{V.stopPropagation()}},B)})(),getPopupContainer:C},Object.assign(Object.assign({},E),{rootClassName:Y(x,E.rootClassName),open:$,onOpenChange:U,dropdownRender:()=>typeof(E==null?void 0:E.dropdownRender)=="function"?E.dropdownRender(F):F}));return l.createElement("div",{className:`${d}-column`},l.createElement("span",{className:`${i}-column-title`},y),l.createElement($o,Object.assign({},ke)))},Xn=(e,t,r)=>{let n=[];return(e||[]).forEach((o,i)=>{var d;const a=qt(i,r);if(o.filters||"filterDropdown"in o||"onFilter"in o)if("filteredValue"in o){let c=o.filteredValue;"filterDropdown"in o||(c=(d=c==null?void 0:c.map(String))!==null&&d!==void 0?d:c),n.push({column:o,key:Nt(o,a),filteredKeys:c,forceFiltered:o.filtered})}else n.push({column:o,key:Nt(o,a),filteredKeys:t&&o.defaultFilteredValue?o.defaultFilteredValue:void 0,forceFiltered:o.filtered});"children"in o&&(n=[].concat($e(n),$e(Xn(o.children,t,a))))}),n};function ca(e,t,r,n,o,i,d,a,c){return r.map((s,m)=>{const u=qt(m,a),{filterOnClose:f=!0,filterMultiple:v=!0,filterMode:g,filterSearch:b}=s;let p=s;if(p.filters||p.filterDropdown){const y=Nt(p,u),C=n.find(x=>{let{key:w}=x;return y===w});p=Object.assign(Object.assign({},p),{title:x=>l.createElement(bc,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:p,columnKey:y,filterState:C,filterOnClose:f,filterMultiple:v,filterMode:g,filterSearch:b,triggerFilter:i,locale:o,getPopupContainer:d,rootClassName:c},Sn(s.title,x))})}return"children"in p&&(p=Object.assign(Object.assign({},p),{children:ca(e,t,p.children,n,o,i,d,u,c)})),p})}const fo=e=>{const t={};return e.forEach(r=>{let{key:n,filteredKeys:o,column:i}=r;const d=n,{filters:a,filterDropdown:c}=i;if(c)t[d]=o||null;else if(Array.isArray(o)){const s=jt(a);t[d]=s.filter(m=>o.includes(String(m)))}else t[d]=null}),t},Un=(e,t,r)=>t.reduce((o,i)=>{const{column:{onFilter:d,filters:a},filteredKeys:c}=i;return d&&c&&c.length?o.map(s=>Object.assign({},s)).filter(s=>c.some(m=>{const u=jt(a),f=u.findIndex(g=>String(g)===String(m)),v=f!==-1?u[f]:m;return s[r]&&(s[r]=Un(s[r],t,r)),d(v,s)})):o},e),da=e=>e.flatMap(t=>"children"in t?[t].concat($e(da(t.children||[]))):[t]),xc=e=>{const{prefixCls:t,dropdownPrefixCls:r,mergedColumns:n,onFilterChange:o,getPopupContainer:i,locale:d,rootClassName:a}=e;er();const c=l.useMemo(()=>da(n||[]),[n]),[s,m]=l.useState(()=>Xn(c,!0)),u=l.useMemo(()=>{const b=Xn(c,!1);if(b.length===0)return b;let p=!0;if(b.forEach(y=>{let{filteredKeys:C}=y;C!==void 0&&(p=!1)}),p){const y=(c||[]).map((C,x)=>Nt(C,qt(x)));return s.filter(C=>{let{key:x}=C;return y.includes(x)}).map(C=>{const x=c[y.findIndex(w=>w===C.key)];return Object.assign(Object.assign({},C),{column:Object.assign(Object.assign({},C.column),x),forceFiltered:x.filtered})})}return b},[c,s]),f=l.useMemo(()=>fo(u),[u]),v=b=>{const p=u.filter(y=>{let{key:C}=y;return C!==b.key});p.push(b),m(p),o(fo(p),p)};return[b=>ca(t,r,b,u,d,v,i,void 0,a),u,f]},Cc=(e,t,r)=>{const n=l.useRef({});function o(i){var d;if(!n.current||n.current.data!==e||n.current.childrenColumnName!==t||n.current.getRowKey!==r){let c=function(s){s.forEach((m,u)=>{const f=r(m,u);a.set(f,m),m&&typeof m=="object"&&t in m&&c(m[t]||[])})};const a=new Map;c(e),n.current={data:e,childrenColumnName:t,kvMap:a,getRowKey:r}}return(d=n.current.kvMap)===null||d===void 0?void 0:d.get(i)}return[o]};var Sc=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const ua=10;function wc(e,t){const r={current:e.current,pageSize:e.pageSize};return Object.keys(t&&typeof t=="object"?t:{}).forEach(o=>{const i=e[o];typeof i!="function"&&(r[o]=i)}),r}function Ec(e,t,r){const n=r&&typeof r=="object"?r:{},{total:o=0}=n,i=Sc(n,["total"]),[d,a]=l.useState(()=>({current:"defaultCurrent"in i?i.defaultCurrent:1,pageSize:"defaultPageSize"in i?i.defaultPageSize:ua})),c=Po(d,i,{total:o>0?o:e}),s=Math.ceil((o||e)/c.pageSize);c.current>s&&(c.current=s||1);const m=(f,v)=>{a({current:f??1,pageSize:v||c.pageSize})},u=(f,v)=>{var g;r&&((g=r.onChange)===null||g===void 0||g.call(r,f,v)),m(f,v),t(f,v||(c==null?void 0:c.pageSize))};return r===!1?[{},()=>{}]:[Object.assign(Object.assign({},c),{onChange:u}),m]}const mn="ascend",Mn="descend",gn=e=>typeof e.sorter=="object"&&typeof e.sorter.multiple=="number"?e.sorter.multiple:!1,mo=e=>typeof e=="function"?e:e&&typeof e=="object"&&e.compare?e.compare:!1,$c=(e,t)=>t?e[e.indexOf(t)+1]:e[0],Gn=(e,t,r)=>{let n=[];const o=(i,d)=>{n.push({column:i,key:Nt(i,d),multiplePriority:gn(i),sortOrder:i.sortOrder})};return(e||[]).forEach((i,d)=>{const a=qt(d,r);i.children?("sortOrder"in i&&o(i,a),n=[].concat($e(n),$e(Gn(i.children,t,a)))):i.sorter&&("sortOrder"in i?o(i,a):t&&i.defaultSortOrder&&n.push({column:i,key:Nt(i,a),multiplePriority:gn(i),sortOrder:i.defaultSortOrder}))}),n},fa=(e,t,r,n,o,i,d,a)=>(t||[]).map((s,m)=>{const u=qt(m,a);let f=s;if(f.sorter){const v=f.sortDirections||o,g=f.showSorterTooltip===void 0?d:f.showSorterTooltip,b=Nt(f,u),p=r.find(h=>{let{key:K}=h;return K===b}),y=p?p.sortOrder:null,C=$c(v,y);let x;if(s.sortIcon)x=s.sortIcon({sortOrder:y});else{const h=v.includes(mn)&&l.createElement(Zi,{className:Y(`${e}-column-sorter-up`,{active:y===mn})}),K=v.includes(Mn)&&l.createElement(Yi,{className:Y(`${e}-column-sorter-down`,{active:y===Mn})});x=l.createElement("span",{className:Y(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!!(h&&K)})},l.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},h,K))}const{cancelSort:w,triggerAsc:S,triggerDesc:E}=i||{};let I=w;C===Mn?I=E:C===mn&&(I=S);const P=typeof g=="object"?Object.assign({title:I},g):{title:I};f=Object.assign(Object.assign({},f),{className:Y(f.className,{[`${e}-column-sort`]:y}),title:h=>{const K=`${e}-column-sorters`,z=l.createElement("span",{className:`${e}-column-title`},Sn(s.title,h)),R=l.createElement("div",{className:K},z,x);return g?typeof g!="boolean"&&(g==null?void 0:g.target)==="sorter-icon"?l.createElement("div",{className:`${K} ${e}-column-sorters-tooltip-target-sorter`},z,l.createElement(Pr,Object.assign({},P),x)):l.createElement(Pr,Object.assign({},P),R):R},onHeaderCell:h=>{var K;const z=((K=s.onHeaderCell)===null||K===void 0?void 0:K.call(s,h))||{},R=z.onClick,k=z.onKeyDown;z.onClick=O=>{n({column:s,key:b,sortOrder:C,multiplePriority:gn(s)}),R==null||R(O)},z.onKeyDown=O=>{O.keyCode===et.ENTER&&(n({column:s,key:b,sortOrder:C,multiplePriority:gn(s)}),k==null||k(O))};const N=Fs(s.title,{}),$=N==null?void 0:N.toString();return y&&(z["aria-sort"]=y==="ascend"?"ascending":"descending"),z["aria-label"]=$||"",z.className=Y(z.className,`${e}-column-has-sorters`),z.tabIndex=0,s.ellipsis&&(z.title=(N??"").toString()),z}})}return"children"in f&&(f=Object.assign(Object.assign({},f),{children:fa(e,f.children,r,n,o,i,d,u)})),f}),vo=e=>{const{column:t,sortOrder:r}=e;return{column:t,order:r,field:t.dataIndex,columnKey:t.key}},po=e=>{const t=e.filter(r=>{let{sortOrder:n}=r;return n}).map(vo);if(t.length===0&&e.length){const r=e.length-1;return Object.assign(Object.assign({},vo(e[r])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},Yn=(e,t,r)=>{const n=t.slice().sort((d,a)=>a.multiplePriority-d.multiplePriority),o=e.slice(),i=n.filter(d=>{let{column:{sorter:a},sortOrder:c}=d;return mo(a)&&c});return i.length?o.sort((d,a)=>{for(let c=0;c<i.length;c+=1){const s=i[c],{column:{sorter:m},sortOrder:u}=s,f=mo(m);if(f&&u){const v=f(d,a,u);if(v!==0)return u===mn?v:-v}}return 0}).map(d=>{const a=d[r];return a?Object.assign(Object.assign({},d),{[r]:Yn(a,t,r)}):d}):o},Nc=e=>{const{prefixCls:t,mergedColumns:r,sortDirections:n,tableLocale:o,showSorterTooltip:i,onSorterChange:d}=e,[a,c]=l.useState(Gn(r,!0)),s=(b,p)=>{const y=[];return b.forEach((C,x)=>{const w=qt(x,p);if(y.push(Nt(C,w)),Array.isArray(C.children)){const S=s(C.children,w);y.push.apply(y,$e(S))}}),y},m=l.useMemo(()=>{let b=!0;const p=Gn(r,!1);if(!p.length){const w=s(r);return a.filter(S=>{let{key:E}=S;return w.includes(E)})}const y=[];function C(w){b?y.push(w):y.push(Object.assign(Object.assign({},w),{sortOrder:null}))}let x=null;return p.forEach(w=>{x===null?(C(w),w.sortOrder&&(w.multiplePriority===!1?b=!1:x=!0)):(x&&w.multiplePriority!==!1||(b=!1),C(w))}),y},[r,a]),u=l.useMemo(()=>{var b,p;const y=m.map(C=>{let{column:x,sortOrder:w}=C;return{column:x,order:w}});return{sortColumns:y,sortColumn:(b=y[0])===null||b===void 0?void 0:b.column,sortOrder:(p=y[0])===null||p===void 0?void 0:p.order}},[m]),f=b=>{let p;b.multiplePriority===!1||!m.length||m[0].multiplePriority===!1?p=[b]:p=[].concat($e(m.filter(y=>{let{key:C}=y;return C!==b.key})),[b]),c(p),d(po(p),p)};return[b=>fa(t,b,m,f,n,o,i),m,u,()=>po(m)]},ma=(e,t)=>e.map(n=>{const o=Object.assign({},n);return o.title=Sn(n.title,t),"children"in o&&(o.children=ma(o.children,t)),o}),Rc=e=>[l.useCallback(r=>ma(r,e),[e])],Ic=ea((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),kc=na((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),Oc=e=>{const{componentCls:t,lineWidth:r,lineType:n,tableBorderColor:o,tableHeaderBg:i,tablePaddingVertical:d,tablePaddingHorizontal:a,calc:c}=e,s=`${A(r)} ${n} ${o}`,m=(u,f,v)=>({[`&${t}-${u}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${A(c(f).mul(-1).equal())}
              ${A(c(c(v).add(r)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:s,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:s,borderTop:s,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:s},"> thead":{"> tr:not(:last-child) > th":{borderBottom:s},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:s}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${A(c(d).mul(-1).equal())} ${A(c(c(a).add(r)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:r,bottom:0,borderInlineEnd:s,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},m("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),m("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:s,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${A(r)} 0 ${A(r)} ${i}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:s}}}},Kc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},Ra),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},Pc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}},Tc=e=>{const{componentCls:t,antCls:r,motionDurationSlow:n,lineWidth:o,paddingXS:i,lineType:d,tableBorderColor:a,tableExpandIconBg:c,tableExpandColumnWidth:s,borderRadius:m,tablePaddingVertical:u,tablePaddingHorizontal:f,tableExpandedRowBg:v,paddingXXS:g,expandIconMarginTop:b,expandIconSize:p,expandIconHalfInner:y,expandIconScale:C,calc:x}=e,w=`${A(o)} ${d} ${a}`,S=x(g).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:s},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},Ia(e)),{position:"relative",float:"left",width:p,height:p,color:"inherit",lineHeight:A(p),background:c,border:w,borderRadius:m,transform:`scale(${C})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${n} ease-out`,content:'""'},"&::before":{top:y,insetInlineEnd:S,insetInlineStart:S,height:o},"&::after":{top:S,bottom:S,insetInlineStart:y,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:b,marginInlineEnd:i},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:v}},[`${r}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${A(x(u).mul(-1).equal())} ${A(x(f).mul(-1).equal())}`,padding:`${A(u)} ${A(f)}`}}}},Dc=e=>{const{componentCls:t,antCls:r,iconCls:n,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:i,paddingXXS:d,paddingXS:a,colorText:c,lineWidth:s,lineType:m,tableBorderColor:u,headerIconColor:f,fontSizeSM:v,tablePaddingHorizontal:g,borderRadius:b,motionDurationSlow:p,colorTextDescription:y,colorPrimary:C,tableHeaderFilterActiveBg:x,colorTextDisabled:w,tableFilterDropdownBg:S,tableFilterDropdownHeight:E,controlItemBgHover:I,controlItemBgActive:P,boxShadowSecondary:h,filterDropdownMenuBg:K,calc:z}=e,R=`${r}-dropdown`,k=`${t}-filter-dropdown`,N=`${r}-tree`,$=`${A(s)} ${m} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:z(d).mul(-1).equal(),marginInline:`${A(d)} ${A(z(g).div(2).mul(-1).equal())}`,padding:`0 ${A(d)}`,color:f,fontSize:v,borderRadius:b,cursor:"pointer",transition:`all ${p}`,"&:hover":{color:y,background:x},"&.active":{color:C}}}},{[`${r}-dropdown`]:{[k]:Object.assign(Object.assign({},sn(e)),{minWidth:o,backgroundColor:S,borderRadius:b,boxShadow:h,overflow:"hidden",[`${R}-menu`]:{maxHeight:E,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:K,"&:empty::after":{display:"block",padding:`${A(a)} 0`,color:w,fontSize:v,textAlign:"center",content:'"Not Found"'}},[`${k}-tree`]:{paddingBlock:`${A(a)} 0`,paddingInline:a,[N]:{padding:0},[`${N}-treenode ${N}-node-content-wrapper:hover`]:{backgroundColor:I},[`${N}-treenode-checkbox-checked ${N}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:P}}},[`${k}-search`]:{padding:a,borderBottom:$,"&-input":{input:{minWidth:i},[n]:{color:w}}},[`${k}-checkall`]:{width:"100%",marginBottom:d,marginInlineStart:d},[`${k}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${A(z(a).sub(s).equal())} ${A(a)}`,overflow:"hidden",borderTop:$}})}},{[`${r}-dropdown ${k}, ${k}-submenu`]:{[`${r}-checkbox-wrapper + span`]:{paddingInlineStart:a,color:c},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},Mc=e=>{const{componentCls:t,lineWidth:r,colorSplit:n,motionDurationSlow:o,zIndexTableFixed:i,tableBg:d,zIndexTableSticky:a,calc:c}=e,s=n;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:i,background:d},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:c(r).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:c(r).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:c(a).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${s}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},zc=e=>{const{componentCls:t,antCls:r,margin:n}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${r}-pagination`]:{margin:`${A(n)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},Bc=e=>{const{componentCls:t,tableRadius:r}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${A(r)} ${A(r)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:r,borderStartEndRadius:r,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:r},"> *:last-child":{borderStartEndRadius:r}}},"&-footer":{borderRadius:`0 0 ${A(r)} ${A(r)}`}}}}},Lc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},_c=e=>{const{componentCls:t,antCls:r,iconCls:n,fontSizeIcon:o,padding:i,paddingXS:d,headerIconColor:a,headerIconHoverColor:c,tableSelectionColumnWidth:s,tableSelectedRowBg:m,tableSelectedRowHoverBg:u,tableRowHoverBg:f,tablePaddingHorizontal:v,calc:g}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:s,[`&${t}-selection-col-with-dropdown`]:{width:g(s).add(o).add(g(i).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:g(s).add(g(d).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:g(s).add(o).add(g(i).div(4)).add(g(d).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${r}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:g(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:A(g(v).div(4).equal()),[n]:{color:a,fontSize:o,verticalAlign:"baseline","&:hover":{color:c}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:m,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:f}}}}}},Hc=e=>{const{componentCls:t,tableExpandColumnWidth:r,calc:n}=e,o=(i,d,a,c)=>({[`${t}${t}-${i}`]:{fontSize:c,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${A(d)} ${A(a)}`},[`${t}-filter-trigger`]:{marginInlineEnd:A(n(a).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${A(n(d).mul(-1).equal())} ${A(n(a).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:A(n(d).mul(-1).equal()),marginInline:`${A(n(r).sub(a).equal())} ${A(n(a).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:A(n(a).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),o("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},jc=e=>{const{componentCls:t,marginXXS:r,fontSizeIcon:n,headerIconColor:o,headerIconHoverColor:i}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:r,color:o,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:n,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:i}}}},Ac=e=>{const{componentCls:t,opacityLoading:r,tableScrollThumbBg:n,tableScrollThumbBgHover:o,tableScrollThumbSize:i,tableScrollBg:d,zIndexTableSticky:a,stickyScrollBarBorderRadius:c,lineWidth:s,lineType:m,tableBorderColor:u}=e,f=`${A(s)} ${m} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:a,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${A(i)} !important`,zIndex:a,display:"flex",alignItems:"center",background:d,borderTop:f,opacity:r,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:i,backgroundColor:n,borderRadius:c,transition:`all ${e.motionDurationSlow}, transform none`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},go=e=>{const{componentCls:t,lineWidth:r,tableBorderColor:n,calc:o}=e,i=`${A(r)} ${e.lineType} ${n}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:i}}},[`div${t}-summary`]:{boxShadow:`0 ${A(o(r).mul(-1).equal())} 0 ${n}`}}}},Fc=e=>{const{componentCls:t,motionDurationMid:r,lineWidth:n,lineType:o,tableBorderColor:i,calc:d}=e,a=`${A(n)} ${o} ${i}`,c=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`
            & > ${t}-row, 
            & > div:not(${t}-row) > ${t}-row
          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:a,transition:`background ${r}`},[`${t}-expanded-row`]:{[`${c}${c}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${A(n)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:a,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:a,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:d(n).mul(-1).equal(),borderInlineStart:a}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:a,borderBottom:a}}}}}},Wc=e=>{const{componentCls:t,fontWeightStrong:r,tablePaddingVertical:n,tablePaddingHorizontal:o,tableExpandColumnWidth:i,lineWidth:d,lineType:a,tableBorderColor:c,tableFontSize:s,tableBg:m,tableRadius:u,tableHeaderTextColor:f,motionDurationMid:v,tableHeaderBg:g,tableHeaderCellSplitColor:b,tableFooterTextColor:p,tableFooterBg:y,calc:C}=e,x=`${A(d)} ${a} ${c}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},ka()),{[t]:Object.assign(Object.assign({},sn(e)),{fontSize:s,background:m,borderRadius:`${A(u)} ${A(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${A(u)} ${A(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${A(n)} ${A(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${A(n)} ${A(o)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:f,fontWeight:r,textAlign:"start",background:g,borderBottom:x,transition:`background ${v} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:b,transform:"translateY(-50%)",transition:`background-color ${v}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${v}, border-color ${v}`,borderBottom:x,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:A(C(n).mul(-1).equal()),marginInline:`${A(C(i).sub(o).equal())}
                ${A(C(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:f,fontWeight:r,textAlign:"start",background:g,borderBottom:x,transition:`background ${v} ease`}}},[`${t}-footer`]:{padding:`${A(n)} ${A(o)}`,color:p,background:y}})}},Vc=e=>{const{colorFillAlter:t,colorBgContainer:r,colorTextHeading:n,colorFillSecondary:o,colorFillContent:i,controlItemBgActive:d,controlItemBgActiveHover:a,padding:c,paddingSM:s,paddingXS:m,colorBorderSecondary:u,borderRadiusLG:f,controlHeight:v,colorTextPlaceholder:g,fontSize:b,fontSizeSM:p,lineHeight:y,lineWidth:C,colorIcon:x,colorIconHover:w,opacityLoading:S,controlInteractiveSize:E}=e,I=new Zt(o).onBackground(r).toHexString(),P=new Zt(i).onBackground(r).toHexString(),h=new Zt(t).onBackground(r).toHexString(),K=new Zt(x),z=new Zt(w),R=E/2-C,k=R*2+C*3;return{headerBg:h,headerColor:n,headerSortActiveBg:I,headerSortHoverBg:P,bodySortBg:h,rowHoverBg:h,rowSelectedBg:d,rowSelectedHoverBg:a,rowExpandedBg:t,cellPaddingBlock:c,cellPaddingInline:c,cellPaddingBlockMD:s,cellPaddingInlineMD:m,cellPaddingBlockSM:m,cellPaddingInlineSM:m,borderColor:u,headerBorderRadius:f,footerBg:h,footerColor:n,cellFontSize:b,cellFontSizeMD:b,cellFontSizeSM:b,headerSplitColor:u,fixedHeaderSortActiveBg:I,headerFilterHoverBg:i,filterDropdownMenuBg:r,filterDropdownBg:r,expandIconBg:r,selectionColumnWidth:v,stickyScrollBarBg:g,stickyScrollBarBorderRadius:100,expandIconMarginTop:(b*y-C*3)/2-Math.ceil((p*1.4-C*3)/2),headerIconColor:K.clone().setA(K.a*S).toRgbString(),headerIconHoverColor:z.clone().setA(z.a*S).toRgbString(),expandIconHalfInner:R,expandIconSize:k,expandIconScale:E/k}},ho=2,qc=yn("Table",e=>{const{colorTextHeading:t,colorSplit:r,colorBgContainer:n,controlInteractiveSize:o,headerBg:i,headerColor:d,headerSortActiveBg:a,headerSortHoverBg:c,bodySortBg:s,rowHoverBg:m,rowSelectedBg:u,rowSelectedHoverBg:f,rowExpandedBg:v,cellPaddingBlock:g,cellPaddingInline:b,cellPaddingBlockMD:p,cellPaddingInlineMD:y,cellPaddingBlockSM:C,cellPaddingInlineSM:x,borderColor:w,footerBg:S,footerColor:E,headerBorderRadius:I,cellFontSize:P,cellFontSizeMD:h,cellFontSizeSM:K,headerSplitColor:z,fixedHeaderSortActiveBg:R,headerFilterHoverBg:k,filterDropdownBg:N,expandIconBg:$,selectionColumnWidth:O,stickyScrollBarBg:D,calc:_}=e,M=bn(e,{tableFontSize:P,tableBg:n,tableRadius:I,tablePaddingVertical:g,tablePaddingHorizontal:b,tablePaddingVerticalMiddle:p,tablePaddingHorizontalMiddle:y,tablePaddingVerticalSmall:C,tablePaddingHorizontalSmall:x,tableBorderColor:w,tableHeaderTextColor:d,tableHeaderBg:i,tableFooterTextColor:E,tableFooterBg:S,tableHeaderCellSplitColor:z,tableHeaderSortBg:a,tableHeaderSortHoverBg:c,tableBodySortBg:s,tableFixedHeaderSortActiveBg:R,tableHeaderFilterActiveBg:k,tableFilterDropdownBg:N,tableRowHoverBg:m,tableSelectedRowBg:u,tableSelectedRowHoverBg:f,zIndexTableFixed:ho,zIndexTableSticky:_(ho).add(1).equal({unit:!1}),tableFontSizeMiddle:h,tableFontSizeSmall:K,tableSelectionColumnWidth:O,tableExpandIconBg:$,tableExpandColumnWidth:_(o).add(_(e.padding).mul(2)).equal(),tableExpandedRowBg:v,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:D,tableScrollThumbBgHover:t,tableScrollBg:r});return[Wc(M),zc(M),go(M),jc(M),Dc(M),Oc(M),Bc(M),Tc(M),go(M),Pc(M),_c(M),Mc(M),Ac(M),Kc(M),Hc(M),Lc(M),Fc(M)]},Vc,{unitless:{expandIconScale:!0}}),Xc=[],Uc=(e,t)=>{var r,n;const{prefixCls:o,className:i,rootClassName:d,style:a,size:c,bordered:s,dropdownPrefixCls:m,dataSource:u,pagination:f,rowSelection:v,rowKey:g="key",rowClassName:b,columns:p,children:y,childrenColumnName:C,onChange:x,getPopupContainer:w,loading:S,expandIcon:E,expandable:I,expandedRowRender:P,expandIconColumnIndex:h,indentSize:K,scroll:z,sortDirections:R,locale:k,showSorterTooltip:N={target:"full-header"},virtual:$}=e;er();const O=l.useMemo(()=>p||lr(y),[p,y]),D=l.useMemo(()=>O.some(ie=>ie.responsive),[O]),_=Ro(D),M=l.useMemo(()=>{const ie=new Set(Object.keys(_).filter(ce=>_[ce]));return O.filter(ce=>!ce.responsive||ce.responsive.some(Ke=>ie.has(Ke)))},[O,_]),G=Co(e,["className","style","columns"]),{locale:ee=Oa,direction:ye,table:oe,renderEmpty:pe,getPrefixCls:xe,getPopupContainer:ue}=l.useContext(hn),J=wo(c),te=Object.assign(Object.assign({},ee.Table),k),Ce=u||Xc,le=xe("table",o),U=xe("dropdown",m),[,j]=Jn(),H=Va(le),[q,Z,F]=qc(le,H),ne=Object.assign(Object.assign({childrenColumnName:C,expandIconColumnIndex:h},I),{expandIcon:(r=I==null?void 0:I.expandIcon)!==null&&r!==void 0?r:(n=oe==null?void 0:oe.expandable)===null||n===void 0?void 0:n.expandIcon}),{childrenColumnName:Ie="children"}=ne,We=l.useMemo(()=>Ce.some(ie=>ie==null?void 0:ie[Ie])?"nest":P||I!=null&&I.expandedRowRender?"row":null,[Ce]),ke={body:l.useRef(null)},B=As(le),V=l.useRef(null),ae=l.useRef(null);Hs(t,()=>Object.assign(Object.assign({},ae.current),{nativeElement:V.current}));const fe=l.useMemo(()=>typeof g=="function"?g:ie=>ie==null?void 0:ie[g],[g]),[me]=Cc(Ce,Ie,fe),Oe={},ze=function(ie,ce){let Ke=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;var Be,Xe,at,it;const Ve=Object.assign(Object.assign({},Oe),ie);Ke&&((Be=Oe.resetPagination)===null||Be===void 0||Be.call(Oe),!((Xe=Ve.pagination)===null||Xe===void 0)&&Xe.current&&(Ve.pagination.current=1),f&&((at=f.onChange)===null||at===void 0||at.call(f,1,(it=Ve.pagination)===null||it===void 0?void 0:it.pageSize))),z&&z.scrollToFirstRowOnChange!==!1&&ke.body.current&&ti(0,{getContainer:()=>ke.body.current}),x==null||x(Ve.pagination,Ve.filters,Ve.sorter,{currentDataSource:Un(Yn(Ce,Ve.sorterStates,Ie),Ve.filterStates,Ie),action:ce})},je=(ie,ce)=>{ze({sorter:ie,sorterStates:ce},"sort",!1)},[ge,ve,Q,L]=Nc({prefixCls:le,mergedColumns:M,onSorterChange:je,sortDirections:R||["ascend","descend"],tableLocale:te,showSorterTooltip:N}),Ne=l.useMemo(()=>Yn(Ce,ve,Ie),[Ce,ve]);Oe.sorter=L(),Oe.sorterStates=ve;const Te=(ie,ce)=>{ze({filters:ie,filterStates:ce},"filter",!0)},[se,Pe,Se]=xc({prefixCls:le,locale:te,dropdownPrefixCls:U,mergedColumns:M,onFilterChange:Te,getPopupContainer:w||ue,rootClassName:Y(d,H)}),Le=Un(Ne,Pe,Ie);Oe.filters=Se,Oe.filterStates=Pe;const Ae=l.useMemo(()=>{const ie={};return Object.keys(Se).forEach(ce=>{Se[ce]!==null&&(ie[ce]=Se[ce])}),Object.assign(Object.assign({},Q),{filters:ie})},[Q,Se]),[Ue]=Rc(Ae),vt=(ie,ce)=>{ze({pagination:Object.assign(Object.assign({},Oe.pagination),{current:ie,pageSize:ce})},"paginate")},[De,xt]=Ec(Le.length,vt,f);Oe.pagination=f===!1?{}:wc(De,f),Oe.resetPagination=xt;const Ct=l.useMemo(()=>{if(f===!1||!De.pageSize)return Le;const{current:ie=1,total:ce,pageSize:Ke=ua}=De;return Le.length<ce?Le.length>Ke?Le.slice((ie-1)*Ke,ie*Ke):Le:Le.slice((ie-1)*Ke,ie*Ke)},[!!f,Le,De==null?void 0:De.current,De==null?void 0:De.pageSize,De==null?void 0:De.total]),[Ge,nt]=Ls({prefixCls:le,data:Le,pageData:Ct,getRowKey:fe,getRecordByKey:me,expandType:We,childrenColumnName:Ie,locale:te,getPopupContainer:w||ue},v),rt=(ie,ce,Ke)=>{let Be;return typeof b=="function"?Be=Y(b(ie,ce,Ke)):Be=Y(b),Y({[`${le}-row-selected`]:nt.has(fe(ie,ce))},Be)};ne.__PARENT_RENDER_ICON__=ne.expandIcon,ne.expandIcon=ne.expandIcon||E||js(te),We==="nest"&&ne.expandIconColumnIndex===void 0?ne.expandIconColumnIndex=v?1:0:ne.expandIconColumnIndex>0&&v&&(ne.expandIconColumnIndex-=1),typeof ne.indentSize!="number"&&(ne.indentSize=typeof K=="number"?K:15);const Ye=l.useCallback(ie=>Ue(Ge(se(ge(ie)))),[ge,se,Ge]);let qe,Je;if(f!==!1&&(De!=null&&De.total)){let ie;De.size?ie=De.size:ie=J==="small"||J==="middle"?"small":void 0;const ce=Xe=>l.createElement(Pi,Object.assign({},De,{className:Y(`${le}-pagination ${le}-pagination-${Xe}`,De.className),size:ie})),Ke=ye==="rtl"?"left":"right",{position:Be}=De;if(Be!==null&&Array.isArray(Be)){const Xe=Be.find(Ve=>Ve.includes("top")),at=Be.find(Ve=>Ve.includes("bottom")),it=Be.every(Ve=>`${Ve}`=="none");!Xe&&!at&&!it&&(Je=ce(Ke)),Xe&&(qe=ce(Xe.toLowerCase().replace("top",""))),at&&(Je=ce(at.toLowerCase().replace("bottom","")))}else Je=ce(Ke)}let Me;typeof S=="boolean"?Me={spinning:S}:typeof S=="object"&&(Me=Object.assign({spinning:!0},S));const _e=Y(F,H,`${le}-wrapper`,oe==null?void 0:oe.className,{[`${le}-wrapper-rtl`]:ye==="rtl"},i,d,Z),ot=Object.assign(Object.assign({},oe==null?void 0:oe.style),a),Xt=typeof(k==null?void 0:k.emptyText)<"u"?k.emptyText:(pe==null?void 0:pe("Table"))||l.createElement(Ya,{componentName:"Table"}),Ut=$?kc:Ic,ht={},Gt=l.useMemo(()=>{const{fontSize:ie,lineHeight:ce,lineWidth:Ke,padding:Be,paddingXS:Xe,paddingSM:at}=j,it=Math.floor(ie*ce);switch(J){case"middle":return at*2+it+Ke;case"small":return Xe*2+it+Ke;default:return Be*2+it+Ke}},[j,J]);return $&&(ht.listItemHeight=Gt),q(l.createElement("div",{ref:V,className:_e,style:ot},l.createElement(Lo,Object.assign({spinning:!1},Me),qe,l.createElement(Ut,Object.assign({},ht,G,{ref:ae,columns:M,direction:ye,expandable:ne,prefixCls:le,className:Y({[`${le}-middle`]:J==="middle",[`${le}-small`]:J==="small",[`${le}-bordered`]:s,[`${le}-empty`]:Ce.length===0},F,H,Z),data:Ct,rowKey:fe,rowClassName:rt,emptyText:Xt,internalHooks:dn,internalRefs:ke,transformColumns:Ye,getContainerWidth:B})),Je)))},Gc=l.forwardRef(Uc),Yc=(e,t)=>{const r=l.useRef(0);return r.current+=1,l.createElement(Gc,Object.assign({},e,{ref:t,_renderTimes:r.current}))},Rt=l.forwardRef(Yc);Rt.SELECTION_COLUMN=wt;Rt.EXPAND_COLUMN=bt;Rt.SELECTION_ALL=Fn;Rt.SELECTION_INVERT=Wn;Rt.SELECTION_NONE=Vn;Rt.Column=Rs;Rt.ColumnGroup=Is;Rt.Summary=Fo;const nd=e=>Array.isArray(e)&&e.length>0;export{Rt as F,nd as i};
