import{a as re,s as $,r as u,u as T,j as e,C,L as b,M as w,R as E,B as y,b as ae,c as ce,d as de,e as ue,f as J,T as L,g as pe,h as v,S as G,F as I,I as O,i as M,k as ee,D as j,l as me,E as te,A as xe,m as ne,n as he,o as D,p as ge,q as fe}from"./vendor-CKw6qmF3.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))o(i);new MutationObserver(i=>{for(const p of i)if(p.type==="childList")for(const d of p.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function s(i){const p={};return i.integrity&&(p.integrity=i.integrity),i.referrerPolicy&&(p.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?p.credentials="include":i.crossOrigin==="anonymous"?p.credentials="omit":p.credentials="same-origin",p}function o(i){if(i.ep)return;i.ep=!0;const p=s(i);fetch(i.href,p)}})();const je=()=>{let t="";for(let n=1;n<=32;n++){let s=Math.floor(Math.random()*16).toString(16);t+=s,(n==8||n==12||n==16||n==20)&&(t+="-")}return t};function ye(t){let n,s=[],o,i;for(n=0;n<t.length;n++)(o=t.charCodeAt(n))<128?s.push(o):o<2048?s.push(192+(o>>6&31),128+(o&63)):((i=o^55296)>>10==0?(o=(i<<10)+(t.charCodeAt(++n)^56320)+65536,s.push(240+(o>>18&7),128+(o>>12&63))):s.push(224+(o>>12&15)),s.push(128+(o>>6&63),128+(o&63)));return s}const Q=t=>{let n=new Uint8Array(ye(t)),s,o,i,p=(n.length+8>>>6<<4)+16,d=new Uint8Array(p<<2);for(d.set(new Uint8Array(n.buffer)),t=new Uint32Array(d.buffer),i=new DataView(t.buffer),s=0;s<p;s++)t[s]=i.getUint32(s<<2);t[n.length>>2]|=128<<24-(n.length&3)*8,t[p-1]=n.length<<3;let c=[],l=[function(){return a[1]&a[2]|~a[1]&a[3]},function(){return a[1]^a[2]^a[3]},function(){return a[1]&a[2]|a[1]&a[3]|a[2]&a[3]},function(){return a[1]^a[2]^a[3]}],m=function(f,x){return f<<x|f>>>32-x},h=[1518500249,1859775393,-1894007588,-899497514],a=[1732584193,-271733879,null,null,-1009589776];for(a[2]=~a[0],a[3]=~a[1],s=0;s<t.length;s+=16){let f=a.slice(0);for(o=0;o<80;o++)c[o]=o<16?t[s+o]:m(c[o-3]^c[o-8]^c[o-14]^c[o-16],1),i=m(a[0],5)+l[o/20|0]()+a[4]+c[o]+h[o/20|0]|0,a[1]=m(a[1],30),a.pop(),a.unshift(i);for(o=0;o<5;o++)a[o]=a[o]+f[o]|0}i=new DataView(new Uint32Array(a).buffer);for(let f=0;f<5;f++)a[f]=i.getUint32(f<<2);return Array.prototype.map.call(new Uint8Array(new Uint32Array(a).buffer),function(f){return(f<16?"0":"")+f.toString(16)}).join("")},be="http://test1.fengxuan.cn",Ie=je(),Se=new Date().getTime();Q(Q("1D3CB8DECCD51d1e04b7d64fff82e40C0BD6251C44E036C8C56a3f5f5aa9e9b1&"+Ie+"&"+Se));const B=re.create({baseURL:be,timeout:1e4,headers:{"Content-Type":"application/x-www-form-urlencoded","X-Requested-With":"XMLHttpRequest"}});B.interceptors.request.use(t=>{const n=localStorage.getItem("access_token");return console.log("access_token",n),n&&(t.headers.Authorization="Bearer "+n),t},t=>Promise.reject(t));B.interceptors.response.use(t=>t.data,t=>($.error("服务端无响应，请刷新重试！"),Promise.reject(t)));const P={get(t,n){return B.get(t,n)},put(t,n){return B.put(t,n)},post(t,n){return B.post(t,n)},delete(t,n){return B.delete(t,n)}},Z=t=>P.post("/Booking/GetBookingListByPid",t),ke=t=>P.post("/Booking/SaveBooking",t),ve=t=>P.post("/Booking/EnableBooking",t),W=t=>P.post("/Patient/GetGetPatientByUid",t),Pe=t=>P.post("/Patient/SavePatient",t),De=t=>P.post("/Patient/DelPatient",t),we=t=>P.post("/User/GetUserByCode",t),Te=t=>P.post("/User/GetUserInfoByOpenid",t),Ce=t=>P.post("/Report/GetReportList",t),X=t=>{if(t){const n=new Date,s=new Date(t);let o=n.getFullYear()-s.getFullYear();const i=n.getMonth()-s.getMonth();return(i<0||i===0&&n.getDate()<s.getDate())&&o--,o+"岁"}return"0岁"},Me=[],$e=()=>{const[t,n]=u.useState(!1),[s,o]=u.useState(null),[i,p]=u.useState(Me),[d,c]=u.useState(""),[l,m]=u.useState(null),h=T(),a=new URLSearchParams(window.location.search),[g,f]=u.useState(a.get("code")),[x,k]=u.useState(a.get("openid")),[K,A]=u.useState(!1);u.useEffect(()=>{sessionStorage.removeItem("patient"),K?W({uid:localStorage.getItem("uid")}).then(r=>{if(console.log("getPatientList",r),r.code==0){let _=r.result.map(U=>({...U,age:X(U.birthday)}));p(_)}}):A(!0)},[l]),u.useEffect(()=>{x?Te({openid:x}).then(r=>{console.log("getUserInfoByOpenid",r),r.code==0&&(localStorage.setItem("openid",r.result.openid),localStorage.setItem("access_token",r.token.access_token),localStorage.setItem("uid",r.result.id),m(r.result.id))}):we({code:g}).then(r=>{console.log("getUserByCode",r),r.code==0&&(localStorage.setItem("openid",r.openid),localStorage.setItem("access_token",r.access_token),localStorage.setItem("uid",r.id),m(r.id))})},[]);const F=()=>Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15);u.useEffect(()=>{const r="wx4e8858689fca9c85",_=new Date().getTime(),U=F();wx.config({debug:!0,appId:r,timestamp:_,nonceStr:U,signature:"1D3CB8DECCD51d1e04b7d64fff82e40C0BD6251C44E036C8C56a3f5f5aa9e9b1",jsApiList:["hideOptionMenu"]}),wx.ready(function(){wx.hideOptionMenu()}),wx.error(function(S){console.error("微信 JSAPI 配置错误:",S)}),document.addEventListener("touchstart",function(S){S.touches.length>1&&S.preventDefault()},{passive:!1});let V=0;document.addEventListener("touchend",function(S){const R=Date.now();R-V<=300&&S.preventDefault(),V=R},{passive:!1});const Y=document.createElement("meta");Y.name="viewport",Y.content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",document.head.appendChild(Y);const q=new URLSearchParams(window.location.search).get("openid");if(q)k(q),console.log("OpenID from URL:",q),localStorage.setItem("openid",q);else{const S="mock_openid_"+Math.random().toString(36).substring(7);k(S),console.log("Generated Mock OpenID:",S),localStorage.setItem("openid",S)}return()=>{document.removeEventListener("touchstart",function(S){S.touches.length>1&&S.preventDefault()}),document.removeEventListener("touchend",function(S){const R=Date.now();R-V<=300&&S.preventDefault(),V=R}),document.head.removeChild(Y)}},[]);const se=[{title:"在线预约",icon:e.jsx(ae,{style:{fontSize:"24px",color:"#1890ff"}}),path:"/online-booking",requiresPatientSelection:!1},{title:"我的预约",icon:e.jsx(ce,{style:{fontSize:"24px",color:"#52c41a"}}),path:"/my-bookings",requiresPatientSelection:!0},{title:"我的报告",icon:e.jsx(de,{style:{fontSize:"24px",color:"#faad14"}}),path:"/my-reports",requiresPatientSelection:!0},{title:"就诊人管理",icon:e.jsx(E,{style:{fontSize:"24px",color:"#722ed1"}}),path:"/patient-list",requiresPatientSelection:!1},{title:"在线问诊",icon:e.jsx(ue,{style:{fontSize:"24px",color:"#52c41a"}}),path:"https://work.weixin.qq.com/kfid/",requiresPatientSelection:!1}],oe=r=>{if(r.path.startsWith("http"))window.open(r.path,"_blank");else if(r.requiresPatientSelection){if((i==null?void 0:i.length)===0){w.info({title:"请先添加就诊人",onOk:()=>h("/new-patient"),okText:"去添加"});return}else if((i==null?void 0:i.length)==1){localStorage.setItem("selectedPid",i[0].id),h(r.path);return}c(r.path),n(!0)}else h(r.path)},ie=r=>{o(r),localStorage.setItem("selectedPid",r.id),localStorage.setItem("selectedPatientName",r.name),console.log("Selected patient:",r.name,"for route:",d),n(!1),h(d)},le=()=>{n(!1),o(null),c("")};return x?e.jsxs("div",{className:"no-zoom",style:{padding:"16px"},children:[e.jsx(C,{title:"门诊档案",variant:"outlined",children:e.jsx(b,{itemLayout:"horizontal",dataSource:se,renderItem:r=>e.jsxs(b.Item,{onClick:()=>oe(r),style:{cursor:"pointer"},children:[e.jsx(b.Item.Meta,{avatar:r.icon,title:e.jsx("span",{style:{fontSize:"16px"},children:r.title})}),e.jsx("div",{children:">"})]})})}),e.jsxs(w,{title:"选择就诊人",open:t,onCancel:le,footer:null,children:[(i==null?void 0:i.length)>0?e.jsx(b,{dataSource:i,renderItem:r=>e.jsx(b.Item,{onClick:()=>ie(r),style:{cursor:"pointer"},children:e.jsx(b.Item.Meta,{avatar:e.jsx(E,{}),title:r.name,description:`性别: ${r.sex} 年龄: ${r.age}`})})}):e.jsx("p",{children:"没有绑定的就诊人信息，请先添加。"}),e.jsx(y,{type:"primary",block:!0,disabled:(i==null?void 0:i.length)>=6,onClick:()=>{n(!1),h("/new-patient")},style:{marginTop:"10px"},children:"添加新就诊人"})]})]}):e.jsx("div",{className:"no-zoom",children:"正在获取用户信息..."})},Le=[],Oe=()=>{const t=T(),[n,s]=J.useState(Le),[o,i]=J.useState(!0);u.useEffect(()=>{sessionStorage.removeItem("patient"),setTimeout(()=>{W({uid:localStorage.getItem("uid")}).then(c=>{if(console.log("getPatientList",c),c.code==0){let l=c.result.map(m=>({...m,age:X(m.birthday)}));s(l)}i(!1)})},500)},[]);const p=c=>{c.birthday=v(c.birthday).format("YYYY-MM-DD"),sessionStorage.setItem(`patient_${c.id}`,JSON.stringify(c)),t(`/patient-detail/${c.id}`)},d=()=>{t("/new-patient")};return o?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载就诊人信息中..."}):e.jsx("div",{style:{padding:"16px"},children:e.jsxs(C,{title:"就诊人列表",children:[n.length>0?e.jsx(b,{itemLayout:"horizontal",dataSource:n,renderItem:c=>e.jsx(b.Item,{onClick:()=>p(c),actions:[e.jsx("a",{children:">"},"list-edit")],children:e.jsx(b.Item.Meta,{avatar:e.jsx(E,{style:{fontSize:"24px"}}),title:e.jsx(L.Text,{strong:!0,children:c.name}),description:`性别: ${c.sex}  年龄: ${c.age} | 证件号: ${c.idcard}`})})}):e.jsx(L.Text,{children:"暂无就诊人信息"}),e.jsxs("div",{style:{marginTop:"20px",display:"flex",flexDirection:"column",gap:"10px"},children:[e.jsx(y,{type:"primary",icon:e.jsx(pe,{}),onClick:d,block:!0,style:{marginTop:"20px"},disabled:(n==null?void 0:n.length)>=6,children:"添加就诊人"}),e.jsx(y,{onClick:()=>t("/"),block:!0,children:"返回首页"})]})]})})},{Title:Xe,Paragraph:N,Text:Ke}=L,{Option:Qe}=G,Be=()=>{const t=T(),[n]=I.useForm(),[s,o]=u.useState(!1),[i,p]=u.useState(null),[d,c]=u.useState(null),[l,m]=u.useState(null);u.useEffect(()=>{const x=sessionStorage.getItem("patient");console.log("patient",x),x&&(n.setFieldsValue(JSON.parse(x)),p(JSON.parse(x).sex),c(JSON.parse(x).birthday),m(JSON.parse(x).id),console.log("sex",JSON.parse(x).sex))},[]);const h=x=>{Pe({id:l||"",uid:localStorage.getItem("uid"),name:n.getFieldValue("name"),phone:n.getFieldValue("phone"),idcard:n.getFieldValue("idcard"),sex:n.getFieldValue("sex"),birthday:n.getFieldValue("birthday")}).then(k=>{console.log("savePatient",k),w.success({title:"保存成功",content:`就诊人 ${x.name} ${l?"已编辑":"已成功添加"}。`,onOk:()=>t("/"),okText:"返回首页"})})},a=x=>{const k=x.target.value;if(n.setFieldsValue({idcard:k}),k.length===18){const A=k.charAt(16)%2===0?"女":"男";console.log("sexhandle",A),n.setFieldsValue({sex:A});const F=Ne(k);F?n.setFieldsValue({birthday:F}):n.setFieldsValue({birthday:null})}else n.setFieldsValue({sex:null})},g=()=>{o(!1)},f=()=>{o(!1)};return e.jsxs("div",{style:{padding:"16px"},children:[e.jsxs(C,{title:l?"编辑就诊人":"添加新就诊人",children:[e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#f0f8ff",borderRadius:"4px"},children:[e.jsx(N,{strong:!0,style:{fontSize:"13px"},children:"注：请确保姓名及证件号码与您证件（身份证）上的信息一致否则无法取号或取消预约，请您认真填写。"}),e.jsx(N,{style:{fontSize:"13px"},children:"同一账号最多绑定6位历史就诊人，解绑之后也无法再添加新的就诊人。"}),e.jsx(N,{style:{fontSize:"13px"},children:"我们收集您的身份证号码是为了确保医疗服务的真实性和安全性，以满足政府或法律规定的实名制要求。此信息将用于您的医疗记录、预约确认、挂号和费用结算。"})]}),e.jsxs(I,{form:n,layout:"vertical",name:"new_patient_form",onFinish:h,initialValues:{agreedToPolicy:!1,idType:"ID_CARD"},children:[e.jsx(I.Item,{name:"name",label:"就诊⼈姓名",rules:[{required:!0,message:"请输入姓名!"}],children:e.jsx(O,{placeholder:"请输入姓名"})}),e.jsx(I.Item,{name:"phone",label:"手机号码",rules:[{required:!0,message:"请输入手机号码!"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码!"}],children:e.jsx(O,{placeholder:"请输入手机号码",maxLength:11,minLength:11})}),e.jsx(I.Item,{name:"idcard",label:"居民身份证",rules:[{required:!0,message:"请输入证件号码!"},{validator:(x,k)=>!k||Re(k)?Promise.resolve():Promise.reject(new Error("居民身份证件号码格式不正确!"))}],children:e.jsx(O,{placeholder:"请输入证件号码",onChange:a,maxLength:18,minLength:18})}),e.jsx(I.Item,{name:"sex",label:"性别",rules:[{required:!0,message:"请选择性别!"}],children:e.jsxs(M.Group,{value:i,children:[e.jsx(M,{value:"男",disabled:!0,children:"男"}),e.jsx(M,{value:"女",disabled:!0,children:"女"})]})}),e.jsx(I.Item,{name:"birthday",label:"出生日期",rules:[{required:!0,message:"请输入出生日期!"}],children:e.jsx(O,{disabled:!0})}),e.jsx(I.Item,{children:e.jsx(y,{type:"primary",htmlType:"submit",block:!0,children:"保存"})}),e.jsx(I.Item,{children:e.jsx(y,{block:!0,onClick:()=>t(-1),children:"返回"})})]})]}),e.jsxs(w,{title:"法律声明及隐私权政策",visible:s,onOk:g,onCancel:f,footer:[e.jsx(y,{type:"primary",onClick:g,children:"我知道了"},"confirm")],children:[e.jsx(N,{children:"这里是法律声明和隐私权政策的详细内容..."}),e.jsx(N,{children:"请仔细阅读..."})]})]})};function Re(t){if(!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(t))return!1;const s=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],o=["1","0","X","9","8","7","6","5","4","3","2"];let i=0;for(let d=0;d<17;d++)i+=parseInt(t[d],10)*s[d];return o[i%11].toUpperCase()===t[17].toUpperCase()}const Ne=t=>{if(!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(t))return null;const s=t.slice(6,10),o=t.slice(10,12),i=t.slice(12,14);return`${s}-${o}-${i}`},{Option:Ze}=G,Ee=()=>{const t=T(),[n,s]=u.useState(null),[o,i]=u.useState(!1),[p]=I.useForm(),{id:d}=ee();u.useEffect(()=>{const l=JSON.parse(sessionStorage.getItem(`patient_${d}`));console.log("fetchedPatient",l),l?(s(l),p.setFieldsValue(l)):($.error("数据异常"),t("/"))},[p,t]);const c=()=>{w.confirm({title:"确认删除就诊人吗?",icon:e.jsx(me,{}),content:`您确定要删除就诊人 ${n==null?void 0:n.name} 吗？此操作无法撤销。`,okText:"确认删除",okType:"danger",cancelText:"取消",onOk(){console.log("Patient deleted:",n==null?void 0:n.id),De({id:n==null?void 0:n.id}).then(l=>{console.log("delPatient",l),l.code==0&&($.success("就诊人已删除"),t("/patient-list"))})},onCancel(){console.log("Delete cancelled")}})};return n?e.jsx("div",{style:{padding:"16px"},children:e.jsxs(C,{title:"就诊人信息",children:[e.jsxs(j,{variant:"outlined",column:1,size:"small",children:[e.jsx(j.Item,{label:"姓名",children:n.name}),e.jsx(j.Item,{label:"性别",children:n.sex}),e.jsx(j.Item,{label:"年龄",children:n.age}),e.jsx(j.Item,{label:"证件类型",children:"居民身份证"}),e.jsx(j.Item,{label:"证件号",children:n.idcard}),e.jsx(j.Item,{label:"手机号",children:n.phone}),e.jsx(j.Item,{label:"出生日期",children:n.birthday})]}),e.jsxs("div",{style:{marginTop:"20px",display:"flex",flexDirection:"column",gap:"10px"},children:[e.jsx(y,{type:"primary",onClick:()=>t("/new-patient"),block:!0,children:"编辑信息"}),e.jsx(y,{type:"danger",onClick:c,block:!0,children:"删除就诊人"}),e.jsx(y,{onClick:()=>t("/patient-list"),block:!0,children:"返回列表"})]})]})}):e.jsx("div",{children:"加载中..."})},{Option:et}=G,{Title:tt}=L,Ae=[],Fe=()=>{const t=T(),[n]=I.useForm(),[s,o]=u.useState(!1),[i,p]=u.useState(Ae),[d,c]=u.useState(null),l=g=>{console.log("Received values of booking form: ",g);const f={...g,pid:d?d.id:null,uid:localStorage.getItem("uid")};console.log("Booking Data to send to API:",f),ke(f).then(x=>{console.log("saveBooking",x)}),w.success({title:"预约成功",content:`您已成功为 ${(d==null?void 0:d.name)||"-"} 预约 ${g.type}，时间：${f.bookingTime||"未选"}。`,onOk:()=>t("/"),okText:"返回首页"})},m=()=>{W({uid:localStorage.getItem("uid")}).then(g=>{if(console.log("getPatientList",g),g.code==0){let f=g.result.map(x=>({...x,age:X(x.birthday),sex:x.sex=="MALE"?"男":"女"}));p(f)}}),o(!0)},h=g=>{c(g),n.setFieldsValue({patientName:g.name}),localStorage.setItem("selectedPid",g.id),o(!1)},a=()=>{o(!1)};return e.jsxs("div",{style:{padding:"16px"},children:[e.jsx(C,{title:"在线预约",children:e.jsxs(I,{form:n,layout:"vertical",name:"online_booking_form",onFinish:l,children:[e.jsx(I.Item,{name:"patientName",label:"选择就诊人",rules:[{required:!0,message:"请选择就诊人!"}],children:e.jsx(O,{placeholder:"请选择就诊人",onClick:m,readOnly:!0,addonAfter:e.jsx(E,{})})}),e.jsx(I.Item,{name:"type",label:"检查类型",rules:[{required:!0,message:"请选择检查类型!"}],children:e.jsxs(M.Group,{children:[e.jsx(M,{value:"1",style:{fontWeight:"bold",marginRight:50},children:"PET"}),e.jsx(M,{value:"2",style:{fontWeight:"bold",marginRight:50},children:"DR"}),e.jsx(M,{value:"3",style:{fontWeight:"bold"},children:"CT"})]})}),e.jsx(I.Item,{name:"bookingTime",label:"选择就诊时间",rules:[{required:!0,message:"请选择就诊时间!"}],children:e.jsx(O,{placeholder:"请选择日期和时间"})}),e.jsx(I.Item,{children:e.jsxs("div",{style:{marginTop:"20px",display:"flex",flexDirection:"column",gap:"10px"},children:[e.jsx(y,{type:"primary",htmlType:"submit",block:!0,children:"预约"}),e.jsx(y,{onClick:()=>t("/"),block:!0,children:"返回首页"})]})})]})}),e.jsxs(w,{title:"选择就诊人",visible:s,onCancel:a,footer:null,children:[(i==null?void 0:i.length)>0?e.jsx(b,{dataSource:i,renderItem:g=>e.jsx(b.Item,{onClick:()=>h(g),style:{cursor:"pointer"},children:e.jsx(b.Item.Meta,{avatar:e.jsx(E,{}),title:g.name,description:`性别: ${g.sex} 年龄: ${g.age}`})})}):e.jsx("p",{children:"没有绑定的就诊人信息，请先添加。"}),e.jsx(y,{type:"link",onClick:()=>{o(!1),t("/new-patient")},block:!0,style:{marginTop:"10px"},children:"+ 添加新就诊人"})]})]})},{Title:nt,Text:Ue}=L,Ve=()=>{const t=T(),[n,s]=u.useState([]),[o,i]=u.useState(null),[p,d]=u.useState(!0);u.useEffect(()=>{const l=localStorage.getItem("selectedPid"),m=localStorage.getItem("selectedPatientName");l?(i({id:l,name:m||"当前就诊人"}),console.log(`Fetching bookings for patient ID: ${l}`),setTimeout(()=>{Z({pid:l}).then(h=>{console.log("getBookingList",h),h.code==0&&(s(h.result||[]),d(!1))})},500)):($.warn("请先在首页选择就诊人"),t("/"),d(!1))},[t]);const c=l=>{w.confirm({title:"确认取消预约吗?",content:"此操作无法撤销。",okText:"确认取消",okType:"danger",cancelText:"返回",onOk(){console.log("Booking cancelled:",l),ve({id:l}).then(m=>{m.code==0&&($.success("预约已取消"),Z({pid}).then(h=>{console.log("getBookingList",h),h.code==0&&(s(h.result||[]),d(!1))}))})}})};return p?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载预约信息中..."}):e.jsx("div",{style:{padding:"16px"},children:e.jsxs(C,{title:o?`${o.name} - 我的预约`:"我的预约",children:[n.length>0?e.jsx(b,{itemLayout:"horizontal",dataSource:n.sort((l,m)=>v(m.bookingtime).valueOf()-v(l.bookingtime).valueOf()),renderItem:l=>{let m;switch(l.type){case 1:m="PET";break;case 2:m="DR";break;case 3:m="CT";break;default:m="其他"}return e.jsx(b.Item,{actions:[l.status==="待检查"?e.jsx(y,{type:"link",danger:!0,onClick:()=>c(l.id),children:"取消预约"}):l.status==="已取消"?e.jsx(y,{type:"link",disabled:!0,children:"已取消"}):e.jsx(y,{type:"link",disabled:!0,children:l.status})],children:e.jsx(b.Item.Meta,{title:e.jsx(Ue,{strong:!0,children:`${m}`}),description:v(l.bookingtime).format("YYYY年MM月DD日 HH:mm:ss")})})}}):e.jsx(te,{description:o?"暂无预约记录":"请先选择就诊人以查看预约"}),e.jsx(y,{type:"default",onClick:()=>t("/"),style:{marginTop:"20px"},block:!0,children:"返回首页"})]})})},{Title:st,Text:Ye}=L,qe=()=>{const t=T(),[n,s]=u.useState([]),[o,i]=u.useState(null),[p,d]=u.useState(!0);u.useEffect(()=>{const l=localStorage.getItem("selectedPid"),m=localStorage.getItem("selectedpatientName");l?(i({id:l,name:m||"当前就诊人"}),console.log(`Fetching reports for patient ID: ${l}`),setTimeout(()=>{Ce({pid:l}).then(h=>{console.log("getReport",h),h.code==0?(s(h.result||[]),d(!1)):($.error(h.msg),d(!1))})},500)):($.warn("请先在首页选择就诊人"),t("/"),d(!1))},[t]);const c=l=>{sessionStorage.setItem(`report_${l.accessionNo}`,JSON.stringify(l)),t(`/report-detail/${l.accessionNo}`)};return p?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载报告信息中..."}):e.jsx("div",{style:{padding:"16px"},children:e.jsxs(C,{title:o?`${o.name} - 我的报告`:"我的报告",children:[n.length>0?e.jsx(b,{itemLayout:"horizontal",dataSource:n.sort((l,m)=>v(m.studyTime).valueOf()-v(l.studyTime).valueOf()),renderItem:l=>e.jsx(b.Item,{onClick:()=>c(l),actions:[e.jsx("a",{children:">"},`view-${l.id}`)],children:e.jsx(b.Item.Meta,{title:e.jsx(Ye,{strong:!0,children:l.procedureName}),description:e.jsxs("div",{children:[e.jsxs("div",{children:["检查类型：",l.modalityType]}),e.jsxs("div",{children:["检查日期：",v(l.studyTime).format("YYYY年MM月DD日 HH:mm")]})]})})})}):e.jsx(te,{description:o?"暂无报告记录":"请先选择就诊人以查看报告"}),e.jsx(y,{type:"default",onClick:()=>t("/"),style:{marginTop:"20px"},block:!0,children:"返回首页"})]})})},{Title:z,Paragraph:H}=L,_e=()=>{const{id:t}=ee(),n=T(),[s,o]=u.useState(null),[i,p]=u.useState(!0),[d,c]=u.useState(null),l=sessionStorage.getItem(`report_${t}`);return u.useEffect(()=>{setTimeout(()=>{const m=JSON.parse(l);console.log("reportData",m),console.log("accessionNo",t),m?o(m):c("未找到该报告的详细信息。"),p(!1)},500)},[t]),i?e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"加载报告详情中..."}):d?e.jsxs("div",{style:{padding:"16px"},children:[e.jsx(xe,{message:"错误",description:d,type:"error",showIcon:!0}),e.jsx(y,{onClick:()=>n(-1),style:{marginTop:"20px"},block:!0,children:"返回上一页"})]}):s?e.jsx("div",{style:{padding:"16px"},children:e.jsxs(C,{title:s.procedureName||"报告详情",children:[e.jsxs(j,{variant:"outlined",column:1,size:"small",children:[e.jsx(j.Item,{label:"影像号",children:s.accessionNo}),e.jsxs(j.Item,{label:"就诊人",children:[s.patientName,s.sex=="M"?" （男":s.sex=="F"?" （女":"（",s.age?`${s.age}岁）`:"）"]}),e.jsx(j.Item,{label:"电话号码",children:s.phoneNumber}),e.jsx(j.Item,{label:"就诊类型",children:s.patientType=="O"?"门诊":"住院"}),e.jsx(j.Item,{label:"检查院所",children:s.locationName}),e.jsx(j.Item,{label:"检查科室",children:s.procedureOffice}),e.jsx(j.Item,{label:"检查方法",children:s.procedureName}),e.jsx(j.Item,{label:"检查日期",children:v(s.studyTime).format("YYYY年MM月DD日 HH:mm")}),e.jsx(j.Item,{label:"报告医生",children:s.reportDoctorName}),e.jsx(j.Item,{label:"报告日期",children:v(s.reportDatetime).format("YYYY年MM月DD日 HH:mm")}),e.jsx(j.Item,{label:"审核医生",children:s.auditDoctorName}),e.jsx(j.Item,{label:"审核日期",children:v(s.auditDatetime).format("YYYY年MM月DD日 HH:mm")})]}),e.jsx(z,{level:5,style:{marginTop:"20px"},children:"检查所见"}),e.jsx(H,{children:s.imageDescription}),e.jsx(z,{level:5,style:{marginTop:"20px"},children:"印象"}),e.jsx(H,{children:s.conclusion}),s.recommendations&&e.jsxs(e.Fragment,{children:[e.jsx(z,{level:5,style:{marginTop:"20px"},children:"建议"}),e.jsx(H,{children:s.recommendations})]}),e.jsx(y,{type:"primary",onClick:()=>window.open(s.report_url,"_blank"),style:{marginTop:"20px"},block:!0,children:"查看报告"}),e.jsx(y,{type:"default",onClick:()=>n(-1),style:{marginTop:"20px"},block:!0,children:"返回列表"})]})}):e.jsx("div",{style:{padding:"16px",textAlign:"center"},children:"报告信息不存在。"})},{Content:ze}=ne;function He(){return e.jsx(ne,{style:{minHeight:"100vh"},children:e.jsx(ze,{style:{padding:"0",margin:"0"},children:e.jsxs(he,{children:[e.jsx(D,{path:"/",element:e.jsx($e,{})}),e.jsx(D,{path:"/patient-list",element:e.jsx(Oe,{})}),e.jsx(D,{path:"/new-patient",element:e.jsx(Be,{})}),e.jsx(D,{path:"/patient-detail/:id",element:e.jsx(Ee,{})}),e.jsx(D,{path:"/online-booking",element:e.jsx(Fe,{})}),e.jsx(D,{path:"/my-bookings",element:e.jsx(Ve,{})}),e.jsx(D,{path:"/my-reports",element:e.jsx(qe,{})}),e.jsx(D,{path:"/report-detail/:id",element:e.jsx(_e,{})})]})})})}ge.createRoot(document.getElementById("root")).render(e.jsx(J.StrictMode,{children:e.jsx(fe,{basename:"/wechat",children:e.jsx(He,{})})}));
