-- 插入测试用户
INSERT INTO users (u_id, u_name, u_phone) 
VALUES (1001, '测试用户', '13800138000');

-- 插入测试患者
INSERT INTO patients (p_uid, p_idcard, p_phone, p_name, p_sex, p_birthday, p_time, p_del) 
VALUES (1001, '110101199001011234', '13800138000', '张三', '男', '1990-01-01', NOW(), 0);

-- 插入测试预约
INSERT INTO bookings (b_uid, b_pid, b_type, b_time, b_bookingtime, b_enabletime)
VALUES (1001, 1, 1, NOW(), '2024-03-20 10:00:00', '2024-03-20 11:00:00');

-- 验证数据
SELECT '验证用户数据' as '测试';
SELECT * FROM users WHERE u_id = 1001;

SELECT '验证患者数据' as '测试';
SELECT * FROM patients WHERE p_uid = 1001;

SELECT '验证预约数据' as '测试';
SELECT * FROM bookings WHERE b_uid = 1001; 