import{r,e as pe,o as me,c as X,H as fe,u as Oe,_ as Pe,L as Ne,w as je,z as _e,a as ne,h as tt,y as Ie,d as te,b as ze,R as U,x as nt}from"./index-C0F_BgRl.js";import{L as Te,aI as at,a2 as ie,l as Me,m as Ve,S as Fe,Q as De,a8 as ve,p as ot,D as we,a5 as Le,aJ as rt,I as st,a1 as Be,G as $e,B as it,aD as lt,u as ke,E as ut,aK as ct,N as dt,aL as Ae,O as ft,n as pt,aM as mt,a3 as vt,aN as gt,a4 as xt}from"./index-Cv9X8VLK.js";var bt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"},ht=function(n,a){return r.createElement(Te,pe({},n,{ref:a,icon:bt}))},Ct=r.forwardRef(ht);const yt=e=>{const{getPrefixCls:n,direction:a}=r.useContext(me),{prefixCls:t,className:o}=e,i=n("input-group",t),l=n("input"),[f,x,c]=at(l),p=X(i,c,{[`${i}-lg`]:e.size==="large",[`${i}-sm`]:e.size==="small",[`${i}-compact`]:e.compact,[`${i}-rtl`]:a==="rtl"},x,o),m=r.useContext(ie),S=r.useMemo(()=>Object.assign(Object.assign({},m),{isFormItemInput:!1}),[m]);return f(r.createElement("span",{className:p,style:e.style,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onFocus:e.onFocus,onBlur:e.onBlur},r.createElement(ie.Provider,{value:S},e.children)))},St=e=>{const{componentCls:n,paddingXS:a}=e;return{[n]:{display:"inline-flex",alignItems:"center",flexWrap:"nowrap",columnGap:a,"&-rtl":{direction:"rtl"},[`${n}-input`]:{textAlign:"center",paddingInline:e.paddingXXS},[`&${n}-sm ${n}-input`]:{paddingInline:e.calc(e.paddingXXS).div(2).equal()},[`&${n}-lg ${n}-input`]:{paddingInline:e.paddingXS}}}},zt=Me(["Input","OTP"],e=>{const n=Ve(e,Fe(e));return[St(n)]},De);var wt=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};const Rt=r.forwardRef((e,n)=>{const{value:a,onChange:t,onActiveChange:o,index:i,mask:l}=e,f=wt(e,["value","onChange","onActiveChange","index","mask"]),x=a&&typeof l=="string"?l:a,c=u=>{t(i,u.target.value)},p=r.useRef(null);r.useImperativeHandle(n,()=>p.current);const m=()=>{fe(()=>{var u;const I=(u=p.current)===null||u===void 0?void 0:u.input;document.activeElement===I&&I&&I.select()})},S=u=>{const{key:I,ctrlKey:F,metaKey:D}=u;I==="ArrowLeft"?o(i-1):I==="ArrowRight"?o(i+1):I==="z"&&(F||D)&&u.preventDefault(),m()},N=u=>{u.key==="Backspace"&&!a&&o(i-1),m()};return r.createElement(ve,Object.assign({type:l===!0?"password":"text"},f,{ref:p,value:x,onInput:c,onFocus:m,onKeyDown:S,onKeyUp:N,onMouseDown:m,onMouseUp:m}))});var Et=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};function ce(e){return(e||"").split("")}const Ot=e=>{const{index:n,prefixCls:a,separator:t}=e,o=typeof t=="function"?t(n):t;return o?r.createElement("span",{className:`${a}-separator`},o):null},It=r.forwardRef((e,n)=>{const{prefixCls:a,length:t=6,size:o,defaultValue:i,value:l,onChange:f,formatter:x,separator:c,variant:p,disabled:m,status:S,autoFocus:N,mask:u,type:I,onInput:F,inputMode:D}=e,z=Et(e,["prefixCls","length","size","defaultValue","value","onChange","formatter","separator","variant","disabled","status","autoFocus","mask","type","onInput","inputMode"]),{getPrefixCls:j,direction:H}=r.useContext(me),C=j("otp",a),E=ot(z,{aria:!0,data:!0,attr:!0}),[V,Y,L]=zt(C),P=we(s=>o??s),$=r.useContext(ie),_=Le($.status,S),G=r.useMemo(()=>Object.assign(Object.assign({},$),{status:_,hasFeedback:!1,feedbackIcon:null}),[$,_]),T=r.useRef(null),w=r.useRef({});r.useImperativeHandle(n,()=>({focus:()=>{var s;(s=w.current[0])===null||s===void 0||s.focus()},blur:()=>{var s;for(let d=0;d<t;d+=1)(s=w.current[d])===null||s===void 0||s.blur()},nativeElement:T.current}));const y=s=>x?x(s):s,[B,W]=r.useState(()=>ce(y(i||"")));r.useEffect(()=>{l!==void 0&&W(ce(l))},[l]);const Q=Oe(s=>{W(s),F&&F(s),f&&s.length===t&&s.every(d=>d)&&s.some((d,g)=>B[g]!==d)&&f(s.join(""))}),J=Oe((s,d)=>{let g=Pe(B);for(let R=0;R<s;R+=1)g[R]||(g[R]="");d.length<=1?g[s]=d:g=g.slice(0,s).concat(ce(d)),g=g.slice(0,t);for(let R=g.length-1;R>=0&&!g[R];R-=1)g.pop();const q=y(g.map(R=>R||" ").join(""));return g=ce(q).map((R,k)=>R===" "&&!g[k]?g[k]:R),g}),v=(s,d)=>{var g;const q=J(s,d),R=Math.min(s+d.length,t-1);R!==s&&q[s]!==void 0&&((g=w.current[R])===null||g===void 0||g.focus()),Q(q)},b=s=>{var d;(d=w.current[s])===null||d===void 0||d.focus()},O={variant:p,disabled:m,status:_,mask:u,type:I,inputMode:D};return V(r.createElement("div",Object.assign({},E,{ref:T,className:X(C,{[`${C}-sm`]:P==="small",[`${C}-lg`]:P==="large",[`${C}-rtl`]:H==="rtl"},L,Y)}),r.createElement(ie.Provider,{value:G},Array.from({length:t}).map((s,d)=>{const g=`otp-${d}`,q=B[d]||"";return r.createElement(r.Fragment,{key:g},r.createElement(Rt,Object.assign({ref:R=>{w.current[d]=R},index:d,size:P,htmlSize:1,className:`${C}-input`,onChange:v,value:q,onActiveChange:b,autoFocus:d===0&&N},O)),d<t-1&&r.createElement(Ot,{separator:c,index:d,prefixCls:C}))}))))});var $t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"}},{tag:"path",attrs:{d:"M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"}}]},name:"eye-invisible",theme:"outlined"},At=function(n,a){return r.createElement(Te,pe({},n,{ref:a,icon:$t}))},Pt=r.forwardRef(At),Nt=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};const jt=e=>e?r.createElement(Ct,null):r.createElement(Pt,null),_t={click:"onClick",hover:"onMouseOver"},Tt=r.forwardRef((e,n)=>{const{disabled:a,action:t="click",visibilityToggle:o=!0,iconRender:i=jt}=e,l=r.useContext(Ne),f=a??l,x=typeof o=="object"&&o.visible!==void 0,[c,p]=r.useState(()=>x?o.visible:!1),m=r.useRef(null);r.useEffect(()=>{x&&p(o.visible)},[x,o]);const S=rt(m),N=()=>{var P;if(f)return;c&&S();const $=!c;p($),typeof o=="object"&&((P=o.onVisibleChange)===null||P===void 0||P.call(o,$))},u=P=>{const $=_t[t]||"",_=i(c),G={[$]:N,className:`${P}-icon`,key:"passwordIcon",onMouseDown:T=>{T.preventDefault()},onMouseUp:T=>{T.preventDefault()}};return r.cloneElement(r.isValidElement(_)?_:r.createElement("span",null,_),G)},{className:I,prefixCls:F,inputPrefixCls:D,size:z}=e,j=Nt(e,["className","prefixCls","inputPrefixCls","size"]),{getPrefixCls:H}=r.useContext(me),C=H("input",D),E=H("input-password",F),V=o&&u(E),Y=X(E,I,{[`${E}-${z}`]:!!z}),L=Object.assign(Object.assign({},st(j,["suffix","iconRender","visibilityToggle"])),{type:c?"text":"password",className:Y,prefixCls:C,suffix:V});return z&&(L.size=z),r.createElement(ve,Object.assign({ref:je(n,m)},L))});var Mt=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};const Vt=r.forwardRef((e,n)=>{const{prefixCls:a,inputPrefixCls:t,className:o,size:i,suffix:l,enterButton:f=!1,addonAfter:x,loading:c,disabled:p,onSearch:m,onChange:S,onCompositionStart:N,onCompositionEnd:u}=e,I=Mt(e,["prefixCls","inputPrefixCls","className","size","suffix","enterButton","addonAfter","loading","disabled","onSearch","onChange","onCompositionStart","onCompositionEnd"]),{getPrefixCls:F,direction:D}=r.useContext(me),z=r.useRef(!1),j=F("input-search",a),H=F("input",t),{compactSize:C}=Be(j,D),E=we(v=>{var b;return(b=i??C)!==null&&b!==void 0?b:v}),V=r.useRef(null),Y=v=>{v!=null&&v.target&&v.type==="click"&&m&&m(v.target.value,v,{source:"clear"}),S==null||S(v)},L=v=>{var b;document.activeElement===((b=V.current)===null||b===void 0?void 0:b.input)&&v.preventDefault()},P=v=>{var b,O;m&&m((O=(b=V.current)===null||b===void 0?void 0:b.input)===null||O===void 0?void 0:O.value,v,{source:"input"})},$=v=>{z.current||c||P(v)},_=typeof f=="boolean"?r.createElement(lt,null):null,G=`${j}-button`;let T;const w=f||{},y=w.type&&w.type.__ANT_BUTTON===!0;y||w.type==="button"?T=$e(w,Object.assign({onMouseDown:L,onClick:v=>{var b,O;(O=(b=w==null?void 0:w.props)===null||b===void 0?void 0:b.onClick)===null||O===void 0||O.call(b,v),P(v)},key:"enterButton"},y?{className:G,size:E}:{})):T=r.createElement(it,{className:G,type:f?"primary":void 0,size:E,disabled:p,key:"enterButton",onMouseDown:L,onClick:P,loading:c,icon:_},f),x&&(T=[T,$e(x,{key:"addonAfter"})]);const B=X(j,{[`${j}-rtl`]:D==="rtl",[`${j}-${E}`]:!!E,[`${j}-with-button`]:!!f},o),W=Object.assign(Object.assign({},I),{className:B,prefixCls:H,type:"search"}),Q=v=>{z.current=!0,N==null||N(v)},J=v=>{z.current=!1,u==null||u(v)};return r.createElement(ve,Object.assign({ref:je(V,n),onPressEnter:$},W,{size:E,onCompositionStart:Q,onCompositionEnd:J,addonAfter:T,suffix:l,onChange:Y,disabled:p}))});var Ft=`
  min-height:0 !important;
  max-height:none !important;
  height:0 !important;
  visibility:hidden !important;
  overflow:hidden !important;
  position:absolute !important;
  z-index:-1000 !important;
  top:0 !important;
  right:0 !important;
  pointer-events: none !important;
`,Dt=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],he={},K;function Lt(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(n&&he[a])return he[a];var t=window.getComputedStyle(e),o=t.getPropertyValue("box-sizing")||t.getPropertyValue("-moz-box-sizing")||t.getPropertyValue("-webkit-box-sizing"),i=parseFloat(t.getPropertyValue("padding-bottom"))+parseFloat(t.getPropertyValue("padding-top")),l=parseFloat(t.getPropertyValue("border-bottom-width"))+parseFloat(t.getPropertyValue("border-top-width")),f=Dt.map(function(c){return"".concat(c,":").concat(t.getPropertyValue(c))}).join(";"),x={sizingStyle:f,paddingSize:i,borderSize:l,boxSizing:o};return n&&a&&(he[a]=x),x}function Bt(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;K||(K=document.createElement("textarea"),K.setAttribute("tab-index","-1"),K.setAttribute("aria-hidden","true"),K.setAttribute("name","hiddenTextarea"),document.body.appendChild(K)),e.getAttribute("wrap")?K.setAttribute("wrap",e.getAttribute("wrap")):K.removeAttribute("wrap");var o=Lt(e,n),i=o.paddingSize,l=o.borderSize,f=o.boxSizing,x=o.sizingStyle;K.setAttribute("style","".concat(x,";").concat(Ft)),K.value=e.value||e.placeholder||"";var c=void 0,p=void 0,m,S=K.scrollHeight;if(f==="border-box"?S+=l:f==="content-box"&&(S-=i),a!==null||t!==null){K.value=" ";var N=K.scrollHeight-i;a!==null&&(c=N*a,f==="border-box"&&(c=c+i+l),S=Math.max(c,S)),t!==null&&(p=N*t,f==="border-box"&&(p=p+i+l),m=S>p?"":"hidden",S=Math.min(p,S))}var u={height:S,overflowY:m,resize:"none"};return c&&(u.minHeight=c),p&&(u.maxHeight=p),u}var kt=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],Ce=0,ye=1,Se=2,Ht=r.forwardRef(function(e,n){var a=e,t=a.prefixCls,o=a.defaultValue,i=a.value,l=a.autoSize,f=a.onResize,x=a.className,c=a.style,p=a.disabled,m=a.onChange;a.onInternalAutoSize;var S=_e(a,kt),N=ke(o,{value:i,postState:function(s){return s??""}}),u=ne(N,2),I=u[0],F=u[1],D=function(s){F(s.target.value),m==null||m(s)},z=r.useRef();r.useImperativeHandle(n,function(){return{textArea:z.current}});var j=r.useMemo(function(){return l&&tt(l)==="object"?[l.minRows,l.maxRows]:[]},[l]),H=ne(j,2),C=H[0],E=H[1],V=!!l,Y=function(){try{if(document.activeElement===z.current){var s=z.current,d=s.selectionStart,g=s.selectionEnd,q=s.scrollTop;z.current.setSelectionRange(d,g),z.current.scrollTop=q}}catch{}},L=r.useState(Se),P=ne(L,2),$=P[0],_=P[1],G=r.useState(),T=ne(G,2),w=T[0],y=T[1],B=function(){_(Ce)};Ie(function(){V&&B()},[i,C,E,V]),Ie(function(){if($===Ce)_(ye);else if($===ye){var O=Bt(z.current,!1,C,E);_(Se),y(O)}else Y()},[$]);var W=r.useRef(),Q=function(){fe.cancel(W.current)},J=function(s){$===Se&&(f==null||f(s),l&&(Q(),W.current=fe(function(){B()})))};r.useEffect(function(){return Q},[]);var v=V?w:null,b=te(te({},c),v);return($===Ce||$===ye)&&(b.overflowY="hidden",b.overflowX="hidden"),r.createElement(ut,{onResize:J,disabled:!(l||f)},r.createElement("textarea",pe({},S,{ref:z,style:b,className:X(t,x,ze({},"".concat(t,"-disabled"),p)),disabled:p,value:I,onChange:D})))}),Wt=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],Kt=U.forwardRef(function(e,n){var a,t=e.defaultValue,o=e.value,i=e.onFocus,l=e.onBlur,f=e.onChange,x=e.allowClear,c=e.maxLength,p=e.onCompositionStart,m=e.onCompositionEnd,S=e.suffix,N=e.prefixCls,u=N===void 0?"rc-textarea":N,I=e.showCount,F=e.count,D=e.className,z=e.style,j=e.disabled,H=e.hidden,C=e.classNames,E=e.styles,V=e.onResize,Y=e.onClear,L=e.onPressEnter,P=e.readOnly,$=e.autoSize,_=e.onKeyDown,G=_e(e,Wt),T=ke(t,{value:o,defaultValue:t}),w=ne(T,2),y=w[0],B=w[1],W=y==null?"":String(y),Q=U.useState(!1),J=ne(Q,2),v=J[0],b=J[1],O=U.useRef(!1),s=U.useState(null),d=ne(s,2),g=d[0],q=d[1],R=r.useRef(null),k=r.useRef(null),A=function(){var h;return(h=k.current)===null||h===void 0?void 0:h.textArea},ae=function(){A().focus()};r.useImperativeHandle(n,function(){var M;return{resizableTextArea:k.current,focus:ae,blur:function(){A().blur()},nativeElement:((M=R.current)===null||M===void 0?void 0:M.nativeElement)||A()}}),r.useEffect(function(){b(function(M){return!j&&M})},[j]);var oe=U.useState(null),Re=ne(oe,2),ge=Re[0],He=Re[1];U.useEffect(function(){if(ge){var M;(M=A()).setSelectionRange.apply(M,Pe(ge))}},[ge]);var Z=ct(F,I),re=(a=Z.max)!==null&&a!==void 0?a:c,We=Number(re)>0,xe=Z.strategy(W),Ke=!!re&&xe>re,Ee=function(h,ee){var ue=ee;!O.current&&Z.exceedFormatter&&Z.max&&Z.strategy(ee)>Z.max&&(ue=Z.exceedFormatter(ee,{max:Z.max}),ee!==ue&&He([A().selectionStart||0,A().selectionEnd||0])),B(ue),Ae(h.currentTarget,h,f,ue)},Xe=function(h){O.current=!0,p==null||p(h)},Ge=function(h){O.current=!1,Ee(h,h.currentTarget.value),m==null||m(h)},qe=function(h){Ee(h,h.target.value)},Ue=function(h){h.key==="Enter"&&L&&L(h),_==null||_(h)},Ye=function(h){b(!0),i==null||i(h)},Qe=function(h){b(!1),l==null||l(h)},Ze=function(h){B(""),ae(),Ae(A(),h,f)},be=S,se;Z.show&&(Z.showFormatter?se=Z.showFormatter({value:W,count:xe,maxLength:re}):se="".concat(xe).concat(We?" / ".concat(re):""),be=U.createElement(U.Fragment,null,be,U.createElement("span",{className:X("".concat(u,"-data-count"),C==null?void 0:C.count),style:E==null?void 0:E.count},se)));var Je=function(h){var ee;V==null||V(h),(ee=A())!==null&&ee!==void 0&&ee.style.height&&q(!0)},et=!$&&!I&&!x;return U.createElement(dt,{ref:R,value:W,allowClear:x,handleReset:Ze,suffix:be,prefixCls:u,classNames:te(te({},C),{},{affixWrapper:X(C==null?void 0:C.affixWrapper,ze(ze({},"".concat(u,"-show-count"),I),"".concat(u,"-textarea-allow-clear"),x))}),disabled:j,focused:v,className:X(D,Ke&&"".concat(u,"-out-of-range")),style:te(te({},z),g&&!et?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":typeof se=="string"?se:void 0}},hidden:H,readOnly:P,onClear:Y},U.createElement(Ht,pe({},G,{autoSize:$,maxLength:c,onKeyDown:Ue,onChange:qe,onFocus:Ye,onBlur:Qe,onCompositionStart:Xe,onCompositionEnd:Ge,className:X(C==null?void 0:C.textarea),style:te(te({},E==null?void 0:E.textarea),{},{resize:z==null?void 0:z.resize}),disabled:j,prefixCls:u,onResize:Je,ref:k,readOnly:P})))});const de=2,Xt=(e,n)=>{n.offsetWidth-e<de?n.style.width=`${e+de}px`:n.offsetWidth-e>de&&(n.style.width=`${e+de}px`)},Gt=()=>U.useCallback(n=>{if(n&&n.resizableTextArea.textArea.style.width.includes("px")){const a=Number.parseInt(n.resizableTextArea.textArea.style.width.replace(/px/,""));fe(()=>Xt(a,n.nativeElement))}},[]),qt=e=>{const{componentCls:n,paddingLG:a}=e,t=`${n}-textarea`;return{[t]:{position:"relative","&-show-count":{[`> ${n}`]:{height:"100%"},[`${n}-data-count`]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},[`
        &-allow-clear > ${n},
        &-affix-wrapper${t}-has-feedback ${n}
      `]:{paddingInlineEnd:a},[`&-affix-wrapper${n}-affix-wrapper`]:{padding:0,[`> textarea${n}`]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},[`${n}-suffix`]:{margin:0,"> *:not(:last-child)":{marginInline:0},[`${n}-clear-icon`]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},[`${t}-suffix`]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},[`&-affix-wrapper${n}-affix-wrapper-sm`]:{[`${n}-suffix`]:{[`${n}-clear-icon`]:{insetInlineEnd:e.paddingInlineSM}}}}}},Ut=Me(["Input","TextArea"],e=>{const n=Ve(e,Fe(e));return[qt(n)]},De,{resetFont:!1});var Yt=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};const Qt=r.forwardRef((e,n)=>{var a;const{prefixCls:t,bordered:o=!0,size:i,disabled:l,status:f,allowClear:x,classNames:c,rootClassName:p,className:m,style:S,styles:N,variant:u,showCount:I}=e,F=Yt(e,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant","showCount"]),{getPrefixCls:D,direction:z,allowClear:j,autoComplete:H,className:C,style:E,classNames:V,styles:Y}=nt("textArea"),L=r.useContext(Ne),P=l??L,{status:$,hasFeedback:_,feedbackIcon:G}=r.useContext(ie),T=Le($,f),w=r.useRef(null);r.useImperativeHandle(n,()=>{var k;return{resizableTextArea:(k=w.current)===null||k===void 0?void 0:k.resizableTextArea,focus:A=>{var ae,oe;ft((oe=(ae=w.current)===null||ae===void 0?void 0:ae.resizableTextArea)===null||oe===void 0?void 0:oe.textArea,A)},blur:()=>{var A;return(A=w.current)===null||A===void 0?void 0:A.blur()}}});const y=D("input",t),B=pt(y),[W,Q,J]=mt(y,p),[v]=Ut(y,B),{compactSize:b,compactItemClassnames:O}=Be(y,z),s=we(k=>{var A;return(A=i??b)!==null&&A!==void 0?A:k}),[d,g]=vt("textArea",u,o),q=gt(x??j),R=Gt();return W(v(r.createElement(Kt,Object.assign({autoComplete:H},F,{style:Object.assign(Object.assign({},E),S),styles:Object.assign(Object.assign({},Y),N),disabled:P,allowClear:q,className:X(J,B,m,p,O,C),classNames:Object.assign(Object.assign(Object.assign({},c),V),{textarea:X({[`${y}-sm`]:s==="small",[`${y}-lg`]:s==="large"},Q,c==null?void 0:c.textarea,V.textarea),variant:X({[`${y}-${d}`]:g},xt(y,T)),affixWrapper:X(`${y}-textarea-affix-wrapper`,{[`${y}-affix-wrapper-rtl`]:z==="rtl",[`${y}-affix-wrapper-sm`]:s==="small",[`${y}-affix-wrapper-lg`]:s==="large",[`${y}-textarea-show-count`]:e.showCount||((a=e.count)===null||a===void 0?void 0:a.show)},Q)}),prefixCls:y,suffix:_&&r.createElement("span",{className:`${y}-textarea-suffix`},G),showCount:I,ref:w,onResize:k=>{var A;(A=F.onResize)===null||A===void 0||A.call(F,k),I&&R(w.current)}}))))}),le=ve;le.Group=yt;le.Search=Vt;le.TextArea=Qt;le.Password=Tt;le.OTP=It;export{le as I};
