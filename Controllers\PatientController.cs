﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Common.Attr;
using zhiying_online.Common.BLL;
using zhiying_online.Common.Jwt;

namespace zhiying_online.Controllers
{
    /// <summary>
    /// 就诊人
    /// </summary>
    [Route("[controller]/[action]")]
    public class PatientController : Controller
    {
        /// <summary>
        /// 获取就诊人列表_ht
        /// </summary>
        /// <param name="openid">微信用户openid</param>
        /// <param name="phone">手机号</param>
        /// <param name="idcard">身份证号</param>
        /// <param name="star">添加开始时间</param>
        /// <param name="end">添加结束时间</param>
        /// <param name="PageSize"></param>
        /// <param name="PageNo"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":[{"id":"就诊人id等于后续的pid","uid":"微信用户ID","idcard":"身份证号","phone":"手机号","name":"就诊人名称","sex":"男，女","time":"就诊时间"}],"count":"数量"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtAttribute]
        public IActionResult GetPatientList(string openid, string phone, string idcard, DateTime? star, DateTime? end, int PageSize = 10, int PageNo = 1)
        {
            return Json(PatientHandle.GetPatientList(openid, phone, idcard, star, end, PageSize, PageNo));
        }

        /// <summary>
        /// 根据用户获取就诊人列表
        /// </summary>
        /// <param name="uid"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":[{"id":"就诊人id等于后续的pid","uid":"微信用户ID","idcard":"身份证号","phone":"手机号","name":"就诊人名称","sex":"男，女","time":"就诊时间"}]}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtH5Attribute]
        public IActionResult GetGetPatientByUid(long uid)
        {
            return Json(PatientHandle.GetGetPatientByUid(uid));
        }

        /// <summary>
        /// 保存就诊人
        /// </summary>
        /// <param name="id">就诊人ID</param>
        /// <param name="uid">微信用户ID</param>
        /// <param name="idcard">身份证号</param>
        /// <param name="phone">手机号</param>
        /// <param name="name">就诊人</param>
        /// <param name="sex">性别</param>
        /// <param name="birthday">出生日期</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtH5Attribute]
        [RequestLog]
        public IActionResult SavePatient(long? id, long? uid, string idcard, string phone, string name, string sex,DateTime? birthday)
        {
            return Json(PatientHandle.SavePatient(id,uid, idcard, phone, name, sex, birthday));
        }

        /// <summary>
        /// 删除就诊人
        /// </summary>
        /// <param name="id">就诊记录id</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtH5Attribute]
        [RequestLog]
        public IActionResult DelPatient(long? id)
        {
            return Json(PatientHandle.DelPatient(id));
        }
    }
}
