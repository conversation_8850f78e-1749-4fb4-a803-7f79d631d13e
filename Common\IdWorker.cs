﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zhiying_online.Common
{
    /// <summary>
    /// 雪花算法生成唯一id
    /// </summary>
    public class IdWorker
    {
        //机器ID
        private static long workerId = 0;
        private static long twepoch = 687888001020L; //唯一时间，这是一个避免重复的随机量，自行设定不要大于当前时间戳
        private static long sequence = 0L;
        private static int workerIdBits = 4; //机器码字节数。4个字节用来保存机器码(定义为Long类型会出现，最大偏移64位，所以左移64位没有意义)
        public static long maxWorkerId = -1L ^ -1L << workerIdBits; //最大机器ID
        private static int sequenceBits = 10; //计数器字节数，10个字节用来保存计数码
        private static int workerIdShift = sequenceBits; //机器码数据左移位数，就是后面计数器占用的位数
        private static int timestampLeftShift = sequenceBits + workerIdBits; //时间戳左移动位数就是机器码和计数器总字节数
        public static long sequenceMask = -1L ^ -1L << sequenceBits; //一微秒内可以产生计数，如果达到该值则等到下一微妙在进行生成
        private long lastTimestamp = -1L;


        /// <summary>
        /// 机器码
        /// </summary>
        /// <param name="workerId"></param>
        public IdWorker()
        {
            //定义机器码，如果存在则使用原有机器码。不存在则redis注册
            if (workerId == 0)
            {
                workerId = maxWorkerId;
            }
            //判断是否异常
            if (workerId > maxWorkerId || workerId < 0)
            {
                throw new Exception(string.Format("worker Id can't be greater than {0} or less than 0 ", workerId));
            }
        }

        public long nextId()
        {
            lock (this)
            {
                long timestamp = timeGen();
                //如果时间小于最后一条时间则时间戳按照最后一条算
                if (timestamp < lastTimestamp)
                {
                    timestamp = lastTimestamp;
                    Logger.debug("生成雪花id异常：" + string.Format("Clock moved backwards.  Refusing to generate id for {0} milliseconds",
                      this.lastTimestamp - timestamp));
                }
                //同一微妙中生成ID
                if (this.lastTimestamp == timestamp)
                {
                    IdWorker.sequence = (IdWorker.sequence + 1) & IdWorker.sequenceMask; //用&运算计算该微秒内产生的计数是否已经到达上限
                    if (IdWorker.sequence == 0)
                    {
                        //一微妙内产生的ID计数已达上限，等待下一微妙
                        timestamp = tillNextMillis(this.lastTimestamp);
                    }
                }
                else
                { //不同微秒生成ID
                    IdWorker.sequence = 0; //计数清0
                }
                //if (timestamp < lastTimestamp)
                //{ //如果当前时间戳比上一次生成ID时时间戳还小，抛出异常，因为不能保证现在生成的ID之前没有生成过
                //    //throw new Exception(string.Format("Clock moved backwards.  Refusing to generate id for {0} milliseconds",
                //    //    this.lastTimestamp - timestamp));
                //    Logger.debug("生成雪花id异常：" + string.Format("Clock moved backwards.  Refusing to generate id for {0} milliseconds",
                //        this.lastTimestamp - timestamp));
                //}
                this.lastTimestamp = timestamp; //把当前时间戳保存为最后生成ID的时间戳
                long nextId = (timestamp - twepoch << timestampLeftShift) | IdWorker.workerId << IdWorker.workerIdShift | IdWorker.sequence;
                return nextId;
            }
        }

        /// <summary>
        /// 获取下一微秒时间戳
        /// </summary>
        /// <param name="lastTimestamp"></param>
        /// <returns></returns>
        private long tillNextMillis(long lastTimestamp)
        {
            long timestamp = timeGen();
            while (timestamp <= lastTimestamp)
            {
                timestamp = timeGen();
            }
            return timestamp;
        }

        /// <summary>
        /// 生成当前时间戳
        /// </summary>
        /// <returns></returns>
        private long timeGen()
        {
            return (long)(DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
        }
    }
}
