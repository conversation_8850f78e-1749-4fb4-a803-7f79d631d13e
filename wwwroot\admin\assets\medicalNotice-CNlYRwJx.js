import{r as i,e as be,a as L,R as ia,H as pt,d as ie,I as oa,h as Ze,b as te,c as j,u as la,_ as $t,g as ca,z as Ne,C as sa,l as u,m as Je,B as ke,A as zt,n as Qe,o as Be,p as De,P as qe}from"./index-C0F_BgRl.js";import{L as da,j as pe,E as Ye,M as ua,u as yt,r as va,H as fa,l as Mt,m as Lt,n as ba,R as ma,D as Nt,a3 as ga,I as ha,B as pa,ae as $a}from"./index-Cv9X8VLK.js";import{S as ya}from"./Skeleton-Cxkqevfo.js";import{E as Sa,M as xa,e as Ca,i as St,R as _a}from"./EllipsisOutlined-DUbZADlg.js";import{I as wa}from"./index-4Anx6yNk.js";var Ea={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},Pa=function(t,n){return i.createElement(da,be({},t,{ref:n,icon:Ea}))},Ta=i.forwardRef(Pa);const je=i.createContext(null);var Ra=function(t){var n=t.activeTabOffset,a=t.horizontal,r=t.rtl,o=t.indicator,c=o===void 0?{}:o,l=c.size,s=c.align,d=s===void 0?"center":s,h=i.useState(),g=L(h,2),y=g[0],E=g[1],B=i.useRef(),_=ia.useCallback(function(p){return typeof l=="function"?l(p):typeof l=="number"?l:p},[l]);function R(){pt.cancel(B.current)}return i.useEffect(function(){var p={};if(n)if(a){p.width=_(n.width);var $=r?"right":"left";d==="start"&&(p[$]=n[$]),d==="center"&&(p[$]=n[$]+n.width/2,p.transform=r?"translateX(50%)":"translateX(-50%)"),d==="end"&&(p[$]=n[$]+n.width,p.transform="translateX(-100%)")}else p.height=_(n.height),d==="start"&&(p.top=n.top),d==="center"&&(p.top=n.top+n.height/2,p.transform="translateY(-50%)"),d==="end"&&(p.top=n.top+n.height,p.transform="translateY(-100%)");return R(),B.current=pt(function(){E(p)}),R},[n,a,r,d,_]),{style:y}},xt={width:0,height:0,left:0,top:0};function Ia(e,t,n){return i.useMemo(function(){for(var a,r=new Map,o=t.get((a=e[0])===null||a===void 0?void 0:a.key)||xt,c=o.left+o.width,l=0;l<e.length;l+=1){var s=e[l].key,d=t.get(s);if(!d){var h;d=t.get((h=e[l-1])===null||h===void 0?void 0:h.key)||xt}var g=r.get(s)||ie({},d);g.right=c-g.left-g.width,r.set(s,g)}return r},[e.map(function(a){return a.key}).join("_"),t,n])}function Ct(e,t){var n=i.useRef(e),a=i.useState({}),r=L(a,2),o=r[1];function c(l){var s=typeof l=="function"?l(n.current):l;s!==n.current&&t(s,n.current),n.current=s,o({})}return[n.current,c]}var Oa=.1,_t=.01,Le=20,wt=Math.pow(.995,Le);function za(e,t){var n=i.useState(),a=L(n,2),r=a[0],o=a[1],c=i.useState(0),l=L(c,2),s=l[0],d=l[1],h=i.useState(0),g=L(h,2),y=g[0],E=g[1],B=i.useState(),_=L(B,2),R=_[0],p=_[1],$=i.useRef();function P(x){var T=x.touches[0],m=T.screenX,C=T.screenY;o({x:m,y:C}),window.clearInterval($.current)}function A(x){if(r){var T=x.touches[0],m=T.screenX,C=T.screenY;o({x:m,y:C});var v=m-r.x,S=C-r.y;t(v,S);var Q=Date.now();d(Q),E(Q-s),p({x:v,y:S})}}function K(){if(r&&(o(null),p(null),R)){var x=R.x/y,T=R.y/y,m=Math.abs(x),C=Math.abs(T);if(Math.max(m,C)<Oa)return;var v=x,S=T;$.current=window.setInterval(function(){if(Math.abs(v)<_t&&Math.abs(S)<_t){window.clearInterval($.current);return}v*=wt,S*=wt,t(v*Le,S*Le)},Le)}}var X=i.useRef();function V(x){var T=x.deltaX,m=x.deltaY,C=0,v=Math.abs(T),S=Math.abs(m);v===S?C=X.current==="x"?T:m:v>S?(C=T,X.current="x"):(C=m,X.current="y"),t(-C,-C)&&x.preventDefault()}var I=i.useRef(null);I.current={onTouchStart:P,onTouchMove:A,onTouchEnd:K,onWheel:V},i.useEffect(function(){function x(v){I.current.onTouchStart(v)}function T(v){I.current.onTouchMove(v)}function m(v){I.current.onTouchEnd(v)}function C(v){I.current.onWheel(v)}return document.addEventListener("touchmove",T,{passive:!1}),document.addEventListener("touchend",m,{passive:!0}),e.current.addEventListener("touchstart",x,{passive:!0}),e.current.addEventListener("wheel",C,{passive:!1}),function(){document.removeEventListener("touchmove",T),document.removeEventListener("touchend",m)}},[])}function Bt(e){var t=i.useState(0),n=L(t,2),a=n[0],r=n[1],o=i.useRef(0),c=i.useRef();return c.current=e,oa(function(){var l;(l=c.current)===null||l===void 0||l.call(c)},[a]),function(){o.current===a&&(o.current+=1,r(o.current))}}function Ma(e){var t=i.useRef([]),n=i.useState({}),a=L(n,2),r=a[1],o=i.useRef(typeof e=="function"?e():e),c=Bt(function(){var s=o.current;t.current.forEach(function(d){s=d(s)}),t.current=[],o.current=s,r({})});function l(s){t.current.push(s),c()}return[o.current,l]}var Et={width:0,height:0,left:0,top:0,right:0};function La(e,t,n,a,r,o,c){var l=c.tabs,s=c.tabPosition,d=c.rtl,h,g,y;return["top","bottom"].includes(s)?(h="width",g=d?"right":"left",y=Math.abs(n)):(h="height",g="top",y=-n),i.useMemo(function(){if(!l.length)return[0,0];for(var E=l.length,B=E,_=0;_<E;_+=1){var R=e.get(l[_].key)||Et;if(Math.floor(R[g]+R[h])>Math.floor(y+t)){B=_-1;break}}for(var p=0,$=E-1;$>=0;$-=1){var P=e.get(l[$].key)||Et;if(P[g]<y){p=$+1;break}}return p>=B?[0,0]:[p,B]},[e,t,a,r,o,y,s,l.map(function(E){return E.key}).join("_"),d])}function Pt(e){var t;return e instanceof Map?(t={},e.forEach(function(n,a){t[a]=n})):t=e,JSON.stringify(t)}var Na="TABS_DQ";function Dt(e){return String(e).replace(/"/g,Na)}function et(e,t,n,a){return!(!n||a||e===!1||e===void 0&&(t===!1||t===null))}var jt=i.forwardRef(function(e,t){var n=e.prefixCls,a=e.editable,r=e.locale,o=e.style;return!a||a.showAdd===!1?null:i.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:o,"aria-label":(r==null?void 0:r.addAriaLabel)||"Add tab",onClick:function(l){a.onEdit("add",{event:l})}},a.addIcon||"+")}),Tt=i.forwardRef(function(e,t){var n=e.position,a=e.prefixCls,r=e.extra;if(!r)return null;var o,c={};return Ze(r)==="object"&&!i.isValidElement(r)?c=r:c.right=r,n==="right"&&(o=c.right),n==="left"&&(o=c.left),o?i.createElement("div",{className:"".concat(a,"-extra-content"),ref:t},o):null}),Ba=i.forwardRef(function(e,t){var n=e.prefixCls,a=e.id,r=e.tabs,o=e.locale,c=e.mobile,l=e.more,s=l===void 0?{}:l,d=e.style,h=e.className,g=e.editable,y=e.tabBarGutter,E=e.rtl,B=e.removeAriaLabel,_=e.onTabClick,R=e.getPopupContainer,p=e.popupClassName,$=i.useState(!1),P=L($,2),A=P[0],K=P[1],X=i.useState(null),V=L(X,2),I=V[0],x=V[1],T=s.icon,m=T===void 0?"More":T,C="".concat(a,"-more-popup"),v="".concat(n,"-dropdown"),S=I!==null?"".concat(C,"-").concat(I):null,Q=o==null?void 0:o.dropdownAriaLabel;function H(w,D){w.preventDefault(),w.stopPropagation(),g.onEdit("remove",{key:D,event:w})}var f=i.createElement(Sa,{onClick:function(D){var Y=D.key,Z=D.domEvent;_(Y,Z),K(!1)},prefixCls:"".concat(v,"-menu"),id:C,tabIndex:-1,role:"listbox","aria-activedescendant":S,selectedKeys:[I],"aria-label":Q!==void 0?Q:"expanded dropdown"},r.map(function(w){var D=w.closable,Y=w.disabled,Z=w.closeIcon,F=w.key,ae=w.label,ee=et(D,Z,g,Y);return i.createElement(xa,{key:F,id:"".concat(C,"-").concat(F),role:"option","aria-controls":a&&"".concat(a,"-panel-").concat(F),disabled:Y},i.createElement("span",null,ae),ee&&i.createElement("button",{type:"button","aria-label":B||"remove",tabIndex:0,className:"".concat(v,"-menu-item-remove"),onClick:function(se){se.stopPropagation(),H(se,F)}},Z||g.removeIcon||"×"))}));function J(w){for(var D=r.filter(function(ee){return!ee.disabled}),Y=D.findIndex(function(ee){return ee.key===I})||0,Z=D.length,F=0;F<Z;F+=1){Y=(Y+w+Z)%Z;var ae=D[Y];if(!ae.disabled){x(ae.key);return}}}function O(w){var D=w.which;if(!A){[pe.DOWN,pe.SPACE,pe.ENTER].includes(D)&&(K(!0),w.preventDefault());return}switch(D){case pe.UP:J(-1),w.preventDefault();break;case pe.DOWN:J(1),w.preventDefault();break;case pe.ESC:K(!1);break;case pe.SPACE:case pe.ENTER:I!==null&&_(I,w);break}}i.useEffect(function(){var w=document.getElementById(S);w&&w.scrollIntoView&&w.scrollIntoView(!1)},[I]),i.useEffect(function(){A||x(null)},[A]);var q=te({},E?"marginRight":"marginLeft",y);r.length||(q.visibility="hidden",q.order=1);var k=j(te({},"".concat(v,"-rtl"),E)),ce=c?null:i.createElement(Ca,be({prefixCls:v,overlay:f,visible:r.length?A:!1,onVisibleChange:K,overlayClassName:j(k,p),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:R},s),i.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:q,"aria-haspopup":"listbox","aria-controls":C,id:"".concat(a,"-more"),"aria-expanded":A,onKeyDown:O},m));return i.createElement("div",{className:j("".concat(n,"-nav-operations"),h),style:d,ref:t},ce,i.createElement(jt,{prefixCls:n,locale:o,editable:g}))});const Da=i.memo(Ba,function(e,t){return t.tabMoving});var ja=function(t){var n=t.prefixCls,a=t.id,r=t.active,o=t.focus,c=t.tab,l=c.key,s=c.label,d=c.disabled,h=c.closeIcon,g=c.icon,y=t.closable,E=t.renderWrapper,B=t.removeAriaLabel,_=t.editable,R=t.onClick,p=t.onFocus,$=t.onBlur,P=t.onKeyDown,A=t.onMouseDown,K=t.onMouseUp,X=t.style,V=t.tabCount,I=t.currentPosition,x="".concat(n,"-tab"),T=et(y,h,_,d);function m(H){d||R(H)}function C(H){H.preventDefault(),H.stopPropagation(),_.onEdit("remove",{key:l,event:H})}var v=i.useMemo(function(){return g&&typeof s=="string"?i.createElement("span",null,s):s},[s,g]),S=i.useRef(null);i.useEffect(function(){o&&S.current&&S.current.focus()},[o]);var Q=i.createElement("div",{key:l,"data-node-key":Dt(l),className:j(x,te(te(te(te({},"".concat(x,"-with-remove"),T),"".concat(x,"-active"),r),"".concat(x,"-disabled"),d),"".concat(x,"-focus"),o)),style:X,onClick:m},i.createElement("div",{ref:S,role:"tab","aria-selected":r,id:a&&"".concat(a,"-tab-").concat(l),className:"".concat(x,"-btn"),"aria-controls":a&&"".concat(a,"-panel-").concat(l),"aria-disabled":d,tabIndex:d?null:r?0:-1,onClick:function(f){f.stopPropagation(),m(f)},onKeyDown:P,onMouseDown:A,onMouseUp:K,onFocus:p,onBlur:$},o&&i.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(I," of ").concat(V)),g&&i.createElement("span",{className:"".concat(x,"-icon")},g),s&&v),T&&i.createElement("button",{type:"button",role:"tab","aria-label":B||"remove",tabIndex:r?0:-1,className:"".concat(x,"-remove"),onClick:function(f){f.stopPropagation(),C(f)}},h||_.removeIcon||"×"));return E?E(Q):Q},Aa=function(t,n){var a=t.offsetWidth,r=t.offsetHeight,o=t.offsetTop,c=t.offsetLeft,l=t.getBoundingClientRect(),s=l.width,d=l.height,h=l.left,g=l.top;return Math.abs(s-a)<1?[s,d,h-n.left,g-n.top]:[a,r,c,o]},we=function(t){var n=t.current||{},a=n.offsetWidth,r=a===void 0?0:a,o=n.offsetHeight,c=o===void 0?0:o;if(t.current){var l=t.current.getBoundingClientRect(),s=l.width,d=l.height;if(Math.abs(s-r)<1)return[s,d]}return[r,c]},Me=function(t,n){return t[n?0:1]},Rt=i.forwardRef(function(e,t){var n=e.className,a=e.style,r=e.id,o=e.animated,c=e.activeKey,l=e.rtl,s=e.extra,d=e.editable,h=e.locale,g=e.tabPosition,y=e.tabBarGutter,E=e.children,B=e.onTabClick,_=e.onTabScroll,R=e.indicator,p=i.useContext(je),$=p.prefixCls,P=p.tabs,A=i.useRef(null),K=i.useRef(null),X=i.useRef(null),V=i.useRef(null),I=i.useRef(null),x=i.useRef(null),T=i.useRef(null),m=g==="top"||g==="bottom",C=Ct(0,function(z,b){m&&_&&_({direction:z>b?"left":"right"})}),v=L(C,2),S=v[0],Q=v[1],H=Ct(0,function(z,b){!m&&_&&_({direction:z>b?"top":"bottom"})}),f=L(H,2),J=f[0],O=f[1],q=i.useState([0,0]),k=L(q,2),ce=k[0],w=k[1],D=i.useState([0,0]),Y=L(D,2),Z=Y[0],F=Y[1],ae=i.useState([0,0]),ee=L(ae,2),me=ee[0],se=ee[1],ue=i.useState([0,0]),ge=L(ue,2),M=ge[0],oe=ge[1],$e=Ma(new Map),Re=L($e,2),Ae=Re[0],He=Re[1],U=Ia(P,Ae,Z[0]),G=Me(ce,m),ve=Me(Z,m),Ee=Me(me,m),at=Me(M,m),nt=Math.floor(G)<Math.floor(ve+Ee),de=nt?G-at:G-Ee,Wt="".concat($,"-nav-operations-hidden"),he=0,ye=0;m&&l?(he=0,ye=Math.max(0,ve-de)):(he=Math.min(0,de-ve),ye=0);function Ge(z){return z<he?he:z>ye?ye:z}var We=i.useRef(null),Kt=i.useState(),rt=L(Kt,2),Ie=rt[0],it=rt[1];function Ke(){it(Date.now())}function Xe(){We.current&&clearTimeout(We.current)}za(V,function(z,b){function N(W,le){W(function(ne){var Ce=Ge(ne+le);return Ce})}return nt?(m?N(Q,z):N(O,b),Xe(),Ke(),!0):!1}),i.useEffect(function(){return Xe(),Ie&&(We.current=setTimeout(function(){it(0)},100)),Xe},[Ie]);var Xt=La(U,de,m?S:J,ve,Ee,at,ie(ie({},e),{},{tabs:P})),ot=L(Xt,2),Vt=ot[0],Ft=ot[1],lt=la(function(){var z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,b=U.get(z)||{width:0,height:0,left:0,right:0,top:0};if(m){var N=S;l?b.right<S?N=b.right:b.right+b.width>S+de&&(N=b.right+b.width-de):b.left<-S?N=-b.left:b.left+b.width>-S+de&&(N=-(b.left+b.width-de)),O(0),Q(Ge(N))}else{var W=J;b.top<-J?W=-b.top:b.top+b.height>-J+de&&(W=-(b.top+b.height-de)),Q(0),O(Ge(W))}}),Ut=i.useState(),ct=L(Ut,2),Se=ct[0],Pe=ct[1],qt=i.useState(!1),st=L(qt,2),Yt=st[0],dt=st[1],fe=P.filter(function(z){return!z.disabled}).map(function(z){return z.key}),xe=function(b){var N=fe.indexOf(Se||c),W=fe.length,le=(N+b+W)%W,ne=fe[le];Pe(ne)},Qt=function(b){var N=b.code,W=l&&m,le=fe[0],ne=fe[fe.length-1];switch(N){case"ArrowLeft":{m&&xe(W?1:-1);break}case"ArrowRight":{m&&xe(W?-1:1);break}case"ArrowUp":{b.preventDefault(),m||xe(-1);break}case"ArrowDown":{b.preventDefault(),m||xe(1);break}case"Home":{b.preventDefault(),Pe(le);break}case"End":{b.preventDefault(),Pe(ne);break}case"Enter":case"Space":{b.preventDefault(),B(Se,b);break}case"Backspace":case"Delete":{var Ce=fe.indexOf(Se),re=P.find(function(_e){return _e.key===Se}),Ue=et(re==null?void 0:re.closable,re==null?void 0:re.closeIcon,d,re==null?void 0:re.disabled);Ue&&(b.preventDefault(),b.stopPropagation(),d.onEdit("remove",{key:Se,event:b}),Ce===fe.length-1?xe(-1):xe(1));break}}},Oe={};m?Oe[l?"marginRight":"marginLeft"]=y:Oe.marginTop=y;var ut=P.map(function(z,b){var N=z.key;return i.createElement(ja,{id:r,prefixCls:$,key:N,tab:z,style:b===0?void 0:Oe,closable:z.closable,editable:d,active:N===c,focus:N===Se,renderWrapper:E,removeAriaLabel:h==null?void 0:h.removeAriaLabel,tabCount:fe.length,currentPosition:b+1,onClick:function(le){B(N,le)},onKeyDown:Qt,onFocus:function(){Yt||Pe(N),lt(N),Ke(),V.current&&(l||(V.current.scrollLeft=0),V.current.scrollTop=0)},onBlur:function(){Pe(void 0)},onMouseDown:function(){dt(!0)},onMouseUp:function(){dt(!1)}})}),vt=function(){return He(function(){var b,N=new Map,W=(b=I.current)===null||b===void 0?void 0:b.getBoundingClientRect();return P.forEach(function(le){var ne,Ce=le.key,re=(ne=I.current)===null||ne===void 0?void 0:ne.querySelector('[data-node-key="'.concat(Dt(Ce),'"]'));if(re){var Ue=Aa(re,W),_e=L(Ue,4),ta=_e[0],aa=_e[1],na=_e[2],ra=_e[3];N.set(Ce,{width:ta,height:aa,left:na,top:ra})}}),N})};i.useEffect(function(){vt()},[P.map(function(z){return z.key}).join("_")]);var ze=Bt(function(){var z=we(A),b=we(K),N=we(X);w([z[0]-b[0]-N[0],z[1]-b[1]-N[1]]);var W=we(T);se(W);var le=we(x);oe(le);var ne=we(I);F([ne[0]-W[0],ne[1]-W[1]]),vt()}),Zt=P.slice(0,Vt),Jt=P.slice(Ft+1),ft=[].concat($t(Zt),$t(Jt)),bt=U.get(c),kt=Ra({activeTabOffset:bt,horizontal:m,indicator:R,rtl:l}),ea=kt.style;i.useEffect(function(){lt()},[c,he,ye,Pt(bt),Pt(U),m]),i.useEffect(function(){ze()},[l]);var mt=!!ft.length,Te="".concat($,"-nav-wrap"),Ve,Fe,gt,ht;return m?l?(Fe=S>0,Ve=S!==ye):(Ve=S<0,Fe=S!==he):(gt=J<0,ht=J!==he),i.createElement(Ye,{onResize:ze},i.createElement("div",{ref:ca(t,A),role:"tablist","aria-orientation":m?"horizontal":"vertical",className:j("".concat($,"-nav"),n),style:a,onKeyDown:function(){Ke()}},i.createElement(Tt,{ref:K,position:"left",extra:s,prefixCls:$}),i.createElement(Ye,{onResize:ze},i.createElement("div",{className:j(Te,te(te(te(te({},"".concat(Te,"-ping-left"),Ve),"".concat(Te,"-ping-right"),Fe),"".concat(Te,"-ping-top"),gt),"".concat(Te,"-ping-bottom"),ht)),ref:V},i.createElement(Ye,{onResize:ze},i.createElement("div",{ref:I,className:"".concat($,"-nav-list"),style:{transform:"translate(".concat(S,"px, ").concat(J,"px)"),transition:Ie?"none":void 0}},ut,i.createElement(jt,{ref:T,prefixCls:$,locale:h,editable:d,style:ie(ie({},ut.length===0?void 0:Oe),{},{visibility:mt?"hidden":null})}),i.createElement("div",{className:j("".concat($,"-ink-bar"),te({},"".concat($,"-ink-bar-animated"),o.inkBar)),style:ea}))))),i.createElement(Da,be({},e,{removeAriaLabel:h==null?void 0:h.removeAriaLabel,ref:x,prefixCls:$,tabs:ft,className:!mt&&Wt,tabMoving:!!Ie})),i.createElement(Tt,{ref:X,position:"right",extra:s,prefixCls:$})))}),At=i.forwardRef(function(e,t){var n=e.prefixCls,a=e.className,r=e.style,o=e.id,c=e.active,l=e.tabKey,s=e.children;return i.createElement("div",{id:o&&"".concat(o,"-panel-").concat(l),role:"tabpanel",tabIndex:c?0:-1,"aria-labelledby":o&&"".concat(o,"-tab-").concat(l),"aria-hidden":!c,style:r,className:j(n,c&&"".concat(n,"-active"),a),ref:t},s)}),Ha=["renderTabBar"],Ga=["label","key"],Wa=function(t){var n=t.renderTabBar,a=Ne(t,Ha),r=i.useContext(je),o=r.tabs;if(n){var c=ie(ie({},a),{},{panes:o.map(function(l){var s=l.label,d=l.key,h=Ne(l,Ga);return i.createElement(At,be({tab:s,key:d,tabKey:d},h))})});return n(c,Rt)}return i.createElement(Rt,a)},Ka=["key","forceRender","style","className","destroyInactiveTabPane"],Xa=function(t){var n=t.id,a=t.activeKey,r=t.animated,o=t.tabPosition,c=t.destroyInactiveTabPane,l=i.useContext(je),s=l.prefixCls,d=l.tabs,h=r.tabPane,g="".concat(s,"-tabpane");return i.createElement("div",{className:j("".concat(s,"-content-holder"))},i.createElement("div",{className:j("".concat(s,"-content"),"".concat(s,"-content-").concat(o),te({},"".concat(s,"-content-animated"),h))},d.map(function(y){var E=y.key,B=y.forceRender,_=y.style,R=y.className,p=y.destroyInactiveTabPane,$=Ne(y,Ka),P=E===a;return i.createElement(sa,be({key:E,visible:P,forceRender:B,removeOnLeave:!!(c||p),leavedClassName:"".concat(g,"-hidden")},r.tabPaneMotion),function(A,K){var X=A.style,V=A.className;return i.createElement(At,be({},$,{prefixCls:g,id:n,tabKey:E,animated:h,active:P,style:ie(ie({},_),X),className:j(R,V),ref:K}))})})))};function Va(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},t;return e===!1?t={inkBar:!1,tabPane:!1}:e===!0?t={inkBar:!0,tabPane:!1}:t=ie({inkBar:!0},Ze(e)==="object"?e:{}),t.tabPaneMotion&&t.tabPane===void 0&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}var Fa=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],It=0,Ua=i.forwardRef(function(e,t){var n=e.id,a=e.prefixCls,r=a===void 0?"rc-tabs":a,o=e.className,c=e.items,l=e.direction,s=e.activeKey,d=e.defaultActiveKey,h=e.editable,g=e.animated,y=e.tabPosition,E=y===void 0?"top":y,B=e.tabBarGutter,_=e.tabBarStyle,R=e.tabBarExtraContent,p=e.locale,$=e.more,P=e.destroyInactiveTabPane,A=e.renderTabBar,K=e.onChange,X=e.onTabClick,V=e.onTabScroll,I=e.getPopupContainer,x=e.popupClassName,T=e.indicator,m=Ne(e,Fa),C=i.useMemo(function(){return(c||[]).filter(function(M){return M&&Ze(M)==="object"&&"key"in M})},[c]),v=l==="rtl",S=Va(g),Q=i.useState(!1),H=L(Q,2),f=H[0],J=H[1];i.useEffect(function(){J(ua())},[]);var O=yt(function(){var M;return(M=C[0])===null||M===void 0?void 0:M.key},{value:s,defaultValue:d}),q=L(O,2),k=q[0],ce=q[1],w=i.useState(function(){return C.findIndex(function(M){return M.key===k})}),D=L(w,2),Y=D[0],Z=D[1];i.useEffect(function(){var M=C.findIndex(function($e){return $e.key===k});if(M===-1){var oe;M=Math.max(0,Math.min(Y,C.length-1)),ce((oe=C[M])===null||oe===void 0?void 0:oe.key)}Z(M)},[C.map(function(M){return M.key}).join("_"),k,Y]);var F=yt(null,{value:n}),ae=L(F,2),ee=ae[0],me=ae[1];i.useEffect(function(){n||(me("rc-tabs-".concat(It)),It+=1)},[]);function se(M,oe){X==null||X(M,oe);var $e=M!==k;ce(M),$e&&(K==null||K(M))}var ue={id:ee,activeKey:k,animated:S,tabPosition:E,rtl:v,mobile:f},ge=ie(ie({},ue),{},{editable:h,locale:p,more:$,tabBarGutter:B,onTabClick:se,onTabScroll:V,extra:R,style:_,panes:null,getPopupContainer:I,popupClassName:x,indicator:T});return i.createElement(je.Provider,{value:{tabs:C,prefixCls:r}},i.createElement("div",be({ref:t,id:n,className:j(r,"".concat(r,"-").concat(E),te(te(te({},"".concat(r,"-mobile"),f),"".concat(r,"-editable"),h),"".concat(r,"-rtl"),v),o)},m),i.createElement(Wa,be({},ge,{renderTabBar:A})),i.createElement(Xa,be({destroyInactiveTabPane:P},ue,{animated:S}))))});const qa={motionAppear:!1,motionEnter:!0,motionLeave:!0};function Ya(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{inkBar:!0,tabPane:!1},n;return t===!1?n={inkBar:!1,tabPane:!1}:t===!0?n={inkBar:!0,tabPane:!0}:n=Object.assign({inkBar:!0},typeof t=="object"?t:{}),n.tabPane&&(n.tabPaneMotion=Object.assign(Object.assign({},qa),{motionName:va(e,"switch")})),n}var Qa=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n};function Za(e){return e.filter(t=>t)}function Ja(e,t){if(e)return e;const n=fa(t).map(a=>{if(i.isValidElement(a)){const{key:r,props:o}=a,c=o||{},{tab:l}=c,s=Qa(c,["tab"]);return Object.assign(Object.assign({key:String(r)},s),{label:l})}return null});return Za(n)}const ka=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[St(e,"slide-up"),St(e,"slide-down")]]},en=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:a,cardGutter:r,colorBorderSecondary:o,itemSelectedColor:c}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${u(e.lineWidth)} ${e.lineType} ${o}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:c,background:e.colorBgContainer},[`${t}-tab-focus`]:Object.assign({},zt(e,-3)),[`${t}-ink-bar`]:{visibility:"hidden"},[`& ${t}-tab${t}-tab-focus ${t}-tab-btn`]:{outline:"none"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:u(r)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:u(r)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${u(e.borderRadiusLG)} 0 0 ${u(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},tn=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},Je(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${u(a)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},ke),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${u(e.paddingXXS)} ${u(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},an=e=>{const{componentCls:t,margin:n,colorBorderSecondary:a,horizontalMargin:r,verticalItemPadding:o,verticalItemMargin:c,calc:l}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:r,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${u(e.lineWidth)} ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:o,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:c},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:u(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${u(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${u(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},nn=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:a,horizontalItemPaddingSM:r,horizontalItemPaddingLG:o}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:r,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:o,fontSize:e.titleFontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${u(e.borderRadius)} ${u(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${u(e.borderRadius)} ${u(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${u(e.borderRadius)} ${u(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${u(e.borderRadius)} 0 0 ${u(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a}}}}}},rn=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:a,iconCls:r,tabsHorizontalItemMargin:o,horizontalItemPadding:c,itemSelectedColor:l,itemColor:s}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:c,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:s,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},Qe(e)),"&:hover":{color:a},[`&${d}-active ${d}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${d}-focus ${d}-btn`]:Object.assign({},zt(e)),[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${r}`]:{margin:0},[`${r}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:o}}}},on=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:r,calc:o}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:u(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:u(e.marginXS)},marginLeft:{_skip_check_:!0,value:u(o(e.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:r},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ln=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:a,cardGutter:r,itemHoverColor:o,itemActiveColor:c,colorBorderSecondary:l}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},Je(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:a,marginLeft:{_skip_check_:!0,value:r},padding:u(e.paddingXS),background:"transparent",border:`${u(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:o},"&:active, &:focus:not(:focus-visible)":{color:c}},Qe(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),rn(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},Qe(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},cn=e=>{const t=e.controlHeightLG;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:t,cardPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${e.paddingXXS*1.5}px ${e.padding}px`,cardPaddingLG:`${e.paddingXS}px ${e.padding}px ${e.paddingXXS*1.5}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}},sn=Mt("Tabs",e=>{const t=Lt(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${u(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${u(e.horizontalItemGutter)}`});return[nn(t),on(t),an(t),tn(t),en(t),ln(t),ka(t)]},cn),dn=()=>null;var un=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n};const Ht=e=>{var t,n,a,r,o,c,l,s,d,h,g;const{type:y,className:E,rootClassName:B,size:_,onEdit:R,hideAdd:p,centered:$,addIcon:P,removeIcon:A,moreIcon:K,more:X,popupClassName:V,children:I,items:x,animated:T,style:m,indicatorSize:C,indicator:v}=e,S=un(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:Q}=S,{direction:H,tabs:f,getPrefixCls:J,getPopupContainer:O}=i.useContext(Be),q=J("tabs",Q),k=ba(q),[ce,w,D]=sn(q,k);let Y;y==="editable-card"&&(Y={onEdit:(ue,ge)=>{let{key:M,event:oe}=ge;R==null||R(ue==="add"?oe:M,ue)},removeIcon:(t=A??(f==null?void 0:f.removeIcon))!==null&&t!==void 0?t:i.createElement(ma,null),addIcon:(P??(f==null?void 0:f.addIcon))||i.createElement(Ta,null),showAdd:p!==!0});const Z=J(),F=Nt(_),ae=Ja(x,I),ee=Ya(q,T),me=Object.assign(Object.assign({},f==null?void 0:f.style),m),se={align:(n=v==null?void 0:v.align)!==null&&n!==void 0?n:(a=f==null?void 0:f.indicator)===null||a===void 0?void 0:a.align,size:(l=(o=(r=v==null?void 0:v.size)!==null&&r!==void 0?r:C)!==null&&o!==void 0?o:(c=f==null?void 0:f.indicator)===null||c===void 0?void 0:c.size)!==null&&l!==void 0?l:f==null?void 0:f.indicatorSize};return ce(i.createElement(Ua,Object.assign({direction:H,getPopupContainer:O},S,{items:ae,className:j({[`${q}-${F}`]:F,[`${q}-card`]:["card","editable-card"].includes(y),[`${q}-editable-card`]:y==="editable-card",[`${q}-centered`]:$},f==null?void 0:f.className,E,B,w,D,k),popupClassName:j(V,w,D,k),style:me,editable:Y,more:Object.assign({icon:(g=(h=(d=(s=f==null?void 0:f.more)===null||s===void 0?void 0:s.icon)!==null&&d!==void 0?d:f==null?void 0:f.moreIcon)!==null&&h!==void 0?h:K)!==null&&g!==void 0?g:i.createElement(_a,null),transitionName:`${Z}-slide-up`},X),prefixCls:q,animated:ee,indicator:se})))};Ht.TabPane=dn;var vn=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n};const Gt=e=>{var{prefixCls:t,className:n,hoverable:a=!0}=e,r=vn(e,["prefixCls","className","hoverable"]);const{getPrefixCls:o}=i.useContext(Be),c=o("card",t),l=j(`${c}-grid`,n,{[`${c}-grid-hoverable`]:a});return i.createElement("div",Object.assign({},r,{className:l}))},fn=e=>{const{antCls:t,componentCls:n,headerHeight:a,headerPadding:r,tabsMarginBottom:o}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:`0 ${u(r)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${u(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)} 0 0`},De()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},ke),{[`
          > ${n}-typography,
          > ${n}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:o,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${u(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},bn=e=>{const{cardPaddingBase:t,colorBorderSecondary:n,cardShadow:a,lineWidth:r}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${u(r)} 0 0 0 ${n},
      0 ${u(r)} 0 0 ${n},
      ${u(r)} ${u(r)} 0 0 ${n},
      ${u(r)} 0 0 0 ${n} inset,
      0 ${u(r)} 0 0 ${n} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},mn=e=>{const{componentCls:t,iconCls:n,actionsLiMargin:a,cardActionsIconSize:r,colorBorderSecondary:o,actionsBg:c}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:c,borderTop:`${u(e.lineWidth)} ${e.lineType} ${o}`,display:"flex",borderRadius:`0 0 ${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)}`},De()),{"& > li":{margin:a,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${n}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:u(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${n}`]:{fontSize:r,lineHeight:u(e.calc(r).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${u(e.lineWidth)} ${e.lineType} ${o}`}}})},gn=e=>Object.assign(Object.assign({margin:`${u(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},De()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},ke),"&-description":{color:e.colorTextDescription}}),hn=e=>{const{componentCls:t,colorFillAlter:n,headerPadding:a,bodyPadding:r}=e;return{[`${t}-head`]:{padding:`0 ${u(a)}`,background:n,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${u(e.padding)} ${u(r)}`}}},pn=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},$n=e=>{const{componentCls:t,cardShadow:n,cardHeadPadding:a,colorBorderSecondary:r,boxShadowTertiary:o,bodyPadding:c,extraColor:l}=e;return{[t]:Object.assign(Object.assign({},Je(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:o},[`${t}-head`]:fn(e),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:c,borderRadius:`0 0 ${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)}`},De()),[`${t}-grid`]:bn(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:mn(e),[`${t}-meta`]:gn(e)}),[`${t}-bordered`]:{border:`${u(e.lineWidth)} ${e.lineType} ${r}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:n}},[`${t}-contain-grid`]:{borderRadius:`${u(e.borderRadiusLG)} ${u(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:a}}},[`${t}-type-inner`]:hn(e),[`${t}-loading`]:pn(e),[`${t}-rtl`]:{direction:"rtl"}}},yn=e=>{const{componentCls:t,bodyPaddingSM:n,headerPaddingSM:a,headerHeightSM:r,headerFontSizeSM:o}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:r,padding:`0 ${u(a)}`,fontSize:o,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:n}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},Sn=e=>{var t,n;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,headerHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:(t=e.bodyPadding)!==null&&t!==void 0?t:e.paddingLG,headerPadding:(n=e.headerPadding)!==null&&n!==void 0?n:e.paddingLG}},xn=Mt("Card",e=>{const t=Lt(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[$n(t),yn(t)]},Sn);var Ot=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n};const Cn=e=>{const{actionClasses:t,actions:n=[],actionStyle:a}=e;return i.createElement("ul",{className:t,style:a},n.map((r,o)=>{const c=`action-${o}`;return i.createElement("li",{style:{width:`${100/n.length}%`},key:c},i.createElement("span",null,r))}))},_n=i.forwardRef((e,t)=>{const{prefixCls:n,className:a,rootClassName:r,style:o,extra:c,headStyle:l={},bodyStyle:s={},title:d,loading:h,bordered:g,variant:y,size:E,type:B,cover:_,actions:R,tabList:p,children:$,activeTabKey:P,defaultActiveTabKey:A,tabBarExtraContent:K,hoverable:X,tabProps:V={},classNames:I,styles:x}=e,T=Ot(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:m,direction:C,card:v}=i.useContext(Be),[S]=ga("card",y,g),Q=U=>{var G;(G=e.onTabChange)===null||G===void 0||G.call(e,U)},H=U=>{var G;return j((G=v==null?void 0:v.classNames)===null||G===void 0?void 0:G[U],I==null?void 0:I[U])},f=U=>{var G;return Object.assign(Object.assign({},(G=v==null?void 0:v.styles)===null||G===void 0?void 0:G[U]),x==null?void 0:x[U])},J=i.useMemo(()=>{let U=!1;return i.Children.forEach($,G=>{(G==null?void 0:G.type)===Gt&&(U=!0)}),U},[$]),O=m("card",n),[q,k,ce]=xn(O),w=i.createElement(ya,{loading:!0,active:!0,paragraph:{rows:4},title:!1},$),D=P!==void 0,Y=Object.assign(Object.assign({},V),{[D?"activeKey":"defaultActiveKey"]:D?P:A,tabBarExtraContent:K});let Z;const F=Nt(E),ae=!F||F==="default"?"large":F,ee=p?i.createElement(Ht,Object.assign({size:ae},Y,{className:`${O}-head-tabs`,onChange:Q,items:p.map(U=>{var{tab:G}=U,ve=Ot(U,["tab"]);return Object.assign({label:G},ve)})})):null;if(d||c||ee){const U=j(`${O}-head`,H("header")),G=j(`${O}-head-title`,H("title")),ve=j(`${O}-extra`,H("extra")),Ee=Object.assign(Object.assign({},l),f("header"));Z=i.createElement("div",{className:U,style:Ee},i.createElement("div",{className:`${O}-head-wrapper`},d&&i.createElement("div",{className:G,style:f("title")},d),c&&i.createElement("div",{className:ve,style:f("extra")},c)),ee)}const me=j(`${O}-cover`,H("cover")),se=_?i.createElement("div",{className:me,style:f("cover")},_):null,ue=j(`${O}-body`,H("body")),ge=Object.assign(Object.assign({},s),f("body")),M=i.createElement("div",{className:ue,style:ge},h?w:$),oe=j(`${O}-actions`,H("actions")),$e=R!=null&&R.length?i.createElement(Cn,{actionClasses:oe,actionStyle:f("actions"),actions:R}):null,Re=ha(T,["onTabChange"]),Ae=j(O,v==null?void 0:v.className,{[`${O}-loading`]:h,[`${O}-bordered`]:S!=="borderless",[`${O}-hoverable`]:X,[`${O}-contain-grid`]:J,[`${O}-contain-tabs`]:p==null?void 0:p.length,[`${O}-${F}`]:F,[`${O}-type-${B}`]:!!B,[`${O}-rtl`]:C==="rtl"},a,r,k,ce),He=Object.assign(Object.assign({},v==null?void 0:v.style),o);return q(i.createElement("div",Object.assign({ref:t},Re,{className:Ae,style:He}),Z,se,M,$e))});var wn=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n};const En=e=>{const{prefixCls:t,className:n,avatar:a,title:r,description:o}=e,c=wn(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:l}=i.useContext(Be),s=l("card",t),d=j(`${s}-meta`,n),h=a?i.createElement("div",{className:`${s}-meta-avatar`},a):null,g=r?i.createElement("div",{className:`${s}-meta-title`},r):null,y=o?i.createElement("div",{className:`${s}-meta-description`},o):null,E=g||y?i.createElement("div",{className:`${s}-meta-detail`},g,y):null;return i.createElement("div",Object.assign({},c,{className:d}),h,E)},tt=_n;tt.Grid=Gt;tt.Meta=En;const{TextArea:Pn}=wa,Mn=()=>{const[e,t]=i.useState("");i.useEffect(()=>{const a=localStorage.getItem("notice")||"";t(a)},[]);const n=()=>{localStorage.setItem("notice",e),$a.success("就诊须知已保存")};return qe.jsx(tt,{title:"就诊须知设置",extra:qe.jsx(pa,{type:"primary",onClick:n,children:"保存"}),children:qe.jsx(Pn,{rows:10,placeholder:"请输入就诊须知内容",value:e,onChange:a=>t(a.target.value)})})};export{Mn as default};
