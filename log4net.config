﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <section name="log4net" type="System.Configuration.IgnoreSectionHandler"/>
  </configSections>
  <appSettings>
  </appSettings>
  <log4net>
    <!--定义输出到文件中-->
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <!--1. 文件路径，如果RollingStyle为Composite或Date，则这里设置为目录，文件名在DatePattern里设置，其他则这里要有文件名。已经扩展支持虚拟目录-->
      <file value="Log\" />
      <!--2. 创建新文件的方式，可选为Size（按文件大小），Date（按日期），Once（每启动一次创建一个文件），Composite（按日期及文件大小），默认为Composite-->
      <rollingStyle value="Composite" />
      <!--3. 当RollingStyle为Composite或Date，这里设置文件名格式-->
      <datePattern value='yyyy-MM-dd".log"' />
      <!--4. true/false，默认为true。为true时，RollingStyler的date值将无效。且为true时，需要在file里指定文件名，所有日志都会记录在这个文件里。-->
      <staticLogFileName value="false" />
      <!--5. 当rollingStyle为Composite或Size，这里设置最大文件大小（可以KB，MB，GB为单位，默认为字节）-->
      <maximumFileSize value="30MB" />
      <!--6. 默认值为-1。当文件超过MaximumFileSize的大小时，如果要创建新的文件来存储日志，会根据CountDirection的值来重命名文件。
             大于-1的值时，file里指定的文件名会依次加上.0,.1,.2递增。
             当等于或小于-1时，创建依赖于MaxSizeRollBackups参数值，创建备份日志数。-->
      <countDirection value="-1" />
      <!--7. 备份日志数目，默认为0。在CountDirection为负数时有效。-->
      <maxSizeRollBackups value="-1" />
      <!--8. true/false，默认为true。当文件存在时，是否在原文件上追加内容。-->
      <appendToFile value="true" />

      <layout type="log4net.Layout.PatternLayout">
        <!-- 日志起始输出 -->
        <header value="-----------程序开始运行-----------
" />
        <!-- 日志结束输出 -->
        <footer value="-----------程序结束运行-----------

" />
        <conversionPattern value="%date [%thread] %-5level [%ndc] - %message%newline" />
        <conversionPattern value="%date TID:[%thread] Level：%-5level - %message%newline" />
      </layout>
    </appender>
    <!--定义输出到控制台命令行中-->
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />
      </layout>
    </appender>
    <!--定义输出到windows事件中-->
    <appender name="EventLogAppender" type="log4net.Appender.EventLogAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />
      </layout>
    </appender>
    <!--定义输出到数据库中，这里举例输出到Access数据库中，数据库为C盘的log4net.mdb-->
    <appender name="AdoNetAppender_Access" type="log4net.Appender.AdoNetAppender">
      <connectionString value="Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:log4net.mdb" />
      <commandText value="INSERT INTO LogDetails ([LogDate],[Thread],[Level],[Logger],[Message]) VALUES (@logDate, @thread, @logLevel, @logger,@message)" />
      <!--定义各个参数-->
      <parameter>
        <parameterName value="@logDate" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%date" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@thread" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%thread" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logLevel" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%level" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logger" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%logger" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@message" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
    </appender>
    <!--定义日志的输出媒介，下面定义日志以四种方式输出。也可以下面的按照一种类型或其他类型输出。-->
    <root>
      <!-- 记录哪个级别以上的日志:  OFF->FATAL->ERROR->WARN->INFO->DEBUG/ALL -->
      <level value="DEBUG" />
      <!--文件形式记录日志-->
      <appender-ref ref="RollingLogFileAppender" />
      <!--控制台控制显示日志-->
      <!--<appender-ref ref="ConsoleAppender" />-->
      <!--Windows事件日志-->
      <!--<appender-ref ref="EventLogAppender" />-->
      <!-- 如果不启用相应的日志记录，可以通过这种方式注释掉
      <appender-ref ref="AdoNetAppender_Access" />
      -->
    </root>
  </log4net>
</configuration>