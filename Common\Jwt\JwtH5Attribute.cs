﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using zhiying_online.Common;

namespace zhiying_online.Common.Jwt
{
    /// <summary>
    /// 小程序授权专用验证
    /// </summary>
    public class JwtH5Attribute : ActionFilterAttribute
    {
        /// <summary>
        /// h5授权accesstoken
        /// </summary>
        /// <param name="filterContext"></param>
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            //获取访问的Accesskey
            var T = filterContext.HttpContext.Request.Headers;

            var accesstoken = T["Authorization"];
            if (string.IsNullOrEmpty(accesstoken))
            {
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }
            accesstoken = accesstoken.ToString().Replace("Bearer ", "");

            //解密获取参数
            JwtSecurityToken encodedJwt = null;
            try
            {
                encodedJwt = new JwtSecurityTokenHandler().ReadJwtToken(accesstoken);
            }
            catch
            {
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }

            //判断登录失败
            if (encodedJwt == null)
            {
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }

            //判断用户信息
            if (encodedJwt.Payload.Count() == 0)
            {
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }

            //验证时间
            var epf = encodedJwt.Payload.FirstOrDefault(s => s.Key == "exp").Value;
            if (epf == null)
            {
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }

            //验证时间
            var endtime = epf.ToString();
            if (Tools.GetDateTimeFromXml(long.Parse(endtime)) < DateTime.Now)
            {
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }

            //判断账号类型
            var role = encodedJwt.Payload.FirstOrDefault(s => s.Key == "role").Value.ToString();
            if (role!="user")
            {
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }

            //验证密文
            var Configuration = Tools.GetConfiguration();
            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Configuration["JwtSecret"])), // 替换为你的密钥
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            };
            try
            {
                var principal = tokenHandler.ValidateToken(accesstoken.ToString().Replace("Bearer ", ""), validationParameters, out var validatedToken);
                encodedJwt = (JwtSecurityToken)validatedToken;
            }
            catch (Exception)
            {
                // 签名验证失败或token无效
                ErrHandle(filterContext, RetResult(RetEnum.accesstoken错误));
                return;
            }


            base.OnActionExecuting(filterContext);
        }

        private void ErrHandle(ActionExecutingContext filterContext, object eo)
        {
            var Result = new JsonResult(eo);
            filterContext.Result = Result;
        }

        /// <summary>
        /// 返回对象
        /// </summary>
        /// <param name="rte"></param>
        /// <param name="obj"></param>
        /// <param name="othermsg"></param>
        /// <returns></returns>
        public RetMsg RetResult(RetEnum rte, object obj = null, string othermsg = "")
        {
            return new RetMsg { code = (int)rte, msg = Enum.GetName(typeof(RetEnum), rte) + (!string.IsNullOrEmpty(othermsg) ? "[" + othermsg + "]" : string.Empty), result = obj };
        }

        public enum RetEnum
        {
            accesstoken错误 = -5,
        }
    }
}
