﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zhiying_online.Common.Jwt;
using zhiying_online.Common.wechat;
using zhiying_online.Model;

namespace zhiying_online.Common.BLL
{
    /// <summary>
    /// 获取用户信息
    /// </summary>
    public class UserHandle
    {
        /// <summary>
        /// 获取微信用户列表
        /// </summary>
        /// <param name="openid"></param>
        /// <param name="nickname"></param>
        /// <param name="star"></param>
        /// <param name="end"></param>
        /// <param name="PageSize"></param>
        /// <param name="PageNo"></param>
        /// <returns></returns>
        public static RetMsg GetUserList(string openid, string nickname, DateTime? star, DateTime? end, int PageSize = 10, int PageNo = 1)
        {
            using (var db = new Model.zhiying_online())
            {
                IEnumerable<user> list = db.user.Where(m => m.u_id > 0);
                if (!string.IsNullOrEmpty(openid))
                {
                    list = list.Where(m => m.u_openid == openid);
                }
                if (!string.IsNullOrEmpty(nickname))
                {
                    list = list.Where(m => m.u_nickname.Contains(nickname));
                }
                if (star.HasValue && end.HasValue)
                {
                    list = list.Where(m => m.u_time >= star && m.u_time <= end);
                }

                //获取数量
                var count = list.Count();

                //获取明细
                list = list.OrderByDescending(m => m.u_time).Skip((PageNo - 1) * PageSize).Take(PageSize).ToList();

                return new RetMsg
                {
                    code = 0,
                    msg = "请求成功",
                    count = count,
                    result = list.Select(m => new
                    {
                        id = m.u_id,
                        openid = m.u_openid,
                        headerimg = m.u_headerimg,
                        nickname = m.u_nickname,
                        unionid = m.u_unionid,
                        time = m.u_time
                    })
                };
            }
        }

        /// <summary>
        /// 根据id获取用户
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static user GetUserByID(long id)
        {
            using (var db = new Model.zhiying_online())
            {
                return db.user.Find(id);
            }
        }

        /// <summary>
        /// 根据openid获取用户
        /// </summary>
        /// <param name="openid"></param>
        /// <returns></returns>
        public static user GetUserByOpenid(string openid)
        {
            using (var db = new Model.zhiying_online())
            {
                return db.user.FirstOrDefault(m => m.u_openid == openid);
            }
        }

        /// <summary>
        /// 保存用户
        /// </summary>
        /// <param name="id"></param>
        /// <param name="openid"></param>
        /// <param name="unionid"></param>
        /// <param name="nickname"></param>
        /// <param name="headerimg"></param>
        /// <returns></returns>
        public static user SaveUser(long? id, string openid, string unionid, string nickname, string headerimg)
        {
            using (var db = new Model.zhiying_online())
            {
                user _user = null;
                if (id.HasValue)
                {
                    _user = db.user.Find(id);
                    if (_user == null)
                    {
                        return null;
                    }
                    _user.u_nickname = nickname;
                    _user.u_headerimg = headerimg;
                }
                else
                {
                    _user = new user
                    {
                        u_openid = openid,
                        u_unionid = unionid,
                        u_nickname = nickname,
                        u_headerimg = headerimg,
                        u_time = DateTime.Now,
                        u_id = Tools.Get_Id()
                    };
                    db.user.Add(_user);
                }
                db.SaveChanges();

                return _user;
            }
        }

        /// <summary>
        /// 根据code获取用户
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static object GetUserByCode(string code)
        {
            var wxuser = WechatHandles.GetOpenid(code);
            if (wxuser.errcode != 0)
            {
                var retMsg = new RetMsg
                {
                    code = wxuser.errcode,
                    msg = wxuser.errmsg
                };
                return retMsg;
            }
            user _user = GetUserByOpenid(wxuser.openid);
            if (_user == null)
            {
                _user = SaveUser(null, wxuser.openid, "", "", "");
            }


            //登录成功分发accesstoken
            var Configuration = Tools.GetConfiguration();
            var ExpiresIn = 7200;
            var securityKey = Configuration["JwtSecret"]; // 签名密钥
            var keyByteArray = Encoding.ASCII.GetBytes(securityKey);
            var signingKey = new SymmetricSecurityKey(keyByteArray);
            var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

            //定义角色
            var claim = new JwtHandle.ClaimModel
            {
                bid = "0",
                id = _user.u_openid,
                ip = Tools.GetUserIp(),
                name = _user.u_openid,
                role = "user",
                usetype = "user",
            };

            //调用生成
            var token = JwtHandle.BuildJwtToken(new JwtHandle.PermissionRequirement(signingCredentials)
            {
                claim = claim,
                ExpiresIn = ExpiresIn,
                issuer = "zhiying_online",
                SecurityKey = securityKey,
            });

            return new
            {
                code = 0,
                msg = "请求成功",
                result = new
                {
                    id = _user.u_id,
                    openid = _user.u_openid,
                    headerimg = _user.u_headerimg,
                    nickname = _user.u_nickname,
                    unionid = _user.u_unionid,
                    time = _user.u_time
                },
                token = token
            };
        }

        /// <summary>
        /// 根据openid获取用户
        /// </summary>
        /// <param name="openid"></param>
        /// <returns></returns>
        public static object GetUserInfoByOpenid(string openid)
        {
            user _user = GetUserByOpenid(openid);
            if (_user == null)
            {
                _user = SaveUser(null, _user.u_openid, "", "", "");
            }

            //登录成功分发accesstoken
            var Configuration = Tools.GetConfiguration();
            var ExpiresIn = 7200;
            var securityKey = Configuration["JwtSecret"]; // 签名密钥
            var keyByteArray = Encoding.ASCII.GetBytes(securityKey);
            var signingKey = new SymmetricSecurityKey(keyByteArray);
            var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

            //定义角色
            var claim = new JwtHandle.ClaimModel
            {
                bid = "0",
                id = _user.u_openid,
                ip = Tools.GetUserIp(),
                name = _user.u_openid,
                role = "user",
                usetype = "user",
            };

            //调用生成
            var token = JwtHandle.BuildJwtToken(new JwtHandle.PermissionRequirement(signingCredentials)
            {
                claim = claim,
                ExpiresIn = ExpiresIn,
                issuer = "zhiying_online",
                SecurityKey = securityKey,
            });

            return new
            {
                code = 0,
                msg = "请求成功",
                result = new
                {
                    id = _user.u_id,
                    openid = _user.u_openid,
                    headerimg = _user.u_headerimg,
                    nickname = _user.u_nickname,
                    unionid = _user.u_unionid,
                    time = _user.u_time
                },
                token = token
            };
        }
    }
}
