﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using zhiying_online.Common.Attr;
using zhiying_online.Common.BLL;
using zhiying_online.Common.Jwt;

namespace zhiying_online.Controllers
{
    /// <summary>
    /// 预约
    /// </summary>
    [Route("[controller]/[action]")]
    public class BookingController : Controller
    {
        /// <summary>
        /// 获取预约列表_ht
        /// </summary>
        /// <param name="name">预约人</param>
        /// <param name="phone">手机号</param>
        /// <param name="idcard">身份证</param>
        /// <param name="type">预约类型 1PET 2DR 3CT</param>
        /// <param name="star">预约开始时间</param>
        /// <param name="end">预约结束时间</param>
        /// <param name="status">状态 已取消 待检查 已完成</param>
        /// <param name="PageSize"></param>
        /// <param name="PageNo"></param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":[{"name":"预约人","phone":"就诊手机号","idcard":"就诊人身份证","id":"预约记录id","id":"预约记录id","uid":"微信用户ID","pid":"预约人ID","type":"预约类型","time":"发起预约时间","bookingtime":"预约时间","enabletime":"取消时间","status":"状态"}],"count":"数量"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtAttribute]
        public IActionResult GetBookingList(string name, string phone, string idcard, int? type, DateTime? star, DateTime? end, string status, int PageSize = 10, int PageNo = 1)
        {
            return Json(BookingHandle.GetBookingList(name, phone, idcard, type, star, end, status, PageSize, PageNo));
        }

        /// <summary>
        /// 根据就诊人获取预约列表
        /// </summary>
        /// <param name="pid">预约人ID</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功","result":[{"name":"就诊人","phone":"就诊手机号","idcard":"就诊人身份证","id":"预约记录id","id":"预约记录id","uid":"微信用户ID","pid":"预约人ID","type":"预约类型","time":"发起预约时间","bookingtime":"预约时间","enabletime":"取消时间","status":"状态"}]}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtH5Attribute]
        public IActionResult GetBookingListByPid(long? pid)
        {
            return Json(BookingHandle.GetBookingListByPid(pid));
        }

        /// <summary>
        /// 发起预约
        /// </summary>
        /// <param name="uid">微信用户ID</param>
        /// <param name="pid">就诊人ID</param>
        /// <param name="type">预约类型 1PET 2DR 3CT</param>
        /// <param name="bookingtime">预约时间</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtH5Attribute]
        [RequestLog]
        public IActionResult SaveBooking(long? uid, long? pid, int type, DateTime? bookingtime)
        {
            return Json(BookingHandle.SaveBooking(uid,pid,type, bookingtime));
        }

        /// <summary>
        /// 取消预约
        /// </summary>
        /// <param name="id">预约记录id</param>
        /// <response code="200">
        /// <para>请求成功：{"code":0,"msg":"请求成功"}</para>
        /// <para>请求失败：{"code":1,"msg":"请求失败"}</para>
        /// </response >
        /// <returns></returns>
        [HttpPost]
        [JwtH5Attribute]
        [RequestLog]
        public IActionResult EnableBooking(long? id)
        {
            return Json(BookingHandle.EnableBooking(id));
        }
    }
}
